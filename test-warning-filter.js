// 测试三色预警数据过滤逻辑
const testData = {
  label: ['社区A', '社区B', '社区C', '社区D', '社区E'],
  red: [0, 5, 0, 0, 3],
  yellow: [0, 2, 0, 1, 0],
  blue: [0, 0, 0, 0, 0]
}

// 模拟过滤逻辑
const originalLabels = testData.label || []
const originalRedData = testData.red || []
const originalYellowData = testData.yellow || []
const originalBlueData = testData.blue || []

// 过滤掉所有数据都为0的项目
const filteredData = originalLabels
  .map((label, index) => ({
    label,
    red: originalRedData[index] || 0,
    yellow: originalYellowData[index] || 0,
    blue: originalBlueData[index] || 0
  }))
  .filter(item => item.red > 0 || item.yellow > 0 || item.blue > 0)

console.log('原始数据:', testData)
console.log('过滤后的数据:', filteredData)

// 预期结果：只显示社区B（红5，黄2，蓝0）、社区D（红0，黄1，蓝0）、社区E（红3，黄0，蓝0）
// 社区A和社区C应该被过滤掉，因为它们所有预警数据都是0
