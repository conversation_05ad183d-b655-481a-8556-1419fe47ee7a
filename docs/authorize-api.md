# 授权API使用说明

## 概述

本系统在首页加载时会自动调用第三方授权接口，用于获取系统访问权限。

## 接口信息

- **接口地址**: `https://qlwarehouse.muulin.cn:40300/kangbank/open/authorize`
- **请求方法**: GET
- **参数**:
  - `cityCode`: 固定值 `************`
  - `code`: SM4加密后的字符串

## Code生成规则

### 原始数据格式
```
社区行政编号 + "|" + 13位时间戳
```

**示例**:
```
************|*************
```

### 加密参数
- **加密算法**: SM4
- **加密密钥**: `d92b90556c59b868ec6cec19804b4a37`
- **有效期**: 5分钟

### 加密过程
```javascript
import { sm4 } from 'miniprogram-sm-crypto'

const _dataKey = 'd92b90556c59b868ec6cec19804b4a37'
const data = '************|*************'
const code = sm4.encrypt(data, _dataKey)
```

## 使用方式

### 1. 自动调用
系统在首页（BigScreenView.vue）加载时会自动调用授权接口：

```javascript
// 在 onMounted 中自动执行
onMounted(() => {
  // ... 其他初始化代码
  
  // 调用授权接口
  callAuthorizeApi()
})
```

### 2. 手动调用
如果需要手动调用，可以使用以下方法：

```javascript
import { generateAuthCode } from '@/utils/crypto'
import { authorizeApi } from '@/api/auth'

// 生成授权code
const areaCode = '************' // 固定的社区行政编号
const code = generateAuthCode(areaCode)

// 调用授权接口
const result = await authorizeApi({
  cityCode: '************',
  code: code
})
```

## 相关文件

- `src/utils/crypto.ts` - SM4加密工具函数
- `src/api/auth.ts` - 授权API接口定义
- `src/views/BigScreenView.vue` - 首页，自动调用授权
- `src/types/miniprogram-sm-crypto.d.ts` - SM4库的TypeScript类型定义

## 完整URL示例

```
https://qlwarehouse.muulin.cn:40300/kangbank/open/authorize?cityCode=************&code={加密后的code}
```

## 注意事项

1. **时效性**: code有5分钟的有效期，超时需要重新生成
2. **错误处理**: 授权失败不会影响系统正常使用，只会在控制台记录错误
3. **开发调试**: 开发环境下会在控制台输出详细的加密和调用信息
4. **安全性**: 加密密钥已硬编码在代码中，生产环境建议从环境变量获取
5. **编码统一**: 社区行政编号和城市编码都使用 `************`

## 响应数据解密

当授权接口返回成功时，如果返回的`data`字段包含加密数据，系统会自动进行SM4解密：

```javascript
import { decryptResponseData } from '@/utils/crypto'

// 解密接口返回的数据
const decryptedData = decryptResponseData(response.data)
```

### 解密流程

1. **接口调用成功**：授权接口返回 `{code: 1, msg: "成功", data: "加密数据"}`
2. **自动解密**：使用相同的SM4密钥对`data`字段进行解密
3. **数据处理**：
   - 尝试解析为JSON格式
   - 如果不是JSON，则作为字符串处理
   - 存储到localStorage（可选）
4. **业务处理**：可以根据解密后的数据进行后续业务逻辑

### 数据映射关系

精治数仓接口返回的数据会被映射为以下字段：

| 接口返回字段 | 对应含义 | 目标变量 |
|-------------|----------|----------|
| courtyardTotal | 小区院落数量 | communityOverviewData.courtyardTotal |
| populationTotal | 人口数量 | communityOverviewData.populationTotal |
| labelsTotal | 居民标签数 | communityOverviewData.labelsTotal |
| enterpriseTotal | 企业数 | communityOverviewData.enterpriseTotal |
| residentsHouseTotal | 房人关系数 | communityOverviewData.residentsHouseTotal |
| housesTotal | 房屋数量 | communityOverviewData.housesTotal |

### 相关函数

- `decryptResponseData(encryptedData: string): string` - 解密响应数据
- `handleDecryptedData(decryptedData: string): void` - 处理解密后的数据
- `handleCommunityStatisticsData(data: any): void` - 处理精治数仓社区统计数据

## 后续接口调用

获取到token后，系统会自动调用精治数仓社区概览接口：

### 精治数仓社区概览接口

- **接口地址**: `https://qlwarehouse.muulin.cn:40300/proxy/statistics/open/findCommunityStatistics?cityCode=************&token={token}`
- **请求方法**: GET
- **URL参数**:
  - `cityCode`: 城市编码 (************)
  - `token`: 从授权接口解密得到的token
- **请求头**: 
  - `Content-Type`: `application/json`

### 调用流程

1. **授权成功** → 解密获得token
2. **自动调用** → 精治数仓社区概览接口
3. **数据解密** → 对返回的data进行SM4解密
4. **数据存储** → 解密后的社区统计数据存储到localStorage

```javascript
// 自动调用流程
const token = authorizeData.token
const result = await getCommunityStatisticsApi(token)

// 解密返回的数据
const decryptedData = decryptResponseData(result.data)
const parsedData = JSON.parse(decryptedData)
localStorage.setItem('communityStatistics', JSON.stringify(parsedData))

// 实际请求URL示例
// https://qlwarehouse.muulin.cn:40300/proxy/statistics/open/findCommunityStatistics?cityCode=************&token=101_065664c00aa748768e1948e20427b560
```

## 测试

系统会在控制台输出详细的调用过程：

### 授权流程
- 🔓 开始解密返回数据
- ✅ 解密成功
- 📋 处理解密后的数据
- 💾 已存储到localStorage
- 🔑 获取到token

### 精治数仓流程
- 📊 开始调用精治数仓社区概览接口
- ✅ 精治数仓接口调用成功
- 🎉 获取社区统计数据成功
- 🔓 开始解密精治数仓返回数据
- ✅ 精治数仓数据解密成功
- 📊 精治数仓数据解析为JSON成功
- 💾 解密后的社区统计数据已存储到localStorage
- 📋 处理精治数仓社区统计数据
- 🏘️ 小区院落数量: 129
- 👥 人口数量: 458900
- 🏷️ 居民标签数: 1963
- 🏢 企业数: 1856
- 🏠 房人关系数: 8188
- 🏡 房屋数量: 2866
- ✅ 社区概览数据处理完成 