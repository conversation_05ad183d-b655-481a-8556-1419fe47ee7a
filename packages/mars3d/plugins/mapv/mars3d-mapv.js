/**
 * Mars3D平台插件,结合mapv可视化功能插件  mars3d-mapv
 *
 * 版本信息：v3.9.12
 * 编译日期：2025-06-24 17:13
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：成都潘朵拉科技有限公司 ，2025-06-24
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d')), (window.mapv || require('mapv'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mars3d', 'mapv'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-mapv"] = {}, global.mars3d, global.mapv));
})(this, (function (exports, mars3d, mapv) { 
'use strict';const _0x48b316=_0x45ce;(function(_0x371b51,_0x183a02){const _0x22f136={_0x18699f:0xc0,_0x5878f7:0xf7,_0x53a977:0xb6,_0x128725:0x11c},_0x1ba2bd=_0x45ce,_0x4c1329=_0x371b51();while(!![]){try{const _0x575689=parseInt(_0x1ba2bd(0xed))/0x1*(-parseInt(_0x1ba2bd(0xcb))/0x2)+parseInt(_0x1ba2bd(0x107))/0x3+parseInt(_0x1ba2bd(0xbb))/0x4*(-parseInt(_0x1ba2bd(_0x22f136._0x18699f))/0x5)+parseInt(_0x1ba2bd(0x105))/0x6+parseInt(_0x1ba2bd(0xf2))/0x7+parseInt(_0x1ba2bd(_0x22f136._0x5878f7))/0x8*(-parseInt(_0x1ba2bd(_0x22f136._0x53a977))/0x9)+-parseInt(_0x1ba2bd(0xde))/0xa*(-parseInt(_0x1ba2bd(_0x22f136._0x128725))/0xb);if(_0x575689===_0x183a02)break;else _0x4c1329['push'](_0x4c1329['shift']());}catch(_0x3c815a){_0x4c1329['push'](_0x4c1329['shift']());}}}(_0x1e46,0x2a9c8));function _interopNamespace(_0x3dbd80){if(_0x3dbd80&&_0x3dbd80['__esModule'])return _0x3dbd80;var _0x26a5e9=Object['create'](null);return _0x3dbd80&&Object['keys'](_0x3dbd80)['forEach'](function(_0x33ce71){if(_0x33ce71!=='default'){var _0x56b5c9=Object['getOwnPropertyDescriptor'](_0x3dbd80,_0x33ce71);Object['defineProperty'](_0x26a5e9,_0x33ce71,_0x56b5c9['get']?_0x56b5c9:{'enumerable':!![],'get':function(){return _0x3dbd80[_0x33ce71];}});}}),_0x26a5e9['default']=_0x3dbd80,_0x26a5e9;}var mars3d__namespace=_interopNamespace(mars3d),mapv__namespace=_interopNamespace(mapv);const Cesium$1=mars3d__namespace[_0x48b316(0xc4)],baiduMapLayer=mapv__namespace?mapv__namespace['baiduMapLayer']:null,BaseLayer$1=baiduMapLayer?baiduMapLayer[_0x48b316(0xca)]:Function;class MapVRenderer extends BaseLayer$1{constructor(_0x31f7e5,_0x571562,_0x3587f6,_0x88fd27){const _0x37b3f3={_0x1a4126:0xce,_0x3ded36:0xb7},_0x45647e=_0x48b316;super(_0x31f7e5,_0x571562,_0x3587f6);if(!BaseLayer$1)return;this['map']=_0x31f7e5,this[_0x45647e(_0x37b3f3._0x1a4126)]=_0x31f7e5[_0x45647e(0xce)],this['dataSet']=_0x571562,_0x3587f6=_0x3587f6||{},this['init'](_0x3587f6),this[_0x45647e(_0x37b3f3._0x3ded36)](_0x3587f6),this[_0x45647e(0xaf)](),this['canvasLayer']=_0x88fd27,this['stopAniamation']=!0x1,this['animation']=_0x3587f6['animation'];}[_0x48b316(0xaf)](){const _0x4c34c7={_0x1447e7:0xd2},_0x146eda=_0x48b316;this['devicePixelRatio']=window[_0x146eda(_0x4c34c7._0x1447e7)]||0x1;}['addAnimatorEvent'](){}[_0x48b316(0xfa)](){const _0x186961=_0x48b316,_0x50c48c=this['options'][_0x186961(0xd0)];this['isEnabledTime']()&&this[_0x186961(0x109)]&&(this['steps'][_0x186961(0xf0)]=_0x50c48c['stepsRange']['start']);}[_0x48b316(0x115)](){const _0x393eb9=_0x48b316;this['isEnabledTime']()&&this[_0x393eb9(0x109)];}['getContext'](){const _0x6992a8={_0x1be084:0xe0,_0x5df958:0xf4},_0x4c2c10=_0x48b316;return this[_0x4c2c10(_0x6992a8._0x1be084)][_0x4c2c10(_0x6992a8._0x5df958)]['getContext'](this['context']);}['init'](_0x4dce27){const _0x55a65a={_0x4aa157:0xbe,_0xbcd6c6:0xdd},_0x4d13ec=_0x48b316;this['options']=_0x4dce27,this[_0x4d13ec(_0x55a65a._0x4aa157)](_0x4dce27),this['context']=this['options']['context']||'2d',Cesium$1[_0x4d13ec(_0x55a65a._0xbcd6c6)](this['options']['zIndex'])&&this['canvasLayer']&&this['canvasLayer'][_0x4d13ec(0x10a)]&&this['canvasLayer']['setZIndex'](this['options']['zIndex']),this['initAnimator']();}[_0x48b316(0x10e)](_0x3b6628){const _0x560a89={_0x261a4c:0xce,_0x34aa9a:0xbc,_0x2c3818:0xc3,_0x17bda8:0x112,_0x3773ef:0x118,_0x20e0cb:0x122,_0x5b71f6:0xf3,_0x199896:0x123,_0x5791fd:0xc3,_0xdefcb9:0xfe,_0x46a043:0x117,_0x328ab3:0xc3,_0x4ae21b:0xd3,_0x2b9d3b:0xff,_0x2cd094:0xe1},_0x52b381={_0x3215ec:0xd3,_0x6fea52:0x106},_0x4c21ae=_0x48b316;if(!this['canvasLayer']||this[_0x4c21ae(0xc6)])return;const _0x4e41c9=this[_0x4c21ae(_0x560a89._0x261a4c)],_0x6839bd=this['options']['animation'],_0x716115=this['getContext']();if(this['isEnabledTime']()){if(void 0x0===_0x3b6628)return void this['clear'](_0x716115);this['context']==='2d'&&(_0x716115['save'](),_0x716115['globalCompositeOperation']='destination-out',_0x716115['fillStyle']='rgba(0,\x200,\x200,\x20.1)',_0x716115['fillRect'](0x0,0x0,_0x716115['canvas']['width'],_0x716115[_0x4c21ae(0xf4)]['height']),_0x716115[_0x4c21ae(0xdf)]());}else this['clear'](_0x716115);if(this[_0x4c21ae(0xe4)]==='2d')for(const _0x11c301 in this['options']){_0x716115[_0x11c301]=this['options'][_0x11c301];}else _0x716115['clear'](_0x716115['COLOR_BUFFER_BIT']);const _0x329f05={'transferCoordinate':function(_0x37d782){const _0x53dfd1=_0x4c21ae,_0x7e41e=null;let _0x18202d=_0x4e41c9['mapvFixedHeight'];_0x4e41c9['mapvAutoHeight']&&(_0x18202d=_0x4e41c9['getHeight'](Cesium$1[_0x53dfd1(0xc1)][_0x53dfd1(_0x52b381._0x3215ec)](_0x37d782[0x0],_0x37d782[0x1])));const _0x3fb91=Cesium$1['Cartesian3']['fromDegrees'](_0x37d782[0x0],_0x37d782[0x1],_0x18202d);if(!_0x3fb91)return _0x7e41e;const _0x3eecff=mars3d__namespace['PointTrans']['toWindowCoordinates'](_0x4e41c9,_0x3fb91);if(!_0x3eecff)return _0x7e41e;if(_0x4e41c9['mapvDepthTest']&&_0x4e41c9[_0x53dfd1(0x11f)]===Cesium$1['SceneMode']['SCENE3D']){const _0x4c5707=new Cesium$1['EllipsoidalOccluder'](_0x4e41c9[_0x53dfd1(_0x52b381._0x6fea52)]['ellipsoid'],_0x4e41c9['camera']['positionWC']),_0x236569=_0x4c5707['isPointVisible'](_0x3fb91);if(!_0x236569)return _0x7e41e;}return[_0x3eecff['x'],_0x3eecff['y']];}};void 0x0!==_0x3b6628&&(_0x329f05[_0x4c21ae(_0x560a89._0x34aa9a)]=function(_0x365af6){const _0x43e21e=_0x4c21ae,_0x103d31=_0x6839bd['trails']||0xa;return!!(_0x3b6628&&_0x365af6[_0x43e21e(0xd7)]>_0x3b6628-_0x103d31&&_0x365af6[_0x43e21e(0xd7)]<_0x3b6628);});let _0x3fab04;if(this['options']['draw']==='cluster'&&(!this[_0x4c21ae(_0x560a89._0x2c3818)]['maxClusterZoom']||this['options'][_0x4c21ae(0xee)]>=this[_0x4c21ae(0xc7)]())){this['map'][_0x4c21ae(0xbd)]();const _0x27d257=this['getZoom'](),_0x333945=this[_0x4c21ae(0xd6)]['getClusters']([-0xb4,-0x5a,0xb4,0x5a],_0x27d257);this['pointCountMax']=this['supercluster']['trees'][_0x27d257][_0x4c21ae(_0x560a89._0x17bda8)],this['pointCountMin']=this['supercluster']['trees'][_0x27d257]['min'];let _0x4bf56e={},_0x547953=null,_0x56aa6f=null;this[_0x4c21ae(0xe3)]===this[_0x4c21ae(_0x560a89._0x3773ef)]?(_0x547953=this['options']['fillStyle'],_0x56aa6f=this[_0x4c21ae(0xc3)]['minSize']||0x8):_0x4bf56e=new mapv__namespace['utilDataRangeIntensity']({'min':this['pointCountMin'],'max':this['pointCountMax'],'minSize':this[_0x4c21ae(0xc3)]['minSize']||0x8,'maxSize':this['options'][_0x4c21ae(0xf8)]||0x1e,'gradient':this['options']['gradient']});for(let _0x27497b=0x0;_0x27497b<_0x333945['length'];_0x27497b++){const _0x17c834=_0x333945[_0x27497b];_0x17c834['properties']&&_0x17c834['properties'][_0x4c21ae(_0x560a89._0x20e0cb)]?(_0x333945[_0x27497b][_0x4c21ae(_0x560a89._0x5b71f6)]=_0x56aa6f||_0x4bf56e['getSize'](_0x17c834['properties']['point_count']),_0x333945[_0x27497b]['fillStyle']=_0x547953||_0x4bf56e['getColor'](_0x17c834[_0x4c21ae(_0x560a89._0x199896)]['point_count'])):_0x333945[_0x27497b]['size']=this[_0x4c21ae(_0x560a89._0x5791fd)]['size'];}this['clusterDataSet']['set'](_0x333945),_0x3fab04=this[_0x4c21ae(0xda)][_0x4c21ae(_0x560a89._0xdefcb9)](_0x329f05);}else _0x3fab04=this[_0x4c21ae(0xb0)]['get'](_0x329f05);this[_0x4c21ae(_0x560a89._0x46a043)](_0x3fab04);this['options']['unit']==='m'&&this[_0x4c21ae(0xc3)]['size']&&(this[_0x4c21ae(_0x560a89._0x328ab3)]['_size']=this[_0x4c21ae(0xc3)]['size']);const _0x8a3e50=mars3d__namespace['PointTrans']['toWindowCoordinates'](_0x4e41c9,Cesium$1['Cartesian3'][_0x4c21ae(_0x560a89._0x4ae21b)](0x0,0x0));if(!_0x8a3e50)return;this[_0x4c21ae(0xb8)](_0x716115,new mapv__namespace[(_0x4c21ae(_0x560a89._0x2b9d3b))](_0x3fab04),this['options'],_0x8a3e50),this['options']['updateCallback']&&this['options'][_0x4c21ae(_0x560a89._0x2cd094)](_0x3b6628);}['updateData'](_0xde2b9a,_0x137581){const _0x315c68=_0x48b316;let _0x52d60e=_0xde2b9a;_0x52d60e&&_0x52d60e['get']&&(_0x52d60e=_0x52d60e['get']()),void 0x0!==_0x52d60e&&this[_0x315c68(0xb0)]['set'](_0x52d60e),super[_0x315c68(0xe7)]({'options':_0x137581});}[_0x48b316(0xc5)](_0x295830,_0x5092ab){const _0x517f4c=_0x48b316;let _0xf6384a=_0x295830;_0x295830&&_0x295830[_0x517f4c(0xfe)]&&(_0xf6384a=_0x295830['get']()),this[_0x517f4c(0xb0)][_0x517f4c(0x11b)](_0xf6384a),this['update']({'options':_0x5092ab});}[_0x48b316(0xd8)](){return this['dataSet'];}['removeData'](_0x41e880){const _0x686341={_0x29b502:0xb0},_0x47abdc=_0x48b316;if(this['dataSet']){const _0x25e49f=this[_0x47abdc(_0x686341._0x29b502)]['get']({'filter':function(_0x5205fa){return _0x41e880==null||typeof _0x41e880!=='function'||!_0x41e880(_0x5205fa);}});this['dataSet']['set'](_0x25e49f),this[_0x47abdc(0xe7)]({'options':null});}}['clearData'](){this['dataSet']&&this['dataSet']['clear'](),this['update']({'options':null});}['draw'](){this['canvasLayer']['draw']();}[_0x48b316(0xbf)](_0x383539){const _0x55b804={_0x4dad22:0x104},_0xbf8e1c=_0x48b316;_0x383539&&_0x383539[_0xbf8e1c(0xfc)]&&_0x383539['clearRect'](0x0,0x0,_0x383539[_0xbf8e1c(0xf4)][_0xbf8e1c(_0x55b804._0x4dad22)],_0x383539['canvas']['height']);}['getZoom'](){return this['map']['level'];}['destroy'](){const _0x5a2d35={_0x5d11ed:0xeb},_0x53fbd2=_0x48b316;this['clear'](this['getContext']()),this[_0x53fbd2(_0x5a2d35._0x5d11ed)](),this['animator']&&this[_0x53fbd2(0x109)]['stop'](),this['animator']=null,this['canvasLayer']=null;}}if(mapv__namespace!==null&&mapv__namespace!==void 0x0&&mapv__namespace[_0x48b316(0xff)])mapv__namespace['DataSet']['prototype']['transferCoordinate']=function(_0x1acef1,_0x3a4271,_0x21171e,_0xebf7de){const _0x2b8099={_0x10d829:0xef,_0x4835d0:0x114},_0x203872={_0x44cc88:0xdc},_0x1e8362=_0x48b316;_0xebf7de=_0xebf7de||'_coordinates',_0x21171e=_0x21171e||'coordinates';for(let _0x5711e7=0x0;_0x5711e7<_0x1acef1['length'];_0x5711e7++){const _0x5e16d0=_0x1acef1[_0x5711e7]['geometry'],_0x59f215=_0x5e16d0[_0x21171e];switch(_0x5e16d0['type']){case'Point':{const _0x237656=_0x3a4271(_0x59f215);_0x237656?_0x5e16d0[_0xebf7de]=_0x237656:_0x5e16d0[_0xebf7de]=[-0x3e7,-0x3e7];}break;case'LineString':{const _0x518e50=[];for(let _0x5391e1=0x0;_0x5391e1<_0x59f215['length'];_0x5391e1++){const _0x7a43a=_0x3a4271(_0x59f215[_0x5391e1]);_0x7a43a&&_0x518e50[_0x1e8362(0xdc)](_0x7a43a);}_0x5e16d0[_0xebf7de]=_0x518e50;}break;case _0x1e8362(_0x2b8099._0x10d829):case'Polygon':{const _0x250fe9=_0x4e8876(_0x59f215);_0x5e16d0[_0xebf7de]=_0x250fe9;}break;case'MultiPolygon':{const _0x227a6e=[];for(let _0x1f7a34=0x0;_0x1f7a34<_0x59f215['length'];_0x1f7a34++){const _0x5c2a0f=_0x4e8876(_0x59f215[_0x1f7a34]);_0x5c2a0f[_0x1e8362(_0x2b8099._0x4835d0)]>0x0&&_0x227a6e['push'](_0x5c2a0f);}_0x5e16d0[_0xebf7de]=_0x227a6e;}break;}}function _0x4e8876(_0x36c7b4){const _0x20276d=_0x1e8362,_0x2440ea=[];for(let _0x549dc5=0x0;_0x549dc5<_0x36c7b4['length'];_0x549dc5++){const _0x753081=_0x36c7b4[_0x549dc5],_0x1b2e04=[];for(let _0x2f53ee=0x0;_0x2f53ee<_0x753081['length'];_0x2f53ee++){const _0x34162c=_0x3a4271(_0x753081[_0x2f53ee]);_0x34162c&&_0x1b2e04[_0x20276d(_0x203872._0x44cc88)](_0x34162c);}_0x1b2e04[_0x20276d(0x114)]>0x0&&_0x2440ea['push'](_0x1b2e04);}return _0x2440ea;}return _0x1acef1;};else throw new Error('请引入\x20mapv\x20库\x20');function _0x45ce(_0xbed9d4,_0x371d25){const _0x1e46f4=_0x1e46();return _0x45ce=function(_0x45cea8,_0x1691f7){_0x45cea8=_0x45cea8-0xac;let _0x5719b0=_0x1e46f4[_0x45cea8];return _0x5719b0;},_0x45ce(_0xbed9d4,_0x371d25);}const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace['layer'][_0x48b316(0x110)];class MapVLayer extends BaseLayer{constructor(_0xf39345,_0x3786fc){const _0x33245b={_0x370c43:0xc3,_0x33c5c9:0xb0},_0x488ec5=_0x48b316;super(_0xf39345),this['_pointerEvents']=this[_0x488ec5(_0x33245b._0x370c43)]['pointerEvents'],this[_0x488ec5(_0x33245b._0x33c5c9)]=_0x3786fc||new mapv__namespace['DataSet'](_0xf39345['data']),this['canvas']=null;}get['pointerEvents'](){const _0x143c86=_0x48b316;return this[_0x143c86(0x100)];}set['pointerEvents'](_0x1f5818){const _0x1a7fae={_0x32e55b:0x100,_0x4b11a7:0xb5},_0x49bd09=_0x48b316;this[_0x49bd09(_0x1a7fae._0x32e55b)]=_0x1f5818,this['canvas']&&(_0x1f5818?this['canvas']['style']['pointerEvents']=_0x49bd09(0x116):this['canvas']['style'][_0x49bd09(_0x1a7fae._0x4b11a7)]='none');}['_showHook'](_0x2723e2){const _0x35e23e={_0x6b2494:0xf5,_0x239f55:0x11a},_0x2b88f3=_0x48b316;_0x2723e2?this['canvas'][_0x2b88f3(_0x35e23e._0x6b2494)]['display']=_0x2b88f3(_0x35e23e._0x239f55):this['canvas']['style']['display']='none';}['_mountedHook'](){const _0x2e2e1b=_0x48b316;this[_0x2e2e1b(0xe5)][_0x2e2e1b(0xce)]['mapvDepthTest']=this['options']['depthTest']??!![],this['_map']['scene']['mapvAutoHeight']=this['options']['clampToGround']??![],this['_map']['scene']['mapvFixedHeight']=this['options'][_0x2e2e1b(0x108)]??0x0;}['_addedHook'](){const _0x1ca5fa={_0x496c06:0xb0},_0x521d84=_0x48b316;this[_0x521d84(_0x1ca5fa._0x496c06)]&&(!this['dataSet'][_0x521d84(0xae)]||this['dataSet']['_data'][_0x521d84(0x114)]===0x0)&&(this[_0x521d84(_0x1ca5fa._0x496c06)]['_data']=[]['concat'](this[_0x521d84(0xb0)]['_dataCache'])),this['_mapVRenderer']=new MapVRenderer(this['_map'],this['dataSet'],this['options'],this),this[_0x521d84(0xaf)](),this['canvas']=this['_createCanvas'](),this[_0x521d84(0xd9)]=this['render']['bind'](this),this['bindEvent'](),this['_reset']();}[_0x48b316(0xfd)](){const _0x3334c8={_0x2be437:0xcf,_0x155fd5:0x11e},_0x1e49fa=_0x48b316;this['unbindEvent'](),this['_mapVRenderer']&&(this[_0x1e49fa(0x11e)][_0x1e49fa(_0x3334c8._0x2be437)](),this[_0x1e49fa(_0x3334c8._0x155fd5)]=null),this[_0x1e49fa(0xf4)]['parentElement']['removeChild'](this['canvas']);}['initDevicePixelRatio'](){this['devicePixelRatio']=window['devicePixelRatio']||0x1;}[_0x48b316(0xec)](){const _0x20600f={_0xa81b51:0xc3,_0x5b290e:0x10c},_0x1eb5ca=_0x48b316;var _0x38a015,_0x4f4d42;this['_map']['on'](mars3d__namespace['EventType']['mouseDown'],this['_onMoveStartEvent'],this),this[_0x1eb5ca(0xe5)]['on'](mars3d__namespace['EventType']['cameraMoveStart'],this['_onMoveStartEvent'],this),this['_map']['on'](mars3d__namespace['EventType'][_0x1eb5ca(0xd1)],this[_0x1eb5ca(0x102)],this),(_0x38a015=this['options'])!==null&&_0x38a015!==void 0x0&&(_0x38a015=_0x38a015[_0x1eb5ca(0xac)])!==null&&_0x38a015!==void 0x0&&_0x38a015['click']&&this['_map']['on'](mars3d__namespace['EventType']['click'],this['_onMapClick'],this),(_0x4f4d42=this[_0x1eb5ca(_0x20600f._0xa81b51)])!==null&&_0x4f4d42!==void 0x0&&(_0x4f4d42=_0x4f4d42[_0x1eb5ca(0xac)])!==null&&_0x4f4d42!==void 0x0&&_0x4f4d42[_0x1eb5ca(_0x20600f._0x5b290e)]&&this['_map']['on'](mars3d__namespace['EventType']['mouseMove'],this['_onMapMouseMove'],this);}['unbindEvent'](){const _0x2b15d8={_0x8339f8:0xc2,_0x42a8c1:0xb2},_0x403a7a=_0x48b316;var _0x2c11b9,_0x43b7e8;this['_map']['off'](mars3d__namespace['EventType']['mouseDown'],this['_onMoveStartEvent'],this),this['_map'][_0x403a7a(0xf1)](mars3d__namespace['EventType']['cameraMoveStart'],this['_onMoveStartEvent'],this),this['_map'][_0x403a7a(0xf1)](mars3d__namespace['EventType'][_0x403a7a(0xd1)],this['_onMoveEndEvent'],this),this['_map']['off'](mars3d__namespace['EventType']['postRender'],this[_0x403a7a(0xf6)],this),(_0x2c11b9=this[_0x403a7a(0xc3)])!==null&&_0x2c11b9!==void 0x0&&(_0x2c11b9=_0x2c11b9[_0x403a7a(0xac)])!==null&&_0x2c11b9!==void 0x0&&_0x2c11b9[_0x403a7a(_0x2b15d8._0x8339f8)]&&this['_map']['off'](mars3d__namespace['EventType']['click'],this[_0x403a7a(_0x2b15d8._0x42a8c1)],this),(_0x43b7e8=this['options'])!==null&&_0x43b7e8!==void 0x0&&(_0x43b7e8=_0x43b7e8['methods'])!==null&&_0x43b7e8!==void 0x0&&_0x43b7e8['mousemove']&&this['_map']['off'](mars3d__namespace['EventType']['mouseMove'],this['_onMapMouseMove'],this);}['_onMoveStartEvent'](){const _0x534d8b=_0x48b316;this['_mapVRenderer']&&(this['_mapVRenderer']['animatorMovestartEvent'](),this['_map']['off'](mars3d__namespace[_0x534d8b(0x103)][_0x534d8b(0x10f)],this['_reset'],this),this['_map']['on'](mars3d__namespace['EventType']['postRender'],this['_reset'],this));}['_onMoveEndEvent'](){const _0x310d95={_0x3ec972:0xf6},_0x5a608b=_0x48b316;this['_mapVRenderer']&&(this['_map'][_0x5a608b(0xf1)](mars3d__namespace['EventType']['postRender'],this[_0x5a608b(_0x310d95._0x3ec972)],this),this['_mapVRenderer']['animatorMoveendEvent'](),this['_reset']());}['_setOptionsHook'](_0x1b2bc3,_0x48fa3e){this['_removedHook'](),this['_addedHook']();}[_0x48b316(0xc5)](_0x57b218){const _0x15939d={_0x5db86e:0xc3},_0x43fe60=_0x48b316;this['_mapVRenderer']&&this['_mapVRenderer']['addData'](_0x57b218,this[_0x43fe60(_0x15939d._0x5db86e)]);}['updateData'](_0x13ad06){this['_mapVRenderer']&&this['_mapVRenderer']['updateData'](_0x13ad06,this['options']);}['getData'](){const _0x1fc502=_0x48b316;return this['_mapVRenderer']&&(this['dataSet']=this['_mapVRenderer']['getData']()),this[_0x1fc502(0xb0)];}['removeData'](_0x68597a){const _0x1a983c={_0x396cde:0x11e,_0x1cb4d6:0xea},_0x439b48=_0x48b316;this[_0x439b48(_0x1a983c._0x396cde)]&&this['_mapVRenderer'][_0x439b48(_0x1a983c._0x1cb4d6)](_0x68597a);}[_0x48b316(0xb9)](){const _0x19d773={_0xadb76a:0x11e},_0x137597=_0x48b316;this[_0x137597(_0x19d773._0xadb76a)]&&this[_0x137597(_0x19d773._0xadb76a)][_0x137597(0xeb)]();}[_0x48b316(0x113)](){const _0x5287b1={_0x31c012:0xd4,_0x42e5e9:0xad,_0x305a38:0x104,_0x2c1dd4:0xf4,_0xcab8af:0xf5,_0x4bb44f:0x10d,_0x1981aa:0xf4,_0x26e8eb:0xb1,_0x36c237:0xdb,_0x4c0489:0x101},_0x26487b=_0x48b316,_0x280c3b=mars3d__namespace['DomUtil']['create']('canvas','mars3d-mapv',this['_map'][_0x26487b(_0x5287b1._0x31c012)]);_0x280c3b['id']=this['id'],_0x280c3b['style']['position']=_0x26487b(_0x5287b1._0x42e5e9),_0x280c3b['style']['top']='0px',_0x280c3b['style']['left']='0px',_0x280c3b[_0x26487b(_0x5287b1._0x305a38)]=parseInt(this['_map'][_0x26487b(_0x5287b1._0x2c1dd4)]['width']),_0x280c3b['height']=parseInt(this['_map']['canvas']['height']),_0x280c3b['style']['width']=this['_map']['canvas'][_0x26487b(_0x5287b1._0xcab8af)][_0x26487b(_0x5287b1._0x305a38)],_0x280c3b[_0x26487b(0xf5)][_0x26487b(_0x5287b1._0x4bb44f)]=this[_0x26487b(0xe5)][_0x26487b(_0x5287b1._0x1981aa)]['style']['height'],_0x280c3b['style'][_0x26487b(0xb5)]=this['_pointerEvents']?_0x26487b(_0x5287b1._0x26e8eb):'none',_0x280c3b['style'][_0x26487b(_0x5287b1._0x36c237)]=this['options'][_0x26487b(0xdb)]??0x9;if(this['options'][_0x26487b(0xe4)]==='2d'){const _0x151ef6=this[_0x26487b(0xd2)];_0x280c3b['getContext'](this['options']['context'])[_0x26487b(_0x5287b1._0x4c0489)](_0x151ef6,_0x151ef6);}return _0x280c3b;}['_reset'](){this['resize'](),this['render']();}['draw'](){const _0x1b1ee0=_0x48b316;this[_0x1b1ee0(0xf6)]();}[_0x48b316(0xe9)](){const _0x3a55f5={_0x592586:0xf4},_0x5205ca=_0x48b316;this[_0x5205ca(0x11e)]&&(this['_mapVRenderer'][_0x5205ca(0xcf)](),this['_mapVRenderer']=null),this[_0x5205ca(_0x3a55f5._0x592586)][_0x5205ca(0xe6)]['removeChild'](this['canvas']);}['render'](){this['_mapVRenderer']['_canvasUpdate']();}['resize'](){const _0x4bf458={_0x2d5154:0xcd,_0x564a86:0xad,_0x1f8b94:0x10d,_0x475031:0xf4,_0x421476:0xf5},_0x21f322=_0x48b316;if(this['canvas']){const _0x93bc2f=this[_0x21f322(0xf4)];_0x93bc2f['style'][_0x21f322(_0x4bf458._0x2d5154)]=_0x21f322(_0x4bf458._0x564a86),_0x93bc2f['style'][_0x21f322(0x11d)]='0px',_0x93bc2f['style']['left']='0px',_0x93bc2f['width']=parseInt(this['_map']['canvas']['width']),_0x93bc2f[_0x21f322(_0x4bf458._0x1f8b94)]=parseInt(this['_map'][_0x21f322(_0x4bf458._0x475031)][_0x21f322(0x10d)]),_0x93bc2f['style']['width']=this['_map']['canvas']['style']['width'],_0x93bc2f[_0x21f322(0xf5)]['height']=this['_map']['canvas'][_0x21f322(_0x4bf458._0x421476)]['height'];}}[_0x48b316(0xba)](_0xe178e8){const _0x2fa416={_0x48e82a:0x111,_0xb0f558:0xcc},_0x4627be=_0x48b316;if(!this['dataSet']||!this[_0x4627be(0xb0)]['_data'])return;const _0x42c600=mars3d__namespace[_0x4627be(0xd5)]['getExtentByGeoJSON']({'type':_0x4627be(_0x2fa416._0x48e82a),'features':this['dataSet'][_0x4627be(0xae)]});if(!_0x42c600)return;return _0xe178e8!==null&&_0xe178e8!==void 0x0&&_0xe178e8[_0x4627be(0xc9)]?_0x42c600:Cesium['Rectangle']['fromDegrees'](_0x42c600[_0x4627be(_0x2fa416._0xb0f558)],_0x42c600['ymin'],_0x42c600['xmax'],_0x42c600[_0x4627be(0x119)]);}[_0x48b316(0xb2)](_0x197596){const _0x2e6119=_0x48b316;this[_0x2e6119(0x120)]=_0x197596,this[_0x2e6119(0x11e)]&&this['_mapVRenderer']['clickEvent'](_0x197596['windowPosition'],_0x197596);}['_onMapMouseMove'](_0x1f23b3){this['_cache_event']=_0x1f23b3,this['_mapVRenderer']&&this['_mapVRenderer']['mousemoveEvent'](_0x1f23b3['windowPosition'],_0x1f23b3);}['on'](_0x5ee581,_0x40bb57,_0x56c545){const _0x375956={_0x609d9c:0xc3,_0xea84ad:0xfb},_0x5d8503=_0x48b316;this['options'][_0x5d8503(0xac)]=this[_0x5d8503(_0x375956._0x609d9c)]['methods']||{};if(_0x5ee581===mars3d__namespace['EventType'][_0x5d8503(0xc2)])this['options'][_0x5d8503(0xac)]['click']=_0x557fdd=>{_0x557fdd&&_0x40bb57['bind'](_0x56c545)({...this['_cache_event'],'layer':this,'data':_0x557fdd});},this[_0x5d8503(0xe5)]['on'](mars3d__namespace['EventType']['click'],this['_onMapClick'],this);else _0x5ee581===mars3d__namespace['EventType'][_0x5d8503(0xb4)]&&(this['options']['methods'][_0x5d8503(0x10c)]=_0x147753=>{_0x147753&&_0x40bb57['bind'](_0x56c545)({...this['_cache_event'],'layer':this,'data':_0x147753});},this[_0x5d8503(0xe5)]['on'](mars3d__namespace['EventType']['mouseMove'],this[_0x5d8503(_0x375956._0xea84ad)],this));return this;}['off'](_0x4ddb09,_0x18fca5){const _0x4b9073={_0x22d778:0xac,_0x58add3:0xb4,_0x140b56:0xfb},_0x3ea583=_0x48b316;if(_0x4ddb09==='click'){var _0x9ce6d6;this[_0x3ea583(0xe5)][_0x3ea583(0xf1)](_0x4ddb09,this['_onMapClick'],this),(_0x9ce6d6=this[_0x3ea583(0xc3)]['methods'])!==null&&_0x9ce6d6!==void 0x0&&_0x9ce6d6['mousemove']&&delete this['options'][_0x3ea583(_0x4b9073._0x22d778)]['click'];}else{if(_0x4ddb09===_0x3ea583(_0x4b9073._0x58add3)){var _0x1e01e5;this['_map'][_0x3ea583(0xf1)](_0x4ddb09,this[_0x3ea583(_0x4b9073._0x140b56)],this),(_0x1e01e5=this['options']['methods'])!==null&&_0x1e01e5!==void 0x0&&_0x1e01e5['mousemove']&&delete this[_0x3ea583(0xc3)]['methods']['mousemove'];}}return this;}}function _0x1e46(){const _0x40fa87=['processData','pointCountMin','ymax','block','add','406549ktHyJP','top','_mapVRenderer','mode','_cache_event','Log','cluster_id','properties','methods','absolute','_data','initDevicePixelRatio','dataSet','auto','_onMapClick','keys','mouseMove','pointerEvents','549CeBaLN','argCheck','drawContext','removeAllData','getRectangle','2676aPbMwK','filter','getExtent','initDataRange','clear','2380jfvITD','Cartographic','click','options','Cesium','addData','stopAniamation','getZoom','MapVLayer','isFormat','__proto__','104LrWuoz','xmin','position','scene','destroy','animation','cameraMoveEnd','devicePixelRatio','fromDegrees','container','PolyUtil','supercluster','time','getData','render','clusterDataSet','zIndex','push','defined','30YUcEHm','restore','canvasLayer','updateCallback','mapv','pointCountMax','context','_map','parentElement','update','defineProperty','remove','removeData','clearData','bindEvent','5646cMjtRK','maxClusterZoom','MultiLineString','step','off','1377754khnkBi','size','canvas','style','_reset','18808bFggcL','maxSize','layer','animatorMovestartEvent','_onMapMouseMove','clearRect','_removedHook','get','DataSet','_pointerEvents','scale','_onMoveEndEvent','EventType','width','1851870iNtrdE','globe','940917oldYWS','fixedHeight','animator','setZIndex','forEach','mousemove','height','_canvasUpdate','postRender','BaseLayer','FeatureCollection','max','_createCanvas','length','animatorMoveendEvent','all'];_0x1e46=function(){return _0x40fa87;};return _0x1e46();}mars3d__namespace['LayerUtil']['register'](_0x48b316(0xe2),MapVLayer),mars3d__namespace[_0x48b316(0xf9)][_0x48b316(0xc8)]=MapVLayer,mars3d__namespace['mapv']=mapv__namespace,mars3d__namespace[_0x48b316(0x121)]['logInfo']('mars3d-mapv插件\x20注册成功'),exports['MapVLayer']=MapVLayer,Object[_0x48b316(0xb3)](mapv)[_0x48b316(0x10b)](function(_0x18e26e){const _0x39fe90=_0x48b316;if(_0x18e26e!=='default'&&!exports['hasOwnProperty'](_0x18e26e))Object[_0x39fe90(0xe8)](exports,_0x18e26e,{'enumerable':!![],'get':function(){return mapv[_0x18e26e];}});}),Object['defineProperty'](exports,'__esModule',{'value':!![]});
}));
