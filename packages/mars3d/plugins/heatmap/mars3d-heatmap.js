/**
 * Mars3D平台插件,结合heatmap可视化功能插件  mars3d-heatmap
 *
 * 版本信息：v3.9.12
 * 编译日期：2025-06-24 17:13
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：成都潘朵拉科技有限公司 ，2025-06-24
 */
(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d'))) :
	typeof define === 'function' && define.amd ? define(['exports', 'mars3d'], factory) :
	(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-heatmap"] = {}, global.mars3d));
})(this, (function (exports, mars3d) { 
'use strict';var _0xe350a3=_0x1bce;(function(_0x2f313c,_0x15d944){var _0x2b2e05={_0x54cb6e:0x140,_0x4bd9e0:0x164,_0x1f61c4:0x18e,_0x2bb940:0x116,_0x1c3d5c:0x13d,_0x3079b9:0x150},_0x2218a1=_0x1bce,_0x4efdf5=_0x2f313c();while(!![]){try{var _0x39fc9d=-parseInt(_0x2218a1(_0x2b2e05._0x54cb6e))/0x1*(parseInt(_0x2218a1(_0x2b2e05._0x4bd9e0))/0x2)+parseInt(_0x2218a1(_0x2b2e05._0x1f61c4))/0x3+parseInt(_0x2218a1(0x16c))/0x4*(parseInt(_0x2218a1(_0x2b2e05._0x2bb940))/0x5)+parseInt(_0x2218a1(0x157))/0x6+parseInt(_0x2218a1(_0x2b2e05._0x1c3d5c))/0x7*(-parseInt(_0x2218a1(_0x2b2e05._0x3079b9))/0x8)+-parseInt(_0x2218a1(0x184))/0x9+parseInt(_0x2218a1(0x121))/0xa;if(_0x39fc9d===_0x15d944)break;else _0x4efdf5['push'](_0x4efdf5['shift']());}catch(_0x515f35){_0x4efdf5['push'](_0x4efdf5['shift']());}}}(_0x34ac,0xc2946));function _interopNamespace(_0x53fd6e){var _0x35e878=_0x1bce;if(_0x53fd6e&&_0x53fd6e['__esModule'])return _0x53fd6e;var _0x282ead=Object['create'](null);return _0x53fd6e&&Object['keys'](_0x53fd6e)[_0x35e878(0x175)](function(_0x431509){if(_0x431509!=='default'){var _0x3aeb3a=Object['getOwnPropertyDescriptor'](_0x53fd6e,_0x431509);Object['defineProperty'](_0x282ead,_0x431509,_0x3aeb3a['get']?_0x3aeb3a:{'enumerable':!![],'get':function(){return _0x53fd6e[_0x431509];}});}}),_0x282ead['default']=_0x53fd6e,_0x282ead;}function _mergeNamespaces(_0x591556,_0x418aec){return _0x418aec['forEach'](function(_0x406f04){_0x406f04&&typeof _0x406f04!=='string'&&!Array['isArray'](_0x406f04)&&Object['keys'](_0x406f04)['forEach'](function(_0x456069){if(_0x456069!=='default'&&!(_0x456069 in _0x591556)){var _0x3d6e89=Object['getOwnPropertyDescriptor'](_0x406f04,_0x456069);Object['defineProperty'](_0x591556,_0x456069,_0x3d6e89['get']?_0x3d6e89:{'enumerable':!![],'get':function(){return _0x406f04[_0x456069];}});}});}),_0x591556;}var mars3d__namespace=_interopNamespace(mars3d),commonjsGlobal=typeof globalThis!=='undefined'?globalThis:typeof window!=='undefined'?window:typeof global!==_0xe350a3(0x18c)?global:typeof self!=='undefined'?self:{},heatmap$1={'exports':{}};(function(_0x246c8d){var _0x16375d={_0x42fcac:0x156},_0x26d624={_0x4d58e8:0x15d},_0x3c53c7={_0x4d947d:0x135},_0x1e727d=_0xe350a3;(function(_0x44dd35,_0x1da01a,_0x3c963b){_0x246c8d['exports']?_0x246c8d['exports']=_0x3c963b():_0x1da01a[_0x44dd35]=_0x3c963b();}(_0x1e727d(0x169),commonjsGlobal,function(){var _0x35b911={_0x57c491:0x19b},_0x53b04d={_0x3bbbd:0x165,_0x2f037c:0x165},_0x5a11c9={_0x24ee98:0x127},_0x5541b9={_0x5323ca:0x149,_0x2bde85:0x1a0,_0x5b4a3d:0x1a4,_0x16bd47:0x15d},_0x1c7dac={_0x2489f7:0x1ac,_0x5dd407:0x138,_0xefb552:0x122,_0x2d87e6:0x114,_0x164d62:0x11a,_0x24ad05:0x11d},_0x4be9f5={_0x22d414:0x166,_0x22c98c:0x122},_0x20b76e={_0x292818:0x15d},_0x28a9e1={_0x3d38ba:0x1a5,_0x5090f5:0x182,_0x56d5e8:0x114,_0x30686f:0x19d,_0x2071ca:0x142},_0x133211={_0x32001c:0x1a4,_0x44f646:0x163,_0x5f5fb6:0x141,_0xb96de2:0x185},_0x1b083d={_0x132b45:0x118},_0x182428=_0x1e727d,_0x201414={'defaultRadius':0x28,'defaultRenderer':'canvas2d','defaultGradient':{0.25:'rgb(0,0,255)',0.55:'rgb(0,255,0)',0.85:_0x182428(_0x16375d._0x42fcac),0x1:'rgb(255,0,0)'},'defaultMaxOpacity':0x1,'defaultMinOpacity':0x0,'defaultBlur':0.85,'defaultXField':'x','defaultYField':'y','defaultValueField':_0x182428(0x13a),'plugins':{}},_0x243d34=function _0x24c541(){var _0x1b4d91={_0x378f37:0x137},_0x529d99={_0x5f0f97:0x179,_0x55fc73:0x189,_0x5a16db:0x126,_0x4c5497:0x137,_0x2ca6f3:0x132},_0x3c6044=_0x182428,_0x559a6e=function _0x2f85c(_0x2d9f77){var _0x5e4769=_0x1bce;this['_coordinator']={},this['_data']=[],this['_radi']=[],this['_min']=0xa,this['_max']=0x1,this['_xField']=_0x2d9f77['xField']||_0x2d9f77['defaultXField'],this['_yField']=_0x2d9f77['yField']||_0x2d9f77['defaultYField'],this['_valueField']=_0x2d9f77['valueField']||_0x2d9f77['defaultValueField'],_0x2d9f77['radius']&&(this['_cfgRadius']=_0x2d9f77[_0x5e4769(0x18b)]);},_0x568701=_0x201414[_0x3c6044(_0x3c53c7._0x4d947d)];return _0x559a6e['prototype']={'_organiseData':function(_0x1c00de,_0x5d6d75){var _0x22476c=_0x3c6044,_0x4b3cc4=_0x1c00de[this[_0x22476c(_0x529d99._0x5f0f97)]],_0x183876=_0x1c00de[this['_yField']],_0x477c36=this[_0x22476c(_0x529d99._0x55fc73)],_0x56b08d=this['_data'],_0x25aca7=this[_0x22476c(0x137)],_0x17a93b=this[_0x22476c(0x1ad)],_0x499026=_0x1c00de[this[_0x22476c(_0x529d99._0x5a16db)]]||0x1,_0x5a350b=_0x1c00de['radius']||this['_cfgRadius']||_0x568701;!_0x56b08d[_0x4b3cc4]&&(_0x56b08d[_0x4b3cc4]=[],_0x477c36[_0x4b3cc4]=[]);!_0x56b08d[_0x4b3cc4][_0x183876]?(_0x56b08d[_0x4b3cc4][_0x183876]=_0x499026,_0x477c36[_0x4b3cc4][_0x183876]=_0x5a350b):_0x56b08d[_0x4b3cc4][_0x183876]+=_0x499026;var _0x535fa4=_0x56b08d[_0x4b3cc4][_0x183876];if(_0x535fa4>_0x25aca7)return!_0x5d6d75?this[_0x22476c(_0x529d99._0x4c5497)]=_0x535fa4:this['setDataMax'](_0x535fa4),![];else return _0x535fa4<_0x17a93b?(!_0x5d6d75?this['_min']=_0x535fa4:this[_0x22476c(_0x529d99._0x2ca6f3)](_0x535fa4),![]):{'x':_0x4b3cc4,'y':_0x183876,'value':_0x499026,'radius':_0x5a350b,'min':_0x17a93b,'max':_0x25aca7};},'_unOrganizeData':function(){var _0x1fa054=[],_0x5764fc=this['_data'],_0x42814e=this['_radi'];for(var _0x2fde3d in _0x5764fc){for(var _0x2b3049 in _0x5764fc[_0x2fde3d]){_0x1fa054['push']({'x':_0x2fde3d,'y':_0x2b3049,'radius':_0x42814e[_0x2fde3d][_0x2b3049],'value':_0x5764fc[_0x2fde3d][_0x2b3049]});}}return{'min':this['_min'],'max':this['_max'],'data':_0x1fa054};},'_onExtremaChange':function(){var _0x134a98=_0x3c6044;this['_coordinator']['emit'](_0x134a98(_0x1b083d._0x132b45),{'min':this['_min'],'max':this['_max']});},'addData':function(){var _0xf09098=_0x3c6044;if(arguments[0x0]['length']>0x0){var _0x4f006b=arguments[0x0],_0xd064fb=_0x4f006b['length'];while(_0xd064fb--){this['addData']['call'](this,_0x4f006b[_0xd064fb]);}}else{var _0x2c7ffd=this['_organiseData'](arguments[0x0],!![]);_0x2c7ffd&&(this['_data']['length']===0x0&&(this['_min']=this[_0xf09098(0x137)]=_0x2c7ffd['value']),this[_0xf09098(0x163)][_0xf09098(0x141)]('renderpartial',{'min':this['_min'],'max':this[_0xf09098(0x137)],'data':[_0x2c7ffd]}));}return this;},'setData':function(_0x562134){var _0x1b0bf4=_0x3c6044,_0x4b9db3=_0x562134[_0x1b0bf4(_0x133211._0x32001c)],_0x3d827c=_0x4b9db3['length'];this['_data']=[],this['_radi']=[];for(var _0x103b6d=0x0;_0x103b6d<_0x3d827c;_0x103b6d++){this['_organiseData'](_0x4b9db3[_0x103b6d],![]);}return this['_max']=_0x562134['max'],this['_min']=_0x562134['min']||0x0,this['_onExtremaChange'](),this[_0x1b0bf4(_0x133211._0x44f646)][_0x1b0bf4(_0x133211._0x5f5fb6)]('renderall',this[_0x1b0bf4(_0x133211._0xb96de2)]()),this;},'removeData':function(){},'setDataMax':function(_0x568fee){var _0x27a3bd=_0x3c6044;return this[_0x27a3bd(_0x1b4d91._0x378f37)]=_0x568fee,this['_onExtremaChange'](),this['_coordinator']['emit']('renderall',this['_getInternalData']()),this;},'setDataMin':function(_0x56fde6){return this['_min']=_0x56fde6,this['_onExtremaChange'](),this['_coordinator']['emit']('renderall',this['_getInternalData']()),this;},'setCoordinator':function(_0x30ee09){this['_coordinator']=_0x30ee09;},'_getInternalData':function(){return{'max':this['_max'],'min':this['_min'],'data':this['_data'],'radi':this['_radi']};},'getData':function(){return this['_unOrganizeData']();}},_0x559a6e;}(),_0x55744d=function _0x15228c(){var _0x36fee9={_0x43eb39:0x1b0,_0xf2e3bc:0x1a4,_0x20402c:0x16a,_0x3e06d7:0x149},_0x59f074={_0x4c1ead:0x15b},_0x4ed2c2={_0x5958cd:0x1b4},_0x5121a2={_0x3e4107:0x182,_0x110a69:0x16f,_0x56f9ea:0x122,_0xec0c79:0x1b2,_0x11e474:0x11c,_0x42050d:0x181},_0x4cf5ac={_0x4e32b2:0x1b0,_0x4f1c6c:0x12a},_0x138dcf={_0x14340a:0x178,_0x4d98ce:0x19d},_0x170baa=function(_0x1b401e){var _0x546b1a=_0x1bce,_0x18f3a0=_0x1b401e['gradient']||_0x1b401e[_0x546b1a(_0x28a9e1._0x3d38ba)],_0x3b24c7=document[_0x546b1a(_0x28a9e1._0x5090f5)](_0x546b1a(_0x28a9e1._0x56d5e8)),_0x1a854f=_0x3b24c7['getContext']('2d',{'willReadFrequently':!![]});_0x3b24c7[_0x546b1a(0x178)]=0x100,_0x3b24c7['height']=0x1;var _0x3b21d6=_0x1a854f['createLinearGradient'](0x0,0x0,0x100,0x1);for(var _0x37d897 in _0x18f3a0){_0x3b21d6[_0x546b1a(0x1aa)](_0x37d897,_0x18f3a0[_0x37d897]);}return _0x1a854f[_0x546b1a(_0x28a9e1._0x30686f)]=_0x3b21d6,_0x1a854f[_0x546b1a(_0x28a9e1._0x2071ca)](0x0,0x0,0x100,0x1),_0x1a854f['getImageData'](0x0,0x0,0x100,0x1)[_0x546b1a(0x1a4)];},_0x4b4f6f=function(_0x27349c,_0x2acc16){var _0x15436b=_0x1bce,_0x4dcd6c=document['createElement']('canvas'),_0x43a4b4=_0x4dcd6c['getContext']('2d',{'willReadFrequently':!![]}),_0x54e7ab=_0x27349c,_0x2697ef=_0x27349c;_0x4dcd6c[_0x15436b(_0x138dcf._0x14340a)]=_0x4dcd6c['height']=_0x27349c*0x2;if(_0x2acc16==0x1)_0x43a4b4[_0x15436b(0x1a7)](),_0x43a4b4[_0x15436b(0x187)](_0x54e7ab,_0x2697ef,_0x27349c,0x0,0x2*Math['PI'],![]),_0x43a4b4[_0x15436b(_0x138dcf._0x4d98ce)]='rgba(0,0,0,1)',_0x43a4b4[_0x15436b(0x117)]();else{var _0x471441=_0x43a4b4['createRadialGradient'](_0x54e7ab,_0x2697ef,_0x27349c*_0x2acc16,_0x54e7ab,_0x2697ef,_0x27349c);_0x471441['addColorStop'](0x0,'rgba(0,0,0,1)'),_0x471441[_0x15436b(0x1aa)](0x1,'rgba(0,0,0,0)'),_0x43a4b4['fillStyle']=_0x471441,_0x43a4b4['fillRect'](0x0,0x0,0x2*_0x27349c,0x2*_0x27349c);}return _0x4dcd6c;},_0x4a36aa=function(_0x266d42){var _0x4dac42=_0x1bce,_0x48601e=[],_0x234fd7=_0x266d42[_0x4dac42(_0x4cf5ac._0x4e32b2)],_0x142ab4=_0x266d42['max'],_0x445e13=_0x266d42['radi'],_0x266d42=_0x266d42['data'],_0x35f325=Object[_0x4dac42(_0x4cf5ac._0x4f1c6c)](_0x266d42),_0x1a1314=_0x35f325['length'];while(_0x1a1314--){var _0x158868=_0x35f325[_0x1a1314],_0x796ad2=Object['keys'](_0x266d42[_0x158868]),_0x59c251=_0x796ad2['length'];while(_0x59c251--){var _0x2954da=_0x796ad2[_0x59c251],_0x2f9b0c=_0x266d42[_0x158868][_0x2954da],_0x5185ae=_0x445e13[_0x158868][_0x2954da];_0x48601e['push']({'x':_0x158868,'y':_0x2954da,'value':_0x2f9b0c,'radius':_0x5185ae});}}return{'min':_0x234fd7,'max':_0x142ab4,'data':_0x48601e};};function _0x44b092(_0x4ab3a1){var _0x385f3c=_0x1bce,_0x1e9d74=_0x4ab3a1[_0x385f3c(0x17d)],_0x36f02c=this['shadowCanvas']=document[_0x385f3c(_0x5121a2._0x3e4107)]('canvas'),_0x43c7a7=this['canvas']=_0x4ab3a1['canvas']||document['createElement'](_0x385f3c(0x114));this['_renderBoundaries']=[0x2710,0x2710,0x0,0x0];var _0xe0aa09=getComputedStyle(_0x4ab3a1['container'])||{};_0x43c7a7[_0x385f3c(0x1af)]=_0x385f3c(_0x5121a2._0x110a69),this[_0x385f3c(0x166)]=_0x43c7a7['width']=_0x36f02c['width']=_0x4ab3a1['width']||+_0xe0aa09[_0x385f3c(0x178)]['replace'](/px/,''),this[_0x385f3c(_0x5121a2._0x56f9ea)]=_0x43c7a7['height']=_0x36f02c['height']=_0x4ab3a1['height']||+_0xe0aa09['height'][_0x385f3c(_0x5121a2._0xec0c79)](/px/,''),this['shadowCtx']=_0x36f02c['getContext']('2d',{'willReadFrequently':!![]}),this['ctx']=_0x43c7a7['getContext']('2d',{'willReadFrequently':!![]}),_0x43c7a7['style']['cssText']=_0x36f02c['style'][_0x385f3c(_0x5121a2._0x11e474)]=_0x385f3c(0x183),_0x1e9d74['style'][_0x385f3c(_0x5121a2._0x42050d)]='relative',_0x1e9d74['appendChild'](_0x43c7a7),this['_palette']=_0x170baa(_0x4ab3a1),this['_templates']={},this['_setStyles'](_0x4ab3a1);}return _0x44b092['prototype']={'renderPartial':function(_0x2e2b55){var _0x1ae5fa=_0x1bce;_0x2e2b55[_0x1ae5fa(0x1a4)]['length']>0x0&&(this[_0x1ae5fa(_0x4ed2c2._0x5958cd)](_0x2e2b55),this['_colorize']());},'renderAll':function(_0x5f2727){var _0x34f5fd=_0x1bce;this[_0x34f5fd(0x167)](),_0x5f2727[_0x34f5fd(0x1a4)][_0x34f5fd(_0x20b76e._0x292818)]>0x0&&(this[_0x34f5fd(0x1b4)](_0x4a36aa(_0x5f2727)),this[_0x34f5fd(0x1a9)]());},'_updateGradient':function(_0x4eb0bc){this['_palette']=_0x170baa(_0x4eb0bc);},'updateConfig':function(_0x2f9ca4){var _0x327009=_0x1bce;_0x2f9ca4['gradient']&&this[_0x327009(0x14d)](_0x2f9ca4),this['_setStyles'](_0x2f9ca4);},'setDimensions':function(_0xa07c81,_0x3f981a){var _0x5e9d8e=_0x1bce;this[_0x5e9d8e(_0x4be9f5._0x22d414)]=_0xa07c81,this[_0x5e9d8e(_0x4be9f5._0x22c98c)]=_0x3f981a,this['canvas']['width']=this[_0x5e9d8e(0x172)][_0x5e9d8e(0x178)]=_0xa07c81,this['canvas']['height']=this['shadowCanvas']['height']=_0x3f981a;},'_clear':function(){var _0x72563d=_0x1bce;this['shadowCtx'][_0x72563d(_0x59f074._0x4c1ead)](0x0,0x0,this['_width'],this['_height']),this['ctx']['clearRect'](0x0,0x0,this['_width'],this['_height']);},'_setStyles':function(_0x5a43bb){var _0x5cf7c5=_0x1bce;this['_blur']=_0x5a43bb['blur']==0x0?0x0:_0x5a43bb[_0x5cf7c5(0x129)]||_0x5a43bb['defaultBlur'],_0x5a43bb['backgroundColor']&&(this['canvas'][_0x5cf7c5(_0x1c7dac._0x2489f7)][_0x5cf7c5(_0x1c7dac._0x5dd407)]=_0x5a43bb[_0x5cf7c5(0x138)]),this[_0x5cf7c5(0x166)]=this['canvas']['width']=this['shadowCanvas']['width']=_0x5a43bb['width']||this[_0x5cf7c5(0x166)],this[_0x5cf7c5(_0x1c7dac._0xefb552)]=this[_0x5cf7c5(_0x1c7dac._0x2d87e6)]['height']=this['shadowCanvas']['height']=_0x5a43bb['height']||this['_height'],this['_opacity']=(_0x5a43bb['opacity']||0x0)*0xff,this['_maxOpacity']=(_0x5a43bb['maxOpacity']||_0x5a43bb[_0x5cf7c5(0x195)])*0xff,this['_minOpacity']=(_0x5a43bb[_0x5cf7c5(_0x1c7dac._0x164d62)]||_0x5a43bb['defaultMinOpacity'])*0xff,this['_useGradientOpacity']=!!_0x5a43bb[_0x5cf7c5(_0x1c7dac._0x24ad05)];},'_drawAlpha':function(_0x24e18b){var _0x370421=_0x1bce,_0xb6fc9=this['_min']=_0x24e18b[_0x370421(_0x36fee9._0x43eb39)],_0x35b7ba=this['_max']=_0x24e18b['max'],_0x24e18b=_0x24e18b[_0x370421(_0x36fee9._0xf2e3bc)]||[],_0x99dce2=_0x24e18b['length'],_0x771816=0x1-this['_blur'];while(_0x99dce2--){var _0x222d22=_0x24e18b[_0x99dce2],_0x5cac2e=_0x222d22['x'],_0x3943e6=_0x222d22['y'],_0x19c9ff=_0x222d22['radius'],_0xc9133f=Math['min'](_0x222d22['value'],_0x35b7ba),_0x33a957=_0x5cac2e-_0x19c9ff,_0x2c600b=_0x3943e6-_0x19c9ff,_0x2c85c5=this['shadowCtx'],_0x417fb0;!this['_templates'][_0x19c9ff]?this[_0x370421(_0x36fee9._0x20402c)][_0x19c9ff]=_0x417fb0=_0x4b4f6f(_0x19c9ff,_0x771816):_0x417fb0=this[_0x370421(_0x36fee9._0x20402c)][_0x19c9ff];var _0x219780=(_0xc9133f-_0xb6fc9)/(_0x35b7ba-_0xb6fc9);_0x2c85c5['globalAlpha']=_0x219780<0.01?0.01:_0x219780,_0x2c85c5['drawImage'](_0x417fb0,_0x33a957,_0x2c600b),_0x33a957<this['_renderBoundaries'][0x0]&&(this[_0x370421(0x149)][0x0]=_0x33a957),_0x2c600b<this['_renderBoundaries'][0x1]&&(this['_renderBoundaries'][0x1]=_0x2c600b),_0x33a957+0x2*_0x19c9ff>this['_renderBoundaries'][0x2]&&(this[_0x370421(_0x36fee9._0x3e06d7)][0x2]=_0x33a957+0x2*_0x19c9ff),_0x2c600b+0x2*_0x19c9ff>this['_renderBoundaries'][0x3]&&(this['_renderBoundaries'][0x3]=_0x2c600b+0x2*_0x19c9ff);}},'_colorize':function(){var _0x8006ef=_0x1bce,_0x47691b=this['_renderBoundaries'][0x0],_0x262986=this['_renderBoundaries'][0x1],_0x476baa=this[_0x8006ef(_0x5541b9._0x5323ca)][0x2]-_0x47691b,_0x496fa0=this['_renderBoundaries'][0x3]-_0x262986,_0x343205=this['_width'],_0x135769=this['_height'],_0x2d5e40=this['_opacity'],_0x177891=this['_maxOpacity'],_0x5da0c4=this['_minOpacity'],_0x147761=this['_useGradientOpacity'];_0x47691b<0x0&&(_0x47691b=0x0);_0x262986<0x0&&(_0x262986=0x0);_0x47691b+_0x476baa>_0x343205&&(_0x476baa=_0x343205-_0x47691b);_0x262986+_0x496fa0>_0x135769&&(_0x496fa0=_0x135769-_0x262986);var _0x36bdc3=this[_0x8006ef(_0x5541b9._0x2bde85)]['getImageData'](_0x47691b,_0x262986,_0x476baa,_0x496fa0),_0x3c858c=_0x36bdc3[_0x8006ef(_0x5541b9._0x5b4a3d)],_0x302b29=_0x3c858c[_0x8006ef(_0x5541b9._0x16bd47)],_0x293650=this['_palette'];for(var _0x23838c=0x3;_0x23838c<_0x302b29;_0x23838c+=0x4){var _0x2113fa=_0x3c858c[_0x23838c],_0x53a233=_0x2113fa*0x4;if(!_0x53a233)continue;var _0x4a01c5;_0x2d5e40>0x0?_0x4a01c5=_0x2d5e40:_0x2113fa<_0x177891?_0x2113fa<_0x5da0c4?_0x4a01c5=_0x5da0c4:_0x4a01c5=_0x2113fa:_0x4a01c5=_0x177891,_0x3c858c[_0x23838c-0x3]=_0x293650[_0x53a233],_0x3c858c[_0x23838c-0x2]=_0x293650[_0x53a233+0x1],_0x3c858c[_0x23838c-0x1]=_0x293650[_0x53a233+0x2],_0x3c858c[_0x23838c]=_0x147761?_0x293650[_0x53a233+0x3]:_0x4a01c5;}this['ctx']['putImageData'](_0x36bdc3,_0x47691b,_0x262986),this['_renderBoundaries']=[0x3e8,0x3e8,0x0,0x0];},'getValueAt':function(_0x52305e){var _0x4f8dbe=_0x1bce,_0xcef725,_0x4f68c6=this['shadowCtx'],_0x73c5a9=_0x4f68c6[_0x4f8dbe(_0x5a11c9._0x24ee98)](_0x52305e['x'],_0x52305e['y'],0x1,0x1),_0x3b5638=_0x73c5a9['data'][0x3],_0x4811dd=this['_max'],_0x1c185e=this['_min'];return _0xcef725=Math['abs'](_0x4811dd-_0x1c185e)*(_0x3b5638/0xff)>>0x0,_0xcef725;},'getDataURL':function(){return this['canvas']['toDataURL']();}},_0x44b092;}(),_0x2f128d=function _0x19f6d4(){var _0x48d4e4=![];return _0x201414['defaultRenderer']==='canvas2d'&&(_0x48d4e4=_0x55744d),_0x48d4e4;}(),_0x57efc1={'merge':function(){var _0x3c20a9=_0x182428,_0xca5afd={},_0x4a3aa1=arguments[_0x3c20a9(_0x26d624._0x4d58e8)];for(var _0x4c16b3=0x0;_0x4c16b3<_0x4a3aa1;_0x4c16b3++){var _0x2aed84=arguments[_0x4c16b3];for(var _0x5ecdc7 in _0x2aed84){_0xca5afd[_0x5ecdc7]=_0x2aed84[_0x5ecdc7];}}return _0xca5afd;}},_0x55eb88=function _0x5e469c(){var _0x57b05f={_0x578002:0x145},_0x287843={_0x4681ff:0x132},_0x1162db={_0x2d1c90:0x146},_0x287402={_0x6c1f72:0x13f,_0x25a1fa:0x199,_0x4e54be:0x146},_0x695ccd={_0x12f774:0x1b0,_0x54ff66:0x145},_0xa9bd0c={_0x18bfd6:0x19b},_0x18f97d=_0x182428,_0x1d0f17=function _0x5bd95e(){var _0x7e98c8=_0x1bce;function _0x5f053d(){this['cStore']={};}return _0x5f053d[_0x7e98c8(_0xa9bd0c._0x18bfd6)]={'on':function(_0x3ffe24,_0x56b095,_0x1dd554){var _0x27f9b1=this['cStore'];!_0x27f9b1[_0x3ffe24]&&(_0x27f9b1[_0x3ffe24]=[]),_0x27f9b1[_0x3ffe24]['push'](function(_0x356ee1){return _0x56b095['call'](_0x1dd554,_0x356ee1);});},'emit':function(_0x54386d,_0x55cd76){var _0x58ee9d=_0x7e98c8,_0x113b9b=this[_0x58ee9d(0x16e)];if(_0x113b9b[_0x54386d]){var _0x3b8d63=_0x113b9b[_0x54386d]['length'];for(var _0x434635=0x0;_0x434635<_0x3b8d63;_0x434635++){var _0x127b57=_0x113b9b[_0x54386d][_0x434635];_0x127b57(_0x55cd76);}}}},_0x5f053d;}(),_0x2c49a0=function(_0x1ac09c){var _0x3e863d=_0x1bce,_0x1ba18a=_0x1ac09c[_0x3e863d(0x15f)],_0x24e521=_0x1ac09c[_0x3e863d(0x163)],_0xe8e0df=_0x1ac09c['_store'];_0x24e521['on']('renderpartial',_0x1ba18a['renderPartial'],_0x1ba18a),_0x24e521['on']('renderall',_0x1ba18a['renderAll'],_0x1ba18a),_0x24e521['on']('extremachange',function(_0x3d65ea){var _0x46400e=_0x3e863d;_0x1ac09c['_config']['onExtremaChange']&&_0x1ac09c['_config']['onExtremaChange']({'min':_0x3d65ea[_0x46400e(_0x695ccd._0x12f774)],'max':_0x3d65ea[_0x46400e(0x12b)],'gradient':_0x1ac09c['_config']['gradient']||_0x1ac09c[_0x46400e(_0x695ccd._0x54ff66)]['defaultGradient']});}),_0xe8e0df[_0x3e863d(0x112)](_0x24e521);};function _0x48670c(){var _0x507e41=_0x1bce,_0x45e9c6=this['_config']=_0x57efc1[_0x507e41(0x151)](_0x201414,arguments[0x0]||{});this['_coordinator']=new _0x1d0f17();if(_0x45e9c6['plugin']){var _0x39cd10=_0x45e9c6['plugin'];if(!_0x201414[_0x507e41(_0x287402._0x6c1f72)][_0x39cd10])throw new Error('Plugin\x20\x27'+_0x39cd10+'\x27\x20not\x20found.\x20Maybe\x20it\x20was\x20not\x20registered.');else{var _0xd1e6a0=_0x201414['plugins'][_0x39cd10];this['_renderer']=new _0xd1e6a0[(_0x507e41(_0x287402._0x25a1fa))](_0x45e9c6),this[_0x507e41(_0x287402._0x4e54be)]=new _0xd1e6a0['store'](_0x45e9c6);}}else this[_0x507e41(0x15f)]=new _0x2f128d(_0x45e9c6),this['_store']=new _0x243d34(_0x45e9c6);_0x2c49a0(this);}return _0x48670c[_0x18f97d(_0x35b911._0x57c491)]={'addData':function(){return this['_store']['addData']['apply'](this['_store'],arguments),this;},'removeData':function(){var _0x42b68d=_0x18f97d;return this[_0x42b68d(_0x1162db._0x2d1c90)][_0x42b68d(0x12f)]&&this['_store']['removeData']['apply'](this['_store'],arguments),this;},'setData':function(){return this['_store']['setData']['apply'](this['_store'],arguments),this;},'setDataMax':function(){var _0x936e47=_0x18f97d;return this[_0x936e47(0x146)]['setDataMax']['apply'](this['_store'],arguments),this;},'setDataMin':function(){var _0x9dc5e1=_0x18f97d;return this['_store'][_0x9dc5e1(_0x287843._0x4681ff)]['apply'](this[_0x9dc5e1(0x146)],arguments),this;},'configure':function(_0x70c637){var _0x424f23=_0x18f97d;return this['_config']=_0x57efc1['merge'](this[_0x424f23(0x145)],_0x70c637),this['_renderer']['updateConfig'](this[_0x424f23(_0x57b05f._0x578002)]),this['_coordinator']['emit']('renderall',this[_0x424f23(0x146)]['_getInternalData']()),this;},'repaint':function(){var _0x54ae9a=_0x18f97d;return this['_coordinator']['emit'](_0x54ae9a(0x131),this['_store']['_getInternalData']()),this;},'getData':function(){var _0x2e1a82=_0x18f97d;return this['_store'][_0x2e1a82(0x16b)]();},'getDataURL':function(){var _0x3b1eb2=_0x18f97d;return this['_renderer'][_0x3b1eb2(0x14b)]();},'getValueAt':function(_0x4fadea){var _0x414ac6=_0x18f97d;if(this['_store'][_0x414ac6(_0x53b04d._0x3bbbd)])return this[_0x414ac6(0x146)][_0x414ac6(_0x53b04d._0x2f037c)](_0x4fadea);else return this[_0x414ac6(0x15f)]['getValueAt']?this['_renderer']['getValueAt'](_0x4fadea):null;}},_0x48670c;}(),_0x5ac0ba={'create':function(_0x2cc752){return new _0x55eb88(_0x2cc752);},'register':function(_0xf65d81,_0x17aed9){_0x201414['plugins'][_0xf65d81]=_0x17aed9;}};return _0x5ac0ba;}));}(heatmap$1));var heatmap=heatmap$1[_0xe350a3(0x196)],h337=_mergeNamespaces({'__proto__':null,'default':heatmap},[heatmap$1['exports']]),HeatMaterial=_0xe350a3(0x15c);if(!heatmap$1['exports'][_0xe350a3(0x13b)])throw new Error('请引入\x20heatmap.js\x20库\x20');const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace['layer']['BaseLayer'],DEF_HEATSTYLE={'maxOpacity':0.8,'minOpacity':0.1,'blur':0.85,'radius':0x19,'gradient':{0.4:'blue',0.6:'green',0.8:'yellow',0.9:'red'}},DEF_STYLE={'arcRadiusScale':1.5,'arcBlurScale':1.5,'vertexFormat':Cesium[_0xe350a3(0x161)]['VERTEX_FORMAT']};class HeatLayer extends BaseLayer{constructor(_0x3e2d35={}){var _0x16c50f={_0x370def:0x17a,_0x967f24:0x17a},_0x2202cc=_0xe350a3;super(_0x3e2d35),this[_0x2202cc(0x17a)]['redrawRatio']=this[_0x2202cc(_0x16c50f._0x370def)]['redrawRatio']||0x1,this[_0x2202cc(0x17a)]['heatStyle']={...DEF_HEATSTYLE,...this['options']['heatStyle']},this[_0x2202cc(_0x16c50f._0x967f24)][_0x2202cc(0x1ac)]={...DEF_STYLE,...this['options']['style']};}get['layer'](){var _0x5bfdb1={_0x231264:0x18a},_0x4c5fd3=_0xe350a3;return this[_0x4c5fd3(_0x5bfdb1._0x231264)];}get[_0xe350a3(0x158)](){return this['options']['heatStyle'];}set['heatStyle'](_0x20737f){var _0x137be8={_0x35531a:0x17a},_0x914381=_0xe350a3;this[_0x914381(_0x137be8._0x35531a)]['heatStyle']=mars3d__namespace[_0x914381(0x186)]['merge'](this[_0x914381(_0x137be8._0x35531a)][_0x914381(0x158)],_0x20737f);if(this['_heat']){this[_0x914381(0x14e)][_0x914381(0x191)](this['options'][_0x914381(0x158)]);const _0x1bd7b2=getCanvas(this['_heat']['_renderer']['canvas']);this['_updateGraphic'](_0x1bd7b2),_0x20737f['radius']&&this['updateRadius'](_0x20737f['radius']);}}get['style'](){return this['options']['style'];}set[_0xe350a3(0x1ac)](_0x2f617f){var _0x37b0f2=_0xe350a3;this[_0x37b0f2(0x17a)]['style']=mars3d__namespace['Util']['merge'](this['options']['style'],_0x2f617f);}get['positions'](){return this['_positions'];}set['positions'](_0x1c5d64){this['setPositions'](_0x1c5d64);}get['coordinates'](){var _0x58aa3d={_0x242f91:0x143},_0x410bc7=_0xe350a3;const _0x565ccf=[];return this[_0x410bc7(_0x58aa3d._0x242f91)]['forEach'](_0x1931fe=>{_0x565ccf['push'](_0x1931fe['toArray']());}),_0x565ccf;}get['rectangle'](){return this['_rectangle'];}[_0xe350a3(0x119)](_0x3e04c5,_0x5a0fea){var _0x2e0c38={_0x828d04:0x177,_0x3a6d6a:0x1b0,_0x1b698b:0x12b,_0x56cac6:0x15f},_0x3b3e73=_0xe350a3;if(this['_heat']){var _0x128a8a;_0x5a0fea[_0x3b3e73(0x158)]&&(_0x3e04c5[_0x3b3e73(0x158)]=mars3d__namespace['Util']['merge'](_0x3e04c5['heatStyle'],_0x5a0fea['heatStyle']),this['_heat']['configure'](_0x3e04c5['heatStyle']));const _0xd611e3=this['_heat']['getData']();if((_0x128a8a=_0x5a0fea['heatStyle'])!==null&&_0x128a8a!==void 0x0&&_0x128a8a[_0x3b3e73(0x18b)]){const _0x5e96b0=_0x5a0fea['heatStyle']['radius'];if(_0xd611e3!==null&&_0xd611e3!==void 0x0&&_0xd611e3['data'])for(const _0x40f3a in _0xd611e3['data']){const _0x35b99d=_0xd611e3[_0x3b3e73(0x1a4)][_0x40f3a];_0x35b99d['radius']=_0x5e96b0;}}Cesium[_0x3b3e73(_0x2e0c38._0x828d04)](_0x5a0fea[_0x3b3e73(0x1b0)])&&(_0xd611e3['min']=_0x5a0fea[_0x3b3e73(_0x2e0c38._0x3a6d6a)]);Cesium['defined'](_0x5a0fea[_0x3b3e73(_0x2e0c38._0x1b698b)])&&(_0xd611e3['max']=_0x5a0fea['max']);this['_heat']['setData'](_0xd611e3);const _0x273509=getCanvas(this['_heat'][_0x3b3e73(_0x2e0c38._0x56cac6)]['canvas']);this['_updateGraphic'](_0x273509);}_0x5a0fea['positions']&&(this[_0x3b3e73(0x1a2)]=_0x5a0fea['positions']);}['_mountedHook'](){var _0x109fbb={_0x1e5216:0x18d,_0x181734:0x1ae},_0x4f1995=_0xe350a3;this['style'][_0x4f1995(_0x109fbb._0x1e5216)]===_0x4f1995(0x173)?this[_0x4f1995(0x18a)]=new mars3d__namespace['layer'][(_0x4f1995(_0x109fbb._0x181734))]({'crs':'EPSG:3857','private':!![]}):this['_layer']=new mars3d__namespace['layer']['GraphicLayer']({'private':!![]});}['_addedHook'](){var _0x3167ad={_0x55997e:0x144,_0x3d9d31:0x1a8},_0x5dcb20=_0xe350a3;this['_map']['addLayer'](this[_0x5dcb20(0x18a)]),this['_container']=mars3d__namespace['DomUtil']['create']('div','mars3d-heatmap\x20mars3d-hideDiv',this['_map']['container']),this['options']['positions']&&(this['positions']=this['options']['positions']),this[_0x5dcb20(0x17a)]['redrawZoom']&&(this[_0x5dcb20(0x11b)]['on'](mars3d__namespace['EventType'][_0x5dcb20(_0x3167ad._0x55997e)],this['_onCameraMoveEnd'],this),this['_onCameraMoveEnd']()),this[_0x5dcb20(0x17a)][_0x5dcb20(_0x3167ad._0x3d9d31)]&&this['flyTo']();}['_removedHook'](){var _0x3fe19d={_0x1fed71:0x1b3,_0x45ed3a:0x188},_0x18e11f=_0xe350a3;this[_0x18e11f(0x17a)]['redrawZoom']&&this['_map']['off'](mars3d__namespace['EventType']['cameraMoveEnd'],this['_onCameraMoveEnd'],this),this['_container']&&(mars3d__namespace['DomUtil']['remove'](this['_container']),delete this[_0x18e11f(_0x3fe19d._0x1fed71)]),this['clear'](),this['_map'][_0x18e11f(_0x3fe19d._0x45ed3a)](this['_layer']);}['_showHook'](_0x547650){var _0x629747=_0xe350a3;_0x547650&&this[_0x629747(0x153)]();}[_0xe350a3(0x14a)](_0x2e6c9f){this['_positions']=this['_positions']||[],this['_positions']['push'](_0x2e6c9f),this['_updatePositionsHook']();}[_0xe350a3(0x1a6)](_0x4a3e2e){var _0x33834c=_0xe350a3;this[_0x33834c(0x16d)]=_0x4a3e2e,this['_updatePositionsHook']();}['clear'](){var _0x2e1c0b={_0x21cf80:0x18f,_0x58e25f:0x133},_0x3b4996=_0xe350a3;this['_graphic']&&(this['_layer']['removeGraphic'](this[_0x3b4996(_0x2e1c0b._0x21cf80)],!![]),delete this['_graphic']),this['_graphic2']&&(this['_layer'][_0x3b4996(_0x2e1c0b._0x58e25f)](this['_graphic2'],!![]),delete this['_graphic2']);}['_updatePositionsHook'](){var _0x265c93=_0xe350a3;if(!this['show']||!this[_0x265c93(0x11b)]||!this['positions']||this['positions']['length']===0x0)return this;const _0x2f5fa2=this['_getHeatCanvas']();return this['_updateGraphic'](_0x2f5fa2),this;}['getRectangle'](_0x47e872){var _0x2db5e2={_0x26745d:0x139,_0x343f39:0x139},_0x4396fe=_0xe350a3;return _0x47e872!==null&&_0x47e872!==void 0x0&&_0x47e872['isFormat']&&this[_0x4396fe(_0x2db5e2._0x26745d)]?mars3d__namespace['PolyUtil']['formatRectangle'](this[_0x4396fe(_0x2db5e2._0x343f39)]):this['_rectangle'];}['_onCameraMoveEnd'](){var _0x21228d={_0x1ba135:0x17b,_0x3cf7f3:0x17a},_0x2ec977=_0xe350a3;if(!this[_0x2ec977(0x14e)]||!this['show']||!this['_map'])return;let _0x4d61f5;const _0x23fbd8=getSurfaceDistance(this['_map'][_0x2ec977(_0x21228d._0x1ba135)])/0x2;if(_0x23fbd8&&_0x23fbd8<this['_bounds']['radius']){const _0x34e252=this[_0x2ec977(_0x21228d._0x3cf7f3)]['redrawRatio']*_0x23fbd8/this['_bounds']['radius'];_0x4d61f5=this['heatStyle'][_0x2ec977(0x18b)]*_0x34e252,_0x4d61f5=Math['max'](_0x4d61f5,0x2);}else _0x4d61f5=this[_0x2ec977(0x158)][_0x2ec977(0x18b)];_0x4d61f5&&this['updateRadius'](_0x4d61f5);}['_getBounds'](_0x3d7bc2){var _0x3477fb={_0x5d3c5b:0x17a,_0x4184ef:0x190,_0x4a8a54:0x175,_0x515a8b:0x15e,_0x2ad898:0x13e},_0x2c1abc={_0xa75dff:0x162,_0x192eb7:0x162,_0x432934:0x147},_0x16db3d=_0xe350a3;let _0x13fe0b,_0x70b667,_0x3ab942,_0x548173;this['options']['rectangle']?(_0x13fe0b=this['options']['rectangle'][_0x16db3d(0x155)],_0x70b667=this[_0x16db3d(0x17a)]['rectangle']['xmax'],_0x3ab942=this[_0x16db3d(_0x3477fb._0x5d3c5b)]['rectangle'][_0x16db3d(0x11e)],_0x548173=this['options'][_0x16db3d(_0x3477fb._0x4184ef)][_0x16db3d(0x19c)]):_0x3d7bc2[_0x16db3d(_0x3477fb._0x4a8a54)]((_0x1aa23f,_0x53c306)=>{var _0x52513b=_0x16db3d;_0x53c306===0x0?(_0x13fe0b=_0x1aa23f[_0x52513b(_0x2c1abc._0xa75dff)],_0x70b667=_0x1aa23f[_0x52513b(_0x2c1abc._0x192eb7)],_0x3ab942=_0x1aa23f['lat'],_0x548173=_0x1aa23f['lat']):(_0x13fe0b=Math['min'](_0x13fe0b,_0x1aa23f['lng']),_0x70b667=Math['max'](_0x70b667,_0x1aa23f['lng']),_0x3ab942=Math['min'](_0x3ab942,_0x1aa23f['lat']),_0x548173=Math['max'](_0x548173,_0x1aa23f[_0x52513b(_0x2c1abc._0x432934)]));});let _0x42b3f1=_0x70b667-_0x13fe0b,_0xc7fcd5=_0x548173-_0x3ab942;_0x42b3f1===0x0&&(_0x42b3f1=0x1);_0xc7fcd5===0x0&&(_0xc7fcd5=0x1);const _0x571e93=this['options']['rectanglePadding']??0.2;!this['options']['rectangle']&&(_0x13fe0b-=_0x42b3f1*_0x571e93,_0x3ab942-=_0xc7fcd5*_0x571e93,_0x70b667+=_0x42b3f1*_0x571e93,_0x548173+=_0xc7fcd5*_0x571e93);_0x13fe0b=Math['max'](_0x13fe0b,-0xb4),_0x70b667=Math['min'](_0x70b667,0xb4),_0x3ab942=Math[_0x16db3d(0x12b)](_0x3ab942,-0x5a),_0x548173=Math['min'](_0x548173,0x5a);const _0x91a186={'xmin':_0x13fe0b,'xmax':_0x70b667,'ymin':_0x3ab942,'ymax':_0x548173};_0x91a186[_0x16db3d(_0x3477fb._0x515a8b)]=_0x70b667-_0x13fe0b,_0x91a186['diffY']=_0x548173-_0x3ab942,_0x91a186['rectangle']=Cesium['Rectangle'][_0x16db3d(_0x3477fb._0x2ad898)](_0x13fe0b,_0x3ab942,_0x70b667,_0x548173);const _0x2ca594=Math['max'](_0x91a186['rectangle'][_0x16db3d(0x1a1)],_0x91a186['rectangle']['width']);return _0x91a186['granularity']=_0x2ca594,_0x91a186['radius']=Cesium['Math']['chordLength'](_0x2ca594,this['_map']['scene'][_0x16db3d(0x193)]['ellipsoid']['maximumRadius'])/(0x1+0x2*_0x571e93),_0x91a186;}['_getHeatCanvas'](){var _0x16aa77={_0x87d20:0x16d,_0xeb0ab8:0x19f,_0x574b8c:0x11f,_0x26086c:0x113,_0x592066:0x186,_0x3d9bd6:0x19f,_0xa08223:0x1ac,_0x2301b1:0x11c,_0x202523:0x1b3,_0x330cf6:0x13a,_0x420e09:0x1b0},_0x4f7271={_0x1f510e:0x192,_0x128b2e:0x19f,_0x175666:0x155,_0x46d581:0x12b,_0x44e247:0x1b0,_0x970464:0x1a3},_0x2c70f4={_0x841960:0x17a,_0x35a7dc:0x194,_0x399fa6:0x13a},_0x43ca72=_0xe350a3;const _0x368198=this[_0x43ca72(_0x16aa77._0x87d20)],_0x1256b3=[];_0x368198['forEach'](_0x380014=>{var _0x3c38b2=_0x43ca72;let _0x460838;if(_0x380014['position']&&_0x380014['attr']){_0x460838=mars3d__namespace['LngLatPoint']['parse'](_0x380014[_0x3c38b2(0x181)]);if(!_0x460838)return;_0x460838['value']=Number(_0x380014['attr'][this[_0x3c38b2(_0x2c70f4._0x841960)]['valueColumn']||'value']);}else{_0x460838=mars3d__namespace[_0x3c38b2(_0x2c70f4._0x35a7dc)][_0x3c38b2(0x13c)](_0x380014);if(!_0x460838)return;_0x460838['value']=Number(_0x380014[this[_0x3c38b2(0x17a)]['valueColumn']||_0x3c38b2(_0x2c70f4._0x399fa6)]);}(!_0x460838['value']||isNaN(_0x460838['value']))&&(_0x460838[_0x3c38b2(_0x2c70f4._0x399fa6)]=0x1),_0x1256b3['push'](_0x460838);}),this[_0x43ca72(_0x16aa77._0xeb0ab8)]=this['_getBounds'](_0x1256b3),this['_rectangle']=this['_bounds']['rectangle'];let _0x2025ec,_0x48a91b;this['_bounds']['diffX']>this['_bounds']['diffY']?(_0x2025ec=this['options'][_0x43ca72(0x174)]??document['body'][_0x43ca72(_0x16aa77._0x574b8c)],_0x48a91b=mars3d__namespace['Util']['formatNum'](_0x2025ec/this['_bounds']['diffX']*this['_bounds']['diffY'])):(_0x48a91b=this['options']['canvasSize']??document[_0x43ca72(_0x16aa77._0x26086c)][_0x43ca72(0x17c)],_0x2025ec=mars3d__namespace[_0x43ca72(_0x16aa77._0x592066)]['formatNum'](_0x48a91b/this['_bounds'][_0x43ca72(0x124)]*this[_0x43ca72(_0x16aa77._0x3d9bd6)]['diffX']));this['_canvasWidth']=_0x2025ec,this['_canvasHeight']=_0x48a91b,this[_0x43ca72(0x1b3)][_0x43ca72(_0x16aa77._0xa08223)][_0x43ca72(_0x16aa77._0x2301b1)]=_0x43ca72(0x171)+_0x2025ec+'px;height:'+_0x48a91b+_0x43ca72(0x152);const _0x37c93f={...this['heatStyle'],'container':this[_0x43ca72(_0x16aa77._0x202523)]};this['_heat']?this['_heat']['configure'](_0x37c93f):this['_heat']=heatmap$1[_0x43ca72(0x196)]['create'](_0x37c93f);let _0x2c5018=_0x1256b3[0x0][_0x43ca72(0x13a)]??0x1,_0x4067c6=_0x1256b3[0x0][_0x43ca72(_0x16aa77._0x330cf6)]??0x0;const _0x2dbf62=[];_0x1256b3['forEach'](_0x115d75=>{var _0x369039=_0x43ca72;const _0x3842eb=Math[_0x369039(_0x4f7271._0x1f510e)]((_0x115d75['lng']-this[_0x369039(_0x4f7271._0x128b2e)][_0x369039(_0x4f7271._0x175666)])/this['_bounds']['diffX']*_0x2025ec),_0x805e4f=Math['round']((this['_bounds']['ymax']-_0x115d75['lat'])/this['_bounds']['diffY']*_0x48a91b),_0xe4a438=_0x115d75['value']||0x1;_0x2c5018=Math[_0x369039(_0x4f7271._0x46d581)](_0x2c5018,_0xe4a438),_0x4067c6=Math[_0x369039(_0x4f7271._0x44e247)](_0x4067c6,_0xe4a438),_0x2dbf62[_0x369039(_0x4f7271._0x970464)]({'x':_0x3842eb,'y':_0x805e4f,'value':_0xe4a438});});const _0x5475c8={'min':this[_0x43ca72(0x17a)][_0x43ca72(_0x16aa77._0x420e09)]??_0x4067c6,'max':this['options']['max']??_0x2c5018,'data':_0x2dbf62};return this['_heat']['setData'](_0x5475c8),getCanvas(this['_heat'][_0x43ca72(0x15f)]['canvas']);}[_0xe350a3(0x17e)](){var _0x19d039={_0x2c8dba:0x1ac,_0xb89539:0x197,_0x1196e8:0x158,_0x55a8c4:0x168,_0xf6002b:0x136,_0x1f2c17:0x14e},_0x5331bc=_0xe350a3;this['_heat'][_0x5331bc(0x191)]({'radius':this[_0x5331bc(0x158)]['radius']*this[_0x5331bc(_0x19d039._0x2c8dba)][_0x5331bc(_0x19d039._0xb89539)],'blur':this[_0x5331bc(_0x19d039._0x1196e8)][_0x5331bc(0x129)]*this[_0x5331bc(0x1ac)]['arcBlurScale'],'gradient':this['heatStyle'][_0x5331bc(_0x19d039._0x55a8c4)]||{0.25:'rgb(0,0,0)',0.55:_0x5331bc(0x17f),0.85:'rgb(216,216,216)',0x1:_0x5331bc(_0x19d039._0xf6002b)}});const _0x1b4e7f=getCanvas(this[_0x5331bc(_0x19d039._0x1f2c17)]['_renderer']['canvas']);return this[_0x5331bc(_0x19d039._0x1f2c17)]['configure'](this['options']['heatStyle']),_0x1b4e7f;}['updateRadius'](_0x3e52b0){var _0x36fb82=_0xe350a3;const _0x852e16=this['_heat']['getData']();if(_0x852e16!==null&&_0x852e16!==void 0x0&&_0x852e16['data'])for(const _0x525be1 in _0x852e16['data']){const _0x449144=_0x852e16['data'][_0x525be1];_0x449144['radius']=_0x3e52b0;}this['_heat'][_0x36fb82(0x130)](_0x852e16);const _0x332a03=getCanvas(this[_0x36fb82(0x14e)][_0x36fb82(0x15f)]['canvas']);this[_0x36fb82(0x134)](_0x332a03);}[_0xe350a3(0x14c)](_0x2f674a){var _0xe513ca={_0x23d024:0x13c,_0xa81574:0x162,_0x4af619:0x15f,_0x2082a3:0x127},_0x41518a=_0xe350a3;const _0x35b352=mars3d__namespace['LngLatPoint'][_0x41518a(_0xe513ca._0x23d024)](_0x2f674a);if(!_0x35b352||!this['_bounds'])return{};if(_0x35b352['lng']<this['_bounds']['xmin']||_0x35b352['lng']>this['_bounds']['xmax']||_0x35b352['lat']<this['_bounds']['ymin']||_0x35b352['lat']>this['_bounds']['ymax'])return{};const _0x2ab77a=(_0x35b352[_0x41518a(_0xe513ca._0xa81574)]-this['_bounds']['xmin'])/(this[_0x41518a(0x19f)]['xmax']-this['_bounds']['xmin'])*this['_canvasWidth'],_0x4d526c=(this['_bounds']['ymax']-_0x35b352['lat'])/(this['_bounds']['ymax']-this['_bounds']['ymin'])*this['_canvasHeight'],_0x5cdb53=this['_heat']['getValueAt']({'x':_0x2ab77a,'y':_0x4d526c}),_0x4d5d8e=this['_heat'][_0x41518a(_0xe513ca._0x4af619)]['ctx'][_0x41518a(_0xe513ca._0x2082a3)](_0x2ab77a-0x1,_0x4d526c-0x1,0x1,0x1)[_0x41518a(0x1a4)];return{'x':_0x2ab77a,'y':_0x4d526c,'value':_0x5cdb53,'color':'rgba('+_0x4d5d8e[0x0]+','+_0x4d5d8e[0x1]+','+_0x4d5d8e[0x2]+','+_0x4d5d8e[0x3]+')'};}['_updateGraphic'](_0x301807){var _0x5e4ec3={_0x5781a3:0x1ac,_0xd99904:0x18f,_0x8dd2b2:0x173,_0x14ab4e:0x18f},_0x19a2eb=_0xe350a3;if(this['style']['type']==='image')this['_layer']['setOptions']({'url':_0x301807,'rectangle':this['_rectangle'],'opacity':this['style']['opacity']});else this[_0x19a2eb(_0x5e4ec3._0x5781a3)][_0x19a2eb(0x187)]?this['_graphic']&&this[_0x19a2eb(_0x5e4ec3._0xd99904)]['rectangle'][_0x19a2eb(0x180)](this['_rectangle'])?(this['_graphic']['uniforms'][_0x19a2eb(_0x5e4ec3._0x8dd2b2)]=_0x301807,this['_graphic']['uniforms'][_0x19a2eb(0x12d)]=this[_0x19a2eb(0x17e)](),this['_graphic2']&&(this['_graphic2']['uniforms']['image']=_0x301807,this['_graphic2']['uniforms'][_0x19a2eb(0x12d)]=this[_0x19a2eb(0x18f)]['uniforms']['bumpMap'])):this['_createArcGraphic'](_0x301807):this['_graphic']&&this[_0x19a2eb(_0x5e4ec3._0x14ab4e)]['rectangle']['equals'](this['_rectangle'])?this[_0x19a2eb(_0x5e4ec3._0x14ab4e)]['uniforms']['image']=_0x301807:this['_createGraphic'](_0x301807);}[_0xe350a3(0x1b1)](_0x8c61dd){var _0x31a33c={_0x43ae40:0x12c,_0x147350:0x161,_0x295c50:0x159,_0x2a15a9:0x1a2,_0x418080:0x1ab,_0x66efd0:0x120,_0x46c1e1:0x19e,_0x485711:0x18f},_0x1a4336=_0xe350a3;this[_0x1a4336(_0x31a33c._0x43ae40)]();const _0x11bb55={...this['options'],'private':!![],'flyTo':![],'rectangle':this[_0x1a4336(0x139)],'appearance':new Cesium[(_0x1a4336(_0x31a33c._0x147350))]({'material':mars3d__namespace['MaterialUtil']['createMaterial'](mars3d__namespace[_0x1a4336(_0x31a33c._0x295c50)]['Image2'],{'image':_0x8c61dd}),'flat':!![]})};delete _0x11bb55[_0x1a4336(_0x31a33c._0x2a15a9)],this['_graphic']=new mars3d__namespace[(_0x1a4336(_0x31a33c._0x418080))][(_0x1a4336(_0x31a33c._0x66efd0))](_0x11bb55),this['_layer'][_0x1a4336(_0x31a33c._0x46c1e1)](this[_0x1a4336(_0x31a33c._0x485711)]);}['_createArcGraphic'](_0x1ae76a){var _0x51466b={_0x4db353:0x176,_0x439f70:0x154,_0x3f1220:0x123,_0x2a35c7:0x123,_0x4b7f73:0x160,_0x9ed2bd:0x14f,_0x10e5ef:0x120,_0x5bb9e2:0x17a,_0x4bad43:0x139},_0x5a4a1f=_0xe350a3;this['clear']();const _0x260df7=Cesium['RenderState']['fromCache']({'cull':{'enabled':!![]},'depthTest':{'enabled':!![]},'stencilTest':{'enabled':!![],'frontFunction':Cesium['StencilFunction']['ALWAYS'],'frontOperation':{'fail':Cesium[_0x5a4a1f(0x123)][_0x5a4a1f(0x154)],'zFail':Cesium['StencilOperation']['KEEP'],'zPass':Cesium['StencilOperation'][_0x5a4a1f(0x115)]},'backFunction':Cesium[_0x5a4a1f(_0x51466b._0x4db353)]['ALWAYS'],'backOperation':{'fail':Cesium['StencilOperation'][_0x5a4a1f(_0x51466b._0x439f70)],'zFail':Cesium[_0x5a4a1f(_0x51466b._0x3f1220)]['KEEP'],'zPass':Cesium[_0x5a4a1f(_0x51466b._0x2a35c7)]['REPLACE']},'reference':0x2,'mask':0x2},'blending':Cesium['BlendingState']['ALPHA_BLEND']}),_0x57dd23=Math[_0x5a4a1f(_0x51466b._0x4b7f73)](this[_0x5a4a1f(0x1ac)][_0x5a4a1f(_0x51466b._0x9ed2bd)]??this['_bounds']['radius']*0.05)+0.1;this['style']['diffHeight']&&delete this[_0x5a4a1f(0x1ac)]['diffHeight'];this[_0x5a4a1f(0x1ac)]['granularity']=this['_bounds']['granularity']/(this['style'][_0x5a4a1f(0x128)],0x64);const _0x33d381=new Cesium['Material']({'fabric':{'uniforms':{'image':_0x1ae76a,'repeat':new Cesium[(_0x5a4a1f(0x15a))](0x1,0x1),'color':new Cesium[(_0x5a4a1f(0x12e))](0x1,0x1,0x1,0x0),'bumpMap':this['_getArcHeatCanvas']()},'source':HeatMaterial},'translucent':!![]}),_0x4b8b98=this['style']['arcDirection']||0x1;this['_graphic']=new mars3d__namespace['graphic'][(_0x5a4a1f(_0x51466b._0x10e5ef))]({...this[_0x5a4a1f(_0x51466b._0x5bb9e2)],'private':!![],'flyTo':![],'rectangle':this['_rectangle'],'appearance':new Cesium['EllipsoidSurfaceAppearance']({'flat':!![],'aboveGround':!![],'renderState':_0x260df7,'material':_0x33d381,'vertexShaderSource':getVertexShaderSource(_0x57dd23*_0x4b8b98)})}),this['_layer']['addGraphic'](this[_0x5a4a1f(0x18f)]),this[_0x5a4a1f(0x1ac)]['arcDirection']===0x0&&(this['_graphic2']=new mars3d__namespace['graphic']['RectanglePrimitive']({...this[_0x5a4a1f(0x17a)],'private':!![],'flyTo':![],'rectangle':this[_0x5a4a1f(_0x51466b._0x4bad43)],'appearance':new Cesium[(_0x5a4a1f(0x161))]({'flat':!![],'aboveGround':!![],'renderState':_0x260df7,'material':_0x33d381,'vertexShaderSource':getVertexShaderSource(-_0x57dd23)})}),this['_layer']['addGraphic'](this['_graphic2']));}}mars3d__namespace[_0xe350a3(0x19a)]['register']('heat',HeatLayer),mars3d__namespace['layer']['HeatLayer']=HeatLayer,mars3d__namespace[_0xe350a3(0x169)]=h337;function getVertexShaderSource(_0x46dff5){return'in\x20vec3\x20position3DHigh;\x0a\x20\x20in\x20vec3\x20position3DLow;\x0a\x20\x20in\x20vec2\x20st;\x0a\x20\x20in\x20float\x20batchId;\x0a\x20\x20uniform\x20sampler2D\x20bumpMap_3;\x0a\x20\x20out\x20vec3\x20v_positionMC;\x0a\x20\x20out\x20vec3\x20v_positionEC;\x0a\x20\x20out\x20vec2\x20v_st;\x0a\x0a\x20\x20void\x20main()\x0a\x20\x20{\x0a\x20\x20\x20\x20vec4\x20p\x20=\x20czm_computePosition();\x0a\x20\x20\x20\x20v_positionMC\x20=\x20position3DHigh\x20+\x20position3DLow;\x0a\x20\x20\x20\x20v_positionEC\x20=\x20(czm_modelViewRelativeToEye\x20*\x20p).xyz;\x0a\x20\x20\x20\x20v_st\x20=\x20st;\x0a\x20\x20\x20\x20vec4\x20color\x20=\x20texture(bumpMap_3,\x20v_st);\x0a\x20\x20\x20\x20float\x20centerBump\x20=\x20distance(vec3(0.0),color.rgb);\x0a\x20\x20\x20\x20vec3\x20upDir\x20=\x20normalize(v_positionMC.xyz);\x0a\x20\x20\x20\x20vec3\x20disPos\x20=\x20upDir\x20*\x20centerBump\x20*\x20'+_0x46dff5+';\x0a\x20\x20\x20\x20p\x20+=vec4(disPos,0.0);\x0a\x20\x20\x20\x20gl_Position\x20=\x20czm_modelViewProjectionRelativeToEye\x20*\x20p;\x0a\x20\x20}\x0a';}function getCanvas(_0x1a39a8){return _0x1a39a8=mars3d__namespace['DomUtil']['copyCanvas'](_0x1a39a8),_0x1a39a8;}function _0x34ac(){var _0x4df863=['fillStyle','addGraphic','_bounds','shadowCtx','height','positions','push','data','defaultGradient','setPositions','beginPath','flyTo','_colorize','addColorStop','graphic','style','_min','ImageLayer','className','min','_createGraphic','replace','_container','_drawAlpha','setCoordinator','body','canvas','REPLACE','1004215SwlZen','fill','extremachange','_setOptionsHook','minOpacity','_map','cssText','useGradientOpacity','ymin','clientWidth','RectanglePrimitive','14400650YpjSxs','_height','StencilOperation','diffY','pickEllipsoid','_valueField','getImageData','splitNum','blur','keys','max','clear','bumpMap','Color','removeData','setData','renderall','setDataMin','removeGraphic','_updateGraphic','defaultRadius','rgb(255,255,255)','_max','backgroundColor','_rectangle','value','create','parse','2411682orCMRG','fromDegrees','plugins','1NDFAXM','emit','fillRect','points','cameraMoveEnd','_config','_store','lat','HeatLayer','_renderBoundaries','addPosition','getDataURL','getPointData','_updateGradient','_heat','diffHeight','16WQUiBM','merge','px;display:none;','_updatePositionsHook','KEEP','xmin','yellow','7501896wCLbQS','heatStyle','MaterialType','Cartesian2','clearRect','uniform\x20sampler2D\x20image;\x0a\x0aczm_material\x20czm_getMaterial(czm_materialInput\x20materialInput)\x20{\x0a\x20\x20czm_material\x20material\x20=\x20czm_getDefaultMaterial(materialInput);\x0a\x20\x20vec2\x20st\x20=\x20materialInput.st;\x0a\x20\x20vec4\x20colorImage\x20=\x20texture(image,\x20st);\x0a\x20\x20if(colorImage.rgb\x20==\x20vec3(1.0)\x20||\x20colorImage.rgb\x20==\x20vec3(0.0))\x20{\x0a\x20\x20\x20\x20discard;\x0a\x20\x20}\x0a\x20\x20material.diffuse\x20=\x20colorImage.rgb;\x0a\x20\x20material.alpha\x20=\x20colorImage.a;\x0a\x20\x20return\x20material;\x0a}\x0a','length','diffX','_renderer','floor','EllipsoidSurfaceAppearance','lng','_coordinator','1130762hbQNOj','getValueAt','_width','_clear','gradientArc','h337','_templates','getData','4GQrgcx','_positions','cStore','heatmap-canvas','camera','width:','shadowCanvas','image','canvasSize','forEach','StencilFunction','defined','width','_xField','options','scene','clientHeight','container','_getArcHeatCanvas','rgb(140,140,140)','equals','position','createElement','position:absolute;left:0;top:0;','8276211toEyeH','_getInternalData','Util','arc','removeLayer','_radi','_layer','radius','undefined','type','239358KBiTsj','_graphic','rectangle','configure','round','globe','LngLatPoint','defaultMaxOpacity','exports','arcRadiusScale','__esModule','renderer','LayerUtil','prototype','ymax'];_0x34ac=function(){return _0x4df863;};return _0x34ac();}function getSurfaceDistance(_0x2ca4dd){var _0x2780df={_0x57d0e2:0x193},_0x1a9e79=_0xe350a3;const _0x449d50=_0x2ca4dd[_0x1a9e79(_0x2780df._0x57d0e2)]['ellipsoid'],_0x2fadfd=_0x2ca4dd['canvas'],_0x22ef6f=_0x2fadfd['clientWidth']/0x2,_0xfbb0f6=_0x2fadfd['clientHeight']/0x2,_0x42d16e=_0x2fadfd['clientWidth']/0x64,_0x5d37c9=new Cesium['Cartesian2'](_0x22ef6f,_0xfbb0f6);let _0x113ea8,_0x5ddcbb;_0x5d37c9['x']=_0x22ef6f;for(let _0x3cec52=0x0;_0x3cec52<0x64;_0x3cec52++){_0x5d37c9['y']=_0x42d16e*_0x3cec52;const _0x1ae00d=_0x2ca4dd[_0x1a9e79(0x170)][_0x1a9e79(0x125)](_0x5d37c9,_0x449d50);if(_0x1ae00d){_0x113ea8=_0x1ae00d;break;}}for(let _0x5ab5e7=0x64;_0x5ab5e7>0x0;_0x5ab5e7--){_0x5d37c9['y']=_0x42d16e*_0x5ab5e7;const _0x3b9dce=_0x2ca4dd['camera']['pickEllipsoid'](_0x5d37c9,_0x449d50);if(_0x3b9dce){_0x5ddcbb=_0x3b9dce;break;}}return _0x113ea8&&_0x5ddcbb?mars3d__namespace['MeasureUtil']['getSurfaceDistance']([_0x113ea8,_0x5ddcbb]):0x0;}function _0x1bce(_0x3ede9f,_0x19c29a){var _0x34acf4=_0x34ac();return _0x1bce=function(_0x1bced2,_0x53d884){_0x1bced2=_0x1bced2-0x112;var _0x58c78a=_0x34acf4[_0x1bced2];return _0x58c78a;},_0x1bce(_0x3ede9f,_0x19c29a);}mars3d__namespace['Log']['logInfo']('mars3d-heatmap插件\x20注册成功'),exports[_0xe350a3(0x148)]=HeatLayer,Object['defineProperty'](exports,_0xe350a3(0x198),{'value':!![]});
}));
