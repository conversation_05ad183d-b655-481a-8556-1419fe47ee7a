/**
 * Mars3D平台插件,支持气象 风向图 功能插件  mars3d-wind
 *
 * 版本信息：v3.9.12
 * 编译日期：2025-06-24 17:12
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：成都潘朵拉科技有限公司 ，2025-06-24
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-wind"] = {}, global.mars3d));
})(this, (function (exports, mars3d) { 
'use strict';const _0x221f23=_0x2ad5;(function(_0x4d15d6,_0x2301fd){const _0x2c8fad=_0x2ad5,_0x4f8894=_0x4d15d6();while(!![]){try{const _0x4cb380=parseInt(_0x2c8fad(0x233))/0x1+parseInt(_0x2c8fad(0x1a6))/0x2+parseInt(_0x2c8fad(0x232))/0x3+-parseInt(_0x2c8fad(0x199))/0x4*(-parseInt(_0x2c8fad(0x18a))/0x5)+-parseInt(_0x2c8fad(0x191))/0x6*(-parseInt(_0x2c8fad(0x1ec))/0x7)+parseInt(_0x2c8fad(0x1f7))/0x8*(-parseInt(_0x2c8fad(0x1ed))/0x9)+-parseInt(_0x2c8fad(0x1c4))/0xa;if(_0x4cb380===_0x2301fd)break;else _0x4f8894['push'](_0x4f8894['shift']());}catch(_0x31cbda){_0x4f8894['push'](_0x4f8894['shift']());}}}(_0x26fb,0x2b526));function _interopNamespace(_0x3f49c3){if(_0x3f49c3&&_0x3f49c3['__esModule'])return _0x3f49c3;var _0x2393b8=Object['create'](null);return _0x3f49c3&&Object['keys'](_0x3f49c3)['forEach'](function(_0x307dbe){if(_0x307dbe!=='default'){var _0x1bcfdf=Object['getOwnPropertyDescriptor'](_0x3f49c3,_0x307dbe);Object['defineProperty'](_0x2393b8,_0x307dbe,_0x1bcfdf['get']?_0x1bcfdf:{'enumerable':!![],'get':function(){return _0x3f49c3[_0x307dbe];}});}}),_0x2393b8['default']=_0x3f49c3,_0x2393b8;}var mars3d__namespace=_interopNamespace(mars3d);const Cesium$2=mars3d__namespace[_0x221f23(0x1da)];function getU(_0x5ed9e2,_0x2b21e2){const _0x3f5b9c=_0x5ed9e2*Math['cos'](Cesium$2['Math']['toRadians'](_0x2b21e2));return _0x3f5b9c;}function _0x26fb(){const _0xcb951c=['bind','40dvmgxS','frameState.commandList\x20is\x20undefined','setOptions','pointerEvents','pointer-events','canvas','forEach','options','hidden','_updateIng2','attributeLocations','positionWC','vmax','segments','strokeStyle','Math','EllipsoidalOccluder','width','particlesSpeed','array','particlesNumber','CanvasWindLayer','fixedHeight','_addedHook','#version\x20300\x20es\x0a\x0a//\x20the\x20size\x20of\x20UV\x20textures:\x20width\x20=\x20lon,\x20height\x20=\x20lat\x0auniform\x20sampler2D\x20U;\x20//\x20eastward\x20wind\x0auniform\x20sampler2D\x20V;\x20//\x20northward\x20wind\x0auniform\x20sampler2D\x20currentParticlesPosition;\x20//\x20(lon,\x20lat,\x20lev)\x0a\x0auniform\x20vec2\x20uRange;\x20//\x20(min,\x20max)\x0auniform\x20vec2\x20vRange;\x20//\x20(min,\x20max)\x0auniform\x20vec2\x20speedRange;\x20//\x20(min,\x20max)\x0auniform\x20vec2\x20dimension;\x20//\x20(lon,\x20lat)\x0auniform\x20vec2\x20minimum;\x20//\x20minimum\x20of\x20each\x20dimension\x0auniform\x20vec2\x20maximum;\x20//\x20maximum\x20of\x20each\x20dimension\x0a\x0auniform\x20float\x20speedScaleFactor;\x0auniform\x20float\x20frameRateAdjustment;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0avec2\x20getInterval(vec2\x20maximum,\x20vec2\x20minimum,\x20vec2\x20dimension)\x20{\x0a\x20\x20return\x20(maximum\x20-\x20minimum)\x20/\x20(dimension\x20-\x201.0f);\x0a}\x0a\x0avec2\x20mapPositionToNormalizedIndex2D(vec2\x20lonLat)\x20{\x0a\x20\x20\x20\x20//\x20ensure\x20the\x20range\x20of\x20longitude\x20and\x20latitude\x0a\x20\x20lonLat.x\x20=\x20clamp(lonLat.x,\x20minimum.x,\x20maximum.x);\x0a\x20\x20lonLat.y\x20=\x20clamp(lonLat.y,\x20minimum.y,\x20maximum.y);\x0a\x0a\x20\x20vec2\x20interval\x20=\x20getInterval(maximum,\x20minimum,\x20dimension);\x0a\x0a\x20\x20vec2\x20index2D\x20=\x20vec2(0.0f);\x0a\x20\x20index2D.x\x20=\x20(lonLat.x\x20-\x20minimum.x)\x20/\x20interval.x;\x0a\x20\x20index2D.y\x20=\x20(lonLat.y\x20-\x20minimum.y)\x20/\x20interval.y;\x0a\x0a\x20\x20vec2\x20normalizedIndex2D\x20=\x20vec2(index2D.x\x20/\x20dimension.x,\x20index2D.y\x20/\x20dimension.y);\x0a\x20\x20return\x20normalizedIndex2D;\x0a}\x0a\x0afloat\x20getWindComponent(sampler2D\x20componentTexture,\x20vec2\x20lonLat)\x20{\x0a\x20\x20vec2\x20normalizedIndex2D\x20=\x20mapPositionToNormalizedIndex2D(lonLat);\x0a\x20\x20float\x20result\x20=\x20texture(componentTexture,\x20normalizedIndex2D).r;\x0a\x20\x20return\x20result;\x0a}\x0a\x0avec2\x20getWindComponents(vec2\x20lonLat)\x20{\x0a\x20\x20vec2\x20normalizedIndex2D\x20=\x20mapPositionToNormalizedIndex2D(lonLat);\x0a\x20\x20float\x20u\x20=\x20texture(U,\x20normalizedIndex2D).r;\x0a\x20\x20float\x20v\x20=\x20texture(V,\x20normalizedIndex2D).r;\x0a\x20\x20return\x20vec2(u,\x20v);\x0a}\x0a\x0avec2\x20bilinearInterpolation(vec2\x20lonLat)\x20{\x0a\x20\x20float\x20lon\x20=\x20lonLat.x;\x0a\x20\x20float\x20lat\x20=\x20lonLat.y;\x0a\x0a\x20\x20vec2\x20interval\x20=\x20getInterval(maximum,\x20minimum,\x20dimension);\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20grid\x20cell\x20coordinates\x0a\x20\x20float\x20lon0\x20=\x20floor(lon\x20/\x20interval.x)\x20*\x20interval.x;\x0a\x20\x20float\x20lon1\x20=\x20lon0\x20+\x20interval.x;\x0a\x20\x20float\x20lat0\x20=\x20floor(lat\x20/\x20interval.y)\x20*\x20interval.y;\x0a\x20\x20float\x20lat1\x20=\x20lat0\x20+\x20interval.y;\x0a\x0a\x20\x20\x20\x20//\x20Get\x20wind\x20vectors\x20at\x20four\x20corners\x0a\x20\x20vec2\x20v00\x20=\x20getWindComponents(vec2(lon0,\x20lat0));\x0a\x20\x20vec2\x20v10\x20=\x20getWindComponents(vec2(lon1,\x20lat0));\x0a\x20\x20vec2\x20v01\x20=\x20getWindComponents(vec2(lon0,\x20lat1));\x0a\x20\x20vec2\x20v11\x20=\x20getWindComponents(vec2(lon1,\x20lat1));\x0a\x0a\x20\x20\x20\x20//\x20Check\x20if\x20all\x20wind\x20vectors\x20are\x20zero\x0a\x20\x20if(length(v00)\x20==\x200.0f\x20&&\x20length(v10)\x20==\x200.0f\x20&&\x20length(v01)\x20==\x200.0f\x20&&\x20length(v11)\x20==\x200.0f)\x20{\x0a\x20\x20\x20\x20return\x20vec2(0.0f,\x200.0f);\x0a\x20\x20}\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20interpolation\x20weights\x0a\x20\x20float\x20s\x20=\x20(lon\x20-\x20lon0)\x20/\x20interval.x;\x0a\x20\x20float\x20t\x20=\x20(lat\x20-\x20lat0)\x20/\x20interval.y;\x0a\x0a\x20\x20\x20\x20//\x20Perform\x20bilinear\x20interpolation\x20on\x20vector\x20components\x0a\x20\x20vec2\x20v0\x20=\x20mix(v00,\x20v10,\x20s);\x0a\x20\x20vec2\x20v1\x20=\x20mix(v01,\x20v11,\x20s);\x0a\x20\x20return\x20mix(v0,\x20v1,\x20t);\x0a}\x0a\x0avec2\x20lengthOfLonLat(vec2\x20lonLat)\x20{\x0a\x20\x20\x20\x20//\x20unit\x20conversion:\x20meters\x20->\x20longitude\x20latitude\x20degrees\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Geographic_coordinate_system#Length_of_a_degree\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20the\x20length\x20of\x20a\x20degree\x20of\x20latitude\x20and\x20longitude\x20in\x20meters\x0a\x20\x20float\x20latitude\x20=\x20radians(lonLat.y);\x0a\x0a\x20\x20float\x20term1\x20=\x20111132.92f;\x0a\x20\x20float\x20term2\x20=\x20559.82f\x20*\x20cos(2.0f\x20*\x20latitude);\x0a\x20\x20float\x20term3\x20=\x201.175f\x20*\x20cos(4.0f\x20*\x20latitude);\x0a\x20\x20float\x20term4\x20=\x200.0023f\x20*\x20cos(6.0f\x20*\x20latitude);\x0a\x20\x20float\x20latLength\x20=\x20term1\x20-\x20term2\x20+\x20term3\x20-\x20term4;\x0a\x0a\x20\x20float\x20term5\x20=\x20111412.84f\x20*\x20cos(latitude);\x0a\x20\x20float\x20term6\x20=\x2093.5f\x20*\x20cos(3.0f\x20*\x20latitude);\x0a\x20\x20float\x20term7\x20=\x200.118f\x20*\x20cos(5.0f\x20*\x20latitude);\x0a\x20\x20float\x20longLength\x20=\x20term5\x20-\x20term6\x20+\x20term7;\x0a\x0a\x20\x20return\x20vec2(longLength,\x20latLength);\x0a}\x0a\x0avec2\x20convertSpeedUnitToLonLat(vec2\x20lonLat,\x20vec2\x20speed)\x20{\x0a\x20\x20vec2\x20lonLatLength\x20=\x20lengthOfLonLat(lonLat);\x0a\x20\x20float\x20u\x20=\x20speed.x\x20/\x20lonLatLength.x;\x0a\x20\x20float\x20v\x20=\x20speed.y\x20/\x20lonLatLength.y;\x0a\x20\x20vec2\x20windVectorInLonLat\x20=\x20vec2(u,\x20v);\x0a\x0a\x20\x20return\x20windVectorInLonLat;\x0a}\x0a\x0avec2\x20calculateSpeedByRungeKutta2(vec2\x20lonLat)\x20{\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Runge%E2%80%93Kutta_methods#Second-order_methods_with_two_stages\x20for\x20detail\x0a\x20\x20const\x20float\x20h\x20=\x200.5f;\x0a\x0a\x20\x20vec2\x20y_n\x20=\x20lonLat;\x0a\x20\x20vec2\x20f_n\x20=\x20bilinearInterpolation(lonLat);\x0a\x20\x20vec2\x20midpoint\x20=\x20y_n\x20+\x200.5f\x20*\x20h\x20*\x20convertSpeedUnitToLonLat(y_n,\x20f_n)\x20*\x20speedScaleFactor;\x0a\x20\x20vec2\x20speed\x20=\x20h\x20*\x20bilinearInterpolation(midpoint)\x20*\x20speedScaleFactor;\x0a\x0a\x20\x20return\x20speed;\x0a}\x0a\x0avec2\x20calculateWindNorm(vec2\x20speed)\x20{\x0a\x20\x20float\x20speedLength\x20=\x20length(speed.xy);\x0a\x20\x20if(speedLength\x20==\x200.0f)\x20{\x0a\x20\x20\x20\x20return\x20vec2(0.0f);\x0a\x20\x20}\x0a\x0a\x20\x20\x20\x20//\x20Clamp\x20speedLength\x20to\x20range\x0a\x20\x20float\x20clampedSpeed\x20=\x20clamp(speedLength,\x20speedRange.x,\x20speedRange.y);\x0a\x20\x20float\x20normalizedSpeed\x20=\x20(clampedSpeed\x20-\x20speedRange.x)\x20/\x20(speedRange.y\x20-\x20speedRange.x);\x0a\x20\x20return\x20vec2(speedLength,\x20normalizedSpeed);\x0a}\x0a\x0aout\x20vec4\x20fragColor;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20vec2\x20lonLat\x20=\x20texture(currentParticlesPosition,\x20v_textureCoordinates).rg;\x0a\x20\x20vec2\x20speedOrigin\x20=\x20bilinearInterpolation(lonLat);\x0a\x20\x20vec2\x20speed\x20=\x20calculateSpeedByRungeKutta2(lonLat)\x20*\x20frameRateAdjustment;\x0a\x20\x20vec2\x20speedInLonLat\x20=\x20convertSpeedUnitToLonLat(lonLat,\x20speed);\x0a\x0a\x20\x20fragColor\x20=\x20vec4(speedInLonLat,\x20calculateWindNorm(speedOrigin));\x0a}\x0a','stroke','Cartesian2','_colorRamp','frameTime','clientHeight','clientWidth','updateViewerParameters','frameRateMonitor','createSegmentsGeometry','MIN_VALUE','latRange','removeEventListener','_calc_speedRate','windTextures','camera','createRenderingFramebuffers','morphComplete','lat','initWorker','defineProperty','_map','EventType','east','ymin','commandToExecute','windData','getParticles','dynamic','context','CanvasWindField','BaseLayer','nextParticlesPosition','particleSystem','postProcessingPosition','143838VoSgUA','283386jPCnvf','isDestroy','domain','sceneMode','commandType','call','vdata','updateOptions','rows','logInfo','mouseMove','_animateFrame','vmin','WindUtil','bounds','onParticlesTextureSizeChange','_updateIng','north','worker','isArray','umax','postMessage','colors','min','udata','fromGeometry','flipY','_removedHook','createPrimitives','_showHook','drawingBufferHeight','isInExtent','Rectangle','getDefaultRenderState','frameRate','viewerParameters','push','speedRate','lng','layer','max\x20is\x20undefined,\x20calculate\x20max','lonRange','pickEllipsoid','xmax','framebuffer','geometry','_onMouseDownEvent','UNSIGNED_INT','speed','setData','80rslbQv','WindLayer','getCalculateSpeedShader','framebuffers','execute','update','_onMapWhellEvent','4506siPHFb','tlng','computing','abs','floor','globalCompositeOperation','pixelSize','vertexArray','8916VuNWfH','_setOptionsHook','_randomParticle','resize','calculateSpeed','#version\x20300\x20es\x0aprecision\x20highp\x20float;\x0a\x0auniform\x20sampler2D\x20currentParticlesPosition;\x0auniform\x20sampler2D\x20particlesSpeed;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0aout\x20vec4\x20fragColor;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20获取当前粒子的位置\x0a\x20\x20vec2\x20currentPos\x20=\x20texture(currentParticlesPosition,\x20v_textureCoordinates).rg;\x0a\x20\x20\x20\x20//\x20获取粒子的速度\x0a\x20\x20vec2\x20speed\x20=\x20texture(particlesSpeed,\x20v_textureCoordinates).rg;\x0a\x20\x20\x20\x20//\x20计算下一个位置\x0a\x20\x20vec2\x20nextPos\x20=\x20currentPos\x20+\x20speed;\x0a\x0a\x20\x20\x20\x20//\x20将新的位置写入\x20fragColor\x0a\x20\x20fragColor\x20=\x20vec4(nextPos,\x200.0f,\x201.0f);\x0a}\x0a','fragmentShaderSource','#version\x20300\x20es\x0aprecision\x20highp\x20float;\x0a\x0auniform\x20sampler2D\x20nextParticlesPosition;\x0auniform\x20sampler2D\x20particlesSpeed;\x20//\x20(u,\x20v,\x20norm)\x0a\x0a//\x20range\x20(min,\x20max)\x0auniform\x20vec2\x20lonRange;\x0auniform\x20vec2\x20latRange;\x0a\x0a//\x20range\x20(min,\x20max)\x0auniform\x20vec2\x20dataLonRange;\x0auniform\x20vec2\x20dataLatRange;\x0a\x0auniform\x20float\x20randomCoefficient;\x0auniform\x20float\x20dropRate;\x0auniform\x20float\x20dropRateBump;\x0a\x0a//\x20添加新的\x20uniform\x20变量\x0auniform\x20bool\x20useViewerBounds;\x0a\x0ain\x20vec2\x20v_textureCoordinates;\x0a\x0a//\x20pseudo-random\x20generator\x0aconst\x20vec3\x20randomConstants\x20=\x20vec3(12.9898f,\x2078.233f,\x204375.85453f);\x0aconst\x20vec2\x20normalRange\x20=\x20vec2(0.0f,\x201.0f);\x0afloat\x20rand(vec2\x20seed,\x20vec2\x20range)\x20{\x0a\x20\x20vec2\x20randomSeed\x20=\x20randomCoefficient\x20*\x20seed;\x0a\x20\x20float\x20temp\x20=\x20dot(randomConstants.xy,\x20randomSeed);\x0a\x20\x20temp\x20=\x20fract(sin(temp)\x20*\x20(randomConstants.z\x20+\x20temp));\x0a\x20\x20return\x20temp\x20*\x20(range.y\x20-\x20range.x)\x20+\x20range.x;\x0a}\x0a\x0avec2\x20generateRandomParticle(vec2\x20seed)\x20{\x0a\x20\x20vec2\x20range;\x0a\x20\x20float\x20randomLon,\x20randomLat;\x0a\x0a\x20\x20if(useViewerBounds)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20在当前视域范围内生成粒子\x0a\x20\x20\x20\x20randomLon\x20=\x20rand(seed,\x20lonRange);\x0a\x20\x20\x20\x20randomLat\x20=\x20rand(-seed,\x20latRange);\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20在数据范围内生成粒子\x0a\x20\x20\x20\x20randomLon\x20=\x20rand(seed,\x20dataLonRange);\x0a\x20\x20\x20\x20randomLat\x20=\x20rand(-seed,\x20dataLatRange);\x0a\x20\x20}\x0a\x0a\x20\x20return\x20vec2(randomLon,\x20randomLat);\x0a}\x0a\x0abool\x20particleOutbound(vec2\x20particle)\x20{\x0a\x20\x20return\x20particle.y\x20<\x20dataLatRange.x\x20||\x20particle.y\x20>\x20dataLatRange.y\x20||\x20particle.x\x20<\x20dataLonRange.x\x20||\x20particle.x\x20>\x20dataLonRange.y;\x0a}\x0a\x0aout\x20vec4\x20fragColor;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20vec2\x20nextParticle\x20=\x20texture(nextParticlesPosition,\x20v_textureCoordinates).rg;\x0a\x20\x20vec4\x20nextSpeed\x20=\x20texture(particlesSpeed,\x20v_textureCoordinates);\x0a\x20\x20float\x20speedNorm\x20=\x20nextSpeed.a;\x0a\x20\x20float\x20particleDropRate\x20=\x20dropRate\x20+\x20dropRateBump\x20*\x20speedNorm;\x0a\x0a\x20\x20vec2\x20seed1\x20=\x20nextParticle.xy\x20+\x20v_textureCoordinates;\x0a\x20\x20vec2\x20seed2\x20=\x20nextSpeed.rg\x20+\x20v_textureCoordinates;\x0a\x20\x20vec2\x20randomParticle\x20=\x20generateRandomParticle(seed1);\x0a\x20\x20float\x20randomNumber\x20=\x20rand(seed2,\x20normalRange);\x0a\x0a\x20\x20if(randomNumber\x20<\x20particleDropRate\x20||\x20particleOutbound(nextParticle))\x20{\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(randomParticle,\x200.0f,\x201.0f);\x20//\x201.0\x20means\x20this\x20is\x20a\x20random\x20particle\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(nextParticle,\x200.0f,\x200.0f);\x0a\x20\x20}\x0a}\x0a','fromCache','lineLength','NEAREST','initFrameRate','bindEvent','465808kqQMxa','cols','warn','destroy','red','_mountedHook','LINEAR','particlesTextureSize','age','maxAge','rendering','clear','west','length','ymax','RGBA','preExecute','off','createRawRenderState','clearFramebuffers','xmin','SCENE3D','_speedRate','particlesTextures','rectangle','unbindEvent','values','colorTable','stringify','frameRateAdjustment','2356920XLhUkg','setDate','canvasContext','primitiveType','windField','getSegmentDrawFragmentShader','random','primitives','updatePosition','Unknown\x20command\x20type','getPrimitives','addEventListener','redraw','getRandomLatLng','scene','particles','_onMouseMoveEvent','createParticlesTextures','show','onColorTableChange','currentParticlesPosition','autoClear','Cesium','getUVByPoint','canvasHeight','mode','OPAQUE','FLOAT','lighter','requestRender','rawRenderState','lineWidth','grid','#version\x20300\x20es\x0aprecision\x20highp\x20float;\x0a\x0ain\x20vec4\x20speed;\x0ain\x20float\x20v_segmentPosition;\x0ain\x20vec2\x20textureCoordinate;\x0a\x0auniform\x20vec2\x20domain;\x0auniform\x20vec2\x20displayRange;\x0auniform\x20sampler2D\x20colorTable;\x0auniform\x20sampler2D\x20segmentsDepthTexture;\x0a\x0aout\x20vec4\x20fragColor;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20const\x20float\x20zero\x20=\x200.0f;\x0a\x20\x20if(speed.a\x20>\x20zero\x20&&\x20speed.b\x20>\x20displayRange.x\x20&&\x20speed.b\x20<\x20displayRange.y)\x20{\x0a\x20\x20\x20\x20float\x20speedLength\x20=\x20clamp(speed.b,\x20domain.x,\x20domain.y);\x0a\x20\x20\x20\x20float\x20normalizedSpeed\x20=\x20(speedLength\x20-\x20domain.x)\x20/\x20(domain.y\x20-\x20domain.x);\x0a\x20\x20\x20\x20vec4\x20baseColor\x20=\x20texture(colorTable,\x20vec2(normalizedSpeed,\x20zero));\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20使用更平滑的渐变效果\x0a\x20\x20\x20\x20float\x20alpha\x20=\x20smoothstep(0.0f,\x201.0f,\x20v_segmentPosition);\x0a\x20\x20\x20\x20alpha\x20=\x20pow(alpha,\x201.5f);\x20//\x20调整透明度渐变曲线\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20根据速度调整透明度\x0a\x20\x20\x20\x20float\x20speedAlpha\x20=\x20mix(0.3f,\x201.0f,\x20speed.a);\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20组合颜色和透明度\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(baseColor.rgb,\x20baseColor.a\x20*\x20alpha\x20*\x20speedAlpha);\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(zero);\x0a\x20\x20}\x0a\x0a\x20\x20float\x20segmentsDepth\x20=\x20texture(segmentsDepthTexture,\x20textureCoordinate).r;\x0a\x20\x20float\x20globeDepth\x20=\x20czm_unpackDepth(texture(czm_globeDepthTexture,\x20textureCoordinate));\x0a\x20\x20if(segmentsDepth\x20<\x20globeDepth)\x20{\x0a\x20\x20\x20\x20fragColor\x20=\x20vec4(zero);\x0a\x20\x20}\x0a}\x0a','_pointerEvents','height','displayRange','south','changed','Compute','511DJTABT','434853XMBcfY','globe','number','register','clearCommand','createRenderingTextures','fromDegrees','max','style'];_0x26fb=function(){return _0xcb951c;};return _0x26fb();}function getV(_0x2c6a5b,_0x4bd814){const _0x5eb948=_0x221f23,_0x1f016f=_0x2c6a5b*Math['sin'](Cesium$2[_0x5eb948(0x206)]['toRadians'](_0x4bd814));return _0x1f016f;}function getSpeed(_0x177539,_0x48fade){const _0x3cc85e=Math['sqrt'](Math['pow'](_0x177539,0x2)+Math['pow'](_0x48fade,0x2));return _0x3cc85e;}function getDirection(_0x5982ae,_0x12ac5b){const _0xb50484=_0x221f23;let _0x3fad69=Cesium$2[_0xb50484(0x206)]['toDegrees'](Math['atan2'](_0x12ac5b,_0x5982ae));return _0x3fad69+=_0x3fad69<0x0?0x168:0x0,_0x3fad69;}var WindUtil={'__proto__':null,'getU':getU,'getV':getV,'getSpeed':getSpeed,'getDirection':getDirection},updatePositionShader=_0x221f23(0x19e),calculateSpeedShader=_0x221f23(0x20f),postProcessingPositionFragmentShader=_0x221f23(0x1a0),renderParticlesFragmentShader=_0x221f23(0x1e5),renderParticlesVertexShader='#version\x20300\x20es\x0aprecision\x20highp\x20float;\x0a\x0ain\x20vec2\x20st;\x0ain\x20vec3\x20normal;\x0a\x0auniform\x20sampler2D\x20previousParticlesPosition;\x0auniform\x20sampler2D\x20currentParticlesPosition;\x0auniform\x20sampler2D\x20postProcessingPosition;\x0auniform\x20sampler2D\x20particlesSpeed;\x0a\x0auniform\x20float\x20frameRateAdjustment;\x0auniform\x20float\x20particleHeight;\x0auniform\x20float\x20aspect;\x0auniform\x20float\x20pixelSize;\x0auniform\x20vec2\x20lineWidth;\x0auniform\x20vec2\x20lineLength;\x0auniform\x20vec2\x20domain;\x0auniform\x20bool\x20is3D;\x0a\x0a//\x20添加输出变量传递给片元着色器\x0aout\x20vec4\x20speed;\x0aout\x20float\x20v_segmentPosition;\x0aout\x20vec2\x20textureCoordinate;\x0a\x0a//\x20添加结构体定义\x0astruct\x20adjacentPoints\x20{\x0a\x20\x20vec4\x20previous;\x0a\x20\x20vec4\x20current;\x0a\x20\x20vec4\x20next;\x0a};\x0a\x0avec3\x20convertCoordinate(vec2\x20lonLat)\x20{\x0a\x20\x20\x20\x20//\x20WGS84\x20(lon,\x20lat,\x20lev)\x20->\x20ECEF\x20(x,\x20y,\x20z)\x0a\x20\x20\x20\x20//\x20read\x20https://en.wikipedia.org/wiki/Geographic_coordinate_conversion#From_geodetic_to_ECEF_coordinates\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20WGS\x2084\x20geometric\x20constants\x0a\x20\x20float\x20a\x20=\x206378137.0f;\x20//\x20Semi-major\x20axis\x0a\x20\x20float\x20b\x20=\x206356752.3142f;\x20//\x20Semi-minor\x20axis\x0a\x20\x20float\x20e2\x20=\x206.69437999014e-3f;\x20//\x20First\x20eccentricity\x20squared\x0a\x0a\x20\x20float\x20latitude\x20=\x20radians(lonLat.y);\x0a\x20\x20float\x20longitude\x20=\x20radians(lonLat.x);\x0a\x0a\x20\x20float\x20cosLat\x20=\x20cos(latitude);\x0a\x20\x20float\x20sinLat\x20=\x20sin(latitude);\x0a\x20\x20float\x20cosLon\x20=\x20cos(longitude);\x0a\x20\x20float\x20sinLon\x20=\x20sin(longitude);\x0a\x0a\x20\x20float\x20N_Phi\x20=\x20a\x20/\x20sqrt(1.0f\x20-\x20e2\x20*\x20sinLat\x20*\x20sinLat);\x0a\x20\x20float\x20h\x20=\x20particleHeight;\x20//\x20it\x20should\x20be\x20high\x20enough\x20otherwise\x20the\x20particle\x20may\x20not\x20pass\x20the\x20terrain\x20depth\x20test\x0a\x20\x20vec3\x20cartesian\x20=\x20vec3(0.0f);\x0a\x20\x20cartesian.x\x20=\x20(N_Phi\x20+\x20h)\x20*\x20cosLat\x20*\x20cosLon;\x0a\x20\x20cartesian.y\x20=\x20(N_Phi\x20+\x20h)\x20*\x20cosLat\x20*\x20sinLon;\x0a\x20\x20cartesian.z\x20=\x20((b\x20*\x20b)\x20/\x20(a\x20*\x20a)\x20*\x20N_Phi\x20+\x20h)\x20*\x20sinLat;\x0a\x20\x20return\x20cartesian;\x0a}\x0a\x0avec4\x20calculateProjectedCoordinate(vec2\x20lonLat)\x20{\x0a\x20\x20if(is3D)\x20{\x0a\x20\x20\x20\x20vec3\x20particlePosition\x20=\x20convertCoordinate(lonLat);\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20使用\x20modelViewProjection\x20矩阵进行投影变换\x0a\x20\x20\x20\x20vec4\x20projectedPosition\x20=\x20czm_modelViewProjection\x20*\x20vec4(particlePosition,\x201.0f);\x0a\x20\x20\x20\x20return\x20projectedPosition;\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20vec3\x20position2D\x20=\x20vec3(radians(lonLat.x),\x20radians(lonLat.y),\x200.0f);\x0a\x20\x20\x20\x20return\x20czm_modelViewProjection\x20*\x20vec4(position2D,\x201.0f);\x0a\x20\x20}\x0a}\x0a\x0avec4\x20calculateOffsetOnNormalDirection(vec4\x20pointA,\x20vec4\x20pointB,\x20float\x20offsetSign,\x20float\x20widthFactor)\x20{\x0a\x20\x20vec2\x20aspectVec2\x20=\x20vec2(aspect,\x201.0f);\x0a\x20\x20vec2\x20pointA_XY\x20=\x20(pointA.xy\x20/\x20pointA.w)\x20*\x20aspectVec2;\x0a\x20\x20vec2\x20pointB_XY\x20=\x20(pointB.xy\x20/\x20pointB.w)\x20*\x20aspectVec2;\x0a\x0a\x20\x20\x20\x20//\x20计算方向向量\x0a\x20\x20vec2\x20direction\x20=\x20normalize(pointB_XY\x20-\x20pointA_XY);\x0a\x0a\x20\x20\x20\x20//\x20计算法向量\x0a\x20\x20vec2\x20normalVector\x20=\x20vec2(-direction.y,\x20direction.x);\x0a\x20\x20normalVector.x\x20=\x20normalVector.x\x20/\x20aspect;\x0a\x0a\x20\x20\x20\x20//\x20使用\x20widthFactor\x20调整宽度\x0a\x20\x20float\x20offsetLength\x20=\x20widthFactor\x20*\x20lineWidth.y;\x0a\x20\x20normalVector\x20=\x20offsetLength\x20*\x20normalVector;\x0a\x0a\x20\x20vec4\x20offset\x20=\x20vec4(offsetSign\x20*\x20normalVector,\x200.0f,\x200.0f);\x0a\x20\x20return\x20offset;\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20翻转\x20Y\x20轴坐标\x0a\x20\x20vec2\x20flippedIndex\x20=\x20vec2(st.x,\x201.0f\x20-\x20st.y);\x0a\x0a\x20\x20vec2\x20particleIndex\x20=\x20flippedIndex;\x0a\x20\x20speed\x20=\x20texture(particlesSpeed,\x20particleIndex);\x0a\x0a\x20\x20vec2\x20previousPosition\x20=\x20texture(previousParticlesPosition,\x20particleIndex).rg;\x0a\x20\x20vec2\x20currentPosition\x20=\x20texture(currentParticlesPosition,\x20particleIndex).rg;\x0a\x20\x20vec2\x20nextPosition\x20=\x20texture(postProcessingPosition,\x20particleIndex).rg;\x0a\x0a\x20\x20float\x20isAnyRandomPointUsed\x20=\x20texture(postProcessingPosition,\x20particleIndex).a\x20+\x0a\x20\x20\x20\x20texture(currentParticlesPosition,\x20particleIndex).a\x20+\x0a\x20\x20\x20\x20texture(previousParticlesPosition,\x20particleIndex).a;\x0a\x0a\x20\x20adjacentPoints\x20projectedCoordinates;\x0a\x20\x20if(isAnyRandomPointUsed\x20>\x200.0f)\x20{\x0a\x20\x20\x20\x20projectedCoordinates.previous\x20=\x20calculateProjectedCoordinate(previousPosition);\x0a\x20\x20\x20\x20projectedCoordinates.current\x20=\x20projectedCoordinates.previous;\x0a\x20\x20\x20\x20projectedCoordinates.next\x20=\x20projectedCoordinates.previous;\x0a\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20projectedCoordinates.previous\x20=\x20calculateProjectedCoordinate(previousPosition);\x0a\x20\x20\x20\x20projectedCoordinates.current\x20=\x20calculateProjectedCoordinate(currentPosition);\x0a\x20\x20\x20\x20projectedCoordinates.next\x20=\x20calculateProjectedCoordinate(nextPosition);\x0a\x20\x20}\x0a\x0a\x20\x20int\x20pointToUse\x20=\x20int(normal.x);\x0a\x20\x20float\x20offsetSign\x20=\x20normal.y;\x0a\x20\x20vec4\x20offset\x20=\x20vec4(0.0f);\x0a\x0a\x20\x20\x20\x20//\x20计算速度相关的宽度和长度因子\x0a\x20\x20float\x20speedLength\x20=\x20clamp(speed.b,\x20domain.x,\x20domain.y);\x0a\x20\x20float\x20normalizedSpeed\x20=\x20(speedLength\x20-\x20domain.x)\x20/\x20(domain.y\x20-\x20domain.x);\x0a\x0a\x20\x20\x20\x20//\x20根据速度计算宽度\x0a\x20\x20float\x20widthFactor\x20=\x20mix(lineWidth.x,\x20lineWidth.y,\x20normalizedSpeed);\x0a\x20\x20widthFactor\x20*=\x20(pointToUse\x20<\x200\x20?\x201.0f\x20:\x200.5f);\x20//\x20头部更宽，尾部更窄\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20length\x20based\x20on\x20speed\x0a\x20\x20float\x20lengthFactor\x20=\x20mix(lineLength.x,\x20lineLength.y,\x20normalizedSpeed)\x20*\x20pixelSize;\x0a\x0a\x20\x20if(pointToUse\x20==\x201)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20头部位置\x0a\x20\x20\x20\x20offset\x20=\x20pixelSize\x20*\x20calculateOffsetOnNormalDirection(projectedCoordinates.previous,\x20projectedCoordinates.current,\x20offsetSign,\x20widthFactor);\x0a\x20\x20\x20\x20gl_Position\x20=\x20projectedCoordinates.previous\x20+\x20offset;\x0a\x20\x20\x20\x20v_segmentPosition\x20=\x200.0f;\x20//\x20头部\x0a\x20\x20}\x20else\x20if(pointToUse\x20==\x20-1)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20Get\x20direction\x20and\x20normalize\x20it\x20to\x20length\x201.0\x0a\x20\x20\x20\x20vec4\x20direction\x20=\x20normalize(projectedCoordinates.next\x20-\x20projectedCoordinates.current);\x0a\x20\x20\x20\x20vec4\x20extendedPosition\x20=\x20projectedCoordinates.current\x20+\x20direction\x20*\x20lengthFactor;\x0a\x0a\x20\x20\x20\x20offset\x20=\x20pixelSize\x20*\x20calculateOffsetOnNormalDirection(projectedCoordinates.current,\x20extendedPosition,\x20offsetSign,\x20widthFactor);\x0a\x20\x20\x20\x20gl_Position\x20=\x20extendedPosition\x20+\x20offset;\x0a\x20\x20\x20\x20v_segmentPosition\x20=\x201.0f;\x20//\x20尾部\x0a\x20\x20}\x0a\x0a\x20\x20textureCoordinate\x20=\x20st;\x0a}\x0a';const {ShaderSource:ShaderSource$1}=mars3d__namespace[_0x221f23(0x1da)];class ShaderManager{static['getCalculateSpeedShader'](){return new ShaderSource$1({'sources':[calculateSpeedShader]});}static['getUpdatePositionShader'](){return new ShaderSource$1({'sources':[updatePositionShader]});}static['getSegmentDrawVertexShader'](){return new ShaderSource$1({'sources':[renderParticlesVertexShader]});}static['getSegmentDrawFragmentShader'](){return new ShaderSource$1({'sources':[renderParticlesFragmentShader]});}static['getPostProcessingPositionShader'](){return new ShaderSource$1({'sources':[postProcessingPositionFragmentShader]});}}const {BufferUsage:BufferUsage$1,ClearCommand:ClearCommand$1,Color:Color$2,ComputeCommand,DrawCommand,Geometry:Geometry$1,Matrix4,Pass:Pass$1,PrimitiveType:PrimitiveType$1,RenderState,ShaderProgram,ShaderSource,VertexArray:VertexArray$1,defined,destroyObject}=mars3d__namespace['Cesium'];function _0x2ad5(_0x4529b2,_0x9accff){const _0x26fb87=_0x26fb();return _0x2ad5=function(_0x2ad504,_0x556add){_0x2ad504=_0x2ad504-0x188;let _0xf128fa=_0x26fb87[_0x2ad504];return _0xf128fa;},_0x2ad5(_0x4529b2,_0x9accff);}class CustomPrimitive{constructor(_0xd1435f){const _0x3e87fe=_0x221f23;this['commandType']=_0xd1435f['commandType'],this[_0x3e87fe(0x260)]=_0xd1435f[_0x3e87fe(0x260)],this['attributeLocations']=_0xd1435f[_0x3e87fe(0x201)],this[_0x3e87fe(0x1c7)]=_0xd1435f[_0x3e87fe(0x1c7)],this['uniformMap']=_0xd1435f['uniformMap']||{},this['vertexShaderSource']=_0xd1435f['vertexShaderSource'],this[_0x3e87fe(0x19f)]=_0xd1435f['fragmentShaderSource'],this[_0x3e87fe(0x1e2)]=_0xd1435f['rawRenderState'],this['framebuffer']=_0xd1435f['framebuffer'],this['outputTexture']=_0xd1435f['outputTexture'],this[_0x3e87fe(0x1d9)]=_0xd1435f['autoClear']??![],this['preExecute']=_0xd1435f['preExecute'],this['show']=!![],this[_0x3e87fe(0x228)]=undefined,this[_0x3e87fe(0x1f1)]=undefined,this['isDynamic']=_0xd1435f['isDynamic']??(()=>!![]),this[_0x3e87fe(0x1d9)]&&(this['clearCommand']=new ClearCommand$1({'color':new Color$2(0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':this[_0x3e87fe(0x25f)],'pass':Pass$1[_0x3e87fe(0x1de)]}));}['createCommand'](_0x2100ce){const _0x27a16b=_0x221f23;if(this['commandType']==='Draw'){const _0x31e8e2=VertexArray$1['fromGeometry']({'context':_0x2100ce,'geometry':this['geometry'],'attributeLocations':this['attributeLocations'],'bufferUsage':BufferUsage$1['STATIC_DRAW']}),_0x5e0914=ShaderProgram[_0x27a16b(0x1a1)]({'context':_0x2100ce,'vertexShaderSource':this['vertexShaderSource'],'fragmentShaderSource':this['fragmentShaderSource'],'attributeLocations':this[_0x27a16b(0x201)]}),_0x572775=RenderState['fromCache'](this[_0x27a16b(0x1e2)]);return new DrawCommand({'owner':this,'vertexArray':_0x31e8e2,'primitiveType':this[_0x27a16b(0x1c7)],'modelMatrix':Matrix4['IDENTITY'],'renderState':_0x572775,'shaderProgram':_0x5e0914,'framebuffer':this['framebuffer'],'uniformMap':this['uniformMap'],'pass':Pass$1['OPAQUE']});}else{if(this[_0x27a16b(0x237)]===_0x27a16b(0x1eb))return new ComputeCommand({'owner':this,'fragmentShaderSource':this['fragmentShaderSource'],'uniformMap':this['uniformMap'],'outputTexture':this['outputTexture'],'persists':!![]});else throw new Error(_0x27a16b(0x1cd));}}['setGeometry'](_0xf19aad,_0x16f57b){const _0x420d19=_0x221f23;this['geometry']=_0x16f57b,defined(this['commandToExecute'])&&(this['commandToExecute'][_0x420d19(0x198)]=VertexArray$1['fromGeometry']({'context':_0xf19aad,'geometry':this['geometry'],'attributeLocations':this[_0x420d19(0x201)],'bufferUsage':BufferUsage$1['STATIC_DRAW']}));}[_0x221f23(0x18f)](_0x288cb9){const _0x1332ac=_0x221f23;if(!this['isDynamic']())return;if(!this[_0x1332ac(0x1d6)]||!defined(_0x288cb9))return;!defined(this['commandToExecute'])&&(this['commandToExecute']=this['createCommand'](_0x288cb9['context']));defined(this[_0x1332ac(0x1b6)])&&this[_0x1332ac(0x1b6)]();if(!_0x288cb9['commandList']){console[_0x1332ac(0x1a8)](_0x1332ac(0x1f8));return;}defined(this['clearCommand'])&&_0x288cb9['commandList']['push'](this['clearCommand']),defined(this[_0x1332ac(0x228)])&&_0x288cb9['commandList']['push'](this['commandToExecute']);}['isDestroyed'](){return![];}['destroy'](){if(defined(this['commandToExecute'])){var _0x18f972;(_0x18f972=this['commandToExecute']['shaderProgram'])===null||_0x18f972===void 0x0||_0x18f972['destroy'](),this['commandToExecute']['shaderProgram']=undefined;}return destroyObject(this);}}function deepMerge(_0x30098a,_0x2e2d53){const _0x439e50=_0x221f23;if(!_0x30098a)return _0x2e2d53;if(!_0x2e2d53)return _0x30098a;const _0x4b3e17={..._0x2e2d53};for(const _0x2e15e7 in _0x30098a){if(Object['prototype']['hasOwnProperty'][_0x439e50(0x238)](_0x30098a,_0x2e15e7)){const _0x5eb004=_0x30098a[_0x2e15e7],_0xa143dc=_0x2e2d53[_0x2e15e7];if(Array[_0x439e50(0x246)](_0x5eb004)){_0x4b3e17[_0x2e15e7]=_0x5eb004['slice']();continue;}if(_0x5eb004&&typeof _0x5eb004==='object'){_0x4b3e17[_0x2e15e7]=deepMerge(_0x5eb004,_0xa143dc||{});continue;}_0x5eb004!==undefined&&(_0x4b3e17[_0x2e15e7]=_0x5eb004);}}return _0x4b3e17;}const {Cartesian2:Cartesian2$1,FrameRateMonitor,PixelDatatype:PixelDatatype$1,PixelFormat:PixelFormat$1,Sampler:Sampler$1,Texture:Texture$1,TextureMagnificationFilter:TextureMagnificationFilter$1,TextureMinificationFilter:TextureMinificationFilter$1}=mars3d__namespace[_0x221f23(0x1da)];class WindParticlesComputing{constructor(_0x5096bb,_0x4534e7,_0x4dbfc9,_0x845b79,_0x16a278){const _0x4d5ebf=_0x221f23;this['context']=_0x5096bb,this[_0x4d5ebf(0x1fe)]=_0x4dbfc9,this['viewerParameters']=_0x845b79,this['windData']=_0x4534e7,this[_0x4d5ebf(0x255)]=0x3c,this['frameRateAdjustment']=0x1,this['frameRateMonitor']=new FrameRateMonitor({'scene':_0x16a278,'samplingWindow':0x1,'quietPeriod':0x0}),this[_0x4d5ebf(0x1a4)](),this['createWindTextures'](),this['createParticlesTextures'](),this['createComputingPrimitives']();}[_0x221f23(0x1a4)](){const _0x36f47d=_0x221f23,_0x4255e7=()=>{const _0x36a4ad=_0x2ad5;this[_0x36a4ad(0x217)]['lastFramesPerSecond']>0x14&&(this['frameRate']=this[_0x36a4ad(0x217)]['lastFramesPerSecond'],this['frameRateAdjustment']=0x3c/Math['max'](this[_0x36a4ad(0x255)],0x1));};_0x4255e7();const _0x49b03b=setInterval(_0x4255e7,0x3e8),_0x1f3e16=this['destroy']['bind'](this);this[_0x36f47d(0x1a9)]=()=>{clearInterval(_0x49b03b),_0x1f3e16();};}['createWindTextures'](){const _0x100729=_0x221f23,_0x465fd8={'context':this['context'],'width':this['windData']['width'],'height':this[_0x100729(0x229)]['height'],'pixelFormat':PixelFormat$1['RED'],'pixelDatatype':PixelDatatype$1[_0x100729(0x1df)],'flipY':this[_0x100729(0x1fe)]['flipY']??![],'sampler':new Sampler$1({'minificationFilter':TextureMinificationFilter$1['LINEAR'],'magnificationFilter':TextureMagnificationFilter$1['LINEAR']})};this[_0x100729(0x21d)]={'U':new Texture$1({..._0x465fd8,'source':{'arrayBufferView':new Float32Array(this[_0x100729(0x229)]['u']['array'])}}),'V':new Texture$1({..._0x465fd8,'source':{'arrayBufferView':new Float32Array(this['windData']['v'][_0x100729(0x20a)])}})};}[_0x221f23(0x1d5)](){const _0x2addc9=_0x221f23,_0x495896={'context':this['context'],'width':this['options'][_0x2addc9(0x1ad)],'height':this['options']['particlesTextureSize'],'pixelFormat':PixelFormat$1['RGBA'],'pixelDatatype':PixelDatatype$1[_0x2addc9(0x1df)],'flipY':![],'source':{'arrayBufferView':new Float32Array(this['options']['particlesTextureSize']*this['options'][_0x2addc9(0x1ad)]*0x4)['fill'](0x0)},'sampler':new Sampler$1({'minificationFilter':TextureMinificationFilter$1[_0x2addc9(0x1a3)],'magnificationFilter':TextureMagnificationFilter$1[_0x2addc9(0x1a3)]})};this['particlesTextures']={'previousParticlesPosition':new Texture$1(_0x495896),'currentParticlesPosition':new Texture$1(_0x495896),'nextParticlesPosition':new Texture$1(_0x495896),'postProcessingPosition':new Texture$1(_0x495896),'particlesSpeed':new Texture$1(_0x495896)};}['destroyParticlesTextures'](){const _0x2c4853=_0x221f23;Object['values'](this[_0x2c4853(0x1bd)])['forEach'](_0x3388a0=>_0x3388a0['destroy']());}['createComputingPrimitives'](){const _0x158c27=_0x221f23;this[_0x158c27(0x1cb)]={'calculateSpeed':new CustomPrimitive({'commandType':_0x158c27(0x1eb),'uniformMap':{'U':()=>this['windTextures']['U'],'V':()=>this[_0x158c27(0x21d)]['V'],'uRange':()=>new Cartesian2$1(this['windData']['u']['min'],this['windData']['u']['max']),'vRange':()=>new Cartesian2$1(this[_0x158c27(0x229)]['v'][_0x158c27(0x24a)],this['windData']['v'][_0x158c27(0x1f4)]),'speedRange':()=>new Cartesian2$1(this['windData']['speed']['min'],this['windData']['speed'][_0x158c27(0x1f4)]),'currentParticlesPosition':()=>this['particlesTextures'][_0x158c27(0x1d8)],'speedScaleFactor':()=>{const _0x3edadd=_0x158c27;return(this[_0x3edadd(0x256)][_0x3edadd(0x197)]+0x32)*this['options']['speedFactor'];},'frameRateAdjustment':()=>this['frameRateAdjustment'],'dimension':()=>new Cartesian2$1(this['windData']['width'],this['windData']['height']),'minimum':()=>new Cartesian2$1(this['windData'][_0x158c27(0x241)]['west'],this[_0x158c27(0x229)]['bounds'][_0x158c27(0x1e9)]),'maximum':()=>new Cartesian2$1(this['windData']['bounds']['east'],this['windData']['bounds']['north'])},'fragmentShaderSource':ShaderManager[_0x158c27(0x18c)](),'outputTexture':this['particlesTextures']['particlesSpeed'],'preExecute':()=>{const _0x81e863=_0x158c27,_0x178c8f=this[_0x81e863(0x1bd)]['previousParticlesPosition'];this['particlesTextures']['previousParticlesPosition']=this['particlesTextures'][_0x81e863(0x1d8)],this['particlesTextures']['currentParticlesPosition']=this[_0x81e863(0x1bd)][_0x81e863(0x231)],this[_0x81e863(0x1bd)]['postProcessingPosition']=_0x178c8f,this[_0x81e863(0x1cb)]['calculateSpeed']['commandToExecute']&&(this[_0x81e863(0x1cb)]['calculateSpeed']['commandToExecute']['outputTexture']=this['particlesTextures'][_0x81e863(0x209)]);},'isDynamic':()=>this[_0x158c27(0x1fe)]['dynamic']}),'updatePosition':new CustomPrimitive({'commandType':_0x158c27(0x1eb),'uniformMap':{'currentParticlesPosition':()=>this['particlesTextures']['currentParticlesPosition'],'particlesSpeed':()=>this[_0x158c27(0x1bd)]['particlesSpeed']},'fragmentShaderSource':ShaderManager['getUpdatePositionShader'](),'outputTexture':this[_0x158c27(0x1bd)]['nextParticlesPosition'],'preExecute':()=>{const _0x5c9bb4=_0x158c27;this[_0x5c9bb4(0x1cb)][_0x5c9bb4(0x1cc)]['commandToExecute']&&(this[_0x5c9bb4(0x1cb)]['updatePosition']['commandToExecute']['outputTexture']=this['particlesTextures'][_0x5c9bb4(0x22f)]);},'isDynamic':()=>this['options'][_0x158c27(0x22b)]}),'postProcessingPosition':new CustomPrimitive({'commandType':'Compute','uniformMap':{'nextParticlesPosition':()=>this['particlesTextures'][_0x158c27(0x22f)],'particlesSpeed':()=>this[_0x158c27(0x1bd)]['particlesSpeed'],'lonRange':()=>this[_0x158c27(0x256)][_0x158c27(0x25c)],'latRange':()=>this['viewerParameters'][_0x158c27(0x21a)],'dataLonRange':()=>new Cartesian2$1(this['windData']['bounds'][_0x158c27(0x1b2)],this['windData'][_0x158c27(0x241)][_0x158c27(0x226)]),'dataLatRange':()=>new Cartesian2$1(this[_0x158c27(0x229)]['bounds']['south'],this['windData']['bounds']['north']),'randomCoefficient':function(){return Math['random']();},'dropRate':()=>this['options']['dropRate'],'dropRateBump':()=>this[_0x158c27(0x1fe)]['dropRateBump'],'useViewerBounds':()=>this['options']['useViewerBounds']},'fragmentShaderSource':ShaderManager['getPostProcessingPositionShader'](),'outputTexture':this['particlesTextures']['postProcessingPosition'],'preExecute':()=>{const _0x6a8f5=_0x158c27;this['primitives']['postProcessingPosition']['commandToExecute']&&(this['primitives']['postProcessingPosition']['commandToExecute']['outputTexture']=this[_0x6a8f5(0x1bd)]['postProcessingPosition']);},'isDynamic':()=>this[_0x158c27(0x1fe)]['dynamic']})};}['reCreateWindTextures'](){const _0x35ad6c=_0x221f23;this[_0x35ad6c(0x21d)]['U']['destroy'](),this['windTextures']['V']['destroy'](),this['createWindTextures']();}['updateWindData'](_0x473032){this['windData']=_0x473032,this['reCreateWindTextures']();}[_0x221f23(0x23a)](_0xa39674){const _0x63b466=_0x221f23,_0x1fd9f9=_0xa39674['flipY']!==undefined&&_0xa39674['flipY']!==this['options'][_0x63b466(0x24d)];this['options']=deepMerge(_0xa39674,this[_0x63b466(0x1fe)]),_0x1fd9f9&&this['reCreateWindTextures']();}['processWindData'](_0x32e90b){const _0x3682ab=_0x221f23,{array:_0x122c86}=_0x32e90b;let {min:_0x3afd4d,max:_0x552486}=_0x32e90b;const _0x288070=new Float32Array(_0x122c86['length']);_0x3afd4d===undefined&&(console['warn']('min\x20is\x20undefined,\x20calculate\x20min'),_0x3afd4d=Math['min'](..._0x122c86));_0x552486===undefined&&(console['warn'](_0x3682ab(0x25b)),_0x552486=Math['max'](..._0x122c86));const _0x2b0301=Math['max'](Math['abs'](_0x3afd4d),Math[_0x3682ab(0x194)](_0x552486));for(let _0x138477=0x0;_0x138477<_0x122c86['length'];_0x138477++){const _0x55d835=_0x122c86[_0x138477]/_0x2b0301;_0x288070[_0x138477]=_0x55d835;}return _0x288070;}['destroy'](){const _0x469e17=_0x221f23;Object['values'](this['windTextures'])['forEach'](_0x44edef=>_0x44edef['destroy']()),Object[_0x469e17(0x1c0)](this[_0x469e17(0x1bd)])['forEach'](_0x439dc4=>_0x439dc4['destroy']()),Object['values'](this['primitives'])['forEach'](_0x41d390=>_0x41d390[_0x469e17(0x1a9)]()),this['frameRateMonitor']['destroy']();}}const {Appearance,BufferUsage,Cartesian2,Color:Color$1,ComponentDatatype,Framebuffer,Geometry,GeometryAttribute,GeometryAttributes,PixelDatatype,PixelFormat,PrimitiveType,Sampler,SceneMode,Texture,TextureMagnificationFilter,TextureMinificationFilter,TextureWrap,VertexArray}=mars3d__namespace[_0x221f23(0x1da)];class WindParticlesRendering{constructor(_0x30e206,_0x35a9d3,_0x17016d,_0x2049c2){const _0x210e97=_0x221f23;this['context']=_0x30e206,this['options']=_0x35a9d3,this[_0x210e97(0x256)]=_0x17016d,this[_0x210e97(0x193)]=_0x2049c2,(typeof this[_0x210e97(0x1fe)]['particlesTextureSize']!==_0x210e97(0x1ef)||this[_0x210e97(0x1fe)][_0x210e97(0x1ad)]<=0x0)&&(console['error']('Invalid\x20particlesTextureSize.\x20Using\x20default\x20value\x20of\x20256.'),this['options']['particlesTextureSize']=0x100),this['colorTable']=this['createColorTableTexture'](),this['textures']=this[_0x210e97(0x1f2)](),this['framebuffers']=this[_0x210e97(0x21f)](),this['primitives']=this['createPrimitives']();}['createRenderingTextures'](){const _0x22ee85=_0x221f23,_0x54dd7c={'context':this['context'],'width':this['context']['drawingBufferWidth'],'height':this['context']['drawingBufferHeight'],'pixelFormat':PixelFormat['RGBA'],'pixelDatatype':PixelDatatype['UNSIGNED_BYTE']},_0x1f94a0={'context':this['context'],'width':this[_0x22ee85(0x22c)]['drawingBufferWidth'],'height':this['context']['drawingBufferHeight'],'pixelFormat':PixelFormat['DEPTH_COMPONENT'],'pixelDatatype':PixelDatatype[_0x22ee85(0x262)]};return{'segmentsColor':new Texture(_0x54dd7c),'segmentsDepth':new Texture(_0x1f94a0)};}['createRenderingFramebuffers'](){const _0x140a29=_0x221f23;return{'segments':new Framebuffer({'context':this[_0x140a29(0x22c)],'colorTextures':[this['textures']['segmentsColor']],'depthTexture':this['textures']['segmentsDepth']})};}['destoryRenderingFramebuffers'](){const _0x25a8b6=_0x221f23;Object['values'](this[_0x25a8b6(0x18d)])['forEach'](_0x2feea7=>{_0x2feea7['destroy']();});}['createColorTableTexture'](){const _0x560f3d=_0x221f23,_0x16e85f=new Float32Array(this['options']['colors']['flatMap'](_0x1de63f=>{const _0x4a19f7=_0x2ad5,_0x4bcf27=Color$1['fromCssColorString'](_0x1de63f);return[_0x4bcf27[_0x4a19f7(0x1aa)],_0x4bcf27['green'],_0x4bcf27['blue'],_0x4bcf27['alpha']];}));return new Texture({'context':this['context'],'width':this[_0x560f3d(0x1fe)][_0x560f3d(0x249)]['length'],'height':0x1,'pixelFormat':PixelFormat[_0x560f3d(0x1b5)],'pixelDatatype':PixelDatatype['FLOAT'],'sampler':new Sampler({'minificationFilter':TextureMinificationFilter['LINEAR'],'magnificationFilter':TextureMagnificationFilter[_0x560f3d(0x1ac)],'wrapS':TextureWrap['CLAMP_TO_EDGE'],'wrapT':TextureWrap['CLAMP_TO_EDGE']}),'source':{'width':this['options'][_0x560f3d(0x249)][_0x560f3d(0x1b3)],'height':0x1,'arrayBufferView':_0x16e85f}});}[_0x221f23(0x218)](){const _0x174d3d=_0x221f23,_0x1d9786=0x4,_0x3844dc=this[_0x174d3d(0x1fe)]['particlesTextureSize'];let _0x2de229=[];for(let _0x59aeca=0x0;_0x59aeca<_0x3844dc;_0x59aeca++){for(let _0x175a45=0x0;_0x175a45<_0x3844dc;_0x175a45++){for(let _0x3d5ad5=0x0;_0x3d5ad5<_0x1d9786;_0x3d5ad5++){_0x2de229['push'](_0x59aeca/_0x3844dc),_0x2de229['push'](_0x175a45/_0x3844dc);}}}_0x2de229=new Float32Array(_0x2de229);const _0x455137=this['options'][_0x174d3d(0x1ad)]**0x2;let _0x2d2c4e=[];for(let _0x1997b5=0x0;_0x1997b5<_0x455137;_0x1997b5++){_0x2d2c4e['push'](-0x1,-0x1,0x0,-0x1,0x1,0x0,0x1,-0x1,0x0,0x1,0x1,0x0);}_0x2d2c4e=new Float32Array(_0x2d2c4e);let _0x262aab=[];for(let _0xc2164a=0x0,_0x53c81d=0x0;_0xc2164a<_0x455137;_0xc2164a++){_0x262aab[_0x174d3d(0x257)](_0x53c81d+0x0,_0x53c81d+0x1,_0x53c81d+0x2,_0x53c81d+0x2,_0x53c81d+0x1,_0x53c81d+0x3),_0x53c81d+=_0x1d9786;}_0x262aab=new Uint32Array(_0x262aab);const _0x13b0ac=new Geometry({'attributes':new GeometryAttributes({'st':new GeometryAttribute({'componentDatatype':ComponentDatatype['FLOAT'],'componentsPerAttribute':0x2,'values':_0x2de229}),'normal':new GeometryAttribute({'componentDatatype':ComponentDatatype[_0x174d3d(0x1df)],'componentsPerAttribute':0x3,'values':_0x2d2c4e})}),'indices':_0x262aab});return _0x13b0ac;}[_0x221f23(0x1b8)](_0x12d852){const _0xb5b78f=_0x221f23;return Appearance[_0xb5b78f(0x254)](!![],![],{'viewport':undefined,'depthTest':undefined,'depthMask':undefined,'blending':undefined,..._0x12d852});}[_0x221f23(0x24f)](){const _0x2e953b=_0x221f23,_0x4f8e1e=new CustomPrimitive({'commandType':'Draw','attributeLocations':{'st':0x0,'normal':0x1},'geometry':this['createSegmentsGeometry'](),'primitiveType':PrimitiveType['TRIANGLES'],'uniformMap':{'previousParticlesPosition':()=>this['computing']['particlesTextures']['previousParticlesPosition'],'currentParticlesPosition':()=>this['computing'][_0x2e953b(0x1bd)]['currentParticlesPosition'],'postProcessingPosition':()=>this['computing']['particlesTextures']['postProcessingPosition'],'particlesSpeed':()=>this['computing']['particlesTextures']['particlesSpeed'],'frameRateAdjustment':()=>this['computing'][_0x2e953b(0x1c3)],'colorTable':()=>this[_0x2e953b(0x1c1)],'domain':()=>{const _0x1d522b=_0x2e953b;var _0x34f5de,_0x2422ee;const _0x457c00=new Cartesian2(((_0x34f5de=this['options'][_0x1d522b(0x235)])===null||_0x34f5de===void 0x0?void 0x0:_0x34f5de['min'])??this['computing'][_0x1d522b(0x229)][_0x1d522b(0x188)]['min'],((_0x2422ee=this[_0x1d522b(0x1fe)]['domain'])===null||_0x2422ee===void 0x0?void 0x0:_0x2422ee['max'])??this[_0x1d522b(0x193)][_0x1d522b(0x229)]['speed']['max']);return _0x457c00;},'displayRange':()=>{const _0x46c629=_0x2e953b;var _0x267c42,_0x30a3d7;const _0x326ad9=new Cartesian2(((_0x267c42=this['options'][_0x46c629(0x1e8)])===null||_0x267c42===void 0x0?void 0x0:_0x267c42[_0x46c629(0x24a)])??this['computing']['windData']['speed']['min'],((_0x30a3d7=this[_0x46c629(0x1fe)][_0x46c629(0x1e8)])===null||_0x30a3d7===void 0x0?void 0x0:_0x30a3d7['max'])??this['computing'][_0x46c629(0x229)]['speed'][_0x46c629(0x1f4)]);return _0x326ad9;},'particleHeight':()=>this['options']['fixedHeight']||0x0,'aspect':()=>this['context']['drawingBufferWidth']/this['context'][_0x2e953b(0x251)],'pixelSize':()=>this['viewerParameters']['pixelSize'],'lineWidth':()=>{const _0x201d36=_0x2e953b,_0x3c02b0=this['options']['lineWidth']||{'min':0x1,'max':0x2};return new Cartesian2(_0x3c02b0['min'],_0x3c02b0[_0x201d36(0x1f4)]);},'lineLength':()=>{const _0x177afd=_0x2e953b,_0x45f8c4=this['options'][_0x177afd(0x1a2)]||{'min':0x14,'max':0x64};return new Cartesian2(_0x45f8c4[_0x177afd(0x24a)],_0x45f8c4[_0x177afd(0x1f4)]);},'is3D':()=>this[_0x2e953b(0x256)]['sceneMode']===SceneMode['SCENE3D'],'segmentsDepthTexture':()=>this['textures']['segmentsDepth']},'vertexShaderSource':ShaderManager['getSegmentDrawVertexShader'](),'fragmentShaderSource':ShaderManager[_0x2e953b(0x1c9)](),'rawRenderState':this[_0x2e953b(0x1b8)]({'viewport':undefined,'depthTest':{'enabled':!![]},'depthMask':!![],'blending':{'enabled':!![],'blendEquation':WebGLRenderingContext['FUNC_ADD'],'blendFuncSource':WebGLRenderingContext['SRC_ALPHA'],'blendFuncDestination':WebGLRenderingContext['ONE_MINUS_SRC_ALPHA']}})});return{'segments':_0x4f8e1e};}[_0x221f23(0x242)](){const _0x2f16bc=_0x221f23,_0x5f2bfb=this['createSegmentsGeometry']();this['primitives']['segments'][_0x2f16bc(0x260)]=_0x5f2bfb;const _0x125174=VertexArray[_0x2f16bc(0x24c)]({'context':this['context'],'geometry':_0x5f2bfb,'attributeLocations':this['primitives']['segments'][_0x2f16bc(0x201)],'bufferUsage':BufferUsage['STATIC_DRAW']});this['primitives'][_0x2f16bc(0x204)]['commandToExecute']&&(this[_0x2f16bc(0x1cb)]['segments']['commandToExecute'][_0x2f16bc(0x198)]=_0x125174);}['onColorTableChange'](){const _0xad03f6=_0x221f23;this[_0xad03f6(0x1c1)]['destroy'](),this['colorTable']=this['createColorTableTexture']();}['updateOptions'](_0x148ac8){const _0x57890b=_0x221f23,_0x1369d6=_0x148ac8['colors']&&JSON[_0x57890b(0x1c2)](_0x148ac8[_0x57890b(0x249)])!==JSON['stringify'](this[_0x57890b(0x1fe)]['colors']);this[_0x57890b(0x1fe)]=deepMerge(_0x148ac8,this['options']),_0x1369d6&&this[_0x57890b(0x1d7)]();}['destroy'](){const _0x512213=_0x221f23;Object['values'](this['framebuffers'])['forEach'](_0x5c76b8=>{const _0x582a23=_0x2ad5;_0x5c76b8[_0x582a23(0x1a9)]();}),Object['values'](this[_0x512213(0x1cb)])['forEach'](_0x3e2855=>{_0x3e2855['destroy']();}),this['colorTable']['destroy']();}}const {ClearCommand,Color,Pass}=mars3d__namespace['Cesium'];class WindParticleSystem{constructor(_0x1656e8,_0x38c96c,_0x1b4a5d,_0x6aa7a2,_0x587e18){const _0x3828e6=_0x221f23;this['context']=_0x1656e8,this['options']=_0x1b4a5d,this[_0x3828e6(0x256)]=_0x6aa7a2,this[_0x3828e6(0x193)]=new WindParticlesComputing(_0x1656e8,_0x38c96c,_0x1b4a5d,_0x6aa7a2,_0x587e18),this[_0x3828e6(0x1b0)]=new WindParticlesRendering(_0x1656e8,_0x1b4a5d,_0x6aa7a2,this[_0x3828e6(0x193)]),this['clearFramebuffers']();}[_0x221f23(0x1ce)](){const _0x469ac1=_0x221f23,_0x13dfa0=[this['computing']['primitives'][_0x469ac1(0x19d)],this[_0x469ac1(0x193)]['primitives'][_0x469ac1(0x1cc)],this['computing']['primitives']['postProcessingPosition'],this['rendering']['primitives'][_0x469ac1(0x204)]];return _0x13dfa0;}[_0x221f23(0x1b9)](){const _0x3d89c2=_0x221f23,_0x269597=new ClearCommand({'color':new Color(0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':undefined,'pass':Pass[_0x3d89c2(0x1de)]});Object['keys'](this['rendering']['framebuffers'])[_0x3d89c2(0x1fd)](_0x35c8f5=>{const _0x1aae40=_0x3d89c2;_0x269597['framebuffer']=this['rendering']['framebuffers'][_0x35c8f5],_0x269597[_0x1aae40(0x18e)](this['context']);});}['changeOptions'](_0x1000f8){const _0x414429=_0x221f23;let _0x18a29b=![];_0x1000f8['particlesTextureSize']&&this[_0x414429(0x1fe)]['particlesTextureSize']!==_0x1000f8['particlesTextureSize']&&(_0x18a29b=!![]);const _0x5b8881=deepMerge(_0x1000f8,this['options']);if(_0x5b8881['particlesTextureSize']<0x1)throw new Error('particlesTextureSize\x20must\x20be\x20greater\x20than\x200');this['options']=_0x5b8881,this['rendering']['updateOptions'](_0x1000f8),this['computing']['updateOptions'](_0x1000f8),_0x18a29b&&(this['computing']['destroyParticlesTextures'](),this['computing'][_0x414429(0x1d5)](),this[_0x414429(0x1b0)]['onParticlesTextureSizeChange']());}['applyViewerParameters'](_0x31e7eb){this['viewerParameters']=_0x31e7eb,this['computing']['viewerParameters']=_0x31e7eb,this['rendering']['viewerParameters']=_0x31e7eb;}['destroy'](){const _0x27d7f8=_0x221f23;this['computing']['destroy'](),this['rendering'][_0x27d7f8(0x1a9)]();}}const Cesium$1=mars3d__namespace['Cesium'],BaseLayer$1=mars3d__namespace['layer'][_0x221f23(0x22e)],DEF_OPTIONS={'particlesTextureSize':0x64,'fixedHeight':0x0,'lineWidth':{'min':0x1,'max':0x2},'lineLength':{'min':0x14,'max':0x64},'speedFactor':0x1,'dropRate':0.003,'dropRateBump':0.001,'colors':['rgb(206,255,255)'],'flipY':![],'dynamic':!![]};class WindLayer extends BaseLayer$1{constructor(_0x25d5e0={}){_0x25d5e0={...DEF_OPTIONS,..._0x25d5e0},super(_0x25d5e0),this['_setOptionsHook'](_0x25d5e0,_0x25d5e0);}get[_0x221f23(0x25a)](){return this['primitives'];}get['data'](){const _0x33752b=_0x221f23;return this[_0x33752b(0x1fe)]['data'];}set['data'](_0x339a84){const _0x2ed96f=_0x221f23;this['options']['data']=_0x339a84,this[_0x2ed96f(0x189)](_0x339a84);}get['colors'](){const _0x4c483c=_0x221f23;return this[_0x4c483c(0x1fe)]['colors'];}set[_0x221f23(0x249)](_0xc95c94){const _0x417900=_0x221f23;this['options']['colors']=_0xc95c94,this[_0x417900(0x19a)](this['options'],{'colors':_0xc95c94});}['_showHook'](_0x14f7d4){const _0x19a880=_0x221f23;_0x14f7d4?this[_0x19a880(0x20e)]():this['_removedHook']();}[_0x221f23(0x1ab)](){}['_addedHook'](){const _0x343b21=_0x221f23;this[_0x343b21(0x1d2)]=this[_0x343b21(0x224)][_0x343b21(0x1d2)],this['camera']=this['_map']['camera'];this['options']['data']&&this['setData'](this['options']['data']);if(!this[_0x343b21(0x229)]||!this['show'])return;this['viewerParameters']={'lonRange':new Cesium$1['Cartesian2'](-0xb4,0xb4),'latRange':new Cesium$1[(_0x343b21(0x211))](-0x5a,0x5a),'pixelSize':0x3e8,'sceneMode':this[_0x343b21(0x1d2)]['mode']},this['updateViewerParameters'](),this['particleSystem']=new WindParticleSystem(this['scene'][_0x343b21(0x22c)],this['windData'],this['options'],this[_0x343b21(0x256)],this['scene']),this['primitives']=this['particleSystem']['getPrimitives'](),this['primitives']['forEach'](_0x30ca6b=>{this['scene']['primitives']['add'](_0x30ca6b);}),this[_0x343b21(0x21e)]['percentageChanged']=0.01,this[_0x343b21(0x21e)]['changed'][_0x343b21(0x1cf)](this['updateViewerParameters']['bind'](this)),this[_0x343b21(0x1d2)][_0x343b21(0x220)]['addEventListener'](this[_0x343b21(0x216)]['bind'](this)),window['addEventListener'](_0x343b21(0x19c),this['updateViewerParameters']['bind'](this));}['_removedHook'](){const _0x3a8826=_0x221f23;this[_0x3a8826(0x21e)][_0x3a8826(0x1ea)]['removeEventListener'](this[_0x3a8826(0x216)][_0x3a8826(0x1f6)](this)),this['scene']['morphComplete'][_0x3a8826(0x21b)](this[_0x3a8826(0x216)]['bind'](this)),window['removeEventListener']('resize',this['updateViewerParameters']['bind'](this)),this['primitives']&&(this[_0x3a8826(0x1cb)]['forEach'](_0x79511c=>{this['scene']['primitives']['remove'](_0x79511c);}),delete this['primitives']),this[_0x3a8826(0x230)]&&(this[_0x3a8826(0x230)]['destroy'](),delete this[_0x3a8826(0x230)]);}['setData'](_0x4a8eed,_0x468e04){const _0x367065=_0x221f23;this['windData']=this['processWindData'](_0x4a8eed);if(_0x468e04){this[_0x367065(0x24e)](),this['_addedHook']();return;}this['particleSystem']?(this['particleSystem']['computing']['updateWindData'](this['windData']),this['scene'][_0x367065(0x1e1)]()):this[_0x367065(0x20e)]();}['_setOptionsHook'](_0x1f2ffa,_0x19bc39){const _0x4591e9=_0x221f23;this['particleSystem']&&(this['particleSystem']['changeOptions'](_0x19bc39),this['scene'][_0x4591e9(0x1e1)]());}['processWindData'](_0x578732){const _0x4f06da=_0x221f23;var _0x2ac09e,_0x3e25a7;const _0x253f23={..._0x578732};!_0x253f23['height']&&_0x253f23[_0x4f06da(0x23b)]&&(_0x253f23['height']=_0x253f23['rows']);!_0x253f23['width']&&_0x253f23['cols']&&(_0x253f23[_0x4f06da(0x208)]=_0x253f23['cols']);!_0x253f23['bounds']&&(_0x253f23['bounds']={'west':_0x253f23['xmin'],'south':_0x253f23['ymin'],'east':_0x253f23[_0x4f06da(0x25e)],'north':_0x253f23['ymax']});!_0x253f23['u']&&(_0x253f23['u']={'array':_0x578732[_0x4f06da(0x24b)],'min':_0x578732['umin'],'max':_0x578732[_0x4f06da(0x247)]});!_0x253f23['v']&&(_0x253f23['v']={'array':_0x578732[_0x4f06da(0x239)],'min':_0x578732[_0x4f06da(0x23f)],'max':_0x578732[_0x4f06da(0x203)]});if(((_0x2ac09e=_0x253f23['speed'])===null||_0x2ac09e===void 0x0?void 0x0:_0x2ac09e['min'])===undefined||((_0x3e25a7=_0x253f23['speed'])===null||_0x3e25a7===void 0x0?void 0x0:_0x3e25a7[_0x4f06da(0x1f4)])===undefined||_0x253f23['speed']['array']===undefined){const _0x59ea61={'array':new Float32Array(_0x253f23['u']['array']['length']),'min':Number['MAX_VALUE'],'max':Number[_0x4f06da(0x219)]};for(let _0x2405e4=0x0;_0x2405e4<_0x253f23['u']['array']['length'];_0x2405e4++){_0x59ea61['array'][_0x2405e4]=Math['sqrt'](_0x253f23['u']['array'][_0x2405e4]*_0x253f23['u']['array'][_0x2405e4]+_0x253f23['v'][_0x4f06da(0x20a)][_0x2405e4]*_0x253f23['v']['array'][_0x2405e4]),_0x59ea61['array'][_0x2405e4]!==0x0&&(_0x59ea61['min']=Math[_0x4f06da(0x24a)](_0x59ea61['min'],_0x59ea61['array'][_0x2405e4]),_0x59ea61[_0x4f06da(0x1f4)]=Math[_0x4f06da(0x1f4)](_0x59ea61[_0x4f06da(0x1f4)],_0x59ea61['array'][_0x2405e4]));}_0x253f23[_0x4f06da(0x188)]=_0x59ea61;}return _0x253f23;}['updateViewerParameters'](){const _0x4034fd=_0x221f23;var _0x16a828;const _0x6a7a35=this['scene'];if(!_0x6a7a35)return;const _0x304f8a=_0x6a7a35['canvas'],_0x29e092=[{'x':0x0,'y':0x0},{'x':0x0,'y':_0x304f8a['clientHeight']},{'x':_0x304f8a[_0x4034fd(0x215)],'y':0x0},{'x':_0x304f8a['clientWidth'],'y':_0x304f8a[_0x4034fd(0x214)]}];let _0x5c78b4=0xb4,_0x3df0d1=-0xb4,_0x54f42b=0x5a,_0x293bbc=-0x5a,_0x168197=![];for(const _0xdfc07f of _0x29e092){const _0x550c59=_0x6a7a35['camera'][_0x4034fd(0x25d)](new Cesium$1['Cartesian2'](_0xdfc07f['x'],_0xdfc07f['y']),_0x6a7a35['globe']['ellipsoid']);if(!_0x550c59){_0x168197=!![];break;}const _0x1ee6c3=_0x6a7a35[_0x4034fd(0x1ee)]['ellipsoid']['cartesianToCartographic'](_0x550c59),_0x15e164=Cesium$1[_0x4034fd(0x206)]['toDegrees'](_0x1ee6c3['longitude']),_0x5067d4=Cesium$1[_0x4034fd(0x206)]['toDegrees'](_0x1ee6c3['latitude']);_0x5c78b4=Math[_0x4034fd(0x24a)](_0x5c78b4,_0x15e164),_0x3df0d1=Math[_0x4034fd(0x1f4)](_0x3df0d1,_0x15e164),_0x54f42b=Math[_0x4034fd(0x24a)](_0x54f42b,_0x5067d4),_0x293bbc=Math['max'](_0x293bbc,_0x5067d4);}if(!_0x168197){const _0x53bdd2=new Cesium$1['Cartesian2'](Math['max'](this[_0x4034fd(0x229)]['bounds'][_0x4034fd(0x1b2)],_0x5c78b4),Math['min'](this[_0x4034fd(0x229)][_0x4034fd(0x241)]['east'],_0x3df0d1)),_0x280baa=new Cesium$1['Cartesian2'](Math['max'](this['windData'][_0x4034fd(0x241)][_0x4034fd(0x1e9)],_0x54f42b),Math['min'](this['windData']['bounds'][_0x4034fd(0x244)],_0x293bbc)),_0x2c37b4=(_0x53bdd2['y']-_0x53bdd2['x'])*0.05,_0x242835=(_0x280baa['y']-_0x280baa['x'])*0.05;_0x53bdd2['x']=Math['max'](this['windData']['bounds']['west'],_0x53bdd2['x']-_0x2c37b4),_0x53bdd2['y']=Math['min'](this[_0x4034fd(0x229)]['bounds']['east'],_0x53bdd2['y']+_0x2c37b4),_0x280baa['x']=Math['max'](this['windData']['bounds']['south'],_0x280baa['x']-_0x242835),_0x280baa['y']=Math['min'](this['windData']['bounds'][_0x4034fd(0x244)],_0x280baa['y']+_0x242835),this['viewerParameters']['lonRange']=_0x53bdd2,this[_0x4034fd(0x256)]['latRange']=_0x280baa;const _0xaec648=this[_0x4034fd(0x229)][_0x4034fd(0x241)][_0x4034fd(0x226)]-this[_0x4034fd(0x229)][_0x4034fd(0x241)]['west'],_0x1ada5f=this['windData'][_0x4034fd(0x241)]['north']-this['windData']['bounds'][_0x4034fd(0x1e9)],_0x3e104e=(_0x53bdd2['y']-_0x53bdd2['x'])/_0xaec648,_0x5b8356=(_0x280baa['y']-_0x280baa['x'])/_0x1ada5f,_0x478b3f=Math['min'](_0x3e104e,_0x5b8356),_0x4035dd=0x3e8*_0x478b3f;_0x4035dd>0x0&&(this['viewerParameters']['pixelSize']=Math['max'](0x0,Math['min'](0x3e8,_0x4035dd)));}this['viewerParameters'][_0x4034fd(0x236)]=this['scene']['mode'],(_0x16a828=this['particleSystem'])===null||_0x16a828===void 0x0||_0x16a828['applyViewerParameters'](this['viewerParameters']);}['getDataAtLonLat'](_0x275142,_0x46c9ec){const _0x25e171=_0x221f23,{bounds:_0x5e050a,width:_0x373b79,height:_0x4a1d06,u:_0x2f2658,v:_0x4b9c8c,speed:_0x3e4a6f}=this['windData'],{flipY:_0x39cac0}=this[_0x25e171(0x1fe)];if(_0x275142<_0x5e050a[_0x25e171(0x1b2)]||_0x275142>_0x5e050a[_0x25e171(0x226)]||_0x46c9ec<_0x5e050a['south']||_0x46c9ec>_0x5e050a[_0x25e171(0x244)])return null;const _0x211137=(_0x275142-_0x5e050a['west'])/(_0x5e050a['east']-_0x5e050a[_0x25e171(0x1b2)])*(_0x373b79-0x1);let _0x2731e4=(_0x46c9ec-_0x5e050a['south'])/(_0x5e050a[_0x25e171(0x244)]-_0x5e050a['south'])*(_0x4a1d06-0x1);_0x39cac0&&(_0x2731e4=_0x4a1d06-0x1-_0x2731e4);const _0x229dc9=Math[_0x25e171(0x195)](_0x211137),_0x3d74fa=Math['floor'](_0x2731e4),_0x535b44=Math['floor'](_0x211137),_0x500865=Math[_0x25e171(0x24a)](_0x535b44+0x1,_0x373b79-0x1),_0x15bc90=Math['floor'](_0x2731e4),_0x4821bc=Math['min'](_0x15bc90+0x1,_0x4a1d06-0x1),_0x24d558=_0x211137-_0x535b44,_0x4836d1=_0x2731e4-_0x15bc90,_0x16c6a8=_0x3d74fa*_0x373b79+_0x229dc9,_0x160ca2=_0x15bc90*_0x373b79+_0x535b44,_0x245b72=_0x15bc90*_0x373b79+_0x500865,_0x140644=_0x4821bc*_0x373b79+_0x535b44,_0xa11b4=_0x4821bc*_0x373b79+_0x500865,_0x5df05d=_0x2f2658[_0x25e171(0x20a)][_0x160ca2],_0x1a8b5c=_0x2f2658[_0x25e171(0x20a)][_0x245b72],_0x1b675e=_0x2f2658['array'][_0x140644],_0x5bde3c=_0x2f2658['array'][_0xa11b4],_0x2fda9c=(0x1-_0x24d558)*(0x1-_0x4836d1)*_0x5df05d+_0x24d558*(0x1-_0x4836d1)*_0x1a8b5c+(0x1-_0x24d558)*_0x4836d1*_0x1b675e+_0x24d558*_0x4836d1*_0x5bde3c,_0x46c875=_0x4b9c8c['array'][_0x160ca2],_0x4c769d=_0x4b9c8c['array'][_0x245b72],_0x15fc45=_0x4b9c8c[_0x25e171(0x20a)][_0x140644],_0x4bddbe=_0x4b9c8c['array'][_0xa11b4],_0x3da4ee=(0x1-_0x24d558)*(0x1-_0x4836d1)*_0x46c875+_0x24d558*(0x1-_0x4836d1)*_0x4c769d+(0x1-_0x24d558)*_0x4836d1*_0x15fc45+_0x24d558*_0x4836d1*_0x4bddbe,_0x2c941b=Math['sqrt'](_0x2fda9c*_0x2fda9c+_0x3da4ee*_0x3da4ee);return{'original':{'u':_0x2f2658['array'][_0x16c6a8],'v':_0x4b9c8c[_0x25e171(0x20a)][_0x16c6a8],'speed':_0x3e4a6f[_0x25e171(0x20a)][_0x16c6a8]},'interpolated':{'u':_0x2fda9c,'v':_0x3da4ee,'speed':_0x2c941b}};}}mars3d__namespace['LayerUtil']['register']('wind',WindLayer),mars3d__namespace['layer'][_0x221f23(0x18b)]=WindLayer;class CanvasParticle{constructor(){const _0x87bfa=_0x221f23;this[_0x87bfa(0x259)]=null,this[_0x87bfa(0x221)]=null,this['tlng']=null,this['tlat']=null,this['age']=null,this['speed']=null;}['destroy'](){for(const _0x58b8f6 in this){delete this[_0x58b8f6];}}}class CanvasWindField{constructor(_0x1ca3aa){this['setOptions'](_0x1ca3aa);}get['speedRate'](){const _0x27ae68=_0x221f23;return this[_0x27ae68(0x1bc)];}set['speedRate'](_0x497375){const _0x39b255=_0x221f23;this['_speedRate']=(0x64-(_0x497375>0x63?0x63:_0x497375))*0x64,this[_0x39b255(0x21c)]=[(this[_0x39b255(0x25e)]-this['xmin'])/this[_0x39b255(0x1bc)],(this[_0x39b255(0x1b4)]-this['ymin'])/this[_0x39b255(0x1bc)]];}get[_0x221f23(0x1af)](){return this['_maxAge'];}set['maxAge'](_0x2c05c2){this['_maxAge']=_0x2c05c2;}['setOptions'](_0x5e5744){const _0x461126=_0x221f23;this['options']=_0x5e5744,this['maxAge']=_0x5e5744['maxAge']||0x78,this['speedRate']=_0x5e5744['speedRate']||0x32,this['particles']=[];const _0x2267f1=_0x5e5744['particlesNumber']||0x1000;for(let _0x55322f=0x0;_0x55322f<_0x2267f1;_0x55322f++){const _0x3f1618=this[_0x461126(0x19b)](new CanvasParticle());this['particles']['push'](_0x3f1618);}}[_0x221f23(0x1c5)](_0x42f162){const _0x337bfd=_0x221f23;this['rows']=_0x42f162['rows'],this[_0x337bfd(0x1a7)]=_0x42f162[_0x337bfd(0x1a7)],this['xmin']=_0x42f162[_0x337bfd(0x1ba)],this[_0x337bfd(0x25e)]=_0x42f162['xmax'],this['ymin']=_0x42f162['ymin'],this[_0x337bfd(0x1b4)]=_0x42f162[_0x337bfd(0x1b4)],this['grid']=[];const _0x17d63b=_0x42f162['udata'],_0x5208b3=_0x42f162[_0x337bfd(0x239)];let _0x1abe5c=![];_0x17d63b[_0x337bfd(0x1b3)]===this['rows']&&_0x17d63b[0x0][_0x337bfd(0x1b3)]===this['cols']&&(_0x1abe5c=!![]);let _0x27ccf2=0x0,_0x3206c4=null,_0x4a3983=null;for(let _0x565417=0x0;_0x565417<this[_0x337bfd(0x23b)];_0x565417++){_0x3206c4=[];for(let _0x42ead8=0x0;_0x42ead8<this['cols'];_0x42ead8++,_0x27ccf2++){_0x1abe5c?_0x4a3983=this['_calcUV'](_0x17d63b[_0x565417][_0x42ead8],_0x5208b3[_0x565417][_0x42ead8]):_0x4a3983=this['_calcUV'](_0x17d63b[_0x27ccf2],_0x5208b3[_0x27ccf2]),_0x3206c4['push'](_0x4a3983);}this['grid']['push'](_0x3206c4);}!this['options']['flipY']&&this[_0x337bfd(0x1e4)]['reverse']();}['clear'](){const _0x3f6f07=_0x221f23;delete this[_0x3f6f07(0x23b)],delete this['cols'],delete this[_0x3f6f07(0x1ba)],delete this['xmax'],delete this['ymin'],delete this[_0x3f6f07(0x1b4)],delete this['grid'],delete this['particles'];}['toGridXY'](_0x14fb12,_0x22cfa3){const _0x3ae6d=_0x221f23,_0x199315=(_0x14fb12-this['xmin'])/(this['xmax']-this['xmin'])*(this['cols']-0x1),_0x13945a=(this[_0x3ae6d(0x1b4)]-_0x22cfa3)/(this['ymax']-this[_0x3ae6d(0x227)])*(this['rows']-0x1);return[_0x199315,_0x13945a];}['getUVByXY'](_0x587e83,_0x2d4a6a){const _0x5a9194=_0x221f23;if(_0x587e83<0x0||_0x587e83>=this['cols']||_0x2d4a6a>=this['rows'])return[0x0,0x0,0x0];const _0x18745c=Math['floor'](_0x587e83),_0x29ce7a=Math[_0x5a9194(0x195)](_0x2d4a6a);if(_0x18745c===_0x587e83&&_0x29ce7a===_0x2d4a6a)return this[_0x5a9194(0x1e4)][_0x2d4a6a][_0x587e83];const _0x21e7cd=_0x18745c+0x1,_0x287120=_0x29ce7a+0x1,_0x207a7f=this['getUVByXY'](_0x18745c,_0x29ce7a),_0x590aa4=this['getUVByXY'](_0x21e7cd,_0x29ce7a),_0x57ba4a=this['getUVByXY'](_0x18745c,_0x287120),_0x5a8536=this['getUVByXY'](_0x21e7cd,_0x287120);let _0x4317df=null;try{_0x4317df=this['_bilinearInterpolation'](_0x587e83-_0x18745c,_0x2d4a6a-_0x29ce7a,_0x207a7f,_0x590aa4,_0x57ba4a,_0x5a8536);}catch(_0x19e345){console['log'](_0x587e83,_0x2d4a6a);}return _0x4317df;}['_bilinearInterpolation'](_0x4a3d14,_0x5ad6b4,_0x33097f,_0x291b3f,_0x5d5e32,_0x452734){const _0x54ef57=0x1-_0x4a3d14,_0x2df3e3=0x1-_0x5ad6b4,_0x267562=_0x54ef57*_0x2df3e3,_0x33d2dc=_0x4a3d14*_0x2df3e3,_0x33b759=_0x54ef57*_0x5ad6b4,_0x1d59f6=_0x4a3d14*_0x5ad6b4,_0x4ce742=_0x33097f[0x0]*_0x267562+_0x291b3f[0x0]*_0x33d2dc+_0x5d5e32[0x0]*_0x33b759+_0x452734[0x0]*_0x1d59f6,_0x18ea25=_0x33097f[0x1]*_0x267562+_0x291b3f[0x1]*_0x33d2dc+_0x5d5e32[0x1]*_0x33b759+_0x452734[0x1]*_0x1d59f6;return this['_calcUV'](_0x4ce742,_0x18ea25);}['_calcUV'](_0x439656,_0x4a36bf){return[+_0x439656,+_0x4a36bf,Math['sqrt'](_0x439656*_0x439656+_0x4a36bf*_0x4a36bf)];}[_0x221f23(0x1db)](_0x1a6043,_0x126b05){if(!this['isInExtent'](_0x1a6043,_0x126b05))return null;const _0x1420ad=this['toGridXY'](_0x1a6043,_0x126b05),_0x40a58f=this['getUVByXY'](_0x1420ad[0x0],_0x1420ad[0x1]);return _0x40a58f;}[_0x221f23(0x252)](_0x5b5ea6,_0x4402e1){const _0x3b1805=_0x221f23;return _0x5b5ea6>=this['xmin']&&_0x5b5ea6<=this['xmax']&&_0x4402e1>=this['ymin']&&_0x4402e1<=this[_0x3b1805(0x1b4)]?!![]:![];}['getRandomLatLng'](){const _0x330444=fRandomByfloat(this['xmin'],this['xmax']),_0xa778d0=fRandomByfloat(this['ymin'],this['ymax']);return{'lat':_0xa778d0,'lng':_0x330444};}['getParticles'](){const _0x2ade60=_0x221f23;let _0x4cd126,_0x203063,_0x9b1e90;for(let _0x46f1cd=0x0,_0x9e5bbf=this[_0x2ade60(0x1d3)]['length'];_0x46f1cd<_0x9e5bbf;_0x46f1cd++){let _0x333ece=this[_0x2ade60(0x1d3)][_0x46f1cd];_0x333ece['age']<=0x0&&(_0x333ece=this['_randomParticle'](_0x333ece));if(_0x333ece['age']>0x0){const _0x79befe=_0x333ece['tlng'],_0x559153=_0x333ece['tlat'];_0x9b1e90=this['getUVByPoint'](_0x79befe,_0x559153),_0x9b1e90?(_0x4cd126=_0x79befe+this['_calc_speedRate'][0x0]*_0x9b1e90[0x0],_0x203063=_0x559153+this['_calc_speedRate'][0x1]*_0x9b1e90[0x1],_0x333ece['lng']=_0x79befe,_0x333ece['lat']=_0x559153,_0x333ece['tlng']=_0x4cd126,_0x333ece['tlat']=_0x203063,_0x333ece['speed']=_0x9b1e90[0x2],_0x333ece[_0x2ade60(0x1ae)]--):_0x333ece['age']=0x0;}}return this['particles'];}['_randomParticle'](_0x4f152c){const _0x225ec0=_0x221f23;let _0x22937e,_0x456f75;for(let _0x261605=0x0;_0x261605<0x1e;_0x261605++){_0x22937e=this[_0x225ec0(0x1d1)](),_0x456f75=this['getUVByPoint'](_0x22937e[_0x225ec0(0x259)],_0x22937e['lat']);if(_0x456f75&&_0x456f75[0x2]>0x0)break;}if(!_0x456f75)return _0x4f152c;const _0x49e4c5=_0x22937e[_0x225ec0(0x259)]+this['_calc_speedRate'][0x0]*_0x456f75[0x0],_0x5b0bcf=_0x22937e[_0x225ec0(0x221)]+this['_calc_speedRate'][0x1]*_0x456f75[0x1];return _0x4f152c[_0x225ec0(0x259)]=_0x22937e['lng'],_0x4f152c['lat']=_0x22937e['lat'],_0x4f152c[_0x225ec0(0x192)]=_0x49e4c5,_0x4f152c['tlat']=_0x5b0bcf,_0x4f152c[_0x225ec0(0x1ae)]=Math['round'](0xa+Math[_0x225ec0(0x1ca)]()*this['maxAge']),_0x4f152c['speed']=_0x456f75[0x2],_0x4f152c;}['destroy'](){for(const _0x59480e in this){delete this[_0x59480e];}}}function fRandomByfloat(_0x45ce27,_0x453814){return _0x45ce27+Math['random']()*(_0x453814-_0x45ce27);}const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace['layer']['BaseLayer'];class CanvasWindLayer extends BaseLayer{constructor(_0x472635={}){super(_0x472635),this['_setOptionsHook'](_0x472635),this['canvas']=null,_0x472635['colors']&&_0x472635['steps']&&(this['_colorRamp']=new mars3d__namespace['ColorRamp'](_0x472635));}['_setOptionsHook'](_0x2de849,_0x1643d0){const _0x2ea9ad=_0x221f23;this['frameTime']=0x3e8/(_0x2de849['frameRate']||0xa),this[_0x2ea9ad(0x1e6)]=this['options']['pointerEvents']??![],this['color']=_0x2de849['color']||'#ffffff',this[_0x2ea9ad(0x1e3)]=_0x2de849['lineWidth']||0x1,this['fixedHeight']=_0x2de849[_0x2ea9ad(0x20d)]??0x0,this['flipY']=_0x2de849['flipY']??![],this['windField']&&this['windField'][_0x2ea9ad(0x1f9)](_0x2de849);}get['layer'](){const _0x16392f=_0x221f23;return this[_0x16392f(0x1fc)];}get['canvasWidth'](){return this['_map']['scene']['canvas']['clientWidth'];}get[_0x221f23(0x1dc)](){return this['_map']['scene']['canvas']['clientHeight'];}get[_0x221f23(0x1fa)](){return this['_pointerEvents'];}set[_0x221f23(0x1fa)](_0x6bc6ce){const _0x4979e8=_0x221f23;this[_0x4979e8(0x1e6)]=_0x6bc6ce;if(!this['canvas'])return;_0x6bc6ce?this['canvas']['style'][_0x4979e8(0x1fb)]='all':this['canvas']['style']['pointer-events']='none';}get[_0x221f23(0x20b)](){const _0x44cc0d=_0x221f23;return this[_0x44cc0d(0x1fe)]['particlesNumber'];}set[_0x221f23(0x20b)](_0x18eb6b){this['options']['particlesNumber']=_0x18eb6b,clearTimeout(this['_canrefresh']),this['_canrefresh']=setTimeout(()=>{this['redraw']();},0x1f4);}get[_0x221f23(0x258)](){const _0x21c975=_0x221f23;return this['options'][_0x21c975(0x258)];}set['speedRate'](_0x52be74){const _0x176021=_0x221f23;this['options'][_0x176021(0x258)]=_0x52be74,this['windField']&&(this[_0x176021(0x1c8)][_0x176021(0x258)]=_0x52be74);}get['maxAge'](){return this['options']['maxAge'];}set['maxAge'](_0x36750f){const _0x76d891=_0x221f23;this['options']['maxAge']=_0x36750f,this[_0x76d891(0x1c8)]&&(this['windField'][_0x76d891(0x1af)]=_0x36750f);}get['data'](){return this['windData'];}set['data'](_0xe06bb2){const _0x24556d=_0x221f23;this[_0x24556d(0x189)](_0xe06bb2);}get[_0x221f23(0x1be)](){const _0x727647=_0x221f23;let _0x29bfd0=this['windData']['xmin'],_0x4114fd=this['windData']['xmax'],_0x41aa1d=this[_0x727647(0x229)]['ymin'],_0x3045e6=this['windData']['ymax'];return _0x4114fd>=0x167&&_0x29bfd0===0x0&&(_0x29bfd0=-0xb4,_0x4114fd=0xb4),_0x29bfd0=Math['max'](_0x29bfd0,-0xb4),_0x4114fd=Math[_0x727647(0x24a)](_0x4114fd,0xb4),_0x41aa1d=Math[_0x727647(0x1f4)](_0x41aa1d,-0x5a),_0x3045e6=Math['min'](_0x3045e6,0x5a),Cesium[_0x727647(0x253)][_0x727647(0x1f3)](_0x29bfd0,_0x41aa1d,_0x4114fd,_0x3045e6);}[_0x221f23(0x250)](_0x47500f){const _0x4f03ed=_0x221f23;_0x47500f?this['_addedHook']():(this['windData']&&(this['options']['data']=this[_0x4f03ed(0x229)]),this['_removedHook']());}['_mountedHook'](){const _0xfb8dfe=_0x221f23;this['options']['worker']?this[_0xfb8dfe(0x222)]():this[_0xfb8dfe(0x1c8)]=new CanvasWindField(this['options']);}['_addedHook'](){const _0x385418=_0x221f23;this[_0x385418(0x1fc)]=this['_createCanvas'](),this[_0x385418(0x1c6)]=this['canvas']['getContext']('2d',{'willReadFrequently':!![]}),this[_0x385418(0x1a5)](),this['options']['data']&&this['setData'](this['options']['data']);}['_removedHook'](){const _0x15a0ee=_0x221f23;this['clear'](),this[_0x15a0ee(0x1bf)](),this[_0x15a0ee(0x1fc)]&&(this['_map']['container']['removeChild'](this[_0x15a0ee(0x1fc)]),delete this['canvas']);}['_createCanvas'](){const _0x38a6ad=_0x221f23,_0x48d59e=mars3d__namespace['DomUtil']['create']('canvas','mars3d-canvasWind',this['_map']['container']);return _0x48d59e['style']['position']='absolute',_0x48d59e['style']['top']='0px',_0x48d59e['style']['left']='0px',_0x48d59e[_0x38a6ad(0x1f5)]['width']=this['_map']['scene']['canvas'][_0x38a6ad(0x215)]+'px',_0x48d59e['style']['height']=this['_map'][_0x38a6ad(0x1d2)]['canvas']['clientHeight']+'px',_0x48d59e[_0x38a6ad(0x1f5)][_0x38a6ad(0x1fa)]=this['_pointerEvents']?'auto':'none',_0x48d59e[_0x38a6ad(0x1f5)]['zIndex']=this[_0x38a6ad(0x1fe)]['zIndex']??0x9,_0x48d59e[_0x38a6ad(0x208)]=this[_0x38a6ad(0x224)]['scene']['canvas']['clientWidth'],_0x48d59e[_0x38a6ad(0x1e7)]=this['_map'][_0x38a6ad(0x1d2)]['canvas']['clientHeight'],_0x48d59e;}['resize'](){const _0x125f73=_0x221f23;this['canvas']&&(this['canvas'][_0x125f73(0x1f5)]['width']=this[_0x125f73(0x224)]['scene']['canvas']['clientWidth']+'px',this['canvas']['style']['height']=this['_map']['scene']['canvas']['clientHeight']+'px',this['canvas'][_0x125f73(0x208)]=this['_map'][_0x125f73(0x1d2)][_0x125f73(0x1fc)]['clientWidth'],this[_0x125f73(0x1fc)][_0x125f73(0x1e7)]=this['_map']['scene'][_0x125f73(0x1fc)]['clientHeight']);}['bindEvent'](){const _0x191eba=_0x221f23,_0xbb62ac=this;let _0x173c9d=Date['now']();(function _0x30781e(){const _0x30cf07=_0x2ad5;if(_0xbb62ac[_0x30cf07(0x234)])return;_0xbb62ac[_0x30cf07(0x23e)]=window['requestAnimationFrame'](_0x30781e);if(_0xbb62ac[_0x30cf07(0x1d6)]&&_0xbb62ac['windField']){const _0xe94330=Date['now'](),_0x1066ca=_0xe94330-_0x173c9d;_0x1066ca>_0xbb62ac['frameTime']&&(_0x173c9d=_0xe94330-_0x1066ca%_0xbb62ac[_0x30cf07(0x213)],_0xbb62ac[_0x30cf07(0x18f)]());}}(),window['addEventListener'](_0x191eba(0x19c),this['resize']['bind'](this),![]),this['mouse_down']=![],this['mouse_move']=![],this['options']['mouseHidden']&&(this[_0x191eba(0x224)]['on'](mars3d__namespace[_0x191eba(0x225)]['wheel'],this['_onMapWhellEvent'],this),this[_0x191eba(0x224)]['on'](mars3d__namespace[_0x191eba(0x225)]['mouseDown'],this['_onMouseDownEvent'],this),this['_map']['on'](mars3d__namespace['EventType']['mouseUp'],this['_onMouseUpEvent'],this)));}['unbindEvent'](){const _0x3cf2cb=_0x221f23;window['cancelAnimationFrame'](this['_animateFrame']),delete this[_0x3cf2cb(0x23e)],window['removeEventListener']('resize',this['resize']),this['options']['mouseHidden']&&(this[_0x3cf2cb(0x224)]['off'](mars3d__namespace[_0x3cf2cb(0x225)]['wheel'],this['_onMapWhellEvent'],this),this['_map']['off'](mars3d__namespace['EventType']['mouseDown'],this[_0x3cf2cb(0x261)],this),this['_map']['off'](mars3d__namespace['EventType']['mouseUp'],this['_onMouseUpEvent'],this),this['_map'][_0x3cf2cb(0x1b7)](mars3d__namespace['EventType']['mouseMove'],this[_0x3cf2cb(0x1d4)],this));}[_0x221f23(0x190)](_0x3f0c1a){const _0x2639c9=_0x221f23;clearTimeout(this['refreshTimer']);if(!this['show']||!this['canvas'])return;this['canvas'][_0x2639c9(0x1f5)]['visibility']=_0x2639c9(0x1ff),this['refreshTimer']=setTimeout(()=>{const _0x93c081=_0x2639c9;if(!this['show'])return;this['redraw'](),this['canvas'][_0x93c081(0x1f5)]['visibility']='visible';},0xc8);}[_0x221f23(0x261)](_0x1e72ea){const _0x1a2088=_0x221f23;this['mouse_down']=!![],this['_map']['off'](mars3d__namespace['EventType']['mouseMove'],this['_onMouseMoveEvent'],this),this['_map']['on'](mars3d__namespace['EventType'][_0x1a2088(0x23d)],this['_onMouseMoveEvent'],this);}['_onMouseMoveEvent'](_0x502e7a){const _0xe4d54c=_0x221f23;if(!this[_0xe4d54c(0x1d6)]||!this['canvas'])return;this['mouse_down']&&(this['canvas']['style']['visibility']='hidden',this['mouse_move']=!![]);}['_onMouseUpEvent'](_0x6b648){const _0x4d715b=_0x221f23;if(!this[_0x4d715b(0x1d6)]||!this[_0x4d715b(0x1fc)])return;this['_map'][_0x4d715b(0x1b7)](mars3d__namespace[_0x4d715b(0x225)][_0x4d715b(0x23d)],this['_onMouseMoveEvent'],this),this['mouse_down']&&this['mouse_move']&&this[_0x4d715b(0x1d0)](),this[_0x4d715b(0x1fc)][_0x4d715b(0x1f5)]['visibility']='visible',this['mouse_down']=![],this['mouse_move']=![];}['setData'](_0x4324b2){const _0x1376d9=_0x221f23;this['clear'](),this['windData']=_0x4324b2,this['windField'][_0x1376d9(0x1c5)](_0x4324b2),this[_0x1376d9(0x1d0)]();}['redraw'](){const _0x4e5ede=_0x221f23;if(!this['show'])return;this['windField']['setOptions'](this['options']),this[_0x4e5ede(0x18f)]();}['update'](){const _0x373097=_0x221f23;if(this['_updateIng'])return;this['_updateIng']=!![];if(this[_0x373097(0x245)])this['windField']['update']();else{const _0x366c16=this['windField'][_0x373097(0x22a)]();this['_drawLines'](_0x366c16);}this[_0x373097(0x243)]=![];}['_drawLines'](_0x3aa7fc){const _0x1754eb=_0x221f23;this['_canvasParticles']=_0x3aa7fc,this[_0x1754eb(0x1c6)][_0x1754eb(0x196)]='destination-in',this['canvasContext']['fillRect'](0x0,0x0,this['canvasWidth'],this['canvasHeight']),this['canvasContext'][_0x1754eb(0x196)]=_0x1754eb(0x1e0),this['canvasContext']['globalAlpha']=0.9;const _0x34fc57=this['_map']['scene'][_0x1754eb(0x1dd)]!==Cesium['SceneMode'][_0x1754eb(0x1bb)],_0x1b5cfb=this['canvasWidth']*0.25;if(this[_0x1754eb(0x212)])for(let _0x3bf3f0=0x0,_0x1e9a03=_0x3aa7fc[_0x1754eb(0x1b3)];_0x3bf3f0<_0x1e9a03;_0x3bf3f0++){const _0x4ee017=_0x3aa7fc[_0x3bf3f0],_0x269c2c=this['_tomap'](_0x4ee017,_0x4ee017['lng'],_0x4ee017[_0x1754eb(0x221)],_0x4ee017['alt']),_0x161ece=this['_tomap'](_0x4ee017,_0x4ee017['tlng'],_0x4ee017['tlat'],_0x4ee017['talt']);if(!_0x269c2c||!_0x161ece)continue;if(_0x34fc57&&Math['abs'](_0x269c2c[0x0]-_0x161ece[0x0])>=_0x1b5cfb)continue;this['canvasContext']['beginPath'](),this[_0x1754eb(0x1c6)][_0x1754eb(0x1e3)]=this[_0x1754eb(0x1e3)],this['canvasContext']['strokeStyle']=this['_colorRamp']['getColor'](_0x4ee017[_0x1754eb(0x188)]),this['canvasContext']['moveTo'](_0x269c2c[0x0],_0x269c2c[0x1]),this['canvasContext']['lineTo'](_0x161ece[0x0],_0x161ece[0x1]),this['canvasContext'][_0x1754eb(0x210)]();}else{this['canvasContext']['beginPath'](),this['canvasContext'][_0x1754eb(0x1e3)]=this[_0x1754eb(0x1e3)],this['canvasContext'][_0x1754eb(0x205)]=this['color'];for(let _0x52ad29=0x0,_0xfd0b3f=_0x3aa7fc['length'];_0x52ad29<_0xfd0b3f;_0x52ad29++){const _0x5e7f7c=_0x3aa7fc[_0x52ad29],_0x3c4ed5=this['_tomap'](_0x5e7f7c,_0x5e7f7c['lng'],_0x5e7f7c['lat'],_0x5e7f7c['alt']),_0x3c16f0=this['_tomap'](_0x5e7f7c,_0x5e7f7c['tlng'],_0x5e7f7c['tlat'],_0x5e7f7c['talt']);if(!_0x3c4ed5||!_0x3c16f0)continue;if(_0x34fc57&&Math['abs'](_0x3c4ed5[0x0]-_0x3c16f0[0x0])>=_0x1b5cfb)continue;this['canvasContext']['moveTo'](_0x3c4ed5[0x0],_0x3c4ed5[0x1]),this[_0x1754eb(0x1c6)]['lineTo'](_0x3c16f0[0x0],_0x3c16f0[0x1]);}this['canvasContext'][_0x1754eb(0x210)]();}}['_tomap'](_0x2b1d73,_0x24bd7d,_0x5dd5ed,_0x58c675){const _0x3e72d4=_0x221f23,_0x545005=Cesium['Cartesian3']['fromDegrees'](_0x24bd7d,_0x5dd5ed,_0x58c675??this[_0x3e72d4(0x20d)]),_0x38df62=this['_map'][_0x3e72d4(0x1d2)];if(_0x38df62[_0x3e72d4(0x1dd)]===Cesium['SceneMode']['SCENE3D']){const _0x3644d3=new Cesium[(_0x3e72d4(0x207))](_0x38df62[_0x3e72d4(0x1ee)]['ellipsoid'],_0x38df62[_0x3e72d4(0x21e)][_0x3e72d4(0x202)]),_0x3f7b11=_0x3644d3['isPointVisible'](_0x545005);if(!_0x3f7b11)return _0x2b1d73[_0x3e72d4(0x1ae)]=0x0,null;}const _0x4434a4=mars3d__namespace['PointTrans']['toWindowCoordinates'](this['_map']['scene'],_0x545005);return _0x4434a4?[_0x4434a4['x'],_0x4434a4['y']]:null;}['clear'](){const _0x584523=_0x221f23;this[_0x584523(0x1c8)]['clear'](),delete this['windData'];}[_0x221f23(0x222)](){const _0x1595b3=_0x221f23;this[_0x1595b3(0x245)]=new Worker(this['options']['worker']),this['worker']['onmessage']=_0x2399e5=>{this['_drawLines'](_0x2399e5['data']['particles']),this['_updateIng2']=![];},this[_0x1595b3(0x1c8)]={'init':_0x151993=>{this['worker']['postMessage']({'type':'init','options':_0x151993});},'setOptions':_0x338178=>{const _0x4165d5=_0x1595b3;this['worker'][_0x4165d5(0x248)]({'type':'setOptions','options':_0x338178});},'setDate':_0x1298bc=>{this['worker']['postMessage']({'type':'setDate','data':_0x1298bc});},'update':()=>{const _0x4faebd=_0x1595b3;if(this[_0x4faebd(0x200)])return;this['_updateIng2']=!![],this['worker'][_0x4faebd(0x248)]({'type':'update'});},'clear':()=>{const _0x445ae7=_0x1595b3;this[_0x445ae7(0x245)]['postMessage']({'type':_0x445ae7(0x1b1)});}},this['windField']['init'](this['options']);}}mars3d__namespace['LayerUtil'][_0x221f23(0x1f0)]('canvasWind',CanvasWindLayer),mars3d__namespace['layer'][_0x221f23(0x20c)]=CanvasWindLayer,mars3d__namespace['CanvasWindField']=CanvasWindField,mars3d__namespace['Log'][_0x221f23(0x23c)]('mars3d-wind插件\x20注册成功'),mars3d__namespace[_0x221f23(0x240)]=WindUtil,exports[_0x221f23(0x22d)]=CanvasWindField,exports['CanvasWindLayer']=CanvasWindLayer,exports[_0x221f23(0x18b)]=WindLayer,exports[_0x221f23(0x240)]=WindUtil,Object[_0x221f23(0x223)](exports,'__esModule',{'value':!![]});
}));
