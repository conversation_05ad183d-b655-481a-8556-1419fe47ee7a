/**
 * Mars3D平台插件,结合echarts可视化功能插件  mars3d-echarts
 *
 * 版本信息：v3.9.12
 * 编译日期：2025-06-24 17:13
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：成都潘朵拉科技有限公司 ，2025-06-24
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d')), (window.echarts || require('echarts'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mars3d', 'echarts'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-echarts"] = {}, global.mars3d, global.echarts));
})(this, (function (exports, mars3d, echarts) { 
'use strict';const _0x5932c0=_0x71fe;(function(_0x140a61,_0x588680){const _0x2bf51d={_0x1ddb15:0x1e2,_0x530c01:0x1fd,_0x805e24:0x1bb,_0xba6d54:0x1b5,_0x5c2a9b:0x1fb,_0x348928:0x1f7,_0x2451e8:0x1c7,_0x151563:0x1c1,_0x3085b0:0x1d8},_0x2aee6c=_0x71fe,_0x1bd0ba=_0x140a61();while(!![]){try{const _0x1258ad=-parseInt(_0x2aee6c(0x1e1))/0x1*(-parseInt(_0x2aee6c(_0x2bf51d._0x1ddb15))/0x2)+parseInt(_0x2aee6c(0x1be))/0x3+parseInt(_0x2aee6c(_0x2bf51d._0x530c01))/0x4*(-parseInt(_0x2aee6c(_0x2bf51d._0x805e24))/0x5)+parseInt(_0x2aee6c(_0x2bf51d._0xba6d54))/0x6+parseInt(_0x2aee6c(_0x2bf51d._0x5c2a9b))/0x7+parseInt(_0x2aee6c(_0x2bf51d._0x348928))/0x8*(parseInt(_0x2aee6c(_0x2bf51d._0x2451e8))/0x9)+-parseInt(_0x2aee6c(_0x2bf51d._0x151563))/0xa*(parseInt(_0x2aee6c(_0x2bf51d._0x3085b0))/0xb);if(_0x1258ad===_0x588680)break;else _0x1bd0ba['push'](_0x1bd0ba['shift']());}catch(_0x3d24bd){_0x1bd0ba['push'](_0x1bd0ba['shift']());}}}(_0x55ab,0x7333e));function _0x55ab(){const _0x1f3a75=['registerAction','mode','height','default','mars3d-echarts','off','Util','min','DomUtil','ecInstance','Rectangle','_echartsContainer','5234647wjdKAv','0px','setOption','_setOptionsHook','fixedHeight','container','Cartesian3','clientHeight','_map','2TCvfYG','50430JuGwbo','clampToGround','removeChild','eventParent','left','getHeight','scheduler','create','zIndex','_createChartOverlay','style','dataToPoint','_echartsInstance','options','_mars3d_scene','ellipsoid','keys','visibility','mars3dMapRoam','get','forEach','328GvEYUX','isArray','positionWC','getRoamTransform','2938495HyuORs','eachComponent','4AKfsdW','dimensions','_pointerEvents','getRectangle','none','position','5215878kyJeHX','EchartsLayer','addEventListener','echartsAutoHeight','width','Log','202845Iholvg','init','mars3dMap','274950KxfVAt','removeEventListener','coords','30BpKyYI','scene','_api','lng','getZr','visible','111708wvOxOL','echarts','moveHandler','eachSeries','coordinateSystem'];_0x55ab=function(){return _0x1f3a75;};return _0x55ab();}function _interopNamespace(_0xeb74bf){const _0x57dc08={_0x9523d1:0x1f2,_0x162544:0x1cf},_0x42a4f8=_0x71fe;if(_0xeb74bf&&_0xeb74bf['__esModule'])return _0xeb74bf;var _0x1de4e1=Object['create'](null);return _0xeb74bf&&Object[_0x42a4f8(_0x57dc08._0x9523d1)](_0xeb74bf)['forEach'](function(_0x4992e0){const _0x966a48=_0x42a4f8;if(_0x4992e0!=='default'){var _0x51258c=Object['getOwnPropertyDescriptor'](_0xeb74bf,_0x4992e0);Object['defineProperty'](_0x1de4e1,_0x4992e0,_0x51258c[_0x966a48(0x1f5)]?_0x51258c:{'enumerable':!![],'get':function(){return _0xeb74bf[_0x4992e0];}});}}),_0x1de4e1[_0x42a4f8(_0x57dc08._0x162544)]=_0xeb74bf,_0x1de4e1;}var mars3d__namespace=_interopNamespace(mars3d),echarts__namespace=_interopNamespace(echarts);function _0x71fe(_0x522ff1,_0x478e5d){const _0x55abec=_0x55ab();return _0x71fe=function(_0x189fe0,_0x357e52){_0x189fe0=_0x189fe0-0x1b2;let _0x25ea44=_0x55abec[_0x189fe0];return _0x25ea44;},_0x71fe(_0x522ff1,_0x478e5d);}const Cesium$1=mars3d__namespace['Cesium'];class CompositeCoordinateSystem{constructor(_0xcf8676,_0x18b5c2){const _0x11f30d=_0x71fe;this['_mars3d_scene']=_0xcf8676,this[_0x11f30d(0x1fe)]=[_0x11f30d(0x1c4),'lat'],this['_mapOffset']=[0x0,0x0],this['_api']=_0x18b5c2;}['setMapOffset'](_0x22a569){this['_mapOffset']=_0x22a569;}['getBMap'](){return this['_mars3d_scene'];}[_0x5932c0(0x1ed)](_0x5ac855){const _0x49ef99={_0x5d9868:0x1f0,_0x4a9047:0x1b8,_0xb747a0:0x1f1},_0x2758d6=_0x5932c0,_0x26aa1b=this[_0x2758d6(_0x49ef99._0x5d9868)],_0x3ef35e=[NaN,NaN];let _0x51d274=_0x26aa1b['echartsFixedHeight'];_0x26aa1b[_0x2758d6(_0x49ef99._0x4a9047)]&&(_0x51d274=_0x26aa1b['getHeight'](Cesium$1['Cartographic']['fromDegrees'](_0x5ac855[0x0],_0x5ac855[0x1])));const _0x5eb3f9=Cesium$1[_0x2758d6(0x1de)]['fromDegrees'](_0x5ac855[0x0],_0x5ac855[0x1],_0x51d274);if(!_0x5eb3f9)return _0x3ef35e;const _0x5308f3=mars3d__namespace['PointTrans']['toWindowCoordinates'](_0x26aa1b,_0x5eb3f9);if(!_0x5308f3)return _0x3ef35e;if(_0x26aa1b['echartsDepthTest']&&_0x26aa1b[_0x2758d6(0x1cd)]===Cesium$1['SceneMode']['SCENE3D']){const _0x480d4a=new Cesium$1['EllipsoidalOccluder'](_0x26aa1b['globe'][_0x2758d6(_0x49ef99._0xb747a0)],_0x26aa1b['camera'][_0x2758d6(0x1f9)]),_0x26223a=_0x480d4a['isPointVisible'](_0x5eb3f9);if(!_0x26223a)return _0x3ef35e;}return[_0x5308f3['x']-this['_mapOffset'][0x0],_0x5308f3['y']-this['_mapOffset'][0x1]];}['getViewRect'](){const _0x2d6d4b=_0x5932c0,_0x1dd413=this[_0x2d6d4b(0x1c3)];return new echarts__namespace['graphic']['BoundingRect'](0x0,0x0,_0x1dd413['getWidth'](),_0x1dd413[_0x2d6d4b(0x1e7)]());}[_0x5932c0(0x1fa)](){return echarts__namespace['matrix']['create']();}}CompositeCoordinateSystem['dimensions']=['lng','lat'],CompositeCoordinateSystem['create']=function(_0xd7c480,_0x528cf2){const _0x5d7b1a={_0x569c23:0x1d5,_0x132cd3:0x1fc},_0x33d639={_0x5b1d05:0x1c5},_0x360629=_0x5932c0;let _0x4421f9;const _0xfdc7d1=_0xd7c480['scheduler'][_0x360629(_0x5d7b1a._0x569c23)][_0x360629(0x1f0)];_0xd7c480[_0x360629(_0x5d7b1a._0x132cd3)](_0x360629(0x1bd),function(_0x6207cc){const _0x30ec1b=_0x360629,_0xc11356=_0x528cf2[_0x30ec1b(_0x33d639._0x5b1d05)]()['painter'];if(!_0xc11356)return;!_0x4421f9&&(_0x4421f9=new CompositeCoordinateSystem(_0xfdc7d1,_0x528cf2)),_0x6207cc['coordinateSystem']=_0x4421f9,_0x4421f9['setMapOffset'](_0x6207cc['__mapOffset']||[0x0,0x0]);}),_0xd7c480[_0x360629(0x1ca)](function(_0x222749){const _0x5b3b22=_0x360629;_0x222749[_0x5b3b22(0x1f5)](_0x5b3b22(0x1cb))==='mars3dMap'&&(!_0x4421f9&&(_0x4421f9=new CompositeCoordinateSystem(_0xfdc7d1,_0x528cf2)),_0x222749['coordinateSystem']=_0x4421f9);});};if(echarts__namespace!==null&&echarts__namespace!==void 0x0&&echarts__namespace[_0x5932c0(0x1bc)])echarts__namespace['registerCoordinateSystem']('mars3dMap',CompositeCoordinateSystem),echarts__namespace[_0x5932c0(0x1cc)]({'type':'mars3dMapRoam','event':'mars3dMapRoam','update':'updateLayout'},function(_0x4f463d,_0x38db99){}),echarts__namespace['extendComponentModel']({'type':'mars3dMap','getBMap':function(){return this['_mars3d_scene'];},'defaultOption':{'roam':![]}}),echarts__namespace['extendComponentView']({'type':_0x5932c0(0x1bd),'init':function(_0x12aa5b,_0xd7df51){const _0x52a2dc={_0x53e201:0x1b7},_0x23ac17=_0x5932c0;this['api']=_0xd7df51,this['scene']=_0x12aa5b[_0x23ac17(0x1e8)]['ecInstance']['_mars3d_scene'],this['scene']['postRender'][_0x23ac17(_0x52a2dc._0x53e201)](this['moveHandler'],this);},'moveHandler':function(_0x266dcb,_0x1c191f){const _0x3e901d=_0x5932c0;this['api']['dispatchAction']({'type':_0x3e901d(0x1f4)});},'render':function(_0x3ec3cb,_0xd7369f,_0x5aef2e){},'dispose':function(_0xb200cb){const _0x1cf840={_0x13c526:0x1c2,_0x45fc08:0x1c9},_0x576907=_0x5932c0;this[_0x576907(_0x1cf840._0x13c526)]['postRender'][_0x576907(0x1bf)](this[_0x576907(_0x1cf840._0x45fc08)],this);}});else throw new Error('请引入\x20echarts\x20库\x20');const Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace['layer']['BaseLayer'];class EchartsLayer extends BaseLayer{constructor(_0xeeae0c={}){super(_0xeeae0c),this['_pointerEvents']=this['options']['pointerEvents'];}get['layer'](){const _0x407698=_0x5932c0;return this[_0x407698(0x1ee)];}get['pointerEvents'](){return this['_pointerEvents'];}set['pointerEvents'](_0x26919e){const _0x389ab6=_0x5932c0;this[_0x389ab6(0x1ff)]=_0x26919e,this[_0x389ab6(0x1d7)]&&(_0x26919e?this['_echartsContainer']['style']['pointerEvents']='all':this['_echartsContainer']['style']['pointerEvents']='none');}[_0x5932c0(0x1db)](_0xf42f0d,_0xa9e5cf){this['setEchartsOption'](_0xf42f0d);}['_showHook'](_0x416e85){const _0x278a7e={_0x50ce86:0x1c6},_0x4768c7=_0x5932c0;_0x416e85?this['_echartsContainer']['style'][_0x4768c7(0x1f3)]=_0x4768c7(_0x278a7e._0x50ce86):this['_echartsContainer'][_0x4768c7(0x1ec)]['visibility']='hidden';}['_mountedHook'](){const _0x23781a={_0xb96dc4:0x1b8,_0x4d6eb4:0x1e0,_0x3f91f6:0x1dc},_0x2aa1c0=_0x5932c0;this['_map']['scene']['echartsDepthTest']=this['options']['depthTest']??!![],this[_0x2aa1c0(0x1e0)]['scene'][_0x2aa1c0(_0x23781a._0xb96dc4)]=this[_0x2aa1c0(0x1ef)][_0x2aa1c0(0x1e3)]??![],this[_0x2aa1c0(_0x23781a._0x4d6eb4)]['scene']['echartsFixedHeight']=this['options'][_0x2aa1c0(_0x23781a._0x3f91f6)]??0x0;}['_addedHook'](){const _0x361d68={_0x509776:0x1eb,_0x3689c4:0x1ee,_0x4dbe92:0x1ef},_0x21ad5a=_0x5932c0;this['_echartsContainer']=this[_0x21ad5a(_0x361d68._0x509776)](),this['_echartsInstance']=echarts__namespace[_0x21ad5a(0x1bc)](this['_echartsContainer']),this[_0x21ad5a(_0x361d68._0x3689c4)]['_mars3d_scene']=this[_0x21ad5a(0x1e0)]['scene'],this['setEchartsOption'](this[_0x21ad5a(_0x361d68._0x4dbe92)]);}['_removedHook'](){const _0x15e7ad={_0x5d0f7a:0x1d7},_0x1bfd6a=_0x5932c0;this['_echartsInstance']&&(this['_echartsInstance']['clear'](),this['_echartsInstance']['dispose'](),delete this[_0x1bfd6a(0x1ee)]),this[_0x1bfd6a(0x1d7)]&&(this['_map']['container'][_0x1bfd6a(0x1e4)](this['_echartsContainer']),delete this[_0x1bfd6a(_0x15e7ad._0x5d0f7a)]);}['_createChartOverlay'](){const _0x5a29e4={_0xd3dc0b:0x1e9,_0x12352e:0x1d0,_0x1abee4:0x1dd,_0x49276f:0x1ec,_0x5020bb:0x1ff,_0x3769ab:0x1ef},_0x113f8d=_0x5932c0,_0x23d175=mars3d__namespace[_0x113f8d(0x1d4)][_0x113f8d(_0x5a29e4._0xd3dc0b)]('div',_0x113f8d(_0x5a29e4._0x12352e),this['_map'][_0x113f8d(_0x5a29e4._0x1abee4)]);return _0x23d175['id']=this['id'],_0x23d175[_0x113f8d(0x1ec)][_0x113f8d(0x1b4)]='absolute',_0x23d175['style']['top']=_0x113f8d(0x1d9),_0x23d175['style'][_0x113f8d(0x1e6)]='0px',_0x23d175[_0x113f8d(0x1ec)][_0x113f8d(0x1b9)]=this['_map']['scene']['canvas']['clientWidth']+'px',_0x23d175[_0x113f8d(_0x5a29e4._0x49276f)][_0x113f8d(0x1ce)]=this['_map']['scene']['canvas']['clientHeight']+'px',_0x23d175['style']['pointerEvents']=this[_0x113f8d(_0x5a29e4._0x5020bb)]?'all':_0x113f8d(0x1b3),_0x23d175[_0x113f8d(0x1ec)][_0x113f8d(0x1ea)]=this[_0x113f8d(_0x5a29e4._0x3769ab)]['zIndex']??0x9,_0x23d175;}['resize'](){const _0x192b28={_0x2f3a0f:0x1ce},_0x37acbc=_0x5932c0;if(!this['_echartsInstance'])return;this['_echartsContainer']['style']['width']=this['_map']['scene']['canvas']['clientWidth']+'px',this['_echartsContainer']['style'][_0x37acbc(_0x192b28._0x2f3a0f)]=this[_0x37acbc(0x1e0)]['scene']['canvas'][_0x37acbc(0x1df)]+'px',this['_echartsInstance']['resize']();}['setEchartsOption'](_0x17e69b,_0x5d523b,_0x31a15b){const _0x19c575={_0x2686a6:0x1d2,_0x47e0b9:0x1da},_0x13fdae=_0x5932c0;this['_echartsInstance']&&(_0x17e69b={'mars3dMap':{},...mars3d__namespace[_0x13fdae(_0x19c575._0x2686a6)]['getAttrVal'](_0x17e69b,{'onlySimpleType':!![]})},delete _0x17e69b[_0x13fdae(0x1e5)],this['_echartsInstance'][_0x13fdae(_0x19c575._0x47e0b9)](_0x17e69b,_0x5d523b,_0x31a15b));}[_0x5932c0(0x1b2)](_0x13812f){const _0x2034ab={_0x43d06e:0x1ef,_0x55b6ef:0x1d6},_0x55bc11={_0x28bf4e:0x1f6},_0xdc62d9={_0xedab86:0x1c0},_0xaba2d0={_0x3f4dd3:0x1d3},_0x34bf76=_0x5932c0;let _0x3dd6e5,_0x6e5989,_0x349894,_0x690342;function _0x18c25c(_0x222eb3){const _0x1e2dcb=_0x71fe;if(!Array[_0x1e2dcb(0x1f8)](_0x222eb3))return;const _0x27a502=_0x222eb3[0x0]||0x0,_0x26c409=_0x222eb3[0x1]||0x0;_0x27a502!==0x0&&_0x26c409!==0x0&&(_0x3dd6e5===undefined?(_0x3dd6e5=_0x27a502,_0x6e5989=_0x27a502,_0x349894=_0x26c409,_0x690342=_0x26c409):(_0x3dd6e5=Math[_0x1e2dcb(_0xaba2d0._0x3f4dd3)](_0x3dd6e5,_0x27a502),_0x6e5989=Math['max'](_0x6e5989,_0x27a502),_0x349894=Math[_0x1e2dcb(0x1d3)](_0x349894,_0x26c409),_0x690342=Math['max'](_0x690342,_0x26c409)));}const _0x226b7e=this[_0x34bf76(_0x2034ab._0x43d06e)]['series'];_0x226b7e&&_0x226b7e['forEach'](_0x42a0be=>{const _0x1e51cf=_0x34bf76;_0x42a0be['data']&&_0x42a0be['data'][_0x1e51cf(_0x55bc11._0x28bf4e)](_0x15a859=>{const _0x211599=_0x1e51cf;if(_0x15a859['value'])_0x18c25c(_0x15a859['value']);else _0x15a859[_0x211599(_0xdc62d9._0xedab86)]&&_0x15a859[_0x211599(_0xdc62d9._0xedab86)]['forEach'](_0x5b83d6=>{_0x18c25c(_0x5b83d6);});});});if(_0x3dd6e5===0x0&&_0x349894===0x0&&_0x6e5989===0x0&&_0x690342===0x0)return null;return _0x13812f!==null&&_0x13812f!==void 0x0&&_0x13812f['isFormat']?{'xmin':_0x3dd6e5,'xmax':_0x6e5989,'ymin':_0x349894,'ymax':_0x690342}:Cesium[_0x34bf76(_0x2034ab._0x55b6ef)]['fromDegrees'](_0x3dd6e5,_0x349894,_0x6e5989,_0x690342);}['on'](_0x5c9f38,_0x29a1a2,_0x4c0ed4){return this['_echartsInstance']['on'](_0x5c9f38,_0x29a1a2,_0x4c0ed4||this),this;}['onByQuery'](_0x20bc2c,_0x10c352,_0x49bf30,_0x43589a){return this['_echartsInstance']['on'](_0x20bc2c,_0x10c352,_0x49bf30,_0x43589a||this),this;}['off'](_0x3af997,_0x253181,_0xb64a85){const _0x4006de=_0x5932c0;return this['_echartsInstance'][_0x4006de(0x1d1)](_0x3af997,_0x253181,_0xb64a85||this),this;}}mars3d__namespace['LayerUtil']['register']('echarts',EchartsLayer),mars3d__namespace['layer'][_0x5932c0(0x1b6)]=EchartsLayer,mars3d__namespace[_0x5932c0(0x1c8)]=echarts__namespace,mars3d__namespace[_0x5932c0(0x1ba)]['logInfo']('mars3d-echarts插件\x20注册成功'),exports[_0x5932c0(0x1b6)]=EchartsLayer,Object['keys'](echarts)[_0x5932c0(0x1f6)](function(_0x2ba953){if(_0x2ba953!=='default'&&!exports['hasOwnProperty'](_0x2ba953))Object['defineProperty'](exports,_0x2ba953,{'enumerable':!![],'get':function(){return echarts[_0x2ba953];}});}),Object['defineProperty'](exports,'__esModule',{'value':!![]});
}));
