// 清理localStorage中的无效数据
export const cleanInvalidStorage = () => {
  const keys = ['token', 'userInfo', 'communityUserInfo']
  
  keys.forEach(key => {
    const value = localStorage.getItem(key)
    if (value === 'undefined' || value === 'null' || value === '') {
      localStorage.removeItem(key)
      console.log(`清理无效的localStorage数据: ${key}`)
    }
  })
}

// 安全地获取localStorage数据
export const safeGetStorage = (key: string, defaultValue: any = null) => {
  try {
    const value = localStorage.getItem(key)
    if (!value || value === 'undefined' || value === 'null') {
      return defaultValue
    }
    return JSON.parse(value)
  } catch (error) {
    console.error(`解析localStorage数据失败: ${key}`, error)
    localStorage.removeItem(key)
    return defaultValue
  }
}

// 安全地设置localStorage数据
export const safeSetStorage = (key: string, value: any) => {
  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error(`设置localStorage数据失败: ${key}`, error)
  }
} 