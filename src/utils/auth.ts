// 认证相关工具函数

// 获取Authorization header值
export const getAuthHeader = (token: string): string => {
  // 如果token已经包含Bearer前缀，直接返回
  if (token.toLowerCase().startsWith('bearer ')) {
    return token
  }
  
  // 否则添加Bearer前缀（标准JWT格式）
  return `Bearer ${token}`
}

// 从localStorage安全获取token
export const getToken = (): string => {
  const token = localStorage.getItem('token')
  if (!token || token === 'undefined' || token === 'null') {
    return ''
  }
  return token
}

// 检查token是否有效
export const isTokenValid = (token: string): boolean => {
  if (!token || token === 'undefined' || token === 'null') {
    console.log('Token无效: 空token或无效值')
    return false
  }
  
  // 如果是JWT token，可以检查是否过期
  try {
    if (token.includes('.') && token.split('.').length === 3) {
      const payload = JSON.parse(atob(token.split('.')[1]))
      const now = Math.floor(Date.now() / 1000)
      const isExpired = payload.exp && payload.exp < now
      console.log('JWT Token验证:', {
        hasExp: !!payload.exp,
        exp: payload.exp,
        now: now,
        isExpired: isExpired
      })
      return !isExpired
    }
  } catch (error) {
    console.warn('Token格式检查失败:', error)
    // JWT解析失败，但token存在，仍然返回true
    console.log('JWT解析失败，但token存在，视为有效')
    return true
  }
  
  // 对于非JWT token，只要不为空就认为有效
  console.log('非JWT token，视为有效')
  return true
}

// 清理所有认证信息
export const clearAuthData = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
  localStorage.removeItem('communityUserInfo')
  console.log('已清理所有认证数据')
} 