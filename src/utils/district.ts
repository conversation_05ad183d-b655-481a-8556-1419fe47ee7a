// 获取用户的区域代码
export const getDistrictCode = (): string => {
  try {
    const communityUserInfo = localStorage.getItem('communityUserInfo')
    if (communityUserInfo) {
      const userInfo = JSON.parse(communityUserInfo)
      return userInfo.districtCode || '510183' // 默认返回邛崃市代码
    }
    return '510183' // 默认返回邛崃市代码
  } catch (error) {
    console.error('获取区域代码失败:', error)
    return '510183' // 默认返回邛崃市代码
  }
}

// 根据时间范围获取日期范围
export const getDateRange = (timeRange: string) => {
  const now = new Date()
  let startDate = new Date()
  
  switch (timeRange) {
    case '0': // 当天
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      break
    case '1': // 一周
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case '2': // 近三月
      startDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate())
      break
    case '3': // 近六月
      startDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate())
      break
    case '4': // 近一年
      startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
      break
    default:
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  }
  
  return {
    DateRangeMin: startDate.toISOString().split('T')[0],
    DateRangeMax: now.toISOString().split('T')[0]
  }
} 