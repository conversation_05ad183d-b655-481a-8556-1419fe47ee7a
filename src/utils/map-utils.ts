import "mars3d-cesium/Build/Cesium/Widgets/widgets.css"
import "mars3d/mars3d.css"
import * as mars3d from "mars3d"
import { simplify } from "@turf/turf"

// 类型定义
export interface PointData {
  id: number
  title: string
  position: [number, number, number]
}

export interface HeatMapData {
  districtName: string
  districtCode: string
  longitude: string
  latitude: string
  count: number
}

export interface MapConfig {
  containerId: string
  backgroundColor?: string
  basemaps?: any[]
  onAreaClick?: (data: any) => void
  onPointClick?: (data: PointData) => void
}

export class MapUtils {
  private map: mars3d.Map | null = null
  private graphicLayer: mars3d.layer.GraphicLayer | null = null
  private geoJsonLayer: mars3d.layer.GeoJsonLayer | null = null
  private heatMapLayer: mars3d.layer.GraphicLayer | null = null
  private eventTarget = new mars3d.BaseClass()
  private clickDataArr: any[] = []
  private infoArr: any[] = []
  private viewHeightLevel = 0
  private viewHeightArr = [24421.4, 12000, 6000, 3000] // 调整高度到合适的显示大小
  private parentCode: string = ""
  private parentHeightCenter: any
  private config: MapConfig
  private currentLayerType: string = 'town' // 当前图层类型
  private layerMappings = {
    district: '5e249977ce1b454eab17999b9122871e/0/query',
    town: '8780d3715f054a0b948bb3a744d8ba34/0/query',
    village: '5f651de6590d4583ad2943027c86cb9e/0/query',
    grid: '61e0f08c05244354bd6baeaa5cec4bb9/0/query'
  }

  constructor(config: MapConfig) {
    this.config = config
  }

  // 初始化地图
  async initMap(): Promise<mars3d.Map> {
    const color = this.config.backgroundColor || "#02162d"
    
    this.map = new mars3d.Map(this.config.containerId, {
      scene: {
        showSun: false,
        showMoon: false,
        showSkyBox: false,
        showSkyAtmosphere: false,
        fog: false,
        backgroundColor: color,
        globe: {
          baseColor: color,
          showGroundAtmosphere: false,
          enableLighting: false
        }
      },
      control: {
        baseLayerPicker: false,
        selectionIndicator: false, // 禁用选中指示器
        infoBox: false // 禁用信息框
      },
      basemaps: this.config.basemaps || [
        {
          "name": "邛崃市",
          "type": "arcgis",
          "url": "https://api.cdmbc.cn:4432/gateway/gis/1/2e73507f614948998198c0a1cb47c5ea",
          "queryParameters": { "blankTile": false, "AppKey": "700023938926772224" },
          "crs": "EPSG:4326", 
          "show": true
        } 
      ],
    })

    // 创建图层
    this.graphicLayer = new mars3d.layer.GraphicLayer()
    this.map.addLayer(this.graphicLayer)

    // 创建热力图图层
    this.heatMapLayer = new mars3d.layer.GraphicLayer()
    this.map.addLayer(this.heatMapLayer)

    // 绑定事件
    this.eventTarget.on("getInfo", (info) => {
      if (this.config.onAreaClick) {
        this.config.onAreaClick(info)
      }
    })

    return this.map
  }

  // ESRI格式转GeoJSON
  private esriToGeoJSON(esriJson: any) {
    const features = esriJson.features
      .filter((f: any) => f.geometry && f.geometry.rings)
      .map((f: any) => {
        return {
          type: "Feature",
          geometry: {
            type: "Polygon",
            coordinates: f.geometry.rings,
          },
          properties: f.attributes,
        }
      })

    return {
      type: "FeatureCollection",
      features,
    }
  }

  // 加载行政区划层
  async loadAdministrativeLayer(geoId: string, localData?: any, layerType?: string) {
    if (!this.map) throw new Error("地图未初始化")

    let simplifiedGeojson

    if (localData) {
      simplifiedGeojson = localData
    } else {
      const url = `https://api.cdmbc.cn:4432/gateway/gis/1/${geoId}`
      const params = {
        AppKey: "700023938926772224",
        where: "1=1",
        outFields: "*",
        returnGeometry: "true",
        f: "json",
      }

      const queryUrl = `${url}?${new URLSearchParams(params).toString()}`
      const response = await fetch(queryUrl)
      const geojsonData = await response.json()

      const safeGeojson = this.esriToGeoJSON(geojsonData)
      simplifiedGeojson = simplify(safeGeojson, {
        tolerance: 0.0001,
        highQuality: true,
        mutate: true,
      })
    }

    // 销毁旧图层
    if (this.geoJsonLayer) {
      this.map.removeLayer(this.geoJsonLayer, true)
      this.geoJsonLayer = null
    }

    // 创建新图层
    this.geoJsonLayer = new mars3d.layer.GeoJsonLayer({
      data: simplifiedGeojson,
      symbol: {
        type: "polygonP",
        styleOptions: {
          fill: true,
          color: "#37e8f1",
          opacity: 0.45,
          outline: true,
          label: {
            text: "{town}",
            opacity: 1,
            font_size: 20,
            color: "#fcc31f",
            font_family: "楷体",
            outline: true,
            outlineColor: "#000000",
            outlineWidth: 3,
            background: false,
            backgroundColor: "#000000",
            backgroundOpacity: 0.1,
            font_weight: "normal",
            font_style: "normal",
            scaleByDistance: true,
            scaleByDistance_far: 20000000,
            scaleByDistance_farValue: 0.1,
            scaleByDistance_near: 1000,
            scaleByDistance_nearValue: 1,
            distanceDisplayCondition: false,
            distanceDisplayCondition_far: 10000,
            distanceDisplayCondition_near: 0,
            visibleDepth: false,
          },
          outlineStyle: {
            color: "#6bcfd4",
            width: 2,
            opacity: 1,
          },
          highlight: {
            opacity: 0.45, // 与正常状态保持一致
            outlineStyle: {
              color: "#6bcfd4", // 与正常状态保持一致
              width: 2, // 与正常状态保持一致
              opacity: 1.0,
              addHeight: 0, // 不添加高度
            },
          },
        },
      },
      flyTo: true,
    })

    // 绑定点击事件
    this.geoJsonLayer.on(mars3d.EventType.click, async (event) => {
      const graphic = event.graphic
      const attr = graphic.attr
      
      this.eventTarget.fire("getInfo", attr)
      
      this.parentCode = attr.village || attr.town_code
      const point = mars3d.LngLatPoint.fromCartesian(graphic.center)
      this.parentHeightCenter = point

      this.clickDataArr.push({
        parentCode: this.parentCode,
        parentHeightCenter: this.parentHeightCenter,
      })
      
      this.infoArr.push({ info: attr })
      
      // 层级钻取逻辑
      if (this.currentLayerType === 'town') {
        // 如果当前是乡镇界，点击后加载村社区界
        console.log(`🔍 点击了乡镇: ${attr.town || attr.name}, 准备加载村社区界...`)
        await this.drillDownToVillage(attr)
      }
      
      // 使用图形中心点而不是点击位置
      this.clickSetView(graphic)
    })

    this.map.addLayer(this.geoJsonLayer)
    
    // 更新当前图层类型
    if (layerType) {
      this.currentLayerType = layerType
    }
    
    return this.geoJsonLayer
  }

  // 钻取到村社区界
  async drillDownToVillage(townAttr: any) {
    try {
      console.log(`🏘️ 开始加载 ${townAttr.town || townAttr.name} 的村社区界数据...`)
      
      // 加载村社区界图层，但需要根据乡镇过滤
      const villageGeoId = this.layerMappings.village
      await this.loadFilteredAdministrativeLayer(villageGeoId, townAttr, 'village')
      
      console.log(`✅ ${townAttr.town || townAttr.name} 村社区界数据加载完成`)
    } catch (error) {
      console.error('❌ 加载村社区界失败:', error)
    }
  }

  // 加载过滤后的行政区划层
  async loadFilteredAdministrativeLayer(geoId: string, parentAttr: any, layerType: string) {
    if (!this.map) throw new Error("地图未初始化")

    try {
      const url = `https://api.cdmbc.cn:4432/gateway/gis/1/${geoId}`
      const townCode = parentAttr.town_code || parentAttr.code || parentAttr.TOWN_CODE
      
      // 构建过滤条件，只获取该乡镇下的村社区
      const params = {
        AppKey: "700023938926772224",
        where: townCode ? `town_code='${townCode}' OR TOWN_CODE='${townCode}'` : "1=1",
        outFields: "*",
        returnGeometry: "true",
        f: "json",
      }

      console.log(`🔍 过滤条件: ${params.where}`)

      const queryUrl = `${url}?${new URLSearchParams(params).toString()}`
      const response = await fetch(queryUrl)
      const geojsonData = await response.json()

      console.log(`📊 获取到 ${geojsonData.features?.length || 0} 个村社区`)

      const safeGeojson = this.esriToGeoJSON(geojsonData)
      const simplifiedGeojson = simplify(safeGeojson, {
        tolerance: 0.0001,
        highQuality: true,
        mutate: true,
      })

      // 销毁旧图层
      if (this.geoJsonLayer) {
        this.map.removeLayer(this.geoJsonLayer, true)
        this.geoJsonLayer = null
      }

      // 创建新图层，调整村社区的显示样式
      this.geoJsonLayer = new mars3d.layer.GeoJsonLayer({
        data: simplifiedGeojson,
        symbol: {
          type: "polygonP",
          styleOptions: {
            fill: true,
            color: "#37e8f1",
            opacity: 0.35,
            outline: true,
            label: {
              text: "{village}",  // 使用village字段显示村名
              opacity: 1,
              font_size: 18,      // 增大字体让标签更清晰
              color: "#fcc31f",
              font_family: "楷体",
              outline: true,
              outlineColor: "#000000",
              outlineWidth: 3,
              background: false,
              backgroundColor: "#000000",
              backgroundOpacity: 0.1,
              font_weight: "normal",
              font_style: "normal",
              scaleByDistance: false,  // 禁用距离缩放，保持字体大小
              distanceDisplayCondition: false,
              visibleDepth: false,
            },
            outlineStyle: {
              color: "#6bcfd4",
              width: 1,
              opacity: 1,
            },
            highlight: {
              opacity: 0.35,
              outlineStyle: {
                color: "#6bcfd4",
                width: 1,
                opacity: 1.0,
                addHeight: 0,
              },
            },
          },
        },
        flyTo: false,  // 不自动飞行，保持当前视角
      })

      // 绑定点击事件（村社区级别暂时只显示信息）
      this.geoJsonLayer.on(mars3d.EventType.click, (event) => {
        const graphic = event.graphic
        const attr = graphic.attr
        
        console.log(`🏡 点击了村社区: ${attr.village || attr.name}`)
        this.eventTarget.fire("getInfo", attr)
      })

             this.map.addLayer(this.geoJsonLayer)
       
       // 更新当前图层类型
       this.currentLayerType = layerType
       
       // 让地图内容填满可视区域
       setTimeout(() => {
         if (this.geoJsonLayer && this.map) {
           this.map.flyTo(this.geoJsonLayer, {
             duration: 2
           })
         }
       }, 500)
       
       return this.geoJsonLayer
    } catch (error) {
      console.error('加载过滤后的行政区划层失败:', error)
      throw error
    }
  }

  // 设置当前图层类型
  setCurrentLayerType(layerType: string) {
    this.currentLayerType = layerType
  }

  // 获取当前图层类型
  getCurrentLayerType(): string {
    return this.currentLayerType
  }

  // 添加点位
  addPoint(pointData: PointData): mars3d.graphic.BillboardEntity {
    if (!this.graphicLayer) throw new Error("图层未初始化")

    const graphic = new mars3d.graphic.BillboardEntity({
      position: pointData.position,
      style: {
        image: "data:image/svg+xml;base64," + btoa(`
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
            <circle cx="16" cy="16" r="12" fill="#ff4444" stroke="#fff" stroke-width="2"/>
            <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">${pointData.id}</text>
          </svg>
        `),
        scale: 1,
        horizontalOrigin: mars3d.HorizontalOrigin.CENTER,
        verticalOrigin: mars3d.VerticalOrigin.BOTTOM,
        label: {
          text: pointData.title,
          font_size: 18,
          color: "#ffffff",
          pixelOffsetY: -50,
          distanceDisplayCondition: true,
          distanceDisplayCondition_far: 500000,
          distanceDisplayCondition_near: 0,
        },
      },
      attr: pointData,
    })

    // 绑定点击事件
    graphic.on(mars3d.EventType.click, () => {
      if (this.config.onPointClick) {
        this.config.onPointClick(pointData)
      }
    })

    this.graphicLayer.addGraphic(graphic)
    return graphic
  }

  // 批量添加点位
  addPoints(pointDataList: PointData[]) {
    pointDataList.forEach(pointData => {
      this.addPoint(pointData)
    })
  }

  // 删除指定点位
  removePointById(id: number) {
    if (!this.graphicLayer) return

    const graphics = this.graphicLayer.graphics
    for (let i = graphics.length - 1; i >= 0; i--) {
      const graphic = graphics[i]
      if (graphic.attr && graphic.attr.id === id) {
        this.graphicLayer.removeGraphic(graphic)
        break
      }
    }
  }

  // 清空所有点位
  clearAllPoints() {
    if (this.graphicLayer) {
      this.graphicLayer.clear()
    }
  }

  // 返回上一级
  async back() {
    if (this.clickDataArr.length <= 1) {
      // 如果在村社区界，返回到乡镇界
      if (this.currentLayerType === 'village') {
        console.log('🔙 从村社区界返回到乡镇界')
        await this.loadAdministrativeLayer(this.layerMappings.town, null, 'town')
        this.viewHeightLevel = 0
        return
      }
      return
    }

    this.clickDataArr.pop()
    this.infoArr.pop()
    this.viewHeightLevel--

    const lastData = this.clickDataArr[this.clickDataArr.length - 1]
    console.log(this.viewHeightArr[this.viewHeightLevel],'视觉高度')
    if (lastData && this.map) {
      const point = mars3d.LngLatPoint.fromCartesian(lastData.parentHeightCenter)
      this.map.setCameraView({
        lng: point.lng,
        lat: point.lat,
        alt: this.viewHeightArr[this.viewHeightLevel] || 24421.4,
        heading: 0,
        pitch: -89.9,
        roll: 0
      }, {
        duration: 2
      })
    }
  }

  // 点击设置视图
  private clickSetView(graphic: any) {
    console.log('点击了视图')
    if (!this.map) return

    // 使用图形的中心点，确保飞行到正确位置
    const point = mars3d.LngLatPoint.fromCartesian(graphic.center)
    this.viewHeightLevel++

    // 根据当前图层类型设置不同的飞行高度
    let targetAlt = this.viewHeightArr[this.viewHeightLevel] || 3000
    if (this.currentLayerType === 'town') {
      // 乡镇界点击后飞行到村社区界，使用合适的高度显示内容
      targetAlt = 6000
    }

    console.log(`🎯 飞行到区域中心点: ${point.lng}, ${point.lat}, 高度: ${targetAlt}`)

    // 使用setCameraView直接设置alt参数
    this.map.setCameraView({
      lng: point.lng,
      lat: point.lat,
      alt: targetAlt,
      heading: 0,
      pitch: -89.9,
      roll: 0
    }, {
      duration: 2, // 设置飞行时间为2秒
    })
  }

  // 添加热力图数据
  addHeatMapData(heatMapData: HeatMapData[]) {
    if (!this.heatMapLayer || !this.map) {
      console.error('热力图图层或地图未初始化')
      return
    }

    // 清空现有热力图数据
    this.heatMapLayer.clear()

    // 计算数据范围，用于确定圆圈大小和颜色
    const counts = heatMapData.map(item => item.count)
    const maxCount = Math.max(...counts)
    const minCount = Math.min(...counts)

    console.log(`🔥 热力图数据范围: ${minCount} - ${maxCount}`)

    heatMapData.forEach(item => {
      const longitude = parseFloat(item.longitude)
      const latitude = parseFloat(item.latitude)

      // 跳过无效坐标
      if (isNaN(longitude) || isNaN(latitude)) {
        console.warn(`⚠️ 跳过无效坐标: ${item.districtName}`, item)
        return
      }

      // 根据数量计算圆圈大小和颜色强度
      const normalizedValue = maxCount > minCount ? (item.count - minCount) / (maxCount - minCount) : 0.5
      const radius = 15 + normalizedValue * 35 // 15-50像素范围
      const opacity = 0.3 + normalizedValue * 0.5 // 0.3-0.8透明度范围

      // 根据数量确定颜色（红色系热力图）
      let color = '#ff0000' // 默认红色
      if (normalizedValue < 0.3) {
        color = '#ffff00' // 黄色（低值）
      } else if (normalizedValue < 0.7) {
        color = '#ff8000' // 橙色（中值）
      } else {
        color = '#ff0000' // 红色（高值）
      }

      // 创建热力图圆圈
      const graphic = new mars3d.graphic.CircleEntity({
        position: [longitude, latitude, 0],
        style: {
          radius: radius,
          color: color,
          opacity: opacity,
          outline: true,
          outlineColor: color,
          outlineOpacity: 0.8,
          outlineWidth: 2,
          clampToGround: true
        },
        attr: {
          districtName: item.districtName,
          districtCode: item.districtCode,
          count: item.count,
          type: 'heatmap'
        }
      })

      // 添加文本标签显示数量
      const labelGraphic = new mars3d.graphic.LabelEntity({
        position: [longitude, latitude, 0],
        style: {
          text: item.count.toString(),
          font_size: 14,
          font_weight: 'bold',
          color: '#ffffff',
          backgroundColor: color,
          backgroundOpacity: 0.8,
          pixelOffset: [0, 0],
          scaleByDistance: true,
          scaleByDistance_near: 1000,
          scaleByDistance_nearValue: 1.0,
          scaleByDistance_far: 100000,
          scaleByDistance_farValue: 0.5,
          clampToGround: true
        },
        attr: {
          districtName: item.districtName,
          count: item.count,
          type: 'heatmap-label'
        }
      })

      // 绑定点击事件显示详细信息
      graphic.on(mars3d.EventType.click, () => {
        console.log(`🔥 点击热力图区域: ${item.districtName}, 数量: ${item.count}`)
        // 可以在这里添加弹窗显示详细信息
      })

      this.heatMapLayer.addGraphic(graphic)
      this.heatMapLayer.addGraphic(labelGraphic)
    })

    console.log(`🔥 热力图添加完成，共 ${heatMapData.length} 个热力点`)
  }

  // 清空热力图
  clearHeatMap() {
    if (this.heatMapLayer) {
      this.heatMapLayer.clear()
      console.log('🔥 热力图已清空')
    }
  }

  // 添加基于total数据的热力图显示
  addTotalHeatMap(data: Array<{
    districtName: string
    districtCode?: string
    longitude: string
    latitude: string
    total: number
  }>) {
    console.log('🔥 addTotalHeatMap 被调用，数据:', data)

    if (!this.heatMapLayer || !this.map) {
      console.error('❌ 热力图图层或地图未初始化', {
        heatMapLayer: !!this.heatMapLayer,
        map: !!this.map
      })
      return
    }

    // 清空现有热力图数据
    this.heatMapLayer.clear()

    if (!data || data.length === 0) {
      console.warn('⚠️ 没有热力图数据')
      return
    }

    console.log(`🔥 开始添加热力图，共 ${data.length} 个数据点`)

    // 计算数据范围，用于确定圆圈大小和颜色
    const totals = data.map(item => item.total)
    const maxTotal = Math.max(...totals)
    const minTotal = Math.min(...totals)

    console.log(`📊 数据范围: ${minTotal} - ${maxTotal}`)

    data.forEach((item, index) => {
      const longitude = parseFloat(item.longitude)
      const latitude = parseFloat(item.latitude)

      // 跳过无效坐标
      if (isNaN(longitude) || isNaN(latitude)) {
        console.warn(`⚠️ 跳过无效坐标: ${item.districtName}`, item)
        return
      }

      // 根据total数量计算圆圈大小和颜色强度
      const normalizedValue = maxTotal > minTotal ? (item.total - minTotal) / (maxTotal - minTotal) : 0.5
      const radius = 20 + normalizedValue * 40 // 20-60像素范围
      const opacity = 0.4 + normalizedValue * 0.4 // 0.4-0.8透明度范围

      // 根据数量确定颜色（红色系热力图）
      let color = '#ff4444' // 默认红色
      if (normalizedValue < 0.3) {
        color = '#ffaa00' // 橙色（低值）
      } else if (normalizedValue < 0.7) {
        color = '#ff6600' // 深橙色（中值）
      } else {
        color = '#ff0000' // 红色（高值）
      }

      // 创建热力图圆圈
      const circleGraphic = new mars3d.graphic.CircleEntity({
        position: [longitude, latitude, 0],
        style: {
          radius: radius,
          color: color,
          opacity: opacity,
          outline: true,
          outlineColor: color,
          outlineOpacity: 0.9,
          outlineWidth: 2,
          clampToGround: true
        },
        attr: {
          districtName: item.districtName,
          districtCode: item.districtCode || '',
          total: item.total,
          type: 'total-heatmap'
        }
      })

      // 添加文本标签显示total数量
      const labelGraphic = new mars3d.graphic.LabelEntity({
        position: [longitude, latitude, 0],
        style: {
          text: item.total.toString(),
          font_size: 16,
          font_weight: 'bold',
          color: '#ffffff',
          backgroundColor: color,
          backgroundOpacity: 0.9,
          pixelOffset: [0, 0],
          scaleByDistance: true,
          scaleByDistance_near: 1000,
          scaleByDistance_nearValue: 1.0,
          scaleByDistance_far: 100000,
          scaleByDistance_farValue: 0.6,
          clampToGround: true,
          // 添加边框效果
          outlineColor: '#000000',
          outlineWidth: 1
        },
        attr: {
          districtName: item.districtName,
          total: item.total,
          type: 'total-heatmap-label'
        }
      })

      // 绑定点击事件显示详细信息
      circleGraphic.on(mars3d.EventType.click, () => {
        console.log(`🔥 点击热力图区域: ${item.districtName}, 数量: ${item.total}`)
        // 可以在这里添加弹窗显示详细信息
      })

      this.heatMapLayer.addGraphic(circleGraphic)
      this.heatMapLayer.addGraphic(labelGraphic)
    })

    console.log(`✅ 热力图添加完成，共 ${data.length} 个热力点`)
  }

  // 销毁地图
  destroy() {
    if (this.map) {
      this.map.destroy()
      this.map = null
    }
    this.graphicLayer = null
    this.geoJsonLayer = null
    this.heatMapLayer = null
  }

  // 获取地图实例
  getMap(): mars3d.Map | null {
    return this.map
  }

  // 获取事件目标
  getEventTarget() {
    return this.eventTarget
  }
} 