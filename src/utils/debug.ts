// 调试工具函数
import { getToken, isTokenValid } from './auth'

// 测试token和请求拦截器
export const debugTokenInfo = () => {
  console.log('=== Token调试信息 ===')
  
  const token = getToken()
  console.log('1. 从localStorage获取的token:', token ? token.substring(0, 50) + '...' : '无token')
  
  const isValid = isTokenValid(token)
  console.log('2. token有效性:', isValid)
  
  const localStorageToken = localStorage.getItem('token')
  console.log('3. 直接从localStorage读取:', localStorageToken ? localStorageToken.substring(0, 50) + '...' : '无token')
  
  if (token && token.includes('.')) {
    try {
      const parts = token.split('.')
      console.log('4. JWT部分数量:', parts.length)
      if (parts.length >= 2) {
        const payload = JSON.parse(atob(parts[1]))
        console.log('5. JWT payload:', payload)
      }
    } catch (error) {
      console.log('5. JWT解析失败:', error)
    }
  }
  
  return {
    token,
    isValid,
    localStorageToken
  }
}

// 测试API请求
export const testApiWithToken = async () => {
  console.log('=== 测试API请求 ===')
  
  // 先调试token信息
  debugTokenInfo()
  
  // 尝试发送一个简单的请求
  try {
    const { getCommunityUserInfoApi } = await import('@/api/auth')
    console.log('6. 准备发送API请求...')
    const result = await getCommunityUserInfoApi()
    console.log('7. API请求成功:', result)
    return result
  } catch (error) {
    console.log('7. API请求失败:', error)
    throw error
  }
} 