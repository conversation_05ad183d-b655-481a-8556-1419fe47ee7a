import { sm4 } from 'miniprogram-sm-crypto'

// SM4加密密钥
const _dataKey = 'd92b90556c59b868ec6cec19804b4a37'

/**
 * 生成授权code
 * @param areaCode 社区行政编号 (固定值: 510183002014)
 * @returns 加密后的code
 */
export const generateAuthCode = (areaCode: string): string => {
  try {
    // 生成13位时间戳
    const timestamp = Date.now().toString()
    
    // 拼接数据：社区行政编号+"|"+13位时间戳
    const data = `${areaCode}|${timestamp}`
    
    console.log('生成授权数据:', data)
    console.log('使用密钥:', _dataKey)
    
    // SM4加密
    const encryptedCode = sm4.encrypt(data, _dataKey)
    
    console.log('加密后的code:', encryptedCode)
    
    return encryptedCode
  } catch (error) {
    console.error('SM4加密失败:', error)
    throw new Error('生成授权code失败')
  }
}

/**
 * 验证code是否过期（5分钟有效期）
 * @param code 加密的code
 * @returns 是否有效
 */
export const isCodeValid = (code: string): boolean => {
  try {
    // 解密code
    const decryptedData = sm4.decrypt(code, _dataKey)
    const [areaCode, timestamp] = decryptedData.split('|')
    
    // 检查时间戳是否在5分钟内
    const now = Date.now()
    const codeTime = parseInt(timestamp)
    const fiveMinutes = 5 * 60 * 1000 // 5分钟的毫秒数
    
    return (now - codeTime) <= fiveMinutes
  } catch (error) {
    console.error('验证code失败:', error)
    return false
  }
}

/**
 * 解密接口返回的数据
 * @param encryptedData 加密的数据
 * @returns 解密后的数据
 */
export const decryptResponseData = (encryptedData: string): string => {
  try {
    // console.log('开始SM4解密，加密数据:', encryptedData)
    // console.log('使用密钥:', _dataKey)
    
    // SM4解密
    const decryptedData = sm4.decrypt(encryptedData, _dataKey)
    
   // console.log('解密后的数据:', decryptedData)
    
    return decryptedData
  } catch (error) {
    console.error('SM4解密失败:', error)
    throw new Error('解密响应数据失败')
  }
} 