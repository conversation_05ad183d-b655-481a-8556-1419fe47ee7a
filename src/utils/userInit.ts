import { useUserStore } from '@/stores/user'

// 初始化用户信息的函数
export const initUserInfo = () => {
  try {
    const userStore = useUserStore()
    userStore.initUserInfo()
    console.log('用户信息初始化成功')
  } catch (error) {
    console.error('用户信息初始化失败:', error)
  }
}

// 测试获取用户信息接口
export const testGetUserInfo = async () => {
  try {
    const userStore = useUserStore()
    if (userStore.isLoggedIn) {
      await userStore.fetchCommunityUserInfo()
      console.log('用户信息获取成功:', userStore.communityUserInfo)
    } else {
      console.log('用户未登录，跳过获取用户信息')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
} 