<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  index: number
}>()

const currentVal = ref<number>(props.index)

/** 双向绑定 */
const emit = defineEmits(['update:modelValue', 'change'])

const dateData = ref<Record<string, any>[]>([
  {
    label: '今日',
    val: 2
  },
  {
    label: '近一月',
    val: 1
  },
  {
    label: '近一年',
    val: 0
  }
])

/** 切换日期选项 */
function _change(val: number) {
  currentVal.value = val
  emit('change', val)
}
</script>

<template>
  <div>
    <el-button
      :style="currentVal == item.val ? 'background: #0b6cd3;color:#fff;' : ''"
      color="#0B6CD3"
      v-for="(item, index) in dateData"
      :key="index"
      @click="_change(item.val)"
      >{{ item.label }}</el-button
    >
  </div>
</template>

<style lang="scss" scoped>
.ep-button {
  background: rgba(30, 137, 254, 0.4);
  box-shadow: 0px 0px 7px 0px rgba(14, 87, 166, 0.5);
  border-radius: 2px;
  border: 1px solid rgba(17, 88, 159, 0.8);
  font-size: 14px;
  color: #ffffffcc;
  margin-left: 8px;
  width: 63px;
  height: 30px;
  font-weight: bold;
  &:hover {
    background: #0b6cd3;
    color: #fff;
  }
}
</style>
