<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import ModuleTitle from '@/views/sidebar/compoments/ModuleTitle.vue'
import RoughlyData from '@/views/sidebar/compoments/RoughlyData.vue'
import DateGroup from '@/views/sidebar/compoments/DateGroup.vue'
import EchartsColumnar from '@/views/sidebar/compoments/EchartsColumnar.vue'
import EchartsToolPie from '@/views/sidebar/compoments/EchartsToolPie.vue'

import { practicalTools } from '@/common/api/tool'

import x1Img from '@/assets/images/tool/x1.png'
import x2Img from '@/assets/images/tool/x2.png'
import x3Img from '@/assets/images/tool/x3.png'

import { AES_KEY, decrypt, encrypt } from '@/common/utils/aes'
import VScaleScreen from 'v-scale-screen'
import { useCodeStore } from '@/stores/code'

const data = reactive<Record<string, any>>({
  chartData1: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  },
  chartData2: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  },
  pieChartData: []
})

const countData = ref([
  {
    key: 'fileCount',
    title: '政策文件',
    img: x1Img,
    unit: '次',
    count: 0,
    color: 'blue'
  },
  {
    key: 'watermarkCameraCount',
    title: '水印相机',
    img: x2Img,
    unit: '次',
    count: 0,
    color: 'green'
  },
  {
    key: 'idCameraCount',
    title: '证件相机',
    img: x3Img,
    unit: '次',
    count: 0,
    color: 'orange'
  }
])

const codeStore = useCodeStore()

/** 当前日期索引 */
const currentDateIndex = ref<number>(1)

/** 滚动定时器1 */
const timer1 = ref()
/** 滚动定时器2 */
const timer2 = ref()

onMounted(() => {
  getToolData()
})

function getToolData() {

  if (timer1.value) {
    clearInterval(timer1.value)
  }

  if (timer2.value) {
    clearInterval(timer2.value)
  }

  practicalTools({
    // 数据加密
    data: encrypt({
      areaCode: codeStore.code,
      timeType: currentDateIndex.value
    }),
    key: AES_KEY
  }).then((res) => {
    // 数据解密
    const result = decrypt(res.data, res.key)
    console.log(result)

    formatPieData(result)

    countData.value.forEach((item) => {
      item.count = result[item.key]
    })

    if (result.fileTypeList.length > 10) {
      // 10条以上需要变动
      for (let i = 0; i < 10; i++) {
        const item = result.fileTypeList[i]
        data.chartData1.columnarDataYellow.push(item.useCount)
        data.chartData1.labelData.push(item.dictLabel)
      }

      dynamicData1(result.fileTypeList)
    } else {
      // 少于10条不需要动
      result.fileTypeList.forEach((item: Record<string, any>) => {
        data.chartData1.columnarDataYellow.push(item.useCount)
        data.chartData1.labelData.push(item.dictLabel)
      })
    }

    if (result.districtList.length > 10) {
      // 10条以上需要变动
      for (let i = 0; i < 10; i++) {
        const item = result.districtList[i]
        data.chartData2.columnarDataBlue.push(item.useCount)
        data.chartData2.labelData.push(item.name)
      }

      dynamicData2(result.districtList)
    } else {
      // 少于10条不需要动
      result.districtList.forEach((item: Record<string, any>) => {
        data.chartData2.columnarDataBlue.push(item.useCount)
        data.chartData2.labelData.push(item.name)
      })
    }

    // result.fileTypeList.forEach((item: Record<string, any>) => {
    //   data.chartData1.columnarDataYellow.push(item.useCount)
    //   data.chartData1.labelData.push(item.dictLabel)
    // })

    // result.districtList.forEach((item: Record<string, any>) => {
    //   data.chartData2.columnarDataBlue.push(item.useCount)
    //   data.chartData2.labelData.push(item.name)
    // })
  })
}

function formatPieData(result: Record<string, any>) {
  data.pieChartData.push({
    value: result.idCameraPublicCount,
    name: '居民端证件照'
  })

  data.pieChartData.push({
    value: result.idCameraWorkCount,
    name: '工作端证件照'
  })

  data.pieChartData.push({
    value: result.watermarkCameraPublicCount,
    name: '居民端水印相机'
  })

  data.pieChartData.push({
    value: result.watermarkCameraWorkCount,
    name: '工作端水印相机'
  })
}

function changeDate(index: number) {
  console.log(index)

  data.chartData1 = {
    columnarDataBlue: [],
    columnarDataYellow: [],
    labelData: []
  }

  data.chartData2 = {
    columnarDataBlue: [],
    columnarDataYellow: [],
    labelData: []
  }

  currentDateIndex.value = index
  getToolData()
}

function dynamicData1(list: Record<string, any>[]) {
  // 从第10开始展示
  let count = 10
  timer1.value = setInterval(() => {
    if (count >= list.length) {
      count = 0
    }
    data.chartData1.columnarDataYellow.shift()
    data.chartData1.labelData.shift()

    const item = list[count]

    data.chartData1.columnarDataYellow.push(item.useCount)
    data.chartData1.labelData.push(item.dictLabel)

    count += 1
  }, 3000)
}

function dynamicData2(list: Record<string, any>[]) {
  // 从第10开始展示
  let count = 10
  timer2.value = setInterval(() => {
    if (count >= list.length) {
      count = 0
    }
    data.chartData2.columnarDataBlue.shift()
    data.chartData2.labelData.shift()

    const item = list[count]

    data.chartData2.columnarDataBlue.push(item.useCount)
    data.chartData2.labelData.push(item.name)

    count += 1
  }, 3000)
}
</script>
<template>
  <!-- <VScaleScreen :delay="200" width="480" height="935" :bodyOverflowHidden="false" :autoScale="{ x: true, y: false }"
    :boxStyle="{ background: '#021121' }">
    <div class="base-box"> -->
      <div class="title-box">
        <ModuleTitle title="实用工具使用数" />
        <DateGroup :index="currentDateIndex" @change="changeDate" />
      </div>

      <RoughlyData :countData="countData" />
      <ModuleTitle class="module-title" title="政策类型使用分布" />
      <EchartsColumnar borderColor="#DEB565" :chartData="data.chartData1" />
      <!-- <EchartsColumnar :showLabel="false" :chartData="data.chartData2" /> -->
      <ModuleTitle class="module-title" title="实用工具使用分布" />
      <EchartsColumnar :chartData="data.chartData2" />
      <ModuleTitle class="module-title" title="使用人群分布" />
      <EchartsToolPie :chartData="data.pieChartData" />
    <!-- </div>
  </VScaleScreen> -->
</template>

<style lang="scss" scoped>
.v-screen-box {
  height: 935px !important;
}
.module-title {
  margin: 0.520vw 0;
  
  @media (max-width: 2000px) {
    margin: 0.833vw 0;
  }
}
</style>
