<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import ModuleTitle from '@/views/sidebar/compoments/ModuleTitle.vue'
import RoughlyData from '@/views/sidebar/compoments/RoughlyData.vue'
import DateGroup from '@/views/sidebar/compoments/DateGroup.vue'
import EchartsColumnar from '@/views/sidebar/compoments/EchartsColumnar.vue'

import dangerImg from '@/assets/images/warn/danger.png'
import processedImg from '@/assets/images/warn/processed.png'
import pendingImg from '@/assets/images/warn/pending.png'
import { dCEStatistics, deviceDCE } from '@/common/api/system'
import { AES_KEY, decrypt } from '@/common/utils/aes'
import { useRouter } from 'vue-router'
import VScaleScreen from 'v-scale-screen'
import { useCodeStore } from '@/stores/code'
import { POPUP_URL } from '@/common/config'

const data = reactive<Record<string, any>>({
  warnData: '',
  chartData: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  },
  gridData: ''
})

/** 当前日期索引 */
const currentDateIndex = ref<number>(2)

const countData = ref([
  {
    key: 'Total',
    title: '今日预警',
    img: dangerImg,
    unit: '',
    count: 0,
    color: 'orange'
  },
  {
    key: 'Complete',
    title: '已处理',
    img: processedImg,
    unit: '',
    count: 0,
    color: 'green'
  },
  {
    key: 'UnComplete',
    title: '待处置',
    img: pendingImg,
    unit: '',
    count: 0,
    color: 'blue'
  }
])

/** 滚动定时器 */
const timer = ref()

const loading = ref(true)
const codeStore = useCodeStore()

onMounted(() => {
  getDeviceDCE()

  getDCEStatistics()
})

/**
 * 我也不知道为什么,突然之间就变化了,我清楚的记录我为了让我这边的后端对应他们的接口参数,但现在竟然出错了?是我记忆错乱?
 * 现在我把证据写在这里,物联网接口,日期参数,2024年5月8日.
 * 1-近 1年,2-近1月,3-今日
 */
function dateMap(val: number) {
  // 对应今日2近一月1近一年0
  return val + 1
}

function getDCEStatistics() {
  dCEStatistics({ areaCode: codeStore.code }).then(res => {
    data.gridData = decrypt(res, AES_KEY)
    console.log(data.gridData);
  })
}

function getDeviceDCE() {

  if (timer.value) {
    clearInterval(timer.value)
  }

  deviceDCE({
    areaCode: codeStore.code,
    timeRangeType: dateMap(currentDateIndex.value)
  }).then((res) => {
    data.warnData = decrypt(res, AES_KEY)
    console.log(data.warnData)

    countData.value.forEach((item) => {
      item.count = parseInt(data.warnData.Statistics[item.key])
    })

    // TODO 后期优化数据获取
    data.chartData.columnarDataBlue = []
    data.chartData.labelData = []
    // data.warnData.UnCompleteGroup.forEach((item: Record<string, any>) => {
    //   data.chartData.columnarDataBlue.push(item.Count)
    //   data.chartData.labelData.push(item.Name)
    // })

    if (data.warnData.UnCompleteGroup.length > 10) {
      // 10条以上需要变动
      for (let i = 0; i < 10; i++) {
        const item = data.warnData.UnCompleteGroup[i]
        data.chartData.columnarDataBlue.push(item.Count)
        data.chartData.labelData.push(item.Name)
      }

      dynamicData(data.warnData.UnCompleteGroup)
    } else {
      // 少于10条不需要动
      data.warnData.UnCompleteGroup.forEach((item: Record<string, any>) => {
        data.chartData.columnarDataBlue.push(item.Count)
        data.chartData.labelData.push(item.Name)
      })
    }

    loading.value = false
  })
}

function dynamicData(list: Record<string, any>[]) {
  // 从第10开始展示
  let count = 10
  timer.value = setInterval(() => {
    if (count >= list.length) {
      count = 0
    }

    data.chartData.columnarDataBlue.shift()
    data.chartData.labelData.shift()

    const item = list[count]

    data.chartData.columnarDataBlue.push(item.Count)
    data.chartData.labelData.push(item.Name)

    count += 1
  }, 3000)
}

function changeDate(index: number) {
  currentDateIndex.value = index
  getDeviceDCE()
}

const router = useRouter()

function toPage(item: Record<string, any>) {
  window.parent.postMessage(
    {
      width: '1024px',
      height: '628px',
      popTitle: item.TotalEventName,
      popUrl: `${POPUP_URL}/#/warningEventList?areaCode=${codeStore.code}&dCEId=${item.DCEId}&totalEventName=${item.TotalEventName}`
    },
    '*'
  )
}
</script>
<template>
  <!-- <VScaleScreen :delay="200" width="480" height="1340" :bodyOverflowHidden="false" :autoScale="{ x: true, y: false }"
    :boxStyle="{ background: '#021121' }">
    <div class="base-box"> -->
      <div class="title-box">
        <ModuleTitle title="预警详情" />
        <DateGroup :index="currentDateIndex" @change="changeDate" />
      </div>

      <RoughlyData :overturn="true" :countData="countData" />

      <ModuleTitle title="预警消息" />

      <div class="table-box">
        <el-table v-loading="loading" element-loading-text="加载中..." element-loading-background="rgba(0, 0, 0, 0.6)"
          stripe :data="data.warnData.List" :max-height="330">
          <el-table-column label="预警类型" align="center" width="120">
            <template #default="scope">
              <div class="name">{{ scope.row.TotalEventName }}</div>
            </template>
          </el-table-column>
          <el-table-column label="预警小区" align="center" width="100">
            <template #default="scope">
              <div class="park">{{ scope.row.ParkName }}</div>
            </template>
          </el-table-column>
          <el-table-column label="预警时间" align="center">
            <template #default="scope">
              <div class="date">{{ scope.row.EventDate }}</div>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" width="60">
            <template #default="scope">
              <div class="state" v-if="scope.row.CompleteState == '待处理'" style="color: #0b6cd3">
                {{ scope.row.CompleteState }}
              </div>
              <div class="state" v-else style="color: #02fbff">{{ scope.row.CompleteState }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button size="small" @click="toPage(scope.row)">查看 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <ModuleTitle class="module-title" title="待处理事件分布" />
      <EchartsColumnar :chartData="data.chartData" />

      <ModuleTitle class="module-title" title="风险预警点位排名" />
      <div class="block-list">
        <div class="block block-2" v-for="(item, index) in data.gridData.Top3Device" :key="index">
          <div>
            <p>{{ item.DeviceName }}</p>
            <p>{{ item.Count }}</p>
          </div>
        </div>
      </div>
      <ModuleTitle class="module-title" title="风险预警类型排名" />
      <div class="block-list">
        <div class="block" v-for="(item, index) in data.gridData.ByType" :key="index">
          <div>
            <p>{{ item.TotalEventName }}</p>
            <p>{{ item.Count }}</p>
          </div>
        </div>
      </div>
    <!-- </div>
  </VScaleScreen> -->
</template>

<style lang="scss" scoped>
.v-screen-box {
  height: 1340px !important;
}

.table-box {
  margin: 26px 0;

  .name,
  .date {
    color: #fff;
    font-size: 12px;
  }

  .park {
    font-size: 12px;
    color: #00d1f0;
  }

  .state {
    font-size: 12px;
  }
}

.module-title {
  margin: 20px 0;
}

.block-list {
  display: flex;
  flex-wrap: wrap;

  .block {
    width: 33.33%;
    margin-bottom: 12px;

    >div {
      padding: 4px 6px;
      background-color: #0c162d;
      border: 1px solid #24324b;
      border-radius: 2px;
      margin-right: 12px;

      p {

        &:first-of-type {
          color: hsla(0, 0%, 100%, .7);
        }

        &:last-of-type {
          color: hsla(0, 0%, 100%, .9);
          font-weight: 700;
          margin-top: 4px;
        }
      }
    }


  }

  .block-2 {
    width: 50%;
  }
}
</style>
