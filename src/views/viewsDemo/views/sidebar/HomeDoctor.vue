<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import ModuleTitle from '@/views/sidebar/compoments/ModuleTitle.vue'
import RoughlyData from '@/views/sidebar/compoments/RoughlyData.vue'
import EchartsColumnar from '@/views/sidebar/compoments/EchartsColumnar.vue'
import EchartsColumnarDoctor from '@/views/sidebar/compoments/EchartsColumnarDoctor.vue'
import EchartsHorizontalColumnar from '@/views/sidebar/compoments/EchartsHorizontalColumnar.vue'

import x1Img from '@/assets/images/doctor/x1.png'
import x2Img from '@/assets/images/doctor/x2.png'
import x3Img from '@/assets/images/doctor/x3.png'
import VScaleScreen from 'v-scale-screen'
import { familyDoctor } from '@/common/api/tool'
import { AES_KEY, decrypt, encrypt } from '@/common/utils/aes'
import { useCodeStore } from '@/stores/code'

const data = reactive<Record<string, any>>({
  deviceData: '',
  chartData1: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  },
  chartData2: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  },
  chartData3: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  }
})

const countData = ref([
  {
    key: '',
    title: '家庭医生数',
    img: x1Img,
    unit: '人',
    count: 885,
    color: 'blue'
  },
  {
    key: '',
    title: '居民人数',
    img: x2Img,
    unit: '人',
    count: 601300,
    color: 'green'
  },
  {
    key: '',
    title: '签约人数',
    img: x3Img,
    unit: '人',
    count: 255270,
    color: 'orange'
  }
])

const codeStore = useCodeStore()

onMounted(() => {
  getFamilyDoctor()

  let columnarDataBlue1 = [3055, 2812, 1115, 1624, 825, 298, 913, 771, 516, 440, 328, 305, 253, 449]
  let labelData1 = [
    '文君街道',
    '临邛街道',
    '固驿街道',
    '羊安街道',
    '高埂街道',
    '孔明街道',
    '桑园镇',
    '平乐镇',
    '夹关镇',
    '火井镇',
    '临济镇',
    '天台山镇',
    '南宝山镇',
    '大同镇'
  ]

  let columnarDataBlue2 = [148, 87, 65, 155, 80, 41, 81, 66, 38, 31, 26, 26, 15, 26]
  // let columnarDataYellow2 = [20, 140, 53, 100, 280, 190, 53, 100, 280, 190]
  let labelData2 = [
    '文君街道',
    '临邛街道',
    '固驿街道',
    '羊安街道',
    '高埂街道',
    '孔明街道',
    '桑园镇',
    '平乐镇',
    '夹关镇',
    '火井镇',
    '临济镇',
    '天台山镇',
    '南宝山镇',
    '大同镇'
  ]

  for (let i = 0; i < 10; i++) {
    data.chartData1.columnarDataBlue.push(columnarDataBlue1[i])
    data.chartData1.labelData.push(labelData1[i])

    data.chartData2.columnarDataBlue.push(columnarDataBlue2[i])
    // data.chartData2.columnarDataYellow.push(columnarDataYellow2[i])
    data.chartData2.labelData.push(labelData2[i])
  }

  // 从第10开始展示
  let i = 10
  setInterval(() => {
    if (i >= columnarDataBlue1.length) {
      i = 0
    }
    data.chartData1.columnarDataBlue.shift()
    data.chartData1.labelData.shift()

    data.chartData2.columnarDataBlue.shift()
    data.chartData2.columnarDataYellow.shift()
    data.chartData2.labelData.shift()

    data.chartData1.columnarDataBlue.push(columnarDataBlue1[i])
    data.chartData1.labelData.push(labelData1[i])

    data.chartData2.columnarDataBlue.push(columnarDataBlue2[i])
    data.chartData2.labelData.push(labelData2[i])
    // data.chartData2.columnarDataYellow.push(columnarDataYellow2[i])

    i += 1
  }, 3000)
})

async function getFamilyDoctor() {
  const res = await familyDoctor({
    // 数据加密
    data: encrypt({
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  //数据解密
  const result = decrypt(res.data, res.key)
  console.log(result)

  // const result = {
  //   districtList: [
  //     {
  //       name: '文君街道',
  //       useCount: 37.34
  //     },
  //     {
  //       name: '临邛街道',
  //       useCount: 40.66
  //     },
  //     {
  //       name: '固驿街道',
  //       useCount: 48.81
  //     },
  //     {
  //       name: '羊安街道',
  //       useCount: 45.43
  //     },
  //     {
  //       name: '高埂街道',
  //       useCount: 40.63
  //     },
  //     {
  //       name: '孔明街道',
  //       useCount: 42.11
  //     },
  //     {
  //       name: '桑园镇',
  //       useCount: 36.54
  //     },
  //     {
  //       name: '平乐镇',
  //       useCount: 42.13
  //     },
  //     {
  //       name: '夹关镇',
  //       useCount: 47.99
  //     },
  //     {
  //       name: '火井镇',
  //       useCount: 33.15
  //     },
  //     {
  //       name: '临济镇',
  //       useCount: 47.03
  //     },
  //     {
  //       name: '天台山镇',
  //       useCount: 53.02
  //     },
  //     {
  //       name: '南宝山镇',
  //       useCount: 60.04
  //     },
  //     {
  //       name: '大同镇',
  //       useCount: 56.45
  //     }
  //   ]
  // }

  if (result.districtList.length > 5) {
    // 5条以上需要变动
    for (let i = 0; i < 5; i++) {
      const item = result.districtList[i]
      data.chartData3.columnarDataBlue.push(item.useCount)
      data.chartData3.labelData.push(item.name)
    }

    dynamicData(result.districtList)
  } else {
    // 少于5条不需要动
    result.districtList.forEach((item: Record<string, any>) => {
      data.chartData3.columnarDataBlue.push(item.useCount)
      data.chartData3.labelData.push(item.name)
    })
  }
  // })
}

function dynamicData(list: Record<string, any>[]) {
  // 从第5开始展示
  let count = 5
  setInterval(() => {
    if (count >= list.length) {
      count = 0
    }

    data.chartData3.columnarDataBlue.shift()
    data.chartData3.labelData.shift()

    const item = list[count]

    data.chartData3.columnarDataBlue.push(item.useCount)
    data.chartData3.labelData.push(item.name)

    count += 1
  }, 3000)
}
</script>
<template>
  <!-- <VScaleScreen
    :delay="200"
    width="480"
    height="1020"
    :bodyOverflowHidden="false"
    :autoScale="{ x: true, y: false }"
    :boxStyle="{ background: '#021121' }"
  >
    <div class="base-box"> -->
      <ModuleTitle title="服务概况" />
      <RoughlyData :countData="countData" />
      <ModuleTitle class="module-title" title="家庭医生" />
      <EchartsColumnar :formatter="'家庭医生: {c0}'" :showLabel="false" :chartData="data.chartData2" />
      <ModuleTitle class="module-title" title="在管糖尿病患者签约数" />
      <EchartsColumnar :chartData="data.chartData1" />
      <ModuleTitle class="module-title" title="家庭医生签约率" />
      <EchartsHorizontalColumnar :chartData="data.chartData3" />
      <!-- <div class="chart-box">
        <div class="row" v-for="(num,i) in 4" :key="i">
          <p>兰谷里社区{{ num }}</p>
          <div>
            <div class="default" :class="25 > index ? 'active-' + i : ''" v-for="(item, index) in 50" :key="index">
            </div>
          </div>
          <div class="label">
            <p class="value">300</p>
            <p>30%</p>
          </div>
        </div>
      </div> -->
    <!-- </div>
  </VScaleScreen> -->
</template>

<style lang="scss" scoped>
.v-screen-box {
  height: 1020px !important;
}

.module-title {
  margin: 0.520vw 0;
  
  @media (max-width: 2000px) {
    margin: 0.833vw 0;
  }
}

.chart-box {
  display: flex;
  flex-direction: column;
  margin-top: 0.520vw;
  
  @media (max-width: 2000px) {
    margin-top: 0.833vw;
  }

  .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.390vw;
    
    @media (max-width: 2000px) {
      margin-bottom: 0.625vw;
    }

    > p {
      color: rgba($color: #ffffff, $alpha: 0.8);
      font-size: 0.364vw;
      
      @media (max-width: 2000px) {
        font-size: 0.583vw;
      }
    }

    > div {
      display: flex;
      align-items: center;

      .default {
        width: 0.078vw;
        height: 0.260vw;
        border-radius: 0.052vw;
        background: #343a4a;
        margin: 0 0.039vw;
        
        @media (max-width: 2000px) {
          width: 0.125vw;
          height: 0.416vw;
          border-radius: 0.083vw;
          margin: 0 0.062vw;
        }
      }

      .active-0 {
        background: #e95e44;
      }

      .active-1 {
        background: #edb115;
      }
    }

    .label {
      color: rgba($color: #ffffff, $alpha: 0.6);
      font-size: 0.338vw;
      
      @media (max-width: 2000px) {
        font-size: 0.541vw;
      }

      .value {
        margin-right: 0.130vw;
        
        @media (max-width: 2000px) {
          margin-right: 0.208vw;
        }
      }
    }
  }
}
</style>
