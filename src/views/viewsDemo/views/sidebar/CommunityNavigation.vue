<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import ModuleTitle from '@/views/sidebar/compoments/ModuleTitle.vue'
import DateGroup from '@/views/sidebar/compoments/DateGroup.vue'
import EchartsColumnar from '@/views/sidebar/compoments/EchartsColumnar.vue'
import EchartsPie from '@/views/sidebar/compoments/EchartsPie.vue'
import { communityTour } from '@/common/api/tool'
import { AES_KEY, decrypt, encrypt } from '@/common/utils/aes'
import VScaleScreen from 'v-scale-screen'
import { useCodeStore } from '@/stores/code'

import icon1Img from '@/assets/images/pie/icon1.png'
import icon2Img from '@/assets/images/pie/icon2.png'
import icon3Img from '@/assets/images/pie/icon3.png'
import icon4Img from '@/assets/images/pie/icon4.png'
import icon5Img from '@/assets/images/pie/icon5.png'
import icon6Img from '@/assets/images/pie/icon6.png'
import icon7Img from '@/assets/images/pie/icon7.png'

const iconArr = ref([
  {
    color: '#CEC60E',
    blurryColor: 'rgba(206, 198, 14, 0.7)',
    icon: icon1Img
  },
  {
    color: '#8815D4',
    blurryColor: 'rgba(136, 21, 212, 0.7)',
    icon: icon2Img
  },
  {
    color: '#C408E7',
    blurryColor: 'rgba(196, 8, 231, 0.7)',
    icon: icon3Img
  },
  {
    color: '#04EFD4',
    blurryColor: 'rgba(4, 239, 212, 0.7)',
    icon: icon4Img
  },
  {
    color: '#11B8D5',
    blurryColor: 'rgba(17, 184, 213, 0.7)',
    icon: icon5Img
  },
  {
    color: '#F16154',
    blurryColor: 'rgba(17, 184, 213, 0.7)',
    icon: icon6Img
  },
  {
    color: '#166DC8',
    blurryColor: 'rgba(22, 109, 200, 0.7)',
    icon: icon7Img
  }
])

const data = reactive<Record<string, any>>({
  totalNumber: 0,
  chartData1: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  },
  chartData2: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  },
  pieChartData: [],
  pieChartLegendData: [],
  pieChartSerie1Data: [],
  pieChartSerie2Data: []
})

/** 滚动定时器 */
const timer = ref()

/** 当前日期索引 */
const currentDateIndex = ref<number>(1)

/** 切换日期 */
function changeDate(index: number) {
  data.chartData1 = {
    columnarDataBlue: [],
    columnarDataYellow: [],
    labelData: []
  }

  data.chartData2 = {
    columnarDataBlue: [],
    columnarDataYellow: [],
    labelData: []
  }

  data.pieChartData = []
  data.pieChartLegendData = []
  data.pieChartSerie1Data = []
  data.pieChartSerie2Data = []

  currentDateIndex.value = index
  getDistribution()
}

const codeStore = useCodeStore()

onMounted(() => {
  getDistribution()
})

/** 获取资源分布 */
function getDistribution() {
  if (timer.value) {
    clearInterval(timer.value)
  }

  communityTour({
    // 数据加密
    data: encrypt({
      areaCode: codeStore.code,
      timeType: currentDateIndex.value
    }),
    key: AES_KEY
  }).then((res) => {
    // 数据解密
    const result = decrypt(res.data, res.key)
    console.log(result)

    data.totalNumber = result.totalNumber

    // countData.value.forEach((item: Record<string, any>) => {
    //   item.count = result[item.key]
    // })

    /** 环形图数据 */
    result.lifeCircleTypeList.forEach((item: Record<string, any>, i: number) => {
      data.pieChartData.push({
        value: item.lifeCircleCount,
        name: item.typeName
      })

      data.pieChartLegendData.push({
        name: item.typeName,
        icon: 'image://' + iconArr.value[i].icon
      })

      data.pieChartSerie1Data.push({
        value: item.lifeCircleCount,
        name: item.typeName,
        itemStyle: {
          color: iconArr.value[i].blurryColor
        }
      })

      data.pieChartSerie2Data.push({
        value: item.lifeCircleCount,
        name: item.typeName,
        itemStyle: {
          color: iconArr.value[i].color
        }
      })
    })

    /** 柱状图数据 */
    // result.districtList.forEach((item: Record<string, any>) => {
    //   data.chartData.columnarDataBlue.push(item.useCount)
    //   data.chartData.labelData.push(item.name)
    // })

    if (result.districtList.length > 10) {
      // 10条以上需要变动
      for (let i = 0; i < 10; i++) {
        const item = result.districtList[i]

        data.chartData1.columnarDataBlue.push(item.useCount)
        data.chartData1.labelData.push(item.name)

        data.chartData2.columnarDataBlue.push(item.communityLineUseCount)
        data.chartData2.columnarDataYellow.push(item.lifeCircleUseCount)
        data.chartData2.labelData.push(item.name)
      }

      dynamicData(result.districtList)
    } else {
      // 少于10条不需要动
      result.districtList.forEach((item: Record<string, any>) => {
        data.chartData1.columnarDataBlue.push(item.useCount)
        data.chartData1.labelData.push(item.name)

        data.chartData2.columnarDataBlue.push(item.communityLineUseCount)
        data.chartData2.columnarDataYellow.push(item.lifeCircleUseCount)
        data.chartData2.labelData.push(item.name)
      })
    }
  })
}

function dynamicData(list: Record<string, any>[]) {
  // 从第10开始展示
  let count = 10
  timer.value = setInterval(() => {
    if (count >= list.length) {
      count = 0
    }
    data.chartData1.columnarDataBlue.shift()
    data.chartData1.labelData.shift()

    data.chartData1.columnarDataBlue.push(list[count].useCount)
    data.chartData1.labelData.push(list[count].name)

    data.chartData2.columnarDataBlue.shift()
    data.chartData2.columnarDataYellow.shift()
    data.chartData2.labelData.shift()

    data.chartData2.columnarDataBlue.push(list[count].communityLineUseCount)
    data.chartData2.columnarDataYellow.push(list[count].lifeCircleUseCount)
    data.chartData2.labelData.push(list[count].name)

    count += 1
  }, 3000)
}
</script>
<template>
  <!-- <VScaleScreen
    :delay="200"
    width="480"
    height="965"
    :bodyOverflowHidden="false"
    :autoScale="{ x: true, y: false }"
    :boxStyle="{ background: '#021121' }"
  >
    <div class="base-box"> -->
      <div class="title-box date-box">
        <DateGroup :index="currentDateIndex" @change="changeDate" />
      </div>
      <div class="data-box">
        <div class="left-box">
          <img src="@/assets/images/community/x1.png" alt="" />
          <span>使用总人次</span>
        </div>
        <div class="right-box">
          <span class="text-color-blue">{{ data.totalNumber }} <span class="unit">次</span></span>
        </div>
      </div>

      <ModuleTitle class="module-title" title="资源分布" />
      <EchartsPie
        :chartData="data.pieChartData"
        :chartLegendData="data.pieChartLegendData"
        :chartSerie1Data="data.pieChartSerie1Data"
        :chartSerie2Data="data.pieChartSerie2Data"
      />

      <ModuleTitle class="module-title" title="使用数量" />
      <EchartsColumnar :chartData="data.chartData1" />

      <ModuleTitle class="module-title" title="使用热度排行" />
      <EchartsColumnar :chartData="data.chartData2" />
    <!-- </div>
  </VScaleScreen> -->
</template>

<style lang="scss" scoped>
.v-screen-box {
  height: 965px !important;
}

.date-box {
  justify-content: flex-end;
}

.data-box {
  margin: 24px 0 35px 0;
  padding: 0 24px 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  background: #0c162f;
  border-radius: 2px;
  border: 1px solid #293657;
  height: 80px;

  .left-box {
    display: flex;
    align-items: center;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 14px;

    img {
      width: 32px;
      height: 31px;
      margin-right: 10px;
    }
  }

  .right-box {
    font-weight: 900;
    color: transparent;
    font-family: Swei Fist Leg CJK SC;
    font-size: 20px;
    color: #ffffff;

    .text-color-blue {
      color: transparent;
      -webkit-background-clip: text;
      background-image: linear-gradient(0deg, #0b6cd3 20%, #fff 80%);
    }

    .unit {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      margin-left: 5px;
    }
  }
}

.module-title {
  margin: 20px 0;
}
</style>
