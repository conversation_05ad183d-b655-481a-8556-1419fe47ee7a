<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import ModuleTitle from '@/views/sidebar/compoments/ModuleTitle.vue'
import RoughlyData from '@/views/sidebar/compoments/RoughlyData.vue'
import { deviceTypeRunStatistics } from '@/common/api/system'

import aiImg from '@/assets/images/run/ai.png'
import onlineImg from '@/assets/images/run/online.png'
import chartImg from '@/assets/images/run/chart.png'
import { AES_KEY, decrypt } from '@/common/utils/aes'
import { useRouter } from 'vue-router'
import { getDeviceTypeByValue } from '@/common/constants'
import VScaleScreen from 'v-scale-screen'
import { useCodeStore } from '@/stores/code'
import { POPUP_URL } from '@/common/config'

const data = reactive<Record<string, any>>({
  deviceData: '',
})

const countData = ref([
  {
    key: 'Total',
    title: '智能设备数量',
    img: aiImg,
    unit: '台',
    count: 0,
    color: 'blue'
  },
  {
    key: 'Online',
    title: '设备在线数量',
    img: onlineImg,
    unit: '台',
    count: 0,
    color: 'green'
  },
  {
    key: 'LastMonthOnlineGrowthRate',
    title: '在线上月同比',
    img: chartImg,
    unit: '%',
    count: 0,
    color: 'orange'
  }
])

const loading = ref(true)

const codeStore = useCodeStore()

onMounted(() => {
  deviceTypeRunStatistics({
    areaCode: codeStore.code
  }).then((res) => {
    data.deviceData = decrypt(res, AES_KEY)
    console.log(data.deviceData)

    countData.value.forEach((item) => {
      item.count = parseInt(data.deviceData[item.key])
    })

    loading.value = false
  })
})

function toPage(item: Record<string, any>) {
  window.parent.postMessage(
    {
      width: '1024px',
      height: '628px',
      popTitle: item.DeviceTypeName,
      popUrl: `${POPUP_URL}/#/deviceList?areaCode=${codeStore.code}&deviceType=${item.DeviceType}`
    },
    '*'
  )
}

/** 设备分布撒点 */
function sprinkle(item: Record<string, any>) {
  window.parent.postMessage(
    {
      areaCode: codeStore.code,
      deviceType: item.DeviceType,
      token: sessionStorage.getItem('token'),
      aesKey: AES_KEY
    },
    '*'
  )
}
</script>
<template>
  <!-- <VScaleScreen :delay="200" width="480" height="1105" :bodyOverflowHidden="false" :autoScale="{ x: true, y: false }"
    :boxStyle="{ background: '#021121' }">
    <div class="base-box"> -->
      <ModuleTitle title="设备概况" />
      <RoughlyData :countData="countData" />

      <ModuleTitle class="module-title" title="感知设备分布" />
      <div class="block-list">
        <div class="block" v-for="(item, index) in data.deviceData.ByArea" :key="index">
          <div>
            <p>{{ item.Name }}</p>
            <p>{{ item.Count }}</p>
          </div>
        </div>
      </div>

      <ModuleTitle class="module-title" title="设备信息" />
      <div class="tb">
        <el-table :max-height="580" v-loading="loading" element-loading-text="加载中..."
          element-loading-background="rgba(0, 0, 0, 0.6)" stripe :data="data.deviceData.DeviceTypeList">
          <el-table-column label="智能设备数量" align="center" width="150">
            <template #default="scope">
              <div class="tb-no-box">
                <div class="tb-no-left-box">
                  <img :src="getDeviceTypeByValue(scope.row.DeviceType)?.icon" alt="" />
                </div>
                <div class="tb-no-right-box">
                  <span>{{ scope.row.DeviceTypeName }}</span>
                  <!-- <div>{{ scope.row.Count }}</div> -->
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="设备在线数" align="center">
            <template #default="scope">
              <div class="s2_2">
                <img src="@/assets/images/run/signal.png" alt="" />
                <span>{{ scope.row.Online }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="异常情况" align="center" width="80">
            <template #default="scope">
              <span class="s2_3" :class="scope.row.Offline > 0 ? 's2_3_error' : ''">{{
                scope.row.Offline
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="操作" align="center" width="85">
            <template #default="scope">
              <el-button size="small" @click="toPage(scope.row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="分布" align="center" width="60">
            <template #default="scope">
              <img @click="sprinkle(scope.row)" width="15" height="18" src="@/assets/images/run/local.png" alt="" />
            </template>
          </el-table-column>
        </el-table>
      </div>
    <!-- </div>
  </VScaleScreen> -->
</template>

<style lang="scss" scoped>
.v-screen-box {
  height: 1105px !important;
}

.tb {
  margin-top: 37px;

  .tb-no-box {
    display: flex;

    .tb-no-left-box {
      height: 32px;
      width: 32px;
      // background: rgba($color: #17519f, $alpha: 0.4);
      background: rgba(23, 81, 159, 0.4);
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 8px;
      flex-shrink: 0;

      img {
        width: 14px;
        height: 16px;
      }
    }

    .tb-no-right-box {
      line-height: 12px;
      display: flex;
      align-items: center;
      // justify-content: space-between;
      text-align: left;

      span {
        font-size: 12px;
        font-weight: 500;
        color: #ffffff;
      }

      div {
        font-weight: 900;
        color: #166dc8;
        font-size: 12px;
      }
    }
  }

  .s2_2 {
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 14px;
      height: 16px;
      margin-right: 9px;
    }

    span {
      margin-top: 4px;
      font-size: 12px;
      font-weight: 500;
      color: #00d1f0;
    }
  }

  .s2_3 {
    font-weight: 500;
    font-size: 12px;
    color: #ffffff;
  }

  .s2_3_error {
    color: #eb121b;
  }
}

.module-title {
  margin: 20px 0;
}

.block-list {
  display: flex;
  flex-wrap: wrap;

  .block {
    width: 25%;
    margin-bottom: 12px;

    >div {
      padding: 4px 6px;
      background-color: #0c162d;
      border: 1px solid #24324b;
      border-radius: 2px;
      margin-right: 12px;

      p {

        &:first-of-type {
          color: hsla(0, 0%, 100%, .7);
        }

        &:last-of-type {
          color: hsla(0, 0%, 100%, .9);
          font-weight: 700;
          margin-top: 4px;
        }
      }
    }


  }

  .block-2 {
    width: 50%;
  }
}
</style>
