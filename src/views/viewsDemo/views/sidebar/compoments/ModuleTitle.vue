<script setup lang="ts">
defineProps<{
  title: string
}>()
</script>

<template>
  <div class="title-2">
    <img src="@/assets/images/common/corner.png" alt=""><span>{{ title }}</span>
  </div>
</template>

<style lang="scss" scoped>
.title-2 {
  display: flex;
  align-items: center;

  img {
    height: 20px;
    width: 20px;
    margin-right: 8px;
  }

  span {
    font-size: 22px;
    font-weight: 900;
    color: #FFFFFF;
  }

}
</style>