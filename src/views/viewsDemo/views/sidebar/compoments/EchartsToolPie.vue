<script setup lang="ts">
import * as echarts from 'echarts'
import { ref, watch } from 'vue'

import icon1Img from '@/assets/images/pie/icon1.png'
import icon2Img from '@/assets/images/pie/icon2.png'
import icon3Img from '@/assets/images/pie/icon3.png'
import icon4Img from '@/assets/images/pie/icon4.png'
import icon5Img from '@/assets/images/pie/icon5.png'
import icon6Img from '@/assets/images/pie/icon6.png'
import icon7Img from '@/assets/images/pie/icon7.png'

const props = defineProps<{
  chartData: Record<string, any>[]
}>()

const main = ref(null)
const chart = ref()
const lables = ref([
  '居民端证件照',
  '居民端水印相机',
  '工作端证件照',
  '工作端水印相机'
])

watch(props.chartData, (newVal, oldVal) => {
  initChart()
})

/** 圆圈 */
function pie() {
  let dataArr = []
  for (var i = 0; i < 150; i++) {
    if (i % 2 === 0) {
      dataArr.push({
        name: (i + 1).toString(),
        value: 25,
        itemStyle: {
          color: '#00AAF6',
          borderWidth: 0
        }
      })
    } else {
      dataArr.push({
        name: (i + 1).toString(),
        value: 10,
        itemStyle: {
          color: '#021121',
          borderWidth: 0
        }
      })
    }
  }
  return dataArr
}

function initChart() {
  if (!chart.value) {
    chart.value = echarts.init(main.value)
  }

  const option = {
    title: {
      text: '单位: 次',
      left: '8%',
      top: 'center',
      textStyle: {
        fontSize: '14',
        color: '#fff',
        fontWeight: '600'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.5)',
      borderWidth: '1', //边框宽度设置1
      textStyle: {
        color: 'rgba(255,255,255,.8)', //设置文字颜色
        fontSize: 18
      }
    },
    legend: [
      {
        top: 'center',
        width: 100,
        right: '5%',
        selectedMode: false,
        itemWidth: 13,
        itemHeight: 13,
        itemGap: 15,
        textStyle: {
          fontSize: 15,
          color: '#E6E8E9'
        },
        data: [
          {
            name: lables.value[0],
            icon: 'image://' + icon7Img,
          },
          {
            name: lables.value[1],
            icon: 'image://' + icon4Img,
          },
          {
            name: lables.value[2],
            icon: 'image://' + icon6Img,
          },
          {
            name: lables.value[3],
            icon: 'image://' + icon1Img,
          }
        ],
        formatter: function (name: string) {
          return name + ' ' + getVal(name)
        }
      }
    ],
    series: [
      {
        name: '',
        type: 'pie',
        center: ['14%', '50%'],
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        padAngle: 5,
        itemStyle: {
          // borderRadius: 10
        },
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        emphasis: {
          scaleSize: 8,
          label: {
            show: false
          }
        },
        data: [
          {
            value: getVal(lables.value[0]),
            name: lables.value[0],
            itemStyle: {
              color: 'rgba(22, 109, 200, 0.7)'
            }
          },
          {
            value: getVal(lables.value[1]),
            name: lables.value[1],
            itemStyle: {
              color: 'rgba(4, 239, 212, 0.7)'
            }
          },
          {
            value: getVal(lables.value[2]),
            name: lables.value[2],
            itemStyle: {
              color: 'rgba(17, 184, 213, 0.7)'
            }
          },
          {
            value: getVal(lables.value[3]),
            name: lables.value[3],
            itemStyle: {
              color: 'rgba(229, 92, 80, 0.7)'
            }
          }
        ]
      },
      {
        name: '',
        type: 'pie',
        center: ['14%', '50%'],
        radius: ['72%', '80%'],
        avoidLabelOverlap: false,
        padAngle: 5,
        itemStyle: {
          borderRadius: '50%'
        },
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        zlevel: 2,
        emphasis: {
          scaleSize: 5,
          label: {
            show: false
          }
        },
        data: [
          {
            value: getVal(lables.value[0]),
            name: lables.value[0],
            itemStyle: {
              color: '#166DC8'
            }
          },
          {
            value: getVal(lables.value[1]),
            name: lables.value[1],
            itemStyle: {
              color: '#04EFD4'
            }
          },
          {
            value: getVal(lables.value[2]),
            name: lables.value[2],
            itemStyle: {
              color: '#11B8D5'
            }
          },
          {
            value: getVal(lables.value[3]),
            name: lables.value[3],
            itemStyle: {
              color: '#E55C50'
            }
          }
        ]
      },
      {
        type: 'pie',
        zlevel: 0,
        silent: true,
        center: ['14%', '50%'],
        radius: ['89%', '90%'],
        emphasis: {
          label: {
            show: false
          }
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: pie()
      }
    ]
  }

  chart.value.setOption(option)
}

function getVal(name: string) {
  return props.chartData.find((item) => item.name == name)?.value
}
</script>

<template>
  <div class="panel">
    <div class="main" ref="main"></div>
  </div>
</template>

<style lang="scss" scoped>
$panelHeight: 130px;

.main {
  height: $panelHeight;
  width: 100%;
  flex-shrink: 0;
}
</style>
