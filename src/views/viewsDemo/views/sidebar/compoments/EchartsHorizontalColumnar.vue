<script setup lang="ts">
import * as echarts from 'echarts'
import { ref, watch, onMounted } from 'vue'

interface Props {
  /** 数据 */
  chartData: Record<string, any>
  /** 每个柱上是否显示值 */
  showLabel?: boolean
  borderColor?: string
}

/** 默认显示 */
const props = withDefaults(defineProps<Props>(), {
  showLabel: true,
  borderColor: '#2AEFFC'
})

const main = ref(null)
let chart: echarts.ECharts | null = null

watch(
  () => props.chartData,
  (newVal, oldVal) => {
    initChart()
  },
  { deep: true }
)

onMounted(() => {
  initChart()
})

function initChart() {
  if (!chart) {
    chart = echarts.init(main.value)
  }

  const series = []

  if (props.chartData.columnarDataBlue.length > 0) {
    series.push({
      data: props.chartData.columnarDataBlue,
      type: 'bar',
      barWidth: '13px',
      itemStyle: {
        borderRadius: 13,
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#050A1D' },
          { offset: 1, color: '#2AEFFC' }
        ])
      }
    })
  }

  const labelArr: string[] = []
  props.chartData.columnarDataBlue.forEach((item: number) => {
    labelArr.push(item + '%')
  })

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.5)',
      borderWidth: '1', //边框宽度设置1
      borderColor: props.borderColor, //设置边框颜色
      textStyle: {
        color: '#CFE3FC', //设置文字颜色
        fontSize: 18
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      formatter: '{b0}: {c0}%'
    },
    // 设置上下左右边距
    grid: {
      top: '0%',
      left: '5%',
      right: '0%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      show: false,
      type: 'value'
    },
    yAxis: [
      {
        type: 'category',
        data: props.chartData.labelData,
        minInterval: 1,
        axisLabel: {
          fontSize: 14,
          color: 'rgba(255, 255, 255, 0.8)'
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      },
      {
        type: 'category',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 12
        },
        data: labelArr
      }
    ],
    series: series
  }

  chart.setOption(option)
}
</script>

<template>
  <div>
    <div class="main" ref="main"></div>
  </div>
</template>

<style lang="scss" scoped>
.main {
  height: 230px;
}
</style>
