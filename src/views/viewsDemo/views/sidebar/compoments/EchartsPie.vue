<script setup lang="ts">
import * as echarts from 'echarts'
import { ref, watch } from 'vue'

const props = defineProps<{
  chartData: Record<string, any>[]
  chartLegendData: Record<string, any>[]
  chartSerie1Data: Record<string, any>[]
  chartSerie2Data: Record<string, any>[]
}>()

const main = ref(null)
const chart = ref()

watch(props.chartData, (newVal, oldVal) => {
  initChart()
})

/** 圆圈 */
function pie() {
  let dataArr = []
  for (var i = 0; i < 150; i++) {
    if (i % 2 === 0) {
      dataArr.push({
        name: (i + 1).toString(),
        value: 25,
        itemStyle: {
          color: '#00AAF6',
          borderWidth: 0
        }
      })
    } else {
      dataArr.push({
        name: (i + 1).toString(),
        value: 10,
        itemStyle: {
          color: '#021121',
          borderWidth: 0
        }
      })
    }
  }
  return dataArr
}
const legend1Data = ref<Record<string, any>[]>([])
const legend2Data = ref<Record<string, any>[]>([])

function initChart() {
  const half = Math.ceil(props.chartLegendData.length / 2)

  legend1Data.value = props.chartLegendData.slice(0, half)
  legend2Data.value = props.chartLegendData.slice(half)

  if (!chart.value) {
    chart.value = echarts.init(main.value)
  }

  const option = {
    title: {
      text: '单位: 个',
      left: '8%',
      top: 'center',
      textStyle: {
        fontSize: '14',
        color: '#fff',
        fontWeight: '600'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.5)',
      borderWidth: '1', //边框宽度设置1
      textStyle: {
        color: 'rgba(255,255,255,.8)', //设置文字颜色
        fontSize: 18
      }
    },
    legend: [
      {
        top: 'center',
        width: 100,
        right: '5%',
        selectedMode: false,
        itemWidth: 13,
        itemHeight: 13,
        itemGap: 20,
        textStyle: {
          fontSize: 15,
          color: '#E6E8E9'
        },
        data: legend1Data.value,
        formatter: function (name: string) {
          return name + ' ' + getVal(name)
        }
      },
      {
        top: 'center',
        width: 100,
        right: 160,
        itemWidth: 13,
        itemHeight: 13,
        itemGap: 20,
        textStyle: {
          fontSize: 15,
          color: '#E6E8E9'
        },
        selectedMode: false,
        data: legend2Data.value,
        formatter: function (name: string) {
          return name + ' ' + getVal(name)
        }
      }
    ],
    series: [
      {
        name: '',
        type: 'pie',
        center: ['14%', '50%'],
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        minAngle: 10,
        padAngle: 5,
        itemStyle: {
          // borderRadius: 10
        },
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        emphasis: {
          scaleSize: 8,
          label: {
            show: false
          }
        },
        data: props.chartSerie1Data
      },
      {
        name: '',
        type: 'pie',
        center: ['14%', '50%'],
        radius: ['72%', '80%'],
        avoidLabelOverlap: false,
        minAngle: 10,
        padAngle: 5,
        itemStyle: {
          borderRadius: '50%'
        },
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        zlevel: 2,
        emphasis: {
          scaleSize: 5,
          label: {
            show: false
          }
        },
        data: props.chartSerie2Data
      },
      {
        type: 'pie',
        zlevel: 0,
        silent: true,
        center: ['14%', '50%'],
        radius: ['89%', '90%'],
        emphasis: {
          label: {
            show: false
          }
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: pie()
      }
    ]
  }

  chart.value.setOption(option)
}

function getVal(name: string) {
  return props.chartData.find((item) => item.name == name)?.value
}
</script>

<template>
  <div class="panel">
    <div class="main" ref="main"></div>
  </div>
</template>

<style lang="scss" scoped>
$panelHeight: 130px;

.main {
  height: $panelHeight;
  width: 100%;
  flex-shrink: 0;
}
</style>
