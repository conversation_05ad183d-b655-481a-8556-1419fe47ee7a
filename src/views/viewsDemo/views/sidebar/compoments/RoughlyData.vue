<script setup lang="ts">
interface Props {
  /** 数据列表 */
  countData: Record<string, any>[]
  /** 是否翻转 */
  overturn?: boolean
}

/** 设定默认翻转为否 */
withDefaults(defineProps<Props>(), {
  overturn: false
})
</script>

<template>
  <div class="content">
    <div class="item" v-for="(item, index) in countData" :key="index">
      <div class="icon-box" :class="['bg-' + item.color]">
        <img :src="item.img" alt="" />
      </div>
      <div class="count-box" v-if="!overturn">
        <div>{{ item.title }}</div>
        <div class="count-data">
          <span :class="['text-color-' + item.color]"
            >{{ item.count }} <span class="unit">{{ item.unit }}</span></span
          >
        </div>
      </div>
      <div class="count-box" v-else>
        <div class="count-data">
          <span :class="['text-color-' + item.color]"
            >{{ item.count }} <span class="unit">{{ item.unit }}</span></span
          >
        </div>
        <div>{{ item.title }}</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.content {
  display: flex;
  justify-content: space-around;
  margin-top: 36px;
  margin-bottom: 24px;

  .item {
    display: flex;

    .icon-box {
      width: 50px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 9px;

      img {
        height: 32px;
        width: 32px;
      }
    }

    .count-box {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      font-size: 14px;
      color: #ffffff;

      .count-data {
        font-weight: 900;
        color: transparent;
        font-family: Swei Fist Leg CJK SC;
        font-size: 20px;
        color: #ffffff;
        .unit {
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: 400;
        }
      }
    }
  }
}

.bg-green {
  background: url('/src/assets/images/common/bg_g.png') no-repeat;
  background-size: 100% 100%;
}

.bg-blue {
  background: url('/src/assets/images/common/bg_b.png') no-repeat;
  background-size: 100% 100%;
}

.bg-orange {
  background: url('/src/assets/images/common/bg_o.png') no-repeat;
  background-size: 100% 100%;
}

.text-color-green {
  color: transparent;
  -webkit-background-clip: text;
  background-image: linear-gradient(0deg, #01a4ab 20%, #fff 80%);
}

.text-color-blue {
  color: transparent;
  -webkit-background-clip: text;
  background-image: linear-gradient(0deg, #0b6cd3 20%, #fff 80%);
}

.text-color-orange {
  color: transparent;
  -webkit-background-clip: text;
  background-image: linear-gradient(0deg, #e55c50 20%, #fff 80%);
}
</style>
