<script setup lang="ts">
import * as echarts from 'echarts'
import { ref, watch, onMounted } from 'vue'

interface Props {
  /** 数据 */
  chartData: Record<string, any>
  /** 每个柱上是否显示值 */
  showLabel?: boolean
  borderColor?: string
}

/** 默认显示 */
const props = withDefaults(defineProps<Props>(), {
  showLabel: true,
  borderColor: '#2AEFFC'
})

const main = ref(null)
let chart: echarts.ECharts | null = null;

watch(
  () => props.chartData,
  (newVal, oldVal) => {
    initChart()
  },
  { deep: true }
)

onMounted(() => {
  initChart()
})

function initChart() {
  if (!chart) {
    chart = echarts.init(main.value)
  }
  // const chart = echarts.init(main.value)

  const series = []

  if (props.chartData.columnarDataBlue.length > 0) {
    series.push({
      data: props.chartData.columnarDataBlue,
      type: 'bar',
      barWidth: '15px',
      itemStyle: {
        borderRadius: 4,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#2AEFFC' },
          { offset: 1, color: '#050A1D' }
        ])
      },
      label: {
        color: '#2AEFFC',
        show: props.showLabel,
        position: 'top'
      },
      emphasis: {
        itemStyle: {
          color: '#2AEFFC'
        }
      }
    })
  }

  if (props.chartData.columnarDataYellow.length > 0) {
    series.push({
      data: props.chartData.columnarDataYellow,
      type: 'bar',
      barWidth: '15px',
      itemStyle: {
        borderRadius: 4,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#DEB565' },
          { offset: 1, color: '#050A1D' }
        ])
      },
      label: {
        color: '#DEB565',
        show: props.showLabel,
        position: 'top'
      },
      emphasis: {
        itemStyle: {
          color: '#DEB565'
        }
      }
    })
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.5)',
      borderWidth: '1', //边框宽度设置1
      borderColor: props.borderColor, //设置边框颜色
      textStyle: {
        color: '#CFE3FC', //设置文字颜色
        fontSize: 18
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      formatter: '家庭医生: {c0}'
    },
    // 设置上下左右边距
    grid: {
      top: '12%',
      left: '5%',
      right: '0%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.chartData.labelData,
      axisLabel: {
        rotate: 40,
        fontSize: 14,
        margin: 12,
        color: 'rgba(255, 255, 255, 0.8)'
      },
      axisTick: {
        // 轴刻度
        show: false
      },
      axisLine: {
        // 轴线
        show: false
        // lineStyle: {
        //   color: 'rgba(255,255,255,1)'
        // }
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      axisLabel: {
        fontSize: 14,
        color: 'rgba(255, 255, 255, 0.8)'
      },
      splitLine: {
        // 网格线
        show: true,
        lineStyle: {
          //分割线
          color: '#fff',
          width: 1,
          type: 'dashed', //dotted：虚线 solid:实线
          opacity: 0.1
        }
      }
    },
    series: series
  }

  chart.setOption(option)
}
</script>

<template>
  <div>
    <div class="main" ref="main"></div>
  </div>
</template>

<style lang="scss" scoped>
.main {
  height: 215px;
}
</style>
