<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  modelValue: string
  tabs?: Array<{label: string, value: string}>
}>()

const emit = defineEmits(['update:modelValue', 'change'])

const tabs = ref(props.tabs || [
  { label: 'labe1', value: '0' },
  { label: 'labe2', value: '1' }
])

function handleChange(val: string) {
  emit('update:modelValue', val)
  emit('change', val)
}
</script>

<template>
  <div class="split-tab">
    <template v-for="(tab, index) in tabs" :key="tab.value">
      <span
        :class="['tab-item', { active: modelValue === tab.value }]"
        @click="handleChange(tab.value)"
      >
        {{ tab.label }}
      </span>
      <span v-if="index < tabs.length - 1" class="tab-divider">/</span>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.split-tab {
  display: flex;
  align-items: center;
  margin-right: 20px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;

  .tab-item {
    cursor: pointer;
    padding: 0 5px;

    &.active {
      color: #00FFFF;
    }
  }

  .tab-divider {
    padding: 0 5px;
  }
}
</style>