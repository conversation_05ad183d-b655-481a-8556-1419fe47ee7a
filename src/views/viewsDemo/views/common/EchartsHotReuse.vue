<template>
  <div ref="chartRef" style="width: 100%; height: 200px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted,watch,onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  borderColor: {
    type: String,
    default: '#2AEFFC'
  },
  titleData: {
    type: Object,
    required: false,
    default: () => ({
      chartName: '热浏览',
      valueName: '点击量'
    })
  },
  dataKeys: {
    type: Object,
    default: () => ({
      nameKey: 'name',
      valueKey: 'browseNumber'
    })
  },
  loading: {
    type: Boolean,
    default: false
  }
})
const loadingSettings = {
    text: '加载中...',
    color: '#2AEFFC',
    textColor: '#fff',
    maskColor: 'rgba(0, 0, 0, 0.5)'
} 
const chartRef = ref()
let chart: echarts.ECharts | null = null

onMounted(() => {
  chart = echarts.init(chartRef.value)
  if(props.loading){
    // 显示加载动画
    chart.showLoading(loadingSettings)
  }else{
    // 初始化图表
    initChart()
  }
})

// 监听loading状态变化
watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading(loadingSettings)
  } else {
    chart?.hideLoading()
    initChart()
  }
})

function initChart() {
  if (!chart) return
  
  const chartData = Array.isArray(props.chartData) ? props.chartData : [props.chartData]
  // 计算总数
  const total = chartData.reduce((sum, item) => sum + (item?.[props.dataKeys.valueKey] || 0), 0)
  const data = chartData.map(item => ({
    name: item?.[props.dataKeys.nameKey],
    value: total > 0 ? parseFloat(((item?.[props.dataKeys.valueKey] / total) * 100).toFixed(2)) : 0,
    [props.dataKeys.valueKey]: item?.[props.dataKeys.valueKey]
  }));
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.5)',
      borderWidth: '1',
      borderColor: props.borderColor,
      textStyle: {
        color: '#CFE3FC',
        fontSize: 18
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      formatter: function(info: any) {
        return [
          `${info.name}: ${info.value}%`,
          `${props.titleData.valueName}: ${info.data?.[props.dataKeys.valueKey] || 0}`
        ].join('<br/>')
      }
    },
    series: [{
      name: props.titleData.chartName,
      type: 'treemap',
      visibleMin: 0,
      visualMin: 0,
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      data: data,
      breadcrumb: {
        show: false
      },
      roam: false,
      nodeClick: false,
      gapWidth: 0,
      label: {
        show: true,
        formatter: '{b}\n{c}%',
        align: 'center',
        verticalAlign: 'middle',
        lineHeight: 18
      },
      upperLabel: {
        show: false,
        height: 30
      },
      itemStyle: {
        borderColor: '#021121'
      }
    }]
  }
  
  chart.setOption(option)
  
  window.addEventListener('resize', function() {
    chart?.resize()
  })
}
onBeforeUnmount(() => {
  window.removeEventListener('resize', function() {
    chart?.resize()
  })
})
</script>