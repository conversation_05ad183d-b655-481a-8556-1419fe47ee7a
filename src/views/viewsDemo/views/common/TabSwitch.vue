<template>
  <div class="tab-container">
    <div 
      v-for="tab in tabs" 
      :key="tab.value"
      :class="['tab-item', { active: activeTab === tab.value }]"
      @click="handleTabChange(tab.value)"
    >
      <span class="tab-text">{{ tab.label }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  tabs: {
    type: Array as () => Array<{label: string, value: string}>,
    required: true
  },
  activeTab: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['tabChange'])

function handleTabChange(tab: string) {
  emit('tabChange', tab)
}
</script>

<style lang="scss" scoped>
.tab-container {
  display: flex;
  margin-bottom: 20px;
  flex-wrap: wrap;
  
  .tab-item {
    min-width: (calc(25% - 20px));
    min-height: 40px;
    display: flex; 
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #fff;
    cursor: pointer;
    background: url('@/assets/images/common/tabs.png') no-repeat center center;
    background-size: 100% 100%;
    transition: all 0.3s ease;
    filter: brightness(0.6);
    padding: 0 10px;
    
    &.active,&:hover  {
      filter: brightness(1);
      font-weight: bold;
    }
    
    .tab-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
      padding: 0px 10px;
    }
  }
}
</style>