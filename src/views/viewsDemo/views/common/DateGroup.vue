<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  index: string | number
  dateData: Array<{
    label: string
    value: string | number
  }>
}>()

const currentVal = ref<string | number>(props.index)

/** 双向绑定 */
const emit = defineEmits(['update:modelValue', 'change'])

/** 切换日期选项 */
function _change(val: string | number) {
  currentVal.value = val
  emit('change', val)
}
</script>

<template>
  <div class="time-filter">
    <div
      v-for="(item, index) in props.dateData"
      :key="item.value"
      :class="['time-item', { active: item.value === currentVal }]"
      @click="_change(item.value)"
    >
      {{ item.label }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.time-filter {
  display: flex;
  justify-content: space-around; // 改为默认居右
  margin: 15px 0;
  flex-wrap: wrap;  // 允许换行

  &:first-of-type {
    justify-content: flex-end;
  }

  .time-item {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    flex: 0 1 auto;  // 修改flex属性
    text-align: center;
    min-width: 59.6px;  // 添加最小宽度
    max-width: 80px;
    margin: 0 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);

    &.active {
      color: #fff;
      background-color: rgba(22, 119, 255, 1);
    }
  }
}
</style>
