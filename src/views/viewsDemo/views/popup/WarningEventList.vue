<script setup lang="ts">
import { dCEventList, getDCEvent, parkList } from '@/common/api/system'
import { DEVICE_TYPE, getEventTypeByValue } from '@/common/constants'
import { AES_KEY, decrypt } from '@/common/utils/aes'
import VScaleScreen from 'v-scale-screen'
import { Search, Refresh } from '@element-plus/icons-vue'
import { onMounted, nextTick, onUnmounted, reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus'
import AMapLoader from '@amap/amap-jsapi-loader'
import { useRoute } from 'vue-router'
import { useCodeStore } from '@/stores/code'

/** 搜索form */
const form = reactive({
  dateType: null,
  completeState: null,
  targetName: '',
  parkId: null,
  dateBegin: '',
  dateEnd: '',
  deviceTypes: ''
})

const data = reactive<Record<string, any>>({
  eventDetail: '',
  eventList: []
})

/** 搜索提交 */
function onSubmit() {
  getDCDeviceList()
}

const dialogVisible = ref(false)

let map: any = null
const showMap = ref(false)

//进行地图初始化
function initMap(position: string) {
  AMapLoader.load({
    key: '67aad16dea5679977f789de3872b5e8c',
    version: '2.0',
    plugins: [
      'AMap.DistrictSearch',
      'AMap.AutoComplete',
      'AMap.PlaceSearch',
      'AMap.InfoWindow',
      'AMap.MouseTool',
      'AMap.PolyEditor'
    ]
  }).then((AMap) => {
    map = new AMap.Map('map', {
      //设置地图容器id
      viewMode: '3D', //是否为3D地图模式
      zoom: 10, //初始化地图级别
      center: [116.397428, 39.90923] //初始化地图中心点位置
    })

    const pos = position.split(',')

    console.log(pos)

    const marker = new AMap.Marker({
      icon: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png',
      position: new AMap.LngLat(pos[0], pos[1]),
      offset: new AMap.Pixel(-13, -30)
    })
    marker.setMap(map)
    map.setFitView(marker)
  })
}

const route = useRoute()

const areaCode = ref('')

onMounted(() => {
  areaCode.value = route.query.areaCode as string

  getDCDeviceList()
  getParkList()
})

function getDCDeviceList() {
  dCEventList({
    keyword: '',
    isPage: true,
    rows: pageSize.value,
    page: currentPage.value,
    sort: '',
    sqlParameterList: [],
    dateBegin: form.dateBegin,
    dateEnd: form.dateEnd,
    parkId: form.parkId,
    dateType: form.dateType,
    completeState: form.completeState,
    areaCode: areaCode.value,
    TargetName: form.targetName,
    DeviceTypes: form.deviceTypes ? [form.deviceTypes] : []
  }).then((res) => {
    const page = decrypt(res, AES_KEY)
    console.log(page)
    data.eventList = page.rows
    loading.value = false
    totalPage.value = page.total
  })
}

const codeStore = useCodeStore()

function getParkList() {
  const areaCode = route.query.areaCode as string
  codeStore.setCode(areaCode);

  parkList({
    areaCode: codeStore.code
  }).then((res) => {
    const list = decrypt(res, AES_KEY, true)
    console.log(list)
    data.parkList = list
  })
}

/** 详情 */
function toDetail(id: any) {
  console.log('open dialog, id is ' + id)

  dialogVisible.value = true
  getDCEvent({
    Id: id
  }).then((res) => {
    const result = decrypt(res, AES_KEY)
    console.log(result)
    data.eventDetail = result
    console.log(data.eventDetail.Position)
  })
}

/** 当前页 */
const currentPage = ref(1)
const small = ref(false)
const disabled = ref(false)

const pageSize = ref(10)
const totalPage = ref(0)

const handleSizeChange = (val: number) => {
  console.log(`${val} items per page`)
  loading.value = true
  getDCDeviceList()
}

const handleCurrentChange = (val: number) => {
  console.log(`current page: ${val}`)
  loading.value = true
  getDCDeviceList()
}

function change(show: boolean) {
  showMap.value = show
  if (show) {
    nextTick(() => {
      initMap(data.eventDetail.Position)
    })
  }
}

const loading = ref(true)

const processStateArr = ref([
  {
    label: '已处理',
    value: 1
  },
  {
    label: '未处理',
    value: 2
  }
])

const processDateArr = ref([
  {
    label: '今日预警',
    value: 1
  },
  {
    label: '本月预警',
    value: 2
  },
  {
    label: '本年预警',
    value: 3
  }
])

const ruleFormRef = ref<FormInstance>()

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

onUnmounted(() => {
  map?.destroy()
})
</script>

<template>
  <div class="pupup-box">
    <div class="title">预警事件</div>
    <div>
      <el-form ref="ruleFormRef" :inline="true" :model="form">
        <el-form-item prop="dateType">
          <el-select v-model="form.dateType" placeholder="预警日期范围" clearable>
            <el-option
              v-for="(item, index) in processDateArr"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="completeState">
          <el-select v-model="form.completeState" placeholder="处理状态" clearable>
            <el-option
              v-for="(item, index) in processStateArr"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="deviceTypes">
          <!-- <el-input v-model="form.deviceTypes" placeholder="设备类型" clearable /> -->
          <el-select v-model="form.deviceTypes" placeholder="设备类型" filterable clearable>
            <el-option
              v-for="item in DEVICE_TYPE"
              :key="item.Value"
              :label="item.Describe"
              :value="item.Value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="parkId">
          <el-select v-model="form.parkId" placeholder="事件区域" filterable clearable>
            <el-option
              v-for="item in data.parkList"
              :key="item.ParkId"
              :label="item.Name"
              :value="item.ParkId"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="dateBegin">
          <el-date-picker v-model="form.dateBegin" type="date" placeholder="开始日期" clearable />
        </el-form-item>
        <el-form-item prop="dateEnd">
          <el-date-picker v-model="form.dateEnd" type="date" placeholder="结束日期" clearable />
        </el-form-item>
        <el-form-item>
          <el-button class="submit-btn" type="primary" :icon="Search" @click="onSubmit()"
            >查询</el-button
          >
          <el-button class="reset-btn" :icon="Refresh" @click="resetForm(ruleFormRef)"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <div class="">
      <el-table
        :max-height="'calc(100vh - 230px)'"
        :data="data.eventList"
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-background="rgba(0, 0, 0, 0.6)"
      >
        <el-table-column label="事件对象" prop="TargetName" align="center"> </el-table-column>
        <el-table-column label="事件名称" prop="TotalEventName" align="center"> </el-table-column>
        <el-table-column label="抓拍图片" align="center">
          <template #default="scope">
            <div>
              <!-- Photo -->
              <el-image
                :preview-teleported="true"
                v-if="scope.row.Photo"
                style="width: 30px; height: 30px"
                :src="scope.row.Photo"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="[scope.row.Photo]"
                :initial-index="4"
                fit="cover"
              />
              <span v-else>-</span>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="底图图片" align="center">
          <template #default="scope">
            <div class="tb-img">
              <el-image
                :preview-teleported="true"
                v-if="scope.row.BottomImage"
                style="width: 72px; height: 94px"
                :src="scope.row.BottomImage"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="[scope.row.BottomImage]"
                :initial-index="4"
                fit="cover"
              />
              <span v-else>-</span>
            </div>
          </template>
        </el-table-column> -->
        <el-table-column label="事件区域" prop="Name" align="center"> </el-table-column>
        <el-table-column label="时间" align="center">
          <template #default="scope">
            <div>{{ (scope.row.EventDate || '').replace('T', ' ') }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="AssignPerson" label="信息接收人" align="center"> </el-table-column>
        <el-table-column label="状态" align="center" width="85">
          <template #default="scope">
            <div>
              <span v-if="scope.row.IsRead == 1" class="online">已读</span>
              <span v-else class="offline">未读</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="通知状态" align="center" width="100">
          <template #default="scope">
            <div>
              <span v-if="scope.row.MessageState == 1" class="online">已发送</span>
              <span v-else class="offline">未发送</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="85">
          <template #default="scope">
            <el-button size="small" @click="toDetail(scope.row.DcetId)">详情 </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="page-box">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :small="small"
          :disabled="disabled"
          background
          layout="sizes, prev, pager, next"
          :total="totalPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <el-dialog top="0" v-model="dialogVisible" fullscreen align-center>
      <div class="content">
        <div class="event-message">
          <div class="item">
            <div class="title">预警信息</div>
            <div class="info">
              <div>
                <span class="subtitle">预警事件：</span>
                {{ data.eventDetail.TotalEventName }}
              </div>
              <div>
                <span class="subtitle">预警时间：</span>
                {{
                  data.eventDetail.EventDate
                    ? data.eventDetail.EventDate.replace('T', ' ').substring(0, 19)
                    : '-'
                }}
              </div>
            </div>
            <div class="info">
              <div>
                <span class="subtitle">预警类型：</span>
                {{ getEventTypeByValue(data.eventDetail.TotalEventType) }}
              </div>
              <div>
                <span class="subtitle">预警设备：</span>
                <span style="color: #166dc8">{{ data.eventDetail.DeviceName }}</span>
              </div>
            </div>
            <div class="info">
              <span class="subtitle">分析说明：</span>
              {{ data.eventDetail.WarningDescribe }}
            </div>
            <div class="info">
              <span class="subtitle">所属辖区：</span>{{ data.eventDetail.DeviceAddress }}
            </div>
          </div>
          <!-- <div class="item">
            <div class="title">人员信息</div>
            <div class="info">
              <div><span class="subtitle">姓名：</span>{{ data.eventDetail.TargetName }}</div>
              <div><span class="subtitle">身份证号：</span>{{ data.eventDetail.PersonIDCard }}</div>
            </div>
            <div class="info">
              <span class="subtitle">所属房屋：</span>{{ data.eventDetail.DeviceAddress }}
            </div>
            <div class="info">
              <div>
                <span class="subtitle">身份类型：</span
                >{{ data.eventDetail.PersonHouseIdentity || '-' }}
              </div>
              <div>
                <span class="subtitle">信息来源：</span
                >{{ data.eventDetail.PersonHouseIdentity || '-' }}
              </div>
            </div>
          </div> -->
          <div class="item item-img">
            <div class="title">相关照片</div>
            <div class="img-box">
              <!-- Photo -->
              <el-image
                :preview-teleported="true"
                v-if="data.eventDetail.Photo"
                style="width: 72px; height: 94px"
                :src="data.eventDetail.Photo"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="[data.eventDetail.Photo]"
                :initial-index="4"
                fit="cover"
              />
              <span v-else>-</span>
            </div>
          </div>
        </div>
        <div class="item right-box">
          <div class="title">
            <span @click="change(false)" :style="!showMap ? 'color: rgb(22, 109, 200)' : ''"
              >事件过程</span
            >
            /
            <span @click="change(true)" :style="showMap ? 'color: #1e89fe' : ''">预警位置</span>
          </div>
          <div v-show="showMap" id="map" />
          <div v-show="!showMap">
            <el-steps direction="vertical" :active="1">
              <el-step
                :title="item.Title"
                v-for="(item, index) in data.eventDetail.ProcessList"
                :key="index"
              >
                <template #description>
                  <div>{{ item.Message }}</div>
                </template>
              </el-step>
            </el-steps>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.content {
  display: flex;
  justify-content: space-between;
}

.event-message {
  flex: 1;

  .item {
    margin-bottom: 23px;

    .info {
      display: flex;
      margin-bottom: 5px;
      line-height: 28px;

      .subtitle {
        font-weight: 600;
        white-space: nowrap;
      }

      div {
        width: 48%;
        margin-right: 5px;
      }
    }
  }

  .item-img {
    margin-bottom: 0;

    .title {
      margin-bottom: 23px;
    }

    .img-box {
      display: flex;
      width: 440px;
      // height: 111px;
      background: #070f24;
      border: 1px solid #18264b;
      opacity: 0.6;
      padding: 9px 8px 8px;

      img {
        width: 72px;
        height: 94px;
        margin-right: 8px;
      }
    }
  }
}

.right-box {
  flex: 1;
  margin-left: 32px;
}
</style>
