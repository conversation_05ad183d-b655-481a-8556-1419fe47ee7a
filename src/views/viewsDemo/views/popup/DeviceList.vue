<script setup lang="ts">
import { dCDeviceList, getVideo, parkList } from '@/common/api/system'
import { AES_KEY, decrypt } from '@/common/utils/aes'
import VScaleScreen from 'v-scale-screen'
import { Search, Refresh } from '@element-plus/icons-vue'
import { nextTick, onUnmounted, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { FormInstance } from 'element-plus'
import AMapLoader from '@amap/amap-jsapi-loader'
import { useCodeStore } from '@/stores/code'
import Hls from 'hls.js'
import { KEY } from '@/common/config'

/** 路由 */
const router = useRouter()
/** 路由参数 */
const route = useRoute()
const video = ref()
/** 搜索form */
const form = reactive({
  deviceName: '',
  deviceState: null,
  deviceNumber: '',
  parkId: null,
  dateBegin: '',
  dateEnd: ''
})

const data = reactive<Record<string, any>>({
  parkList: [],
  deviceList: []
})

/** 设备状态下拉菜单 */
const deviceStateArr = ref([
  {
    label: '在线',
    value: 1
  },
  {
    label: '离线',
    value: 2
  }
])

const dialogVisible = ref(false)
/** 视频弹窗 */
const dialogVisibleVideo = ref(false)

function onSubmit() {
  getDCDeviceList()
}

function handleClose() {
  dialogVisible.value = false
  dialogVisibleVideo.value = false

  video.value.pause()
}

const params = reactive({
  areaCode: '',
  deviceName: '',
  deviceType: ''
})

onMounted(() => {
  params.deviceName = route.query.deviceName as string
  params.deviceType = route.query.deviceType as string
  params.areaCode = route.query.areaCode as string
  getDCDeviceList()
  getParkList()
})

function getDCDeviceList() {
  dCDeviceList({
    AreaCode: params.areaCode,
    DateBegin: form.dateBegin,
    DateEnd: form.dateEnd,
    DeviceName: form.deviceName,
    DeviceState: form.deviceState,
    DeviceNumber: form.deviceNumber,
    DeviceType: params.deviceType,
    ParkId: form.parkId,
    Rows: pageSize.value,
    Page: currentPage.value
  }).then((res) => {
    const page = decrypt(res, AES_KEY)
    console.log(page)
    data.deviceList = page.rows
    totalPage.value = page.total
    loading.value = false
  })
}

const codeStore = useCodeStore()

/** 获取小区列表 */
function getParkList() {
  const areaCode = route.query.areaCode as string
  codeStore.setCode(areaCode);

  parkList({
    areaCode: codeStore.code
  }).then((res) => {
    const list = decrypt(res, AES_KEY, true)
    console.log(list)
    data.parkList = list
  })
}

/** 显示地图 */
function showMap(position: string) {
  dialogVisible.value = true
  nextTick(() => {
    initMap(position)
  })
}

let map: any = null

//进行地图初始化
function initMap(position: string) {
  AMapLoader.load({
    key: KEY,
    version: '2.0',
    plugins: [
      'AMap.DistrictSearch',
      'AMap.AutoComplete',
      'AMap.PlaceSearch',
      'AMap.InfoWindow',
      'AMap.MouseTool',
      'AMap.PolyEditor'
    ]
  }).then((AMap) => {
    map = new AMap.Map('map', {
      //设置地图容器id
      viewMode: '3D', //是否为3D地图模式
      zoom: 10, //初始化地图级别
      center: [116.397428, 39.90923] //初始化地图中心点位置
    })

    const pos = position.split(',')

    console.log(pos)

    const marker = new AMap.Marker({
      icon: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png',
      position: new AMap.LngLat(pos[0], pos[1]),
      offset: new AMap.Pixel(-13, -30)
    })
    marker.setMap(map)
    map.setFitView(marker)
  })
}

/** 显示视频 */
function showVideo(item: Record<string, any>) {
  dialogVisibleVideo.value = true

  console.log('get video...')

  nextTick(() => {
    getVideo({
      url: `${item.PDLVFP_Url}/api/Device/StreamLive?Id=${item.PDLVFP_Id}`
    }).then((res) => {
      console.log(res.hls)
      const hls = new Hls()
      hls.loadSource(res.hls)
      hls.attachMedia(video.value)
      hls.on(Hls.Events.MANIFEST_PARSED, function () {
        video.value.play()
      })
    })
  })
}

/** 当前页 */
const currentPage = ref(1)
/** 请求页数 */
const pageSize = ref(10)

const small = ref(false)
const disabled = ref(false)

/** 表格加载loading */
const loading = ref(true)
/** 总页数 */
const totalPage = ref(0)

const handleSizeChange = (val: number) => {
  console.log(`${val} items per page`)
  loading.value = true
  getDCDeviceList()
}
const handleCurrentChange = (val: number) => {
  console.log(`current page: ${val}`)
  loading.value = true
  getDCDeviceList()
}

const ruleFormRef = ref<FormInstance>()

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

onUnmounted(() => {
  map?.destroy()
})
</script>

<template>
  <div class="pupup-box">
    <div class="title">{{ params.deviceName }}</div>
    <div>
      <el-form ref="ruleFormRef" :inline="true" :model="form">
        <el-form-item prop="deviceName">
          <el-input v-model="form.deviceName" placeholder="设备名称" clearable />
        </el-form-item>
        <el-form-item prop="deviceState">
          <el-select v-model="form.deviceState" placeholder="设备状态" clearable>
            <el-option
              v-for="(item, index) in deviceStateArr"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="deviceNumber">
          <el-input v-model="form.deviceNumber" placeholder="设备编号" clearable />
        </el-form-item>
        <el-form-item prop="parkId">
          <el-select v-model="form.parkId" placeholder="所在小区" clearable>
            <el-option
              v-for="item in data.parkList"
              :key="item.ParkId"
              :label="item.Name"
              :value="item.ParkId"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="dateBegin">
          <el-date-picker v-model="form.dateBegin" type="date" placeholder="开始日期" />
        </el-form-item>
        <el-form-item prop="dateEnd">
          <el-date-picker v-model="form.dateEnd" type="date" placeholder="结束日期" />
        </el-form-item>
        <el-form-item>
          <el-button class="submit-btn" type="primary" :icon="Search" @click="onSubmit()"
            >查询</el-button
          >
          <el-button class="reset-btn" :icon="Refresh" @click="resetForm(ruleFormRef)"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <div class="table-box">
      <el-table
        :max-height="'calc(100vh - 230px)'"
        :data="data.deviceList"
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-background="rgba(0, 0, 0, 0.6)"
      >
        <el-table-column label="设备名称" prop="DeviceName" align="center"> </el-table-column>
        <el-table-column label="所属小区" prop="ParkName" align="center"> </el-table-column>
        <el-table-column label="设备地址" prop="DeviceAddress" align="center"> </el-table-column>
        <el-table-column label="设备编号" prop="DeviceSN" align="center"> </el-table-column>
        <el-table-column label="坐标" align="center" width="85">
          <template #default="scope">
            <div @click="showMap(scope.row.Position)">查看</div>
          </template>
        </el-table-column>
        <el-table-column label="实时画面" align="center" width="90">
          <template #default="scope">
            <el-icon
              v-if="scope.row.PDLVFP_Id && scope.row.PDLVFP_StreamType && scope.row.PDLVFP_Url"
              color="#b9b9b9"
              size="20"
              @click="showVideo(scope.row)"
            >
              <CameraFilled />
            </el-icon>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="address" label="在线情况" align="center" width="90">
          <template #default="scope">
            <div>
              <span v-if="scope.row.DeviceState == 1" class="online">在线</span>
              <span v-else class="offline">离线</span>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作" align="center" width="85">
          <template #default="scope">
            <el-button size="small" @click="showDetail(scope.row.Type)">查看 </el-button>
          </template>
        </el-table-column> -->
      </el-table>

      <div class="page-box">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :small="small"
          :disabled="disabled"
          background
          layout="sizes, prev, pager, next"
          :total="totalPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <el-dialog title="位置坐标" v-model="dialogVisible">
      <div id="map"></div>
    </el-dialog>
    <el-dialog
      title="视频画面"
      v-model="dialogVisibleVideo"
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <div class="video-box">
        <video ref="video" controls autoplay style="width: 100%; height: 360px;"></video>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.ep-table) {
  background: rgba(4, 11, 29, 0.9);
}

//进度条
video::-webkit-media-controls-timeline {
  display: none;
}
</style>
