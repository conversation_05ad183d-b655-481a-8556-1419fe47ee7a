<script setup lang="ts">
    import { ref} from 'vue'  
    import VScaleScreen from 'v-scale-screen'
    import TabSwitch from '@/views/common/TabSwitch.vue'
    import Tour from '@/views/sharing/tabPages/tour.vue'  
    import Guide from '@/views/sidebar/CommunityNavigation.vue' 

    const activeTab = ref('guide')
    const tabs = [
    { label: '社区导览', value: 'guide' },
    { label: '社区游线', value: 'tour' }
    ]
    function changeTab(tab: string) {
        activeTab.value = tab
    }
</script>
<template>
    <VScaleScreen
      :delay="200"
      width="480"
      height="1500"
      :bodyOverflowHidden="false"
      :autoScale="{ x: true, y: false }"
      :boxStyle="{ background: '#021121' }"
    >
        <div class="base-box">
            <TabSwitch 
            :tabs="tabs" 
            :activeTab="activeTab"
            @tabChange="changeTab"
            />
            <div v-if="activeTab === 'guide'">
               <Guide></Guide>
            </div>
            <div v-if="activeTab === 'tour'">
              <Tour></Tour>
            </div>
        </div>
    </VScaleScreen>
</template>
<style lang="scss" scoped>
.v-screen-box {
  height: 1500px !important;
}

.module-box {
  border: 1px solid #20497F;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
}
</style>