<script setup lang="ts">
    import { ref} from 'vue'  
    import VScaleScreen from 'v-scale-screen'
    import TabSwitch from '@/views/common/TabSwitch.vue'
    import Doctor from '@/views/sidebar/HomeDoctor.vue'  
    import Tools from '@/views/sidebar/UtilityTool.vue' 

    const activeTab = ref('doctor')
    const tabs = [
    { label: '家庭医生', value: 'doctor' },
    { label: '实用工具', value: 'tools' }
    ]
    function changeTab(tab: string) {
        activeTab.value = tab
    }
</script>
<template>
    <VScaleScreen
      :delay="200"
      width="480"
      height="1120"
      :bodyOverflowHidden="false"
      :autoScale="{ x: true, y: false }"
      :boxStyle="{ background: '#021121' }"
    >
        <div class="base-box">
            <TabSwitch 
            :tabs="tabs" 
            :activeTab="activeTab"
            @tabChange="changeTab"
            />
            <div v-if="activeTab === 'doctor'">
               <Doctor></Doctor>
            </div>
            <div v-if="activeTab === 'tools'">
              <Tools></Tools>
            </div>
        </div>
    </VScaleScreen>
</template>
<style lang="scss" scoped>
.v-screen-box {
  height: 1120px !important;
}

.module-box {
  border: 1px solid #20497F;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
}
</style>