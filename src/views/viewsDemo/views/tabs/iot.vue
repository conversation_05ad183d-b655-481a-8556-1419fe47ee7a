<script setup lang="ts">
    import { ref} from 'vue'  
    import VScaleScreen from 'v-scale-screen'
    import TabSwitch from '@/views/common/TabSwitch.vue'
    import There from '@/views/three-color-warning/index.vue'  
    import Info from '@/views/sidebar/EquipmentOperationOverview.vue'   
    import Warning from '@/views/sidebar/EquipmentWarningSituation.vue' 

    const activeTab = ref('there')
    const tabs = [
    { label: '三色预警', value: 'there' },
    { label: '物联设备运行概况', value: 'info' },
    { label: '设备预警概况', value: 'warning' }
    ]
    function changeTab(tab: string) {
        activeTab.value = tab
    }
</script>
<template>
    <VScaleScreen
      :delay="200"
      width="480"
      height="1540"
      :bodyOverflowHidden="false"
      :autoScale="{ x: true, y: false }"
      :boxStyle="{ background: '#021121' }"
    >
        <div class="base-box">
            <TabSwitch 
            :tabs="tabs" 
            :activeTab="activeTab"
            @tabChange="changeTab"
            />
            <div v-if="activeTab === 'there'">
               <There></There>
            </div>
            <div v-if="activeTab === 'info'">
              <Info></Info>
            </div>
            <div v-if="activeTab === 'warning'">
              <Warning></Warning>
            </div>
        </div>
    </VScaleScreen>
</template>
<style lang="scss" scoped>
.v-screen-box {
  height: 1540px !important;
}

.module-box {
  border: 1px solid #20497F;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
}
</style>