<template>
  <div class="reuse-overview">
    <div class="data-item" style="border: 1px solid #1677FF">
      <div class="icon">
        <img src="@/assets/images/common/bell.png" alt="call">
      </div>
      <div class="text">
        <div class="name">{{ titleData.callTitle}}</div>
        <div class="value">{{ overviewData.callCount || 0 }}{{ titleData.callUnit }}</div>
      </div>
    </div>
    <div class="data-item" style="border: 1px solid #00FFFF">
      <div class="icon">
        <img src="@/assets/images/common/person.png" alt="service">
      </div>
      <div class="text">
        <div class="name">{{ titleData.serviceTitle}}</div>
        <div class="value">{{ overviewData.serviceCount || 0 }}{{ titleData.serviceUnit }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  overviewData: {
    type: Object,
    required: true,
    default: () => ({
      callCount: 0,
      serviceCount: 0
    })
  },
  titleData: {
    type: Object,
    required: false,
    default: () => ({
      callTitle: '来电数',
      serviceTitle: '服务人数',
      callUnit: '次',
      serviceUnit: '人'
    })
  }
})
</script>

<style lang="scss" scoped>
.reuse-overview {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  
  .data-item {
    display: flex;
    align-items: center;
    background: transparent; 
    border-radius: 8px;
    padding: 15px;
    width: 140px;
    height: 68px;
    .icon {
      width: 40px;
      height: 40px;
      margin-right: 15px;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    
    .text {
      .name {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0.130vw;
        
       
      }
      
      .value {
        font-size:12px;
        font-weight: bold;
        color: #fff;
        
      
      }
    }
  }
}
</style>