<template>
  <div ref="chartRef" style="width: 100%; height: 300px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
    default: () => ({
      columnarDataBlue: [],
      columnarDataYellow: [],
      labelData: []
    })
  },
  borderColor: {
    type: String,
    default: '#0170FE'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const loadingSettings = {
  text: '加载中...',
  color: '#0170FE',
  textColor: '#fff',
  maskColor: 'rgba(0, 0, 0, 0.5)'
}

const chartRef = ref()
let chart: echarts.ECharts | null = null
let intervalId: number | null = null
const displayCount = 10 // 每次显示的数据条数

watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading(loadingSettings)
  } else {
    chart?.hideLoading()
    initChart()
  }
})

function initChart() {
  if (intervalId) {
    clearInterval(intervalId)
  }
  if (!chart) return

  // 提取数据用于图表
  const chartData = Array.isArray(props.chartData) ? props.chartData : [props.chartData]
  const allLabels = chartData.map(item => item.district?.name || '未知区域')
  const allBlueData = chartData.map(item => item.callCount || 0)
  const allYellowData = chartData.map(item => item.serviceCount || 0)
  
  
  // 初始显示前10条数据
  let currentLabels = allLabels.slice(0, displayCount)
  let currentBlueData = allBlueData.slice(0, displayCount)
  let currentYellowData = allYellowData.slice(0, displayCount)
  
  const updateChart = () => {
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.5)',
        borderWidth: '1',
        borderColor: props.borderColor,
        textStyle: {
          color: '#CFE3FC',
          fontSize: 18
        },
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      legend: {
        data: ['响铃数量', '服务数量'],
        textStyle: {
          color: '#fff'
        },
        icon: 'rect',
        itemWidth: 12,
        itemHeight: 12,
      },
      grid: {
        left: '3%',
        right: '3%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: currentLabels,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          interval: 0,
          rotate: 40,
          fontSize: 14
        },
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)'
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      series: [
        {
          name: '响铃数量',
          type: 'bar',
          data: currentBlueData,
          itemStyle: {
            borderRadius: 4,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#018FFF' },
              { offset: 1, color: 'rgba(1, 143, 255, 0.2)' }
            ])
          }
        },
        {
          name: '服务数量',
          type: 'bar',
          data: currentYellowData,
          itemStyle: {
            borderRadius: 4,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#00FFFF' },
              { offset: 1, color: 'rgba(0, 255, 255, 0.2)' }
            ])
          }
        }
      ]
    }
    chart?.setOption(option)
  }
  
  // 初始化图表
  updateChart()
  
  // 数据轮播逻辑
  if (allLabels.length > displayCount) {
    let currentIndex = 0
    
    intervalId = window.setInterval(() => {
      currentIndex = (currentIndex + 1) % allLabels.length
      
      currentLabels = []
      currentBlueData = []
      currentYellowData = []
      
      for (let i = 0; i < displayCount; i++) {
        const index = (currentIndex + i) % allLabels.length
        currentLabels.push(allLabels[index])
        currentBlueData.push(allBlueData[index])
        currentYellowData.push(allYellowData[index])
      }
      
      updateChart()
    }, 3000)
  }
  
  window.addEventListener('resize', function() {
    chart?.resize()
  })
}

onMounted(() => {
  chart = echarts.init(chartRef.value)
  if(props.loading){
    chart.showLoading(loadingSettings)
  }else{
    initChart()
  }
})

onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
  window.removeEventListener('resize', function() {
    chart?.resize()
  })
  if (chart) {
    chart.dispose()
    chart = null
  }
})
</script>