<template>
  <div ref="chartRef" style="width: 100%; height: 250px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  borderColor: {
    type: String,
    default: '#5B8FF9'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const loadingSettings = {
  text: '加载中...',
  color: '#5B8FF9',
  textColor: '#fff',
  maskColor: 'rgba(0, 0, 0, 0.5)'
}

const chartRef = ref()
let chart: echarts.ECharts | null = null

watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading(loadingSettings)
  } else {
    chart?.hideLoading()
    initChart()
  }
})

function initChart() {
  if (!chart) return
  
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.5)',
      borderWidth: '1',
      borderColor: props.borderColor,
      textStyle: {
        color: '#CFE3FC',
        fontSize: 18
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    legend: {
      data: ['今日来电', '昨日来电'],
      textStyle: {
        color: '#fff'
      },
    },
    grid: {
      left: '3%',
      right: '3%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: '10%',
      data: props.chartData.label,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        interval: 0,
        rotate: 30,
        margin: 15,
        width: 60,
        overflow: 'truncate'
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    series: [
      {
        name: '今日来电',
        type: 'line',
        data: props.chartData.red,
        itemStyle: {
          color: '#5B8FF9'
        },
        lineStyle: {
          width: 3
        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 12,
          formatter: '{c}'
        }
      },
      {
        name: '昨日来电',
        type: 'line',
        data: props.chartData.yellow,
        itemStyle: {
          color: '#5AD8A6'
        },
        lineStyle: {
          width: 3
        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 12,
          formatter: '{c}'
        }
      }
    ]
  }
  
  chart.setOption(option)
  
  window.addEventListener('resize', function() {
    chart?.resize()
  })
}

onMounted(() => {
  chart = echarts.init(chartRef.value)
  if(props.loading){
    chart.showLoading(loadingSettings)
  }else{
    initChart()
  }
})
</script>