<template>
  <VScaleScreen
    :delay="200"
    width="480"
    height="1400"
    :bodyOverflowHidden="false"
    :autoScale="{ x: true, y: false }"
    :boxStyle="{ background: '#021121' }"
  >
    <div class="base-box">
    <!-- 第一个模块：社区响应概况 -->
    <div class="module-box">
      <ModuleTitle title="社区响应概况" />
      <DateGroup index="0" :dateData="timeOptions" @change="changeCommunityResponse" />
      <!-- 假设这里使用一个自定义组件显示来电数和服务人数 -->
      <CommunityResponseOverview :overviewData="communityResponseData" />
    </div>

    <!-- 第二个模块：崃建言处理情况 -->
    <div class="module-box">
      <ModuleTitle title="社区响应处理情况" />
      <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" @change="changeSuggestionHandling" />
      <EchartsSuggestionHandling 
        :chartData="suggestionHandlingData" 
        :seriesNames="{
          suggestions: '响铃数量',
          responses: '响应数量'
        }"
        :loading="isLoadingList"
      />
    </div>

    <!-- 第三个模块：社区来电时段概况 -->
    <div class="module-box">
      <ModuleTitle title="社区来电时段概况" />
      <div style="margin-top: 20px;">
        <EchartsBellCurve :chartData="callTimeOverviewData" :loading="isLoadingCallLine" :borderColor="'#5B8FF9'" />
      </div>
    </div>

    <!-- 第四个模块：AI智能客服热门问题 -->
    <div class="module-box">
      <ModuleTitle title="AI智能客服热门问题" />
      <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))"  @change="changeHotQuestions" />
      <EchartsHotReuse :chartData="hotQuestionsData" :loading="isLoadingHots"
          :dataKeys="{
            nameKey: 'content',
            valueKey: 'count'
          }" :titleData="{chartName: '热浏览',valueName: '点击量'}" />
    </div>
  </div>
</VScaleScreen>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import ModuleTitle from '@/views/sidebar/compoments/ModuleTitle.vue'
import DateGroup from '@/views/common/DateGroup.vue'
import CommunityResponseOverview from './compoments/CommunityResponseOverview.vue'
import EchartsSuggestionHandling from './compoments/EchartsSuggestionHandling.vue'
import EchartsBellCurve from './compoments/EchartsBellCurve.vue'
import EchartsHotReuse from '@/views/common/EchartsHotReuse.vue'
import { useCodeStore } from '@/stores/code'
import VScaleScreen from 'v-scale-screen'
import { AES_KEY, decrypt, encrypt } from '@/common/utils/aes'
import { bellInfo, bellList, bellCallLine,aiHots } from '@/common/api/bellApi'

const isLoadingInfo = ref(true)
const isLoadingList = ref(true)
const isLoadingCallLine = ref(true)
const isLoadingHots = ref(true)

const codeStore = useCodeStore()

// 时间选项
const timeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]

// 第一个模块数据
const communityResponseData = ref({})
const suggestionHandlingData = ref({})
const callTimeOverviewData = ref({})
const hotQuestionsData = ref([])

// API调用函数
async function getBellInfo(timeType: number | string = 0) {
  isLoadingInfo.value = true
  const res = await bellInfo({
    data: encrypt({ timeType }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  communityResponseData.value = data
  isLoadingInfo.value = false
}

async function getBellList(timeType: number | string = 2) {
  isLoadingList.value = true
  const res = await bellList({
    data: encrypt({ 
      timeType:timeType,
      areaCode: codeStore.code 
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  suggestionHandlingData.value = data.list
  isLoadingList.value = false
}

async function getBellCallLine(timeType: number | string = 2) {
  isLoadingCallLine.value = true
  const res = await bellCallLine({
    data: encrypt({ timeType }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  
  // 初始化12个双数时段(2-24)
  const processedData = {
    red: Array(12).fill(0),   // 今日数据
    yellow: Array(12).fill(0), // 昨日数据
    label: Array.from({length: 12}, (_, i) => `${(i+1)*2}`) // 2,4,6...24
  }

  data.list.forEach((item: { hour: string; todayCallCount: any; yesterdayCallCount: any }) => {
    const hour = parseInt(item.hour)
    if (hour >= 0 && hour <= 24) {
      // 累加到后一位双数小时
      const evenHour = Math.ceil(hour / 2) * 2
      const index = (evenHour / 2) - 1 // 调整索引从0开始
      
      // 确保索引不超过数组范围
      if (index >= 0 && index < 12) {
        processedData.red[index] += item.todayCallCount
        processedData.yellow[index] += item.yesterdayCallCount
      }
    }
  })

  callTimeOverviewData.value = processedData
  isLoadingCallLine.value = false
}

async function getAiHots(timeType: number | string = 2) {
  isLoadingHots.value = true
  const res = await aiHots({
    data: encrypt({ timeType }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  hotQuestionsData.value = data.list
  isLoadingHots.value = false
}

// 修改筛选回调函数
const changeCommunityResponse = (value: string) => {
  getBellInfo(value)
}

const changeSuggestionHandling = (value: string) => {
  getBellList(value)
}

const changeHotQuestions = (value: string) => {
  getAiHots(value)
}

onMounted(() => {
  getBellInfo()
  getBellList()
  getBellCallLine()
  getAiHots()
})
</script>

<style scoped>
.v-screen-box {
  height: 1400px !important;
}

.module-title {
  margin: 20px 0;
}

.module-box {
  border: 1px solid #20497F;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
}
</style>