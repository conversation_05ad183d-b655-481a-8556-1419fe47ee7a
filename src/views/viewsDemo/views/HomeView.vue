<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()
</script>

<template>
  <el-card>
    <!-- <el-button @click="router.push('equipmentOperationOverview')">物联设备运行概况</el-button>
    <el-button type="warning" @click="router.push('equipmentWarningSituation')">物联设备预警情况</el-button>
    <el-button type="primary" @click="router.push('communityNavigation')">社区导览</el-button>
    <el-button type="success" @click="router.push('homeDoctor')">家庭医生</el-button>
    <el-button type="primary" @click="router.push('utilityTool')">实用工具</el-button> -->

    <el-button type="primary" @click="router.push('iot')">智能物联</el-button>
    <el-button type="primary" @click="router.push('service')">智能物联</el-button>
          <el-button type="primary" @click="router.push('circle')">心愿桥</el-button>
      <el-button type="primary" @click="router.push('share')">找社区</el-button>
    <el-button type="primary" @click="router.push('economy')">崃建言</el-button>
    <el-button type="primary" @click="router.push('bell')">协商铃</el-button>
    <el-button type="primary" @click="router.push('mutualAid')">微互助</el-button>
    <el-button type="primary" @click="router.push('suggestion')">崃建言</el-button>
  </el-card>
</template>
