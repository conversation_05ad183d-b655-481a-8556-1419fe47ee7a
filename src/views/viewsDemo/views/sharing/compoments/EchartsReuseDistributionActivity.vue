<template>
  <div ref="chartRef" style="width: 100%; height: 300px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted,onBeforeUnmount,watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  borderColor: {
    type: String,
    default: '#2AEFFC'
  },
  loading: {
    type: Boolean,
    default: false
  },
  seriesNames: {
    type: Object,
    default: () => ({
      total: '活动总数',
      ongoing: '进行中',
      ended: '已结束',
      participants: '参与人数'
    })
  }
})
const loadingSettings = {
    text: '加载中...',
    color: '#2AEFFC',
    textColor: '#fff',
    maskColor: 'rgba(0, 0, 0, 0.5)'
} 
const chartRef = ref()
let chart: echarts.ECharts | null = null
let intervalId: number | null = null
const displayCount = 10 // 每次显示的数据条数
// 监听loading状态变化
watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading(loadingSettings)
  } else {
    chart?.hideLoading()
    initChart()
  }
})
function initChart() {
  if (intervalId) {
    clearInterval(intervalId)
  }
  if (!chart) return
  const chartData = Array.isArray(props.chartData) ? props.chartData : [props.chartData]
  const allLabels = chartData.map(item => item.district?.name || '未知区域')
  const allCounts = chartData.map(item => item.total || 0)
  const inCounts = chartData.map(item => item.ongoing || 0)
  const endCounts = chartData.map(item => item.ended || 0)
  const participantCounts = chartData.map(item => item.participants || 0)
  
  // 初始显示前10条数据
  let currentLabels = allLabels.slice(0, displayCount)
  let currentAllCounts = allCounts.slice(0, displayCount)
  let currentInCounts = inCounts.slice(0, displayCount)
  let currentEndCounts = endCounts.slice(0, displayCount)
  let currentParticipantCounts = participantCounts.slice(0, displayCount)
  
  const updateChart = () => {
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.5)',
        borderWidth: '1',
        borderColor: props.borderColor,
        textStyle: {
          color: '#CFE3FC',
          fontSize: 18
        },
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      legend: {
        data: [
          props.seriesNames.total,
          props.seriesNames.ongoing,
          props.seriesNames.ended,
          props.seriesNames.participants
        ],
        textStyle: {
          color: '#fff'
        },
        icon: 'rect',
        itemWidth: 12,
        itemHeight: 12,
      },
      grid: {
        left: '3%',
        right: '3%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: currentLabels,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          interval: 0,
          rotate: 40,
          fontSize: 14
        },
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)'
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      series: [
        {
          name: props.seriesNames.total,
          type: 'bar',
          data: currentAllCounts,
          itemStyle: {
            borderRadius: 4,
            color: '#1677FF'
          }
        },
        {
          name: props.seriesNames.ongoing,
          type: 'bar',
          data: currentInCounts,
          itemStyle: {
            borderRadius: 4,
            color: '#00B578'
          }
        },
        {
          name: props.seriesNames.ended,
          type: 'bar',
          data: currentEndCounts,
          itemStyle: {
            borderRadius: 4,
            color: '#96A9C4'
          }
        },
        {
          name: props.seriesNames.participants,
          type: 'bar',
          data: currentParticipantCounts,
          itemStyle: {
            borderRadius: 4,
            color: '#FF9F18'
          }
        }
      ]
    }
    chart?.setOption(option)
  }
  
  // 初始化图表
  updateChart()
  
  // 数据轮播逻辑
  if (allLabels.length > displayCount) {
    let currentIndex = 0
    
    intervalId = window.setInterval(() => {
      // 计算新的起始索引
      currentIndex = (currentIndex + 1) % allLabels.length
      
      // 获取新的10条数据
      currentLabels = []
      currentAllCounts = []
      currentInCounts = []
      currentEndCounts = []
      currentParticipantCounts = []
      
      for (let i = 0; i < displayCount; i++) {
        const index = (currentIndex + i) % allLabels.length
        currentLabels.push(allLabels[index])
        currentAllCounts.push(allCounts[index])
        currentInCounts.push(inCounts[index])
        currentEndCounts.push(endCounts[index])
        currentParticipantCounts.push(participantCounts[index])
      }
      
      updateChart()
    }, 3000)
  }
  
  window.addEventListener('resize', function() {
    chart?.resize()
  })
}
onMounted(() => {
  chart = echarts.init(chartRef.value)
  if(props.loading){
    // 显示加载动画
    chart.showLoading(loadingSettings)
  }else{
    // 初始化图表
    initChart()
  }
})

onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
  window.removeEventListener('resize', function() {
    chart?.resize()
  })
})
</script>