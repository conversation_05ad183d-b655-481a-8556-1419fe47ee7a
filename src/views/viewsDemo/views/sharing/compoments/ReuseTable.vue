<template>
  <div class="reuse-table">
    <el-table
      :data="tableData"
      v-loading="props.loading"
      style="width: 100%"
      element-loading-text="加载中..." 
      element-loading-background="rgba(0, 0, 0, 0.6)"
      :header-cell-style="{background: 'rgba(255,255,255,0.1)', color: '#fff'}"
      :cell-style="{background: 'rgba(255,255,255,0.05)', color: 'rgba(255,255,255,0.8)'}"
      :max-height="computedMaxHeight"
      empty-text="暂无数据"
    >
      <el-table-column
        v-for="(col, index) in props.columns"
        :key="index"
        :prop="col.prop"
        :label="col.label"
        :width="col.width"
      >
        <template #default="{row}">
          <el-tooltip
            effect="dark"
            :content="row[col.prop]"
            placement="top"
            :disabled="!isOverflow(row[col.prop])"
          >
            <div class="ellipsis-cell" ref="cellRef">
              {{ row[col.prop] }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface ColumnProps {
  prop: string
  label: string
  width?: string | number
}

const props = defineProps({
  tableData: {
    type: Array,
    required: true,
    default: () => []
  },
  columns: {
    type: Array as () => ColumnProps[],
    required: false,
    default: () => [
      { prop: 'districtName', label: '所属辖区', width: '120' },
      { prop: 'name', label: '标题' },
      { prop: 'browseNumber', label: '浏览量', width: '100' }
    ]
  },
  loading: {
    type: Boolean,
    default: false
  },
  maxHeight: {
    type: [String, Number],
    default: 'auto'
  }
})

const cellRef = ref<HTMLElement[]>([])

const isOverflow = (text: string) => {
  if (!text) return false
  return text.length > 8 // 根据实际单元格宽度调整这个值
}

// 计算表格的最大高度
const computedMaxHeight = computed(() => {
  if (props.maxHeight === 'auto') {
    return undefined
  }
  return props.maxHeight
})
</script>

<style lang="scss" scoped>
.reuse-table {
  margin-top: 20px;
  
  :deep(.el-table) {
    background: transparent;
    
    th, tr {
      background: transparent !important;
    }
    
    &::before {
      background-color: rgba(255,255,255,0.1);
    }
  }
  
  .ellipsis-cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }
}
</style>