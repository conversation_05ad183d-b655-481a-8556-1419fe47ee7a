<template>
  <div class="reuse-overview">
    <div class="data-item" style="border-color: #FF9F18; background: rgba(255, 159, 24, 0.2); color: #FF9F18">
      <div class="text">
        <div class="name">活动总数</div>
        <div class="value">{{ overviewData.total || 0 }}</div>
      </div>
    </div>
    <div class="data-item" style="border-color: #00FFFF; background: rgba(0, 255, 255, 0.2); color: #00FFFF">
      <div class="text">
        <div class="name">进行中的活动数</div>
        <div class="value">{{ overviewData.ongoing || 0 }}</div>
      </div>
    </div>
    
    <div class="data-item" style="border-color: #1677FF; background: rgba(22, 119, 255, 0.3); color: #1677FF">
      <div class="text">
        <div class="name">已结束的活动数</div>
        <div class="value">{{ overviewData.ended || 0 }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  overviewData: {
    type: Object,
    required: true,
    default: () => ({
      total: 0,
      ongoing: 0,
      ended: 0
    })
  }
})
</script>

<style lang="scss" scoped>
.reuse-overview {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  
  .data-item {
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent; 
    border-radius: 8px;
    padding: 15px;
    width: 140px;
    height: 68px;
    border-width: 1px;
    border-style: solid;
    text-align: center;
    
    .text {
      width: 100%; 
      
      .name {
        font-size: 14px;
        margin-bottom: 0.130vw;
        
       
      }
      
      .value {
        font-size: 12px;
        font-weight: bold;
        
       
      }
    }
  }
}
</style>