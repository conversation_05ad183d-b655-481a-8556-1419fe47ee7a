<template>
  <div class="reuse-overview">
    <div class="data-item" style="border: 0.026vw solid #FF9F18">
      <div class="icon">
        <img src="@/assets/images/common/goods.png" alt="goods">
      </div>
      <div class="text">
        <div class="name">{{ titleData.itemsTitle}}</div>
        <div class="value">{{ overviewData.goodsCount || 0 }}{{ titleData.itemsUnit }}</div>
      </div>
    </div>
    <div class="data-item" style="border: 0.026vw solid #00FFFF">
      <div class="icon">
        <img src="@/assets/images/common/views.png" alt="views">
      </div>
      <div class="text">
        <div class="name">{{ titleData.clicksTitle}}</div>
        <div class="value">{{ overviewData.viewCount || 0 }}{{ titleData.clicksUnit }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  overviewData: {
    type: Object,
    required: true,
    default: () => ({
      goodsCount: 0,
      viewCount: 0
    })
  },
  titleData: {
    type: Object,
    required: false,
    default: () => ({
      itemsTitle: '物品',
      clicksTitle: '总浏览量',
      itemsUnit: '件',  // 新增物品单位
      clicksUnit: '次'  // 新增浏览量单位
    })
  }
})
</script>

<style lang="scss" scoped>
.reuse-overview {
  display: flex;
  justify-content: space-between;
  gap: 0.520vw;
  
  @media (max-width: 2000px) {
    gap: 0.833vw;
  }
  
  .data-item {
    display: flex;
    align-items: center;
    background: transparent; 
    border-radius: 0.208vw;
    padding:15px;
    width: 140px;
    height: 48px;
 
    
    .icon {
      width: 1.041vw;
      height: 1.041vw;
      margin-right: 0.390vw;
      
      @media (max-width: 2000px) {
        width: 1.666vw;
        height: 1.666vw;
        margin-right: 0.625vw;
      }
      
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    
    .text {
      .name {
        font-size:14px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0.130vw;
        
       
      }
      
      .value {
        font-size:12px;
        font-weight: bold;
        color: #fff;
        
        
      }
    }
  }
}
</style>