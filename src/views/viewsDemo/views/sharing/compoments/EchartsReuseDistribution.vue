<template>
  <div ref="chartRef" style="width: 100%; height: 300px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted,onBeforeUnmount,watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  borderColor: {
    type: String,
    default: '#2AEFFC'
  },
  loading: {
    type: Boolean,
    default: false
  },
  seriesNames: {
    type: Object,
    default: () => ({
      goods: '物品数量',
      views: '浏览量'
    })
  }
})
const loadingSettings = {
    text: '加载中...',
    color: '#2AEFFC',
    textColor: '#fff',
    maskColor: 'rgba(0, 0, 0, 0.5)'
} 
const chartRef = ref()
let chart: echarts.ECharts | null = null
let intervalId: number | null = null
const displayCount = 10 // 每次显示的数据条数
// 监听loading状态变化
watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading(loadingSettings)
  } else {
    chart?.hideLoading()
    initChart()
  }
})
function initChart() {
  if (intervalId) {
    clearInterval(intervalId)
  }
  if (!chart) return
  const chartData = Array.isArray(props.chartData) ? props.chartData : [props.chartData]
  const allLabels = chartData.map(item => item.district?.name || '未知区域')
  const allGoodsCounts = chartData.map(item => item.goodsCount || 0)
  const allViewCounts = chartData.map(item => item.viewCount || 0)
  
  // 初始显示前10条数据
  let currentLabels = allLabels.slice(0, displayCount)
  let currentGoodsCounts = allGoodsCounts.slice(0, displayCount)
  let currentViewCounts = allViewCounts.slice(0, displayCount)
  
  const updateChart = () => {
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.5)',
        borderWidth: '1',
        borderColor: props.borderColor,
        textStyle: {
          color: '#CFE3FC',
          fontSize: 18
        },
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      legend: {
        data: [props.seriesNames.goods, props.seriesNames.views],
        textStyle: {
          color: '#fff'
        },
        icon: 'rect',
        itemWidth: 12,
        itemHeight: 12,
      },
      grid: {
        left: '3%',
        right: '3%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: currentLabels,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          interval: 0,
          rotate: 40,
          fontSize: 14
        },
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)'
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      series: [
        {
          name: props.seriesNames.goods,
          type: 'bar',
          data: currentGoodsCounts,
          itemStyle: {
            borderRadius: 4,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#FF9F18' },
              { offset: 1, color: 'rgba(255, 159, 24, 0.2)' }
            ])
          }
        },
        {
          name: props.seriesNames.views, 
          type: 'bar',
          data: currentViewCounts,
          itemStyle: {
            borderRadius: 4,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#00FFFF' },
              { offset: 1, color: 'rgba(0, 255, 255, 0.2)' }
            ])
          }
        }
      ]
    }
    chart?.setOption(option)
  }
  
  // 初始化图表
  updateChart()
  
  // 数据轮播逻辑
  if (allLabels.length > displayCount) {
    let currentIndex = 0
    
    intervalId = window.setInterval(() => {
      // 计算新的起始索引
      currentIndex = (currentIndex + 1) % allLabels.length
      
      // 获取新的10条数据
      currentLabels = []
      currentGoodsCounts = []
      currentViewCounts = []
      
      for (let i = 0; i < displayCount; i++) {
        const index = (currentIndex + i) % allLabels.length
        currentLabels.push(allLabels[index])
        currentGoodsCounts.push(allGoodsCounts[index])
        currentViewCounts.push(allViewCounts[index])
      }
      
      updateChart()
    }, 3000)
  }
  
  window.addEventListener('resize', function() {
    chart?.resize()
  })
}
onMounted(() => {
  chart = echarts.init(chartRef.value)
  if(props.loading){
    // 显示加载动画
    chart.showLoading(loadingSettings)
  }else{
    // 初始化图表
    initChart()
  }
})

onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
  window.removeEventListener('resize', function() {
    chart?.resize()
  })
})
</script>