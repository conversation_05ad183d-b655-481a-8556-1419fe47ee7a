<script setup lang="ts">
import { ref, reactive,onMounted } from 'vue'
import ModuleTitle from '@/views/sidebar/compoments/ModuleTitle.vue'
import DateGroup from '@/views/common/DateGroup.vue'
import ReuseOverview from './compoments/ReuseOverview.vue'
import EchartsHotReuse from '@/views/common/EchartsHotReuse.vue'
import EchartsReuseDistribution from './compoments/EchartsReuseDistribution.vue'
import ReuseTable from './compoments/ReuseTable.vue'
import VScaleScreen from 'v-scale-screen'
import TabSwitch from '@/views/common/TabSwitch.vue'
import { marketInfo,marketList,marketHots,storeInfo,storeList,storeHots } from '@/common/api/tool'
import { AES_KEY, decrypt, encrypt } from '@/common/utils/aes'
import { useCodeStore } from '@/stores/code'
import { el } from 'element-plus/es/locales.mjs'
const isLoadingInfo = ref(true)
const isLoadingList = ref(true)
const isLoadingHotList = ref(true)

const activeTab = ref('reuse')
const tabs = [
  { label: '二手闲置', value: 'reuse' },
  { label: '社区好物', value: 'goods' },
  { label: '共享物品', value: 'share' }
]

// timeType : 获取时间类型0-当天 1-周 2-3月 3-6月 4- 1年
const timeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]
const codeStore = useCodeStore()

const overviewData = ref({})
const tableData = ref([])
const hotReuseData = ref([])
const distributionData = ref([]);

const overviewDataShare = ref({})
const tableDataShare = ref([])
const hotReuseDataShare = ref([])
const distributionDataShare = ref([]);

const overviewDataStore = ref({})
const tableDataStore = ref([])
const hotReuseDataStore = ref([])
const distributionDataStore = ref([]);

async function getMarketInfo(timeType: number | string = 0,type: number | string = 0) {
  const res = await marketInfo({
    // 数据加密
    data: encrypt({
      timeType:timeType,
      type:type,
      //areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  //数据解密
  const data = decrypt(res.data, res.key)
  if(activeTab.value === 'reuse'){
    overviewData.value = data
  }else if(activeTab.value ==='share'){
    overviewDataShare.value = data
  }
}
async function getMarketHots(timeType: number | string = 2,type: number | string = 0) {
  isLoadingInfo.value = true;
  const res = await marketHots({
    // 数据加密
    data: encrypt({
      timeType:timeType,
      type:type,
      pageNum:1,
      pageSize:10,
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  //数据解密
  const data = decrypt(res.data, res.key)
  if(activeTab.value === 'reuse'){
    hotReuseData.value = data.list.rows;
  }else if(activeTab.value ==='share'){
    hotReuseDataShare.value = data.list.rows;
  }
  isLoadingInfo.value = false
}

async function getMarketList(timeType: number | string = 2,type: number | string = 0) {
  isLoadingList.value = true;
  const res = await marketList({
    // 数据加密
    data: encrypt({
      timeType:timeType,
      type:type,
      pageNum:1,
      pageSize:5,
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  //数据解密
  const data = decrypt(res.data, res.key)
  if(activeTab.value === 'reuse'){
    distributionData.value = data.list;
  }else if(activeTab.value ==='share'){
    distributionDataShare.value = data.list;
  }
  isLoadingList.value = false
}

async function getMarketHotList(timeType: number | string  = 0,type: number | string = 0) {
  isLoadingHotList.value = true;
  const res = await marketHots({
    // 数据加密
    data: encrypt({
      timeType:timeType,
      type:type,
      pageNum:1,
      pageSize:5,
      //areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  //数据解密
  const data = decrypt(res.data, res.key)
  if(activeTab.value === 'reuse'){
    tableData.value = data.list.rows;
  }else if(activeTab.value ==='share'){
    tableDataShare.value = data.list.rows;
  }
  isLoadingHotList.value = false;
}

async function getStoreInfo(timeType: number | string = 0) {
  const res = await storeInfo({
    // 数据加密
    data: encrypt({
      timeType:timeType,
      //areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  //数据解密
  const data = decrypt(res.data, res.key)
  overviewDataStore.value = data
}
async function getStoreHots(timeType: number | string = 2) {
  isLoadingInfo.value = true;
  const res = await storeHots({
    // 数据加密
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:10,
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  //数据解密
  const data = decrypt(res.data, res.key)
  hotReuseDataStore.value = data.list.rows;
  isLoadingInfo.value = false
}

async function getStoreList(timeType: number | string = 2) {
  isLoadingList.value = true;
  const res = await storeList({
    // 数据加密
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:5,
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  //数据解密
  const data = decrypt(res.data, res.key)
  distributionDataStore.value = data.list;
  isLoadingList.value = false
}

async function getStoreHotList(timeType: number | string  = 0,type: number | string = 0) {
  isLoadingHotList.value = true;
  const res = await storeHots({
    // 数据加密
    data: encrypt({
      timeType:timeType,
      type:type,
      pageNum:1,
      pageSize:5,
      //areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  //数据解密
  const data = decrypt(res.data, res.key)
  tableDataStore.value = data.list.rows;
  isLoadingHotList.value = false;
}

onMounted(() => {
  getMarketInfo();
  getMarketHots();
  getMarketList();
  getMarketHotList();
})

function changeTab(tab: string) {
  activeTab.value = tab
  if(activeTab.value === 'reuse'){
    getMarketInfo();
    getMarketHots();
    getMarketList();
    getMarketHotList();
  }else if(activeTab.value ==='goods'){
    getStoreInfo();
    getStoreHots();
    getStoreList();
    getStoreHotList();
  }else if(activeTab.value ==='share'){
    getMarketInfo(0,1);
    getMarketHots(2,1);
    getMarketList(2,1);
    getMarketHotList(0,1);
    return;
  }
}
const changeInfo = (index: string | number)=>{
  if(activeTab.value === 'reuse'){
    getMarketInfo(index,0);
  }else if(activeTab.value ==='goods'){
    getStoreInfo(index);
  }else if(activeTab.value ==='share'){
    getMarketInfo(index,1);
    return;
  }
}
const changeHots = (index: string | number)=>{
  if(activeTab.value === 'reuse'){
    getMarketHots(index,0);
  }else if(activeTab.value ==='goods'){
    getStoreHots(index);
  }else if(activeTab.value ==='share'){
    getMarketHots(index,1);
  }
}
const changeList = (index: string | number)=>{
  if(activeTab.value === 'reuse'){
    getMarketList(index,0);
  }else if(activeTab.value ==='goods'){
    getStoreList(index);
  }else if(activeTab.value ==='share'){
    getMarketList(index,1);
  }
}
const changeHotList = (index: string | number)=>{
  if(activeTab.value === 'reuse'){
    getMarketHotList(index,0);
  }else if(activeTab.value ==='goods'){
    getStoreHotList(index);
  }else if(activeTab.value ==='share'){
    getMarketHotList(index,1);
  }
}
</script>

<template>
  <VScaleScreen
    :delay="200"
    width="480"
    height="1500"
    :bodyOverflowHidden="false"
    :autoScale="{ x: true, y: false }"
    :boxStyle="{ background: '#021121' }"
  >
    <div class="base-box">
      <TabSwitch 
        :tabs="tabs" 
        :activeTab="activeTab"
        @tabChange="changeTab"
      />
      <div v-if="activeTab === 'reuse'">
        <div class="module-box">
          <ModuleTitle title="二手闲置概览" />
          <DateGroup index="0" :dateData="timeOptions" @change="changeInfo"/>
          <ReuseOverview :overviewData="overviewData" 
          :titleData="{itemsTitle:'二手闲置物品',clicksTitle:'总浏览量'}" 
          />
        </div>
        
        <div class="module-box">
          <ModuleTitle title="闲置热浏览概况" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))"
             @change="changeHots"/>
          <EchartsHotReuse :chartData="hotReuseData" 
          :titleData="{chartName: '热浏览',valueName: '点击量'}"
          :loading="isLoadingInfo"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="闲置物品浏览及使用人数分布" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" 
            @change="changeList"/>
          <EchartsReuseDistribution :chartData="distributionData" :loading="isLoadingList"
          :seriesNames="{
            goods: '闲置物品数量',
            views: '浏览量'
          }"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="二手闲置热门贴" />
          <DateGroup index="0" :dateData="timeOptions.filter(i => ['0','1','2','3','4'].includes(i.value))" 
            @change="changeHotList"/>
          <ReuseTable :tableData="tableData" 
          :columns="[
            { prop: 'districtName', label: '所属辖区' },
            { prop: 'name', label: '社区二手闲置标题' },
            { prop: 'browseNumber', label: '浏览量' }
          ]"
          :loading="isLoadingHotList"/>
        </div>
      </div>

      <div v-if="activeTab === 'goods'">
        <div class="module-box">
          <ModuleTitle title="社区好物概览" />
          <DateGroup index="0" :dateData="timeOptions" @change="changeInfo"/>
          <ReuseOverview :overviewData="overviewDataStore" 
          :titleData="{itemsTitle:'社区好物物品',clicksTitle:'总浏览量'}" 
          />
        </div>
        
        <div class="module-box">
          <ModuleTitle title="社区好物热浏览概况" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))"
             @change="changeHots"/>
          <EchartsHotReuse :chartData="hotReuseDataStore" 
          :titleData="{chartName: '热浏览',valueName: '点击量'}"
          :loading="isLoadingInfo"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="社区好物浏览及使用人数分布" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" 
            @change="changeList"/>
          <EchartsReuseDistribution :chartData="distributionDataStore" :loading="isLoadingList"
          :seriesNames="{
            goods: '社区好物数量',
            views: '浏览量'
          }"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="社区好物热门贴" />
          <DateGroup index="0" :dateData="timeOptions.filter(i => ['0','1','2','3','4'].includes(i.value))" 
            @change="changeHotList"/>
          <ReuseTable :tableData="tableDataStore"  
          :columns="[
            { prop: 'districtName', label: '所属辖区' },
            { prop: 'name', label: '社区好物标题' },
            { prop: 'browseNumber', label: '浏览量' }
          ]"
          :loading="isLoadingHotList"/>
        </div>
      </div>

      <div v-if="activeTab === 'share'">
        <div class="module-box">
          <ModuleTitle title="共享物品概览" />
          <DateGroup index="0" :dateData="timeOptions" @change="changeInfo"/>
          <ReuseOverview :overviewData="overviewDataShare" 
          :titleData="{itemsTitle:'共享物品',clicksTitle:'总浏览量'}" 
          />
        </div>
        
        <div class="module-box">
          <ModuleTitle title="共享热浏览概况" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))"
             @change="changeHots"/>
          <EchartsHotReuse :chartData="hotReuseDataShare" 
          :titleData="{chartName: '热浏览',valueName: '点击量'}"
          :loading="isLoadingInfo"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="共享物品浏览及使用人数分布" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" 
            @change="changeList"/>
          <EchartsReuseDistribution :chartData="distributionDataShare" :loading="isLoadingList" 
          :seriesNames="{
            goods: '共享物品数量',
            views: '浏览量'
          }"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="共享物品热门贴" />
          <DateGroup index="0" :dateData="timeOptions.filter(i => ['0','1','2','3','4'].includes(i.value))" 
            @change="changeHotList"/>
          <ReuseTable :tableData="tableDataShare"  
          :columns="[
            { prop: 'districtName', label: '所属辖区' },
            { prop: 'name', label: '社区共享物品标题' },
            { prop: 'browseNumber', label: '浏览量' }
          ]"
          :loading="isLoadingHotList"/>
        </div>
      </div>
    </div>
  </VScaleScreen>
</template>

<style lang="scss" scoped>
.v-screen-box {
  height: 1500px !important;
}

.module-box {
  border: 1px solid #20497F;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
}
</style>