<script setup lang="ts">
import { ref,onMounted } from 'vue'
import ModuleTitle from '@/views/sidebar/compoments/ModuleTitle.vue'
import DateGroup from '@/views/common/DateGroup.vue'
import ReuseOverview from './compoments/ReuseOverview.vue'
import ReuseOverviewActivity from './compoments/ReuseOverviewActivity.vue'
import EchartsHotReuse from '@/views/common/EchartsHotReuse.vue'
import EchartsReuseDistribution from './compoments/EchartsReuseDistribution.vue'
import EchartsReuseDistributionActivity from './compoments/EchartsReuseDistributionActivity.vue'
import ReuseTable from './compoments/ReuseTable.vue'
import VScaleScreen from 'v-scale-screen'
import TabSwitch from '@/views/common/TabSwitch.vue'
import { 
    activityInfo,activityList,activityHots,
    spaceInfo,spaceList,spaceHots,
    craftsmanInfo,craftsmanList,craftsmanHots,
 } from '@/common/api/tool'
import { AES_KEY, decrypt, encrypt } from '@/common/utils/aes'
import { useCodeStore } from '@/stores/code'
import { el } from 'element-plus/es/locales.mjs'
const isLoadingInfo = ref(true)
const isLoadingList = ref(true)
const isLoadingHotList = ref(true)

const activeTab = ref('activity')
const tabs = [
  { label: '社区活动', value: 'activity' },
  { label: '社区空间', value: 'space' },
  { label: '社区匠人', value: 'craftsman' }
]

// timeType : 获取时间类型0-当天 1-周 2-3月 3-6月 4- 1年
const timeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]
const codeStore = useCodeStore()

const overviewData = ref({})
const tableData = ref([])
const hotReuseData = ref([])
const distributionData = ref([]);

const overviewDataSpace = ref({})
const tableDataSpace = ref([])
const hotReuseDataSpace = ref([])
const distributionDataSpace = ref([]);

const overviewDataCraftsman = ref({})
const tableDataCraftsman = ref([])
const hotReuseDataCraftsman = ref([])
const distributionDataCraftsman = ref([]);

async function getActivityInfo(timeType: number | string = 0) {
  const res = await activityInfo({
    data: encrypt({
      timeType:timeType,
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  overviewData.value = data
}

async function getActivityHots(timeType: number | string = 2) {
  isLoadingInfo.value = true;
  const res = await activityHots({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:10,
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  hotReuseData.value = data.list.rows;
  isLoadingInfo.value = false
}

async function getActivityList(timeType: number | string = 2) {
  isLoadingList.value = true;
  const res = await activityList({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:5,
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  distributionData.value = data.list;
  isLoadingList.value = false
}

async function getActivityHotList(timeType: number | string = 0) {
  isLoadingHotList.value = true;
  const res = await activityHots({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:5,
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  tableData.value = data.list.rows;
  isLoadingHotList.value = false;
}

async function getCraftsmanInfo(timeType: number | string = 0) {
  const res = await craftsmanInfo({
    data: encrypt({
      timeType:timeType,
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  overviewDataCraftsman.value = data
}

async function getCraftsmanHots(timeType: number | string = 2) {
  isLoadingInfo.value = true;
  const res = await craftsmanHots({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:10,
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  hotReuseDataCraftsman.value = data.list.rows;
  isLoadingInfo.value = false
}

async function getCraftsmanList(timeType: number | string = 2) {
  isLoadingList.value = true;
  const res = await craftsmanList({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:5,
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  distributionDataCraftsman.value = data.list;
  isLoadingList.value = false
}

async function getCraftsmanHotList(timeType: number | string = 0) {
  isLoadingHotList.value = true;
  const res = await craftsmanHots({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:5,
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  tableDataCraftsman.value = data.list.rows;
  isLoadingHotList.value = false;
}

async function getSpaceInfo(timeType: number | string = 0) {
  const res = await spaceInfo({
    data: encrypt({
      timeType:timeType,
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  overviewDataSpace.value = data
}

async function getSpaceHots(timeType: number | string = 2) {
  isLoadingInfo.value = true;
  const res = await spaceHots({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:10,
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  hotReuseDataSpace.value = data.list.rows;
  isLoadingInfo.value = false
}

async function getSpaceList(timeType: number | string = 2) {
  isLoadingList.value = true;
  const res = await spaceList({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:5,
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  distributionDataSpace.value = data.list;
  isLoadingList.value = false
}

async function getSpaceHotList(timeType: number | string  = 0) {
  isLoadingHotList.value = true;
  const res = await spaceHots({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:5,
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  tableDataSpace.value = data.list.rows;
  isLoadingHotList.value = false;
}

onMounted(() => {
  getActivityInfo();
  getActivityHots();
  getActivityList();
  getActivityHotList();
})

function changeTab(tab: string) {
  activeTab.value = tab
  if(activeTab.value === 'activity'){
    getActivityInfo();
    getActivityHots();
    getActivityList();
    getActivityHotList();
  }else if(activeTab.value ==='space'){
    getSpaceInfo();
    getSpaceHots();
    getSpaceList();
    getSpaceHotList();
  }else if(activeTab.value ==='craftsman'){
    getCraftsmanInfo(0);
    getCraftsmanHots(2);
    getCraftsmanList(2);
    getCraftsmanHotList(0);
  }
}

const changeInfo = (index: string | number)=>{
  if(activeTab.value === 'activity'){
    getActivityInfo(index);
  }else if(activeTab.value ==='space'){
    getSpaceInfo(index);
  }else if(activeTab.value ==='craftsman'){
    getCraftsmanInfo(index);
  }
}

const changeHots = (index: string | number)=>{
  if(activeTab.value === 'activity'){
    getActivityHots(index);
  }else if(activeTab.value ==='space'){
    getSpaceHots(index);
  }else if(activeTab.value ==='craftsman'){
    getCraftsmanHots(index);
  }
}

const changeList = (index: string | number)=>{
  if(activeTab.value === 'activity'){
    getActivityList(index);
  }else if(activeTab.value ==='space'){
    getSpaceList(index);
  }else if(activeTab.value ==='craftsman'){
    getCraftsmanList(index);
  }
}

const changeHotList = (index: string | number)=>{
  if(activeTab.value === 'activity'){
    getActivityHotList(index);
  }else if(activeTab.value ==='space'){
    getSpaceHotList(index);
  }else if(activeTab.value ==='craftsman'){
    getCraftsmanHotList(index);
  }
}
</script>

<template>
  <VScaleScreen
    :delay="200"
    width="480"
    height="1500"
    :bodyOverflowHidden="false"
    :autoScale="{ x: true, y: false }"
    :boxStyle="{ background: '#021121' }"
  >
    <div class="base-box">
      <TabSwitch 
        :tabs="tabs" 
        :activeTab="activeTab"
        @tabChange="changeTab"
      />
      <div v-if="activeTab === 'activity'">
        <div class="module-box">
          <ModuleTitle title="社区活动概览" />
          <DateGroup index="0" :dateData="timeOptions" @change="changeInfo"/>
          <ReuseOverviewActivity :overviewData="overviewData"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="社区活动热浏览概况" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))"
             @change="changeHots"/>
          <EchartsHotReuse :chartData="hotReuseData" 
          :titleData="{chartName: '热浏览',valueName: '参与人数'}"
          :dataKeys="{
            nameKey: 'title',
            valueKey: 'userCount'
          }"
          :loading="isLoadingInfo"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="社区活动浏览及参与人数分布" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" 
            @change="changeList"/>
          <EchartsReuseDistributionActivity :chartData="distributionData" :loading="isLoadingList"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="热门社区活动" />
          <DateGroup index="0" :dateData="timeOptions.filter(i => ['0','1','2','3','4'].includes(i.value))" 
            @change="changeHotList"/>
          <ReuseTable :tableData="tableData" 
          :columns="[
            { prop: 'districtName', label: '所属辖区' },
            { prop: 'title', label: '社区活动标题' },
            { prop: 'userCount', label: '参与人数' }
          ]"
          :loading="isLoadingHotList"/>
        </div>
      </div>

      <div v-if="activeTab === 'space'">
        <div class="module-box">
          <ModuleTitle title="社区空间概览" />
          <DateGroup index="0" :dateData="timeOptions" @change="changeInfo"/>
          <ReuseOverview :overviewData="overviewDataSpace" 
          :titleData="{
            itemsTitle: '社区空间',
            clicksTitle: '总浏览量',
            itemsUnit: '',
            clicksUnit: '次'
          }"
          />
        </div>
        
        <div class="module-box">
          <ModuleTitle title="社区空间热浏览概况" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))"
             @change="changeHots"/>
          <EchartsHotReuse :chartData="hotReuseDataSpace" 
          :titleData="{chartName: '热浏览',valueName: '点击量'}"
          :loading="isLoadingInfo"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="社区空间浏览及使用人数分布" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" 
            @change="changeList"/>
          <EchartsReuseDistribution :chartData="distributionDataSpace" :loading="isLoadingList"
          :seriesNames="{
            goods: '社区空间数量',
            views: '浏览量'
          }"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="热门社区空间" />
          <DateGroup index="0" :dateData="timeOptions.filter(i => ['0','1','2','3','4'].includes(i.value))" 
            @change="changeHotList"/>
          <ReuseTable :tableData="tableDataSpace" 
          :columns="[
            { prop: 'districtName', label: '所属辖区' },
            { prop: 'name', label: '社区空间名称' },
            { prop: 'browseNumber', label: '参与人数' }
          ]"
          :loading="isLoadingHotList"/>
        </div>
      </div>

      <div v-if="activeTab === 'craftsman'">
        <div class="module-box">
          <ModuleTitle title="社区匠人概览" />
          <DateGroup index="0" :dateData="timeOptions" @change="changeInfo"/>
          <ReuseOverview :overviewData="overviewDataCraftsman" 
          :titleData="{
            itemsTitle: '社区匠人',
            clicksTitle: '总浏览量',
            itemsUnit: '个',
            clicksUnit: '次'
          }"  
          />
        </div>
        
        <div class="module-box">
          <ModuleTitle title="社区匠人热浏览概况" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))"
             @change="changeHots"/>
          <EchartsHotReuse :chartData="hotReuseDataCraftsman" 
          :titleData="{chartName: '热浏览',valueName: '点击量'}"
          :loading="isLoadingInfo"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="社区匠人浏览及关注人数分布" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" 
            @change="changeList"/>
          <EchartsReuseDistribution :chartData="distributionDataCraftsman" :loading="isLoadingList"
          :seriesNames="{
            goods: '社区匠人数量',
            views: '浏览量'
          }"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="热门社区匠人" />
          <DateGroup index="0" :dateData="timeOptions.filter(i => ['0','1','2','3','4'].includes(i.value))" 
            @change="changeHotList"/>
          <ReuseTable :tableData="tableDataCraftsman" 
          :columns="[
            { prop: 'districtName', label: '辖区名称' },
            { prop: 'name', label: '社区匠人标题' },
            { prop: 'browseNumber', label: '浏览次数', width: '150' }
          ]" 
          :loading="isLoadingHotList"/>
        </div>
      </div>
    </div>
  </VScaleScreen>
</template>

<style lang="scss" scoped>
.v-screen-box {
  height: 1500px !important;
}

.module-box {
  border: 1px solid #20497F;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
}
</style>