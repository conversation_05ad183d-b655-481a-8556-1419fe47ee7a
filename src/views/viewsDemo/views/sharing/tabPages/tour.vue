<script setup lang="ts">
import { ref,onMounted } from 'vue'
import ModuleTitle from '@/views/sidebar/compoments/ModuleTitle.vue'
import DateGroup from '@/views/common/DateGroup.vue'
import EchartsHotReuse from '@/views/common/EchartsHotReuse.vue'
import ReuseOverview from './../compoments/ReuseOverview.vue'
import EchartsReuseDistribution from './../compoments/EchartsReuseDistribution.vue'
import ReuseTable from './../compoments/ReuseTable.vue'
import { 
    tourInfo,tourList,tourHots,
 } from '@/common/api/tool'
import { AES_KEY, decrypt, encrypt } from '@/common/utils/aes'
import { useCodeStore } from '@/stores/code'
const isLoadingInfo = ref(true)
const isLoadingList = ref(true)
const isLoadingHotList = ref(true)

// timeType : 获取时间类型0-当天 1-周 2-3月 3-6月 4- 1年
const timeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]
const codeStore = useCodeStore()

const overviewDataTour = ref({})
const tableDataTour = ref([])
const hotReuseDataTour = ref([])
const distributionDataTour = ref([]);

async function getTourInfo(timeType: number | string = 0) {
  const res = await tourInfo({
    data: encrypt({
      timeType:timeType,
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  overviewDataTour.value = data
}

async function getTourHots(timeType: number | string = 2) {
  isLoadingInfo.value = true;
  const res = await tourHots({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:10,
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  hotReuseDataTour.value = data.list.rows;
  isLoadingInfo.value = false
}

async function getTourList(timeType: number | string = 2) {
  isLoadingList.value = true;
  const res = await tourList({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:5,
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  distributionDataTour.value = data.list;
  isLoadingList.value = false
}

async function getTourHotList(timeType: number | string  = 0) {
  isLoadingHotList.value = true;
  const res = await tourHots({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:5,
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  tableDataTour.value = data.list.rows;
  isLoadingHotList.value = false;
}

onMounted(() => {
    getTourInfo();
    getTourHots();
    getTourList();
    getTourHotList();
})

const changeInfo = (index: string | number)=>{
    getTourInfo(index);
}

const changeHots = (index: string | number)=>{
    getTourHots(index);
}

const changeList = (index: string | number)=>{
    getTourList(index);
}

const changeHotList = (index: string | number)=>{
    getTourHotList(index);
}
</script>

<template>
    <div class="module-box">
        <ModuleTitle title="社区游线概览" />
        <DateGroup index="0" :dateData="timeOptions" @change="changeInfo"/>
        <ReuseOverview :overviewData="overviewDataTour" 
        :titleData="{
        itemsTitle: '社区游线',
        clicksTitle: '总浏览量',
        itemsUnit: '条',
        clicksUnit: '次'
        }" 
        />
    </div>
    
    <div class="module-box">
        <ModuleTitle title="社区游线热浏览概况" />
        <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))"
            @change="changeHots"/>
        <EchartsHotReuse :chartData="hotReuseDataTour" 
        :titleData="{chartName: '热浏览',valueName: '点击量'}"
        :dataKeys="{
        nameKey: 'tourLineName',
        valueKey: 'browseNumber'
        }"
        :loading="isLoadingInfo"/>
    </div>
    
    <div class="module-box">
        <ModuleTitle title="社区游线浏览及参与人数分布" />
        <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" 
        @change="changeList"/>
        <EchartsReuseDistribution :chartData="distributionDataTour" :loading="isLoadingList"
        :seriesNames="{
        goods: '社区游线数量',
        views: '浏览量'
        }"/>
    </div>
    
    <div class="module-box">
        <ModuleTitle title="热门社区游线" />
        <DateGroup index="0" :dateData="timeOptions.filter(i => ['0','1','2','3','4'].includes(i.value))" 
        @change="changeHotList"/>
        <ReuseTable :tableData="tableDataTour" 
        :columns="[
        { prop: 'districtName', label: '所属辖区' },
        { prop: 'tourLineName', label: '社区游线名称' },
        { prop: 'browseNumber', label: '浏览量' }
        ]"
        :loading="isLoadingHotList"/>
    </div>
</template>

<style lang="scss" scoped>
.v-screen-box {
  height: 1500px !important;
}

.module-box {
  border: 0.026vw solid #20497F;
  border-radius: 0.104vw;
  padding: 0.390vw;
  margin-bottom: 0.260vw;
  
  @media (max-width: 2000px) {
    border: 0.041vw solid #20497F;
    border-radius: 0.166vw;
    padding: 0.625vw;
    margin-bottom: 0.416vw;
  }
}
</style>