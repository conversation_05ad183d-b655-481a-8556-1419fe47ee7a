<template>
  <div ref="chartRef" style="width: 100%; height: 300px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted,onBeforeUnmount,watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  borderColor: {
    type: String,
    default: '#2AEFFC'
  },
  loading: {
    type: Boolean,
    default: false
  },
  seriesNames: {
    type: Object,
    default: () => ({
      publish: '发布数',
      complete: '完成数'
    })
  }
})
const loadingSettings = {
    text: '加载中...',
    color: '#2AEFFC',
    textColor: '#fff',
    maskColor: 'rgba(0, 0, 0, 0.5)'
} 
const chartRef = ref()
let chart: echarts.ECharts | null = null
let intervalId: number | null = null
const displayCount = 10

watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading(loadingSettings)
  } else {
    chart?.hideLoading()
    initChart()
  }
})

function initChart() {
  if (intervalId) {
    clearInterval(intervalId)
  }
  if (!chart) return
  const chartData = Array.isArray(props.chartData) ? props.chartData : [props.chartData]
  const allLabels = chartData.map(item => item.district?.name || '未知区域')
  const allPublishCounts = chartData.map(item => item.total || 0)
  const allCompleteCounts = chartData.map(item => item.ended || 0)
  
  let currentLabels = allLabels.slice(0, displayCount)
  let currentPublishCounts = allPublishCounts.slice(0, displayCount)
  let currentCompleteCounts = allCompleteCounts.slice(0, displayCount)
  
  const updateChart = () => {
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.5)',
        borderWidth: '1',
        borderColor: props.borderColor,
        textStyle: {
          color: '#CFE3FC',
          fontSize: 18
        }
      },
      legend: {
        data: [props.seriesNames.publish, props.seriesNames.complete],
        textStyle: {
          color: '#fff'
        },
        icon: 'rect',
        itemWidth: 12,
        itemHeight: 12,
      },
      grid: {
        left: '3%',
        right: '3%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: currentLabels,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          interval: 0,
          rotate: 40,
          fontSize: 14
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)'
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      series: [
        {
          name: props.seriesNames.publish,
          type: 'bar',
          data: currentPublishCounts,
          itemStyle: {
            borderRadius: 4,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#FF9F18' },
              { offset: 1, color: 'rgba(255, 159, 24, 0.2)' }
            ])
          }
        },
        {
          name: props.seriesNames.complete, 
          type: 'bar',
          data: currentCompleteCounts,
          itemStyle: {
            borderRadius: 4,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#00B578' },
              { offset: 1, color: 'rgba(0, 181, 120, 0.2)' }
            ])
          }
        }
      ]
    }
    chart?.setOption(option)
  }
  
  updateChart()
  
  if (allLabels.length > displayCount) {
    let currentIndex = 0
    intervalId = window.setInterval(() => {
      currentIndex = (currentIndex + 1) % allLabels.length
      currentLabels = []
      currentPublishCounts = []
      currentCompleteCounts = []
      
      for (let i = 0; i < displayCount; i++) {
        const index = (currentIndex + i) % allLabels.length
        currentLabels.push(allLabels[index])
        currentPublishCounts.push(allPublishCounts[index])
        currentCompleteCounts.push(allCompleteCounts[index])
      }
      
      updateChart()
    }, 3000)
  }
  
  window.addEventListener('resize', function() {
    chart?.resize()
  })
}

onMounted(() => {
  chart = echarts.init(chartRef.value)
  if(props.loading){
    chart.showLoading(loadingSettings)
  }else{
    initChart()
  }
})

onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
  window.removeEventListener('resize', function() {
    chart?.resize()
  })
})
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 300px;
  margin-top: 20px;
}
</style>