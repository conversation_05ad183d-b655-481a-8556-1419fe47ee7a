<template>
  <div class="overview-container">
    <div class="overview-item">
      <div class="overview-header">
        <img src="@/assets/images/common/demand.png" class="title-icon" />
        <div class="overview-title">微心愿</div>
      </div>
      <div class="overview-content">
        <div class="overview-row" style="background-color: rgba(22, 119, 255, 0.2);">
          <span class="overview-label"  style="color: #1677FF;">发布</span>
          <span class="overview-value" style="color: #1677FF;">{{ overviewData.demandTotal || 0 }}</span>
        </div>
        <div class="overview-row" style="background-color: rgba(0, 255, 255, 0.2);">
          <span class="overview-label" style="color: #00FFFF;">达成</span>
          <span class="overview-value" style="color: #00FFFF;">{{ overviewData.demandEnded || 0 }}</span>
        </div>
      </div>
    </div>
    <div class="overview-item">
      <div class="overview-header">
        <img src="@/assets/images/common/service.png" class="title-icon" />
        <div class="overview-title">微服务</div>
      </div>
      <div class="overview-content">
        <div class="overview-row" style="background-color: rgba(22, 119, 255, 0.2);">
          <span class="overview-label" style="color: #1677FF;">发布</span>
          <span class="overview-value" style="color: #1677FF;">{{ overviewData.serviceTotal || 0 }}</span>
        </div>
        <div class="overview-row" style="background-color: rgba(0, 255, 255, 0.2);">
          <span class="overview-label" style="color: #00FFFF;">达成</span>
          <span class="overview-value" style="color: #00FFFF;">{{ overviewData.serviceEnded || 0 }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  overviewData: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
.overview-container {
  display: flex;
  justify-content: space-between;
  margin-top: 0.520vw;
  
  @media (max-width: 2000px) {
    margin-top: 0.833vw;
  }
}

.overview-item {
  flex: 1;
  padding: 0.390vw;
  border-radius: 0.104vw;
  margin: 0 0.130vw;
  display: flex;
  flex-direction: column;
  border: 0.026vw solid rgba(255, 255, 255, 0.2);
  width: 140px;
  /* height: 68px; */

}

.overview-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.260vw;
  
  @media (max-width: 2000px) {
    margin-bottom: 0.416vw;
  }
}

.overview-title {
  font-size:14px;
  color: #fff;

}

.title-icon {
  width: 0.807vw;
  height: 0.807vw;
  margin-right: 0.208vw;
  
  @media (max-width: 2000px) {
    width: 1.291vw;
    height: 1.291vw;
    margin-right: 0.333vw;
  }
}

.overview-content {
  display: flex;
  flex-direction: column;
  margin-left: 0.807vw; /* 与图标对齐 */
  
  @media (max-width: 2000px) {
    margin-left: 1.291vw;
  }
}

.overview-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.130vw 0;
  padding: 0 0.312vw;
  border-radius: 0.104vw;
  
  @media (max-width: 2000px) {
    margin: 0.208vw 0;
    padding: 0 0.5vw;
    border-radius: 0.166vw;
  }
}

.overview-value {
  font-size: 13px;
  font-weight: bold;
  
 
}

.overview-label {
  font-size: 0.364vw;
  
  @media (max-width: 2000px) {
    font-size: 0.583vw;
  }
}
</style>