<template>
  <div class="chart-container" ref="chartRef"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const loadingSettings = {
  text: '加载中...',
  color: '#2AEFFC',
  textColor: '#fff',
  maskColor: 'rgba(0, 0, 0, 0.5)'
}

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
let intervalId: number | null = null
const displayCount = 10

watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading(loadingSettings)
  } else {
    chart?.hideLoading()
    initChart()
  }
})

function initChart() {
  if (intervalId) {
    clearInterval(intervalId)
  }
  if (!chart) return
  
  const chartData = Array.isArray(props.chartData) ? props.chartData : [props.chartData]
  const allLabels = chartData.map(item => item.district?.name || '未知区域')
  const allWishData = chartData.map(item => item.wishParticipants || 0)
  const allServiceData = chartData.map(item => item.serviceParticipants || 0)
  
  let currentLabels = allLabels.slice(0, displayCount)
  let currentWishData = allWishData.slice(0, displayCount)
  let currentServiceData = allServiceData.slice(0, displayCount)
  
  const updateChart = () => {
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['微心愿参与人数', '微服务参与人数'],
        textStyle: {
          color: '#fff'
        },
        icon: 'rect',
        itemWidth: 12,
        itemHeight: 12
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#20497F'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(32, 73, 127, 0.5)'
          }
        },
        axisLabel: {
          color: '#fff'
        }
      },
      yAxis: {
        type: 'category',
        data: currentLabels,
        axisLine: {
          lineStyle: {
            color: '#20497F'
          }
        },
        axisLabel: {
          color: '#fff'
        }
      },
      series: [
        {
          name: '微心愿参与人数',
          type: 'bar',
          barWidth: 20,
          data: currentWishData,
          itemStyle: {
            borderRadius: 4,
            color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
              { offset: 0, color: '#FF9F18' },
              { offset: 1, color: 'rgba(255, 159, 24, 0.3)' }
            ])
          },
          label: {
            show: true,
            position: 'right',
            color: '#fff',
            fontSize: 12,
            formatter: function(params:any) {
              if (params.value > 0) {
                return params.value;
              } else {
                return '';
              }
            }
          }
        },
        {
          name: '微服务参与人数',
          type: 'bar',
          barWidth: 20,
          data: currentServiceData,
          itemStyle: {
            borderRadius: 4,
            color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
              { offset: 0, color: '#1677FF' },
              { offset: 1, color: 'rgba(22, 119, 255, 0.3)' }
            ])
          },
          label: {
            show: true,
            position: 'right',
            color: '#fff',
            fontSize: 12,
            formatter: function(params:any) {
              if (params.value > 0) {
                return params.value;
              } else {
                return '';
              }
            }
          }
        }
      ]
    }
    
    chart?.setOption(option)
  }
  
  updateChart()
  
  if (allLabels.length > displayCount) {
    let currentIndex = 0
    intervalId = window.setInterval(() => {
      currentIndex = (currentIndex + 1) % allLabels.length
      currentLabels = []
      currentWishData = []
      currentServiceData = []
      
      for (let i = 0; i < displayCount; i++) {
        const index = (currentIndex + i) % allLabels.length
        currentLabels.push(allLabels[index])
        currentWishData.push(allWishData[index])
        currentServiceData.push(allServiceData[index])
      }
      
      updateChart()
    }, 3000)
  }
  
  window.addEventListener('resize', function() {
    chart?.resize()
  })
}

onMounted(() => {
  chart = echarts.init(chartRef.value)
  if(props.loading) {
    chart.showLoading(loadingSettings)
  } else {
    initChart()
  }
})

onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
  window.removeEventListener('resize', function() {
    chart?.resize()
  })
})
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 300px;
  margin-top: 20px;
}
</style>