<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ModuleTitle from '@/views/sidebar/compoments/ModuleTitle.vue'
import SplitTab from '@/views/common/SplitTab.vue'
import DateGroup from '@/views/common/DateGroup.vue'
import MutualAidOverview from './compoments/MutualAidOverview.vue'
import EchartsCompletionDistribution from './compoments/EchartsCompletionDistribution.vue'
import EchartsParticipantHorizontal from './compoments/EchartsParticipantHorizontal.vue'
import EchartsHotReuse from '@/views/common/EchartsHotReuse.vue'
import VScaleScreen from 'v-scale-screen'
import { mutualAidInfo, mutualAidList, mutualAidHots, mutualAidServiceList } from '@/common/api/mutual-aid'
import { AES_KEY, decrypt, encrypt } from '@/common/utils/aes'
import { useCodeStore } from '@/stores/code'
import { Loading } from '@element-plus/icons-vue'

const isLoadingInfo = ref(true)
const isLoadingList = ref(true)
const isLoadingServiceList = ref(true)
const isLoadingHots = ref(true)


const dateGroupList = ref('2')

const codeStore = useCodeStore()

const timeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]

const overviewData = ref({
  wishPublish: 156,
  wishComplete: 98,
  servicePublish: 234,
  serviceComplete: 187
})

const completionData = ref([
    {district:{name:'社区A'},publishCount:45,completeCount:32},
    {district:{name:'社区B'},publishCount:32,completeCount:25},
    {district:{name:'社区C'},publishCount:67,completeCount:45},
    {district:{name:'社区D'},publishCount:23,completeCount:18},
    {district:{name:'社区E'},publishCount:56,completeCount:42}])

const participantData = ref({
  yAxis: ['社区A', '社区B', '社区C', '社区D', '社区E'],
  wishData: [120, 85, 150, 60, 110],
  serviceData: [180, 120, 200, 90, 160]
})

const heatMapData = ref([
  {name: '周一-08:00', value: 5},
  {name: '周一-12:00', value: 8},
  {name: '周二-09:00', value: 6},
  {name: '周三-18:00', value: 9},
  {name: '周五-10:00', value: 7},
  {name: '周六-15:00', value: 10},
  {name: '周日-20:00', value: 4}
])

onMounted(() => {
  getMutualAidInfo()
  getMutualAidList()
  getMutualAidServiceList()
  getMutualAidHots()
})

async function getMutualAidInfo(timeType: number | string = 0) {
  isLoadingInfo.value = true
  const res = await mutualAidInfo({
    data: encrypt({ timeType }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  overviewData.value = data
  isLoadingInfo.value = false
}

async function getMutualAidList(timeType: number | string = 2) {
  isLoadingList.value = true
  const res = await mutualAidList({
    data: encrypt({ 
      timeType: dateGroupList.value,
      wishType: tabValue.value,
      areaCode: codeStore.code 
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  completionData.value = data.list
  isLoadingList.value = false
}

async function getMutualAidServiceList(timeType: number | string = 2) {
  isLoadingServiceList.value = true
  const res = await mutualAidServiceList({
    data: encrypt({ 
      timeType,
      areaCode: codeStore.code 
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  participantData.value = data.list
  isLoadingServiceList.value = false
}

async function getMutualAidHots(timeType: number | string = 2) {
  isLoadingHots.value = true
  const res = await mutualAidHots({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:5,
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  heatMapData.value = data.list.rows;
  isLoadingHots.value = false
}

const changeInfo = (index: string) => {
  getMutualAidInfo(index)
}

const changeCompletion = (index: string) => {
  dateGroupList.value = index;
  getMutualAidList(index)
}

const changeParticipant = (index: string) => {
  getMutualAidServiceList(index)
}

const changeHeatMap = (index: string) => {
  getMutualAidHots(index)
}

const tabValue = ref('0')

function handleTabChange(val: string) {
  tabValue.value = val
  getMutualAidList()
}
</script>

<template>
  <VScaleScreen
    :delay="200"
    width="480"
    height="1600"
    :bodyOverflowHidden="false"
    :autoScale="{ x: true, y: false }"
    :boxStyle="{ background: '#021121' }"
  >
    <div class="base-box">
      <div class="module-box">
        <ModuleTitle title="微互助概况" />
        <DateGroup index="0" :dateData="timeOptions" @change="changeInfo"/>
        <MutualAidOverview :overviewData="overviewData" />
      </div>
      
      <div class="module-box">
        <ModuleTitle title="微互助达成情况分布" />
        <div style="display: flex; align-items: center;">
          <SplitTab v-model="tabValue" 
          :tabs = "[
            { label: '微心愿', value: '0' },
            { label: '微服务', value: '1' }
            ]" @change="handleTabChange" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" @change="changeCompletion"/>
        </div>
        <EchartsCompletionDistribution :chartData="completionData" :loading="isLoadingList" />
      </div>
      
      <div class="module-box">
        <ModuleTitle title="微互助服务人数" />
        <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" @change="changeParticipant"/>
        <EchartsParticipantHorizontal :chartData="participantData" :loading="isLoadingServiceList"/>
      </div>
      
      <div class="module-box">
        <ModuleTitle title="微心愿/微服务热力图" />
        <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" @change="changeHeatMap"/>
        <EchartsHotReuse :chartData="heatMapData" :loading="isLoadingHots"
          :dataKeys="{
            nameKey: 'title',
            valueKey: 'userCount'
          }" />
      </div>
    </div>
  </VScaleScreen>
</template>

<style lang="scss" scoped>
.v-screen-box {
  height: 1600px !important;
}

.module-title {
  margin: 20px 0;
}

.module-box {
  border: 1px solid #20497F;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
}
</style>