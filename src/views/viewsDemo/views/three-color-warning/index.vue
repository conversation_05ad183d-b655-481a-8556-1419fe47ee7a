<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import ModuleTitle from '@/views/sidebar/compoments/ModuleTitle.vue'
import DateGroup from '@/views/common/DateGroup.vue'
import WarningData from './compoments/WarningData.vue'
import EchartsWarningCurve from './compoments/EchartsWarningCurve.vue'
import EchartsWarningHorizontal from './compoments/EchartsWarningHorizontal.vue'
import VScaleScreen from 'v-scale-screen'
import { DCEventRYBList } from '@/common/api/three'
import { AES_KEY, decrypt, encrypt } from '@/common/utils/aes'

const timeRange = ref('0')
const timeRangeCurve = ref('2')
const timeRangeHorizontal = ref('2')
const timeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]

const warningData = reactive({
  red: 0,
  yellow: 0,
  blue: 0
})

const data = reactive({
  curveData: {
    red: [] as number[],
    yellow: [] as number[],
    blue: [] as number[],
    label: [] as string[]
  },
  horizontalData: {
    red: [] as number[],
    yellow: [] as number[],
    blue: [] as number[],
    label: [] as string[]
  }
})
const getDateRange = (rangeType: string) => {
  const now = new Date()
  const start = new Date(now)
  
  switch(rangeType) {
    case '0': // 当天
      start.setHours(0, 0, 0, 0)
      return {
        start: formatDate(start),
        end: formatDate(now)
      }
    case '1': // 一周
      start.setDate(start.getDate() - 7)
      return {
        start: formatDate(start),
        end: formatDate(now)
      }
    case '2': // 近三月
      start.setMonth(start.getMonth() - 3)
      return {
        start: formatDate(start),
        end: formatDate(now)
      }
    case '3': // 近六月
      start.setMonth(start.getMonth() - 6)
      return {
        start: formatDate(start),
        end: formatDate(now)
      }
    case '4': // 近一年
      start.setFullYear(start.getFullYear() - 1)
      return {
        start: formatDate(start),
        end: formatDate(now)
      }
    default:
      return {
        start: formatDate(start),
        end: formatDate(now)
      }
  }
}

const formatDate = (date: Date) => {
  return date.toISOString().split('T')[0]
}

const getInfo = (rangeType = timeRange.value) => {
  const { start, end } = getDateRange(rangeType)
  DCEventRYBList({
    "areaCode": "510183101001",
    "Page": 1,
    "Rows": 1000000000,
    "DateRangeStart": start,
    "DateRangeEnd": end
  }).then((res) => {
    const result = res.rows;
    // 只处理overview数据
    const counts = { red: 0, yellow: 0, blue: 0 };
    result.forEach((item: { RYBType: number }) => {
      if(item.RYBType === 3) counts.red++;
      else if(item.RYBType === 2) counts.yellow++;
      else if(item.RYBType === 1) counts.blue++;
    });
    warningData.red = counts.red;
    warningData.yellow = counts.yellow;
    warningData.blue = counts.blue;
  })
}

const getCurveData = (rangeType = timeRangeCurve.value) => {
  const { start, end } = getDateRange(rangeType)
  DCEventRYBList({
    "areaCode": "510183101001",
    "Page": 1,
    "Rows": 1000000000,
    "DateRangeStart": start,
    "DateRangeEnd": end
  }).then((res) => {
    const result = res.rows;
    const monthMap = new Map();
    const now = new Date();
    const startDate = new Date(start);
    
    // 生成从当前月份往前推算的月份列表
    const allMonths = [];
    let currentDate = new Date(now);
    currentDate.setDate(1); // 设置为当月第一天
    const endDate = new Date(startDate);
    endDate.setDate(1); // 设置为起始月份第一天
    
    while (currentDate >= endDate) {
      const month = `${currentDate.getMonth() + 1}月`;
      allMonths.unshift(month); // 往前插入保证顺序正确
      currentDate.setMonth(currentDate.getMonth() - 1);
    }
    console.log(allMonths)
    // 初始化所有月份数据为0
    allMonths.forEach(month => {
      monthMap.set(month, { red: 0, yellow: 0, blue: 0 });
    });
    
    // 填充实际数据
    result.forEach((item: { RYBType: number; EventTime: string; }) => {
      const eventDate = new Date(item.EventTime);
      const month = `${eventDate.getMonth() + 1}月`;
      
      if (monthMap.has(month)) {
        const monthData = monthMap.get(month);
        if (item.RYBType === 3) monthData.red++;
        else if (item.RYBType === 2) monthData.yellow++;
        else if (item.RYBType === 1) monthData.blue++;
      }
    });
    
    data.curveData = {
      red: allMonths.map(month => monthMap.get(month).red),
      yellow: allMonths.map(month => monthMap.get(month).yellow),
      blue: allMonths.map(month => monthMap.get(month).blue),
      label: allMonths
    };
  })
}

const getHorizontalData = (rangeType = timeRangeHorizontal.value) => {
  const { start, end } = getDateRange(rangeType)
  DCEventRYBList({
    "areaCode": "510183101001",
    "Page": 1,
    "Rows": 1000000000,
    "DateRangeStart": start,
    "DateRangeEnd": end
  }).then((res) => {
    const result = res.rows;
    const communityMap = new Map();
    
    // 按社区分类统计预警数据
    result.forEach((item: { RYBType: number; CommunityName: string; }) => {
      const communityName = item.CommunityName || '未知社区';
      
      if (!communityMap.has(communityName)) {
        communityMap.set(communityName, { red: 0, yellow: 0, blue: 0 });
      }
      
      const communityData = communityMap.get(communityName);
      if (item.RYBType === 3) communityData.red++;
      else if (item.RYBType === 2) communityData.yellow++;
      else if (item.RYBType === 1) communityData.blue++;
    });
    
    // 转换为数组并按社区名称排序
    const sortedCommunities = Array.from(communityMap.keys()).sort();
    
    data.horizontalData = {
      red: sortedCommunities.map(community => communityMap.get(community).red),
      yellow: sortedCommunities.map(community => communityMap.get(community).yellow),
      blue: sortedCommunities.map(community => communityMap.get(community).blue),
      label: sortedCommunities
    };
  })
}

const change = (index: string) => {
  timeRange.value = index
  getInfo(index)
}

const changeCurve = (index: string) => {
  timeRangeCurve.value = index
  getCurveData(index)
}

const changeHorizontal = (index: string) => {
  timeRangeHorizontal.value = index
  getHorizontalData(index)
}

onMounted(() => {
  getInfo()
  getCurveData()
  getHorizontalData()
})
</script>

<template>
  <!-- <VScaleScreen
    :delay="200"
    width="480"
    height="1060"
    :bodyOverflowHidden="false"
    :autoScale="{ x: true, y: false }"
    :boxStyle="{ background: '#021121' }"
  >
    <div class="base-box"> -->
      <div class="module-box">
        <ModuleTitle title="三色预警概况" />
        <DateGroup index="0" :dateData="timeOptions" @change="change"/>
        <WarningData :warningData="warningData" />
      </div>
      
      <div class="module-box">
        <ModuleTitle class="module-title" title="三色预警趋势" />
        <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" @change="changeCurve"/>
        <EchartsWarningCurve :chartData="data.curveData" />
      </div>
      
      <div class="module-box">
        <ModuleTitle class="module-title" title="三色预警统计" />
        <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" @change="changeHorizontal"/>
        <EchartsWarningHorizontal :chartData="data.horizontalData" />
      </div>
    <!-- </div>
  </VScaleScreen> -->
</template>

<style lang="scss" scoped>
.v-screen-box {
  height: 1060px !important;
}

.module-title {
  margin: 20px 0;
}

.module-box {
  border: 1px solid #20497F;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
}
</style>