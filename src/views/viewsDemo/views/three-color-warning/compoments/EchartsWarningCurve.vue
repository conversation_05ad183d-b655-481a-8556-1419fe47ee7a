<template>
  <div ref="chartRef" style="width: 100%; height: 250px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  borderColor: {
    type: String,
    default: '#2AEFFC'
  }
})

const chartRef = ref()

onMounted(() => {
  initChart()
})
// 监听props变化
watch(() => props.chartData, () => {
  initChart()
}, { deep: true })

function initChart() {
  const chart = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.5)',
      borderWidth: '1',
      borderColor: props.borderColor,
      textStyle: {
        color: '#CFE3FC',
        fontSize: 18
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    legend: {
      data: ['红色预警', '黄色预警', '蓝色预警'],
      textStyle: {
        color: '#fff'
      },
    },
    grid: {
      left: '3%',
      right: '3%',
      top: '12%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: '10%',
      data: props.chartData.label,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        interval: 0, // 强制显示所有标签
        rotate: 30, // 标签旋转角度
        margin: 15, // 标签与轴线距离
        width: 60, // 标签宽度
        overflow: 'truncate' // 超出部分显示省略号
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    series: [
      {
        name: '红色预警',
        type: 'line',
        data: props.chartData.red,
        itemStyle: {
          color: '#FF3141'
        },
        lineStyle: {
          width: 3
        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 12,
          formatter: '{c}'
        }
      },
      {
        name: '黄色预警',
        type: 'line',
        data: props.chartData.yellow,
        itemStyle: {
          color: '#FF9F18'
        },
        lineStyle: {
          width: 3
        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 12,
          formatter: '{c}'
        }
      },
      {
        name: '蓝色预警',
        type: 'line',
        data: props.chartData.blue,
        itemStyle: {
          color: '#1677FF'
        },
        lineStyle: {
          width: 3
        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 12,
          formatter: '{c}'
        }
      }
    ]
  }
  
  chart.setOption(option)
  
  window.addEventListener('resize', function() {
    chart.resize()
  })
}
</script>