<template>
  <div class="warning-data">
    <div class="warning-item red">
      <div class="name">红色预警</div>
      <div class="count">{{ warningData.red }}条</div>
    </div>
    <div class="warning-item yellow">
      <div class="name">黄色预警</div>
      <div class="count">{{ warningData.yellow }}条</div>
    </div>
    <div class="warning-item blue">
      <div class="name">蓝色预警</div>
      <div class="count">{{ warningData.blue }}条</div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  warningData: {
    type: Object,
    required: true
  }
})
</script>

<style lang="scss" scoped>
.warning-data {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;

  .warning-item {
    text-align: center;
    padding: 15px;
    border-radius: 8px;
    width: 30%;
    margin: 0 5px;

    .name {
      font-size: 14px;
      margin-bottom: 0.260vw;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      
     
      
      &::before {
        content: '';
        display: inline-block;
        width: 0.572vw;
        height: 0.572vw;
        margin-right: 0.208vw;
        background: url('@/assets/images/three/light.png') no-repeat center;
        background-size: contain;
        
        @media (max-width: 2000px) {
          width: 0.916vw;
          height: 0.916vw;
          margin-right: 0.333vw;
        }
      }
    }

    .count {
      font-size: 0.625vw;
      font-weight: bold;
      color: #fff;
      
      @media (max-width: 2000px) {
        font-size: 1vw;
      }
    }

    &.red {
      background: #FF3141;
    }

    &.yellow {
      background: #FF9F18;
    }

    &.blue {
      background: #1677FF;
    }
  }
}
</style>