<template>
  <div ref="chartRef" style="width: 100%; height: 200px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  borderColor: {
    type: String,
    default: '#2AEFFC'
  }
})

const chartRef = ref()

onMounted(() => {
  initChart()
})
// 监听props变化
watch(() => props.chartData, () => {
  initChart()
}, { deep: true })

function initChart() {
  const chart = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.5)',
      borderWidth: '1',
      borderColor: props.borderColor,
      textStyle: {
        color: '#CFE3FC',
        fontSize: 18
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    legend: {
      data: ['红色预警', '黄色预警', '蓝色预警'],
      textStyle: {
        color: '#fff'
      },
      icon: 'rect',
      itemWidth: 12,
      itemHeight: 12
    },
    grid: {
      left: '3%',
      right: '5%',
      top: '12%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      show: false,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: props.chartData.label,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)'
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    series: [
      {
        name: '红色预警',
        type: 'bar',
        data: props.chartData.red,
        itemStyle: {
          borderRadius: 4,
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#FF3141' },
            { offset: 1, color: 'rgba(255, 49, 65, 0.3)' }
          ])
        },
        barWidth: '20%',
        barGap: '30%',
        barCategoryGap: '40%',
        label: {
          show: true,
          position: 'right',
          color: '#fff',
          fontSize: 12,
          formatter: '{c}'
        }
      },
      {
        name: '黄色预警',
        type: 'bar',
        data: props.chartData.yellow,
        itemStyle: {
          borderRadius: 4,
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#FF9F18' },
            { offset: 1, color: 'rgba(255, 159, 24, 0.3)' }
          ])
        },
        barWidth: '20%',
        barGap: '30%',
        barCategoryGap: '40%',
        label: {
          show: true,
          position: 'right',
          color: '#fff',
          fontSize: 12,
          formatter: '{c}'
        }
      },
      {
        name: '蓝色预警',
        type: 'bar',
        data: props.chartData.blue,
        itemStyle: {
          borderRadius: 4,
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#1677FF' },
            { offset: 1, color: 'rgba(22, 119, 255, 0.3)' }
          ])
        },
        barWidth: '20%',
        barGap: '30%',
        barCategoryGap: '40%',
        label: {
          show: true,
          position: 'right',
          color: '#fff',
          fontSize: 12,
          formatter: '{c}'
        }
      }
    ]
  }
  
  chart.setOption(option)
  
  window.addEventListener('resize', function() {
    chart.resize()
  })
}
</script>