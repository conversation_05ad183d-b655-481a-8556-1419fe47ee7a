<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ModuleTitle from '@/views/sidebar/compoments/ModuleTitle.vue'
import DateGroup from '@/views/common/DateGroup.vue'
import SuggestionOverview from './compoments/SuggestionOverview.vue'
import EchartsAdoptionRate from './compoments/EchartsAdoptionRate.vue'
import EchartsProcessStatus from './compoments/EchartsProcessStatus.vue'
import EchartsHotReuse from '@/views/common/EchartsHotReuse.vue'
import VScaleScreen from 'v-scale-screen'
import { suggestionInfo, suggestionHots, suggestionList } from '@/common/api/suggestion'
import { AES_KEY, decrypt, encrypt } from '@/common/utils/aes'
import { useCodeStore } from '@/stores/code'

const isLoadingInfo = ref(true)
const isLoadingList = ref(true)
const isLoadingPie = ref(true)
const isLoadingHots = ref(true)

// timeType : 获取时间类型0-当天 1-周 2-3月 3-6月 4- 1年
const timeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]
const codeStore = useCodeStore()

const overviewData = ref({})

const adoptionData = ref([
  { name: '采纳', value: 65, color: '#018FFF' },
  { name: '部分采纳', value: 23, color: '#E9B902' },
  { name: '留作参考', value: 12, color: '#5D7092' }
])

const processData = ref([])

const hotWordsData = ref([])

async function getSuggestionInfo(timeType: number | string = 0) {
  isLoadingInfo.value = true
  const res = await suggestionInfo({
    data: encrypt({ timeType }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  overviewData.value = data
  isLoadingInfo.value = false
}

async function getSuggestionPie(timeType: number | string = 2) {
  isLoadingPie.value = true
  await new Promise(resolve => setTimeout(resolve, 500))
  isLoadingPie.value = false
}

async function getSuggestionList(timeType: number | string = 2) {
  isLoadingList.value = true
  const res = await suggestionList({
    data: encrypt({
      timeType:timeType,
      pageNum:1,
      pageSize:5,
      areaCode: codeStore.code
    }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  processData.value = data.list;
  isLoadingList.value = false
}

async function getSuggestionHots(timeType: number | string = 2) {
  isLoadingHots.value = true
  const res = await suggestionHots({
    data: encrypt({ timeType }),
    key: AES_KEY
  })
  const data = decrypt(res.data, res.key)
  hotWordsData.value = data.list
  isLoadingHots.value = false
}

onMounted(() => {
  getSuggestionInfo()
  getSuggestionList()
  getSuggestionPie()
  getSuggestionHots()
})

const changeInfo = (index: string | number) => {
  getSuggestionInfo(index)
}
const changePie = (index: string | number) => {
  getSuggestionPie()
}
const changeList = (index: string | number) => {
  getSuggestionList(index)
}
const changeHots = (index: string | number) => {
  getSuggestionHots(index)
}
</script>

<template>
  <VScaleScreen
    :delay="200"
    width="480"
    height="1500"
    :bodyOverflowHidden="false"
    :autoScale="{ x: true, y: false }"
    :boxStyle="{ background: '#021121' }"
  >
    <div class="base-box">
      <div class="content">
        <div class="module-box">
          <ModuleTitle title="崃建言概览" />
          <DateGroup index="0" :dateData="timeOptions" @change="changeInfo"/>
          <SuggestionOverview :overviewData="overviewData" 
            :titleData="{receivedTitle:'已接收', feedbackTitle:'已反馈'}" 
          />
        </div>
        
        <div class="module-box">
          <ModuleTitle title="崃建言采纳情况" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))"
             @change="changePie"/>
          <EchartsAdoptionRate :chartData="adoptionData" :loading="isLoadingPie"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="崃建言处理情况" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" 
            @change="changeList"/>
          <EchartsProcessStatus :chartData="processData" :loading="isLoadingList"/>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="崃建言高频词热力图" />
          <DateGroup index="2" :dateData="timeOptions.filter(i => ['2','3','4'].includes(i.value))" 
            @change="changeHots"/>
          <EchartsHotReuse :chartData="hotWordsData" :loading="isLoadingHots"
          :dataKeys="{
            nameKey: 'dict_label',
            valueKey: 'id_count'
          }"/>
        </div>
      </div>
    </div>
  </VScaleScreen>
</template>

<style lang="scss" scoped>
.v-screen-box {
  height: 1500px !important;
}

.module-title {
  margin: 20px 0;
}

.module-box {
  border: 1px solid #20497F;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
}
</style>