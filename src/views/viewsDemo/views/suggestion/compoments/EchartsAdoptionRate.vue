<template>
  <div ref="chartRef" style="width: 100%; height: 300px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const chartRef = ref()
let chart: echarts.ECharts | null = null

const initChart = () => {
  if (!chart) return
  
  const option = {
    legend: {
      orient: 'horizontal',
      top: 0,
      textStyle: {
        color: '#fff'
      },
      data: props.chartData.map((item: { name: any }) => item.name),
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 20
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.5)',
      borderColor: '#2AEFFC',
      textStyle: {
        color: '#CFE3FC'
      }
    },
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      itemStyle: {
        borderRadius: 10,
        borderColor: '#021121',
        borderWidth: 2
      },
      data: props.chartData.map((item: { value: any; name: any; color: any }) => ({
        value: item.value,
        name: item.name,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: `${item.color}00` },
            { offset: 1, color: item.color }
          ])
        }
      })),
      label: {
        color: '#fff'
      }
    }]
  }
  
  chart.setOption(option)
}

watch(() => props.chartData, initChart)
watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading({
      text: '加载中...',
      color: '#2AEFFC',
      textColor: '#fff',
      maskColor: 'rgba(0, 0, 0, 0.5)'
    })
  } else {
    chart?.hideLoading()
    initChart()
  }
})

onMounted(() => {
  chart = echarts.init(chartRef.value)
  if (props.loading) {
    chart.showLoading()
  } else {
    initChart()
  }
})
</script>