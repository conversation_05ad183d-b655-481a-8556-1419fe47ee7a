<template>
  <div class="reuse-overview">
    <div class="data-item" style="border: 1px solid #FF9F18">
      <div class="icon">
        <img src="@/assets/images/common/received.png" alt="views">
      </div>
      <div class="text">
        <div class="name">{{ titleData.receivedTitle}}</div>
        <div class="value">{{ overviewData.receivedCount || 0 }}{{ titleData.receivedUnit }}</div>
      </div>
    </div>
    <div class="data-item" style="border: 1px solid #00B578">
      <div class="icon">
        <img src="@/assets/images/common/feedback.png" alt="views">
      </div>
      <div class="text">
        <div class="name">{{ titleData.feedbackTitle}}</div>
        <div class="value">{{ overviewData.feedbackCount || 0 }}{{ titleData.feedbackUnit }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  overviewData: {
    type: Object,
    required: true,
    default: () => ({
      receivedCount: 0,
      feedbackCount: 0
    })
  },
  titleData: {
    type: Object,
    required: false,
    default: () => ({
      receivedTitle: '已接收',
      feedbackTitle: '已反馈',
      receivedUnit: '条',
      feedbackUnit: '条'
    })
  }
})
</script>

<style lang="scss" scoped>
.reuse-overview {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  
  .data-item {
    display: flex;
    align-items: center;
    background: transparent; 
    border-radius: 8px;
    padding: 15px;
    width: 140px;
    height: 68px;
    
    .icon {
      width: 40px;
      height: 40px;
      margin-right: 15px;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    
    .text {
      .name {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0.130vw;
        
      
      }
      
      .value {
        font-size: 12px;
        font-weight: bold;
        color: #fff;
        
       
      }
    }
  }
}
</style>