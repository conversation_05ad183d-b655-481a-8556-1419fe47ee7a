<template>
  <div
    class="big-screen-container"
    :class="{
      'is-fullscreen': isFullscreen,
      'is-wide': screenMode === 'wide',
      'is-narrow': screenMode === 'narrow',
    }"
  >
    <!-- 头部标题 -->
    <header class="screen-header">
      <!-- 装饰线条 -->
      <div class="header-decoration-left"></div>
      <div class="header-decoration-right"></div>

      <!-- 左侧信息 -->
      <div class="header-left">
        <div class="system-info">
          <!-- <span class="system-name">本地-微网实格大屏</span> -->
        </div>
        <div class="current-time">
          <div class="time-date">{{ currentDate }}</div>
          <div class="time-clock">{{ currentClock }}</div>
        </div>
      </div>

      <!-- 中间标题 -->
      <div class="header-center">
        <h1 class="screen-title">党建引领基层治理服务平台</h1>
      </div>

      <!-- 右侧功能区 -->
      <div class="header-right">
        <!-- <div class="screen-info">
          <span class="screen-width">{{ screenWidth }}px</span>
          <span class="layout-mode">{{ layoutMode }}</span>
        </div> -->
        <el-button
          class="fullscreen-btn"
          @click="toggleScreenMode"
          :class="{ active: screenMode === 'wide' }"
        >
          <el-icon><Switch /></el-icon>
          {{ screenMode === "wide" ? "窄屏模式" : "宽屏模式" }}
        </el-button>
        <el-button
          class="fullscreen-btn"
          @click="toggleFullscreen"
          :class="{ active: isFullscreen }"
        >
          <el-icon><FullScreen /></el-icon>
          {{ isFullscreen ? "退出全屏" : "全屏" }}
        </el-button>
        <el-button class="logout-btn" @click="handleLogout">
          <el-icon><SwitchButton /></el-icon>
          退出
        </el-button>
      </div>
    </header>

    <!-- 主体内容区域 -->
    <main class="screen-main">
      <!-- 左侧面板 -->
      <div class="left-panels">
        <!-- 左1：三色预警 -->
        <section class="left1-panel">
          <Left1Component />
        </section>

        <!-- 左2：崃建言 (仅在宽屏和全屏模式显示) -->
        <section class="left2-panel responsive-panel">
          <Left2Component />
        </section>
      </div>

      <!-- 中央：地图区域 -->
      <section class="center-panel">
        <div class="map-container">
          <MapComponent />
        </div>
      </section>

      <!-- 右侧面板 -->
      <div class="right-panels">
        <!-- 右1：找社区 -->
        <section class="right1-panel">
          <Right1Component />
        </section>

        <!-- 右2：微互助 (仅在宽屏和全屏模式显示) -->
        <section class="right2-panel responsive-panel">
          <Right2Component />
        </section>
      </div>
    </main>

    <!-- 修改密码弹窗 -->
    <ChangePasswordDialog
      v-model:visible="changePasswordVisible"
      @success="handlePasswordChangeSuccess"
    />

    <!-- 全屏控制组件（用于测试） -->
    <FullscreenControl />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { Location, FullScreen, SwitchButton } from "@element-plus/icons-vue";
import Left1Component from "@/components/bigscreen/Left1Component.vue";
import Left2Component from "@/components/bigscreen/Left2Component.vue";
import Right1Component from "@/components/bigscreen/Right1Component.vue";
import Right2Component from "@/components/bigscreen/Right2Component.vue";
import CommunityEconomy from "@/components/CommunityEconomy.vue";
import CommunitySharing from "@/components/CommunitySharing.vue";
import CommunityQuantum from "@/components/CommunityQuantum.vue";
import ChangePasswordDialog from "@/components/ChangePasswordDialog.vue";
import FullscreenControl from "@/components/FullscreenControl.vue";
import { getCommunityUserInfoApi, logoutApi, authorizeApi } from "@/api/auth";
import { getCommunityStatisticsApi } from "@/api/community";
import { useUserStore } from "@/stores/user";
import { useFullscreenStore } from "@/stores/fullscreen";
import { generateAuthCode, decryptResponseData } from "@/utils/crypto";
import MapComponent from "@/components/map/HomeView.vue";
const router = useRouter();
const userStore = useUserStore();
const fullscreenStore = useFullscreenStore();

// 当前时间
const currentTime = ref("");
const currentDate = ref("");
const currentClock = ref("");

// 屏幕宽度和布局模式
const screenWidth = ref(window.innerWidth);
const layoutMode = ref("");

// 全屏状态 - 使用store中的状态
const isFullscreen = computed(() => fullscreenStore.isFullscreen);
const screenMode = computed(() => fullscreenStore.screenMode);

// 修改密码弹窗
const changePasswordVisible = ref(false);

// 宽屏模式切换
const setWideMode = () => {
  fullscreenStore.setWideMode();
  ElMessage.success("已切换到宽屏模式");
  console.log("切换到宽屏模式");
};

// 窄屏模式切换
const setNarrowMode = () => {
  fullscreenStore.setNarrowMode();
  ElMessage.success("已切换到窄屏模式");
  console.log("切换到窄屏模式");
};

// 屏幕模式切换（新增的切换函数）
const toggleScreenMode = () => {
  if (screenMode.value === "wide") {
    setNarrowMode();
  } else {
    setWideMode();
  }
};

// 更新时间
const updateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hour = now.getHours();
  const minute = String(now.getMinutes()).padStart(2, "0");
  const second = String(now.getSeconds()).padStart(2, "0");

  const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
  const weekday = weekdays[now.getDay()];

  currentTime.value = `${year}年${month}月${day}日 ${weekday} ${hour}:${minute}:${second}`;
  currentDate.value = `${year}年${month}月${day}日 ${weekday}`;
  currentClock.value = `${hour}:${minute}:${second}`;
};

// 更新布局模式
const updateLayoutMode = () => {
  screenWidth.value = window.innerWidth;
  const currentFullscreen = !!document.fullscreenElement;
  layoutMode.value = currentFullscreen ? "全屏模式" : "默认模式";
};

// 全屏状态变化监听
const handleFullscreenChange = () => {
  updateLayoutMode();
  console.log("全屏状态变化:", isFullscreen.value ? "全屏模式" : "默认模式");
};

// 检查是否需要修改密码
const checkPasswordChange = async () => {
  try {
    const result = await getCommunityUserInfoApi();
    const userInfo = result.data;

    console.log("用户信息:", userInfo);

    // 如果defaultPassword为1，表示需要修改密码
    if (userInfo.defaultPassword === 1) {
      console.log("检测到默认密码，需要修改");
      // 延迟一秒显示弹窗，确保页面已完全加载
      setTimeout(() => {
        changePasswordVisible.value = true;
      }, 1000);
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
  }
};

// 修改密码成功后的处理
const handlePasswordChangeSuccess = () => {
  console.log("密码修改成功");
  // 可以在这里更新用户信息或其他逻辑
};

// 处理解密后的数据
const handleDecryptedData = (decryptedData: string) => {
  try {
    console.log("📋 处理解密后的数据:", decryptedData);

    // 尝试解析为JSON（如果是JSON格式）
    let parsedData;
    try {
      parsedData = JSON.parse(decryptedData);
      console.log("📊 解析为JSON成功:", parsedData);
    } catch {
      // 如果不是JSON格式，直接使用字符串
      parsedData = decryptedData;
      console.log("📝 使用字符串格式:", parsedData);
    }

    // 存储到localStorage（可选）
    localStorage.setItem("authorizeData", decryptedData);
    console.log("💾 已存储到localStorage");

    // 如果解析成功且包含token，调用精治数仓接口
    if (parsedData && typeof parsedData === "object" && parsedData.token) {
      console.log("🔑 获取到token:", parsedData.token);
      // 调用精治数仓社区概览接口
      callCommunityStatisticsApi(parsedData.token);

      // 通知Left1Component调用文件统计接口
      setTimeout(() => {
        if ((window as any).getFileAndResLogStatistics) {
          (window as any).getFileAndResLogStatistics();
        }
        if ((window as any).getResidentsTagStatistics) {
          (window as any).getResidentsTagStatistics();
        }
        if ((window as any).getMultipleLabelsData) {
          (window as any).getMultipleLabelsData();
        }
        if ((window as any).getResResidentsLogList) {
          (window as any).getResResidentsLogList();
        }
        if ((window as any).getAnticipateList) {
          (window as any).getAnticipateList();
        }
        if ((window as any).getAgeSummary) {
          (window as any).getAgeSummary();
        }
        if ((window as any).getFamilyDoctorOverview) {
          (window as any).getFamilyDoctorOverview();
        }
      }, 1000);
    } else {
      console.warn("⚠️ 未找到token，无法调用精治数仓接口");
    }
  } catch (error) {
    console.error("❌ 处理解密数据失败:", error);
  }
};

// 处理精治数仓社区统计数据
const handleCommunityStatisticsData = (data: any) => {
  try {
    console.log("📋 处理精治数仓社区统计数据:", data);

    // 定义社区概览数据变量
    const communityOverviewData = {
      courtyardTotal: 0, // 小区院落数量
      populationTotal: 0, // 人口数量
      labelsTotal: 0, // 居民标签数
      enterpriseTotal: 0, // 企业数
      residentsHouseTotal: 0, // 房人关系数
      housesTotal: 0, // 房屋数量
    };

    // 如果数据是数组格式，遍历并赋值
    if (Array.isArray(data)) {
      data.forEach((item: any) => {
        switch (item.name) {
          case "courtyardTotal":
            communityOverviewData.courtyardTotal = item.total || 0;
            console.log("🏘️ 小区院落数量:", item.total);
            break;
          case "populationTotal":
            communityOverviewData.populationTotal = item.total || 0;
            console.log("👥 人口数量:", item.total);
            break;
          case "labelsTotal":
            communityOverviewData.labelsTotal = item.total || 0;
            console.log("🏷️ 居民标签数:", item.total);
            break;
          case "enterpriseTotal":
            communityOverviewData.enterpriseTotal = item.total || 0;
            console.log("🏢 企业数:", item.total);
            break;
          case "residentsHouseTotal":
            communityOverviewData.residentsHouseTotal = item.total || 0;
            console.log("🏠 房人关系数:", item.total);
            break;
          case "housesTotal":
            communityOverviewData.housesTotal = item.total || 0;
            console.log("🏡 房屋数量:", item.total);
            break;
          default:
            console.log("❓ 未知数据类型:", item.name, item.total);
        }
      });
    }

    console.log("✅ 社区概览数据处理完成:", communityOverviewData);

    // 存储处理后的数据
    localStorage.setItem("communityOverviewData", JSON.stringify(communityOverviewData));

    // 触发Left1Component数据更新
    updateLeft1CommunityData(communityOverviewData);
  } catch (error) {
    console.error("❌ 处理社区统计数据失败:", error);
  }
};

// 更新Left1Component的社区数据
const updateLeft1CommunityData = (overviewData: any) => {
  try {
    console.log("🔄 BigScreenView开始更新Left1Component社区数据:", overviewData);

    // 方式1: 通过自定义事件通知Left1Component更新数据（传递原始数据）
    const updateEvent = new CustomEvent("updateCommunityData", {
      detail: overviewData,
    });
    window.dispatchEvent(updateEvent);

    // 方式2: 直接调用全局更新函数（如果存在）
    if ((window as any).updateLeft1CommunityData) {
      console.log("📞 调用全局更新函数");
      (window as any).updateLeft1CommunityData(overviewData);
    } else {
      console.log("⚠️ 全局更新函数尚未准备就绪，将在500ms后重试");
      setTimeout(() => {
        if ((window as any).updateLeft1CommunityData) {
          console.log("📞 延迟调用全局更新函数");
          (window as any).updateLeft1CommunityData(overviewData);
        } else {
          console.log("❌ 延迟调用仍然失败，全局更新函数不存在");
        }
      }, 500);
    }

    console.log("✅ 已发送更新事件到Left1Component");
  } catch (error) {
    console.error("❌ 更新Left1Component数据失败:", error);
  }
};

// 调用精治数仓社区概览接口
const callCommunityStatisticsApi = async (token: string) => {
  try {
    console.log("📊 开始调用精治数仓社区概览接口");

    const result = await getCommunityStatisticsApi(token);

    console.log("✅ 精治数仓接口调用成功:", result);

    // 处理返回的社区统计数据
    if (result.code === 1 || result.code === 200) {
      console.log("🎉 获取社区统计数据成功");

      // 对返回的data进行SM4解密
      if (result.data) {
        try {
          // console.log('🔓 开始解密精治数仓返回数据:', result.data)
          const decryptedCommunityData = decryptResponseData(result.data);
          // console.log('✅ 精治数仓数据解密成功:', decryptedCommunityData)

          // 尝试解析为JSON
          let parsedCommunityData;
          try {
            parsedCommunityData = JSON.parse(decryptedCommunityData);
            // console.log('📊 精治数仓数据解析为JSON成功:', parsedCommunityData)
          } catch {
            parsedCommunityData = decryptedCommunityData;
            // console.log('📝 精治数仓数据使用字符串格式:', parsedCommunityData)
          }

          // 存储解密后的数据
          localStorage.setItem("communityStatistics", JSON.stringify(parsedCommunityData));
          //console.log('💾 解密后的社区统计数据已存储到localStorage')

          // 处理解密后的社区统计数据
          handleCommunityStatisticsData(parsedCommunityData);
        } catch (error) {
          // console.error('❌ 精治数仓数据解密失败:', error)
          // 解密失败时，存储原始数据作为后备
          localStorage.setItem("communityStatistics", JSON.stringify(result.data));
        }
      } else {
        //console.log('ℹ️ 没有需要解密的精治数仓数据')
      }
    } else {
      // console.warn('⚠️ 获取社区统计数据失败:', result.message)
    }
  } catch (error) {
    // console.error('❌ 精治数仓接口调用失败:', error)
    // 接口调用失败不影响页面正常使用
  }
};

// 调用授权接口
const callAuthorizeApi = async () => {
  try {
    console.log("🔐 开始调用授权接口");

    // 使用固定的社区行政编号
    const areaCode = "510183002014";
    // console.log('社区行政编号:', areaCode)
    // console.log('当前时间戳:', Date.now())

    // 生成加密code
    const code = generateAuthCode(areaCode);
    //console.log('生成的加密code:', code)

    const cityCode = "510183002014";
    // console.log('城市编码:', cityCode)

    // 调用授权接口
    const result = await authorizeApi({
      cityCode: cityCode,
      code: code,
    });

    //console.log('✅ 授权接口调用成功:', result)

    // 可以根据返回结果进行后续处理
    if (result.code === 1) {
      console.log("🎉 授权成功");

      // 对返回的data进行SM4解密
      if (result.data) {
        try {
          //  console.log('🔓 开始解密返回数据:', result.data)
          const decryptedData = decryptResponseData(result.data);
          // console.log('✅ 解密成功:', decryptedData)

          // 处理解密后的数据
          handleDecryptedData(decryptedData);
        } catch (error) {
          // console.error('❌ 解密失败:', error)
        }
      } else {
        // console.log('ℹ️ 没有需要解密的数据')
      }
    } else {
      //console.warn('⚠️ 授权失败:', result.message || '未知错误')
    }
  } catch (error) {
    // console.error('❌ 授权接口调用失败:', error)
    // 授权失败不影响页面正常使用，只记录错误
    // 可以考虑在这里添加重试逻辑或用户提示
  }
};

// 退出登录
const handleLogout = async () => {
  try {
    // 确认退出
    await ElMessageBox.confirm("确定要退出系统吗？", "退出确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    try {
      // 调用退出接口
      await logoutApi();
      console.log("服务器退出成功");
    } catch (error) {
      console.warn("服务器退出失败，但继续本地退出:", error);
      // 即使服务器退出失败，也继续本地退出流程
    }

    // 清除本地用户信息
    userStore.clearUserInfo();

    // 清除本地存储
    localStorage.removeItem("token");
    localStorage.removeItem("userInfo");
    localStorage.removeItem("communityUserInfo");

    ElMessage.success("退出成功");

    // 跳转到登录页面
    router.push("/login");
  } catch (error) {
    if (error !== "cancel") {
      console.error("退出失败:", error);
    }
  }
};

let timeInterval: number;

onMounted(() => {
  updateTime();
  updateLayoutMode();
  timeInterval = setInterval(updateTime, 1000);
  window.addEventListener("resize", updateLayoutMode);
  document.addEventListener("fullscreenchange", handleFullscreenChange);

  // 检查是否需要修改密码
  checkPasswordChange();

  // 调用授权接口
  console.log("🚀 BigScreenView组件挂载完成，开始调用授权接口");

  // 延迟一点时间确保Left1Component已经完全挂载
  setTimeout(() => {
    callAuthorizeApi();
  }, 100);

  // 调试：定期检查地图组件状态
  const checkMapStatus = () => {
    console.log("🔍 定期检查地图组件状态:", {
      mapComponentLoaded: (window as any).mapComponentLoaded,
      addTotalHeatMap: typeof (window as any).addTotalHeatMap,
      addHeatMapData: typeof (window as any).addHeatMapData,
      windowMapKeys: Object.keys(window).filter(
        (key) =>
          key.includes("map") ||
          key.includes("Map") ||
          key.includes("heat") ||
          key.includes("Heat") ||
          key.includes("add")
      ),
    });
  };

  // 立即检查一次
  setTimeout(checkMapStatus, 500);

  // 每2秒检查一次，持续10次
  let checkCount = 0;
  const checkInterval = setInterval(() => {
    checkCount++;
    checkMapStatus();

    if (checkCount >= 10 || (window as any).mapComponentLoaded) {
      clearInterval(checkInterval);
      console.log("🔍 停止定期检查地图组件状态");
    }
  }, 2000);
});

onUnmounted(() => {
  clearInterval(timeInterval);
  window.removeEventListener("resize", updateLayoutMode);
  document.removeEventListener("fullscreenchange", handleFullscreenChange);
});

// 切换全屏状态
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    // 进入全屏
    document.documentElement
      .requestFullscreen()
      .then(() => {
        ElMessage.success("已进入全屏模式");
        // 手动更新状态，确保立即生效
        setTimeout(() => {
          fullscreenStore.setFullscreen(true);
          fullscreenStore.setLeftPage(1); // 初始化左侧为第一屏
          fullscreenStore.setRightPage(1); // 初始化右侧为第一屏
          console.log("进入全屏模式，设置fullscreenStore状态为 true");
        }, 100);
      })
      .catch((err) => {
        ElMessage.error("全屏模式不支持");
        console.error("全屏失败:", err);
      });
  } else {
    // 退出全屏
    document
      .exitFullscreen()
      .then(() => {
        ElMessage.success("已退出全屏模式");
        // 手动更新状态，确保立即生效
        setTimeout(() => {
          fullscreenStore.setFullscreen(false);
          console.log("退出全屏模式，设置fullscreenStore状态为 false");
        }, 100);
      })
      .catch((err) => {
        ElMessage.error("退出全屏失败");
        console.error("退出全屏失败:", err);
      });
  }
};
</script>

<style scoped>
.big-screen-container {
  width: 100vw;
  height: 100vh;
  background: #0b1124;
  color: white;
  font-family: "Microsoft YaHei", sans-serif;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  font-size: 14px; /* 固定字体大小 */
  overflow: auto; /* 允许滚动 */
  /* 隐藏滚动条样式 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
}

/* 隐藏 webkit 浏览器的滚动条 */
.big-screen-container::-webkit-scrollbar {
  display: none;
}

/* 头部样式 */
.screen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 85px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
  margin-bottom: 20px;
  padding: 0 20px;
  background: rgba(0, 0, 0, 0.2);
  background: url("@/assets/common/title01..png") no-repeat center center;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

/* 装饰线条 */
.header-decoration-left {
  position: absolute;
  left: 0;
  top: 0;
  width: 100px;
  height: 100%;
  background: linear-gradient(90deg, rgba(0, 212, 255, 0.1) 0%, transparent 100%);
  pointer-events: none;
}

.header-decoration-right {
  position: absolute;
  right: 0;
  top: 0;
  width: 100px;
  height: 100%;
  background: linear-gradient(270deg, rgba(0, 212, 255, 0.1) 0%, transparent 100%);
  pointer-events: none;
}

/* 左侧信息 */
.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
  min-width: 300px;
  flex: 1;
}

.system-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.system-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
  }
  50% {
    box-shadow: 0 0 16px rgba(16, 185, 129, 0.8);
  }
  100% {
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
  }
}
.el-button + .el-button {
  margin-left: 0;
}
.system-name {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
  white-space: nowrap;
}

.current-time {
  font-size: 12px;
  color: #00d4ff;
  font-weight: 500;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 2px;
}

.time-date {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
  line-height: 1.2;
  margin-right: 10px;
}

.time-clock {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 600;
  line-height: 1.2;
}

/* 中间标题 */
.header-center {
  flex: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 0; /* 允许flex收缩 */
}

.screen-title {
  font-family: PingFang SC;
  font-weight: bold;
  font-size: 48px;
  color: #ffffff;
  margin: 0;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 右侧功能区 */
.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
  min-width: 200px;
  flex: 1;
  justify-content: flex-end;
}

.screen-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.screen-width {
  font-size: 12px;
  color: #00d4ff;
  font-weight: 500;
}

.layout-mode {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.fullscreen-btn {
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 4px;
  padding: 6px 22px;
  font-size: 12px;
  font-weight: 500;
  color: #00d4ff;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.fullscreen-btn:hover {
  background: rgba(0, 212, 255, 0.2);
  border-color: rgba(0, 212, 255, 0.6);
  box-shadow: 0 0 12px rgba(0, 212, 255, 0.3);
  transform: translateY(-1px);
}

.fullscreen-btn .el-icon {
  font-size: 14px;
  margin-right: 2px;
}

.fullscreen-btn.active {
  background: rgba(0, 212, 255, 0.3);
  border-color: rgba(0, 212, 255, 0.8);
  box-shadow: 0 0 16px rgba(0, 212, 255, 0.4);
  color: #ffffff;
}

.logout-btn {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.4);
  border-radius: 4px;
  padding: 6px 22px;
  font-size: 12px;
  font-weight: 500;
  color: #ff6b6b;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.logout-btn:hover {
  background: rgba(255, 107, 107, 0.2);
  border-color: rgba(255, 107, 107, 0.6);
  box-shadow: 0 0 12px rgba(255, 107, 107, 0.3);
  transform: translateY(-1px);
}

.logout-btn .el-icon {
  font-size: 14px;
  margin-right: 2px;
}

/* 1920分辨率下的特殊样式 */
@media screen and (width: 1920px) {
  .screen-header {
    height: 60px;
    /* background: url('@/assets/common/title02..png') no-repeat; */
    background-size: 100% 60px;
  }

  .screen-title {
    font-size: 32px;
  }

  .screen-main {
    height: calc(100vh - 60px - 20px); /* 调整主体区域高度，适配新的header高度 */
  }
}

/* 删除所有响应式设计，使用固定布局 */

/* 主体区域布局 */
.screen-main {
  position: relative;
  width: 100%;
  height: calc(100vh - 85px - 20px); /* 固定头部高度85px + 底部margin */
  overflow: hidden; /* 防止内容溢出 */
  padding: 0 10px 10px 10px;
}

/* 窄屏模式：只显示left1和right1 */
.big-screen-container.is-narrow .left-panels {
  position: absolute;
  left: 10px;
  top: 0;
  width: 410px;
  height: 100%;
}

.big-screen-container.is-narrow .center-panel {
  position: absolute;
  left: 430px; /* 410px + 20px gap */
  right: 430px; /* 410px + 20px gap */
  top: 0;
  height: 100%;
}

.big-screen-container.is-narrow .right-panels {
  position: absolute;
  right: 10px;
  top: 0;
  width: 410px;
  height: 100%;
}

.big-screen-container.is-narrow .responsive-panel {
  display: none; /* 隐藏左2和右2 */
}

/* 宽屏模式：显示left1+left2和right1+right2 */
.big-screen-container.is-wide .left-panels {
  position: absolute;
  left: 10px;
  top: 0;
  width: 830px; /* 410px * 2 + 10px gap */
  height: 100%;
}

.big-screen-container.is-wide .center-panel {
  position: absolute;
  left: 850px; /* 830px + 20px gap */
  right: 850px; /* 830px + 20px gap */
  top: 0;
  height: 100%;
}

.big-screen-container.is-wide .right-panels {
  position: absolute;
  right: 10px;
  top: 0;
  width: 830px; /* 410px * 2 + 10px gap */
  height: 100%;
}

.big-screen-container.is-wide .responsive-panel {
  display: flex; /* 显示左2和右2 */
}

/* 全屏模式：使用绝对定位 */
.big-screen-container.is-fullscreen .left-panels {
  position: absolute;
  left: 10px;
  top: 0;
  width: 830px; /* 410px * 2 + 10px gap */
  height: 100%;
}

.big-screen-container.is-fullscreen .center-panel {
  position: absolute;
  left: 850px; /* 830px + 20px gap */
  right: 850px; /* 830px + 20px gap */
  top: 0;
  height: 100%;
}

.big-screen-container.is-fullscreen .right-panels {
  position: absolute;
  right: 10px;
  top: 0;
  width: 830px; /* 410px * 2 + 10px gap */
  height: 100%;
}

.big-screen-container.is-fullscreen .responsive-panel {
  display: flex; /* 显示左2和右2 */
}

.left-panels {
  display: flex;
  gap: 5px;
  /* 绝对定位，不受其他元素影响 */
  z-index: 1;
}

.left1-panel {
  flex: 1;
  height: 100%;
  overflow-y: auto; /* 只允许垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
  /* 隐藏滚动条样式 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
  /* 确保内容能够完全滚动 */
  padding-bottom: 50px; /* 增加底部留白 */
}

.left1-panel::-webkit-scrollbar {
  display: none;
}

.left2-panel {
  flex: 1;
  height: 100%;
  overflow-y: auto; /* 只允许垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
  /* 隐藏滚动条样式 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
  /* 确保内容能够完全滚动 */
  padding-bottom: 20px; /* 底部留白 */
}

.left2-panel::-webkit-scrollbar {
  display: none;
}

.center-panel {
  /* background: rgba(255, 255, 255, 0.05); */
  /* border: 1px solid rgba(0, 212, 255, 0.3); */
  border-radius: 12px;
  /* padding: 10px; */
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  /* 绝对定位，独立于其他元素 */
  z-index: 0;
  height: 100%;
}

.right-panels {
  display: flex;
  gap: 5px;
  /* 绝对定位，不受其他元素影响 */
  z-index: 1;
}

.right1-panel {
  flex: 1;
  height: 100%;
  overflow-y: auto; /* 只允许垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
  /* 隐藏滚动条样式 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
  /* 确保内容能够完全滚动 */
  padding-bottom: 20px; /* 底部留白 */
}

.right1-panel::-webkit-scrollbar {
  display: none;
}

.right2-panel {
  flex: 1;
  height: 100%;
  overflow-y: auto; /* 只允许垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
  /* 隐藏滚动条样式 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
  /* 确保内容能够完全滚动 */
  padding-bottom: 20px; /* 底部留白 */
}

.right2-panel::-webkit-scrollbar {
  display: none;
}

/* 中央地图区域 */
.panel-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.panel-title {
  margin: 0;
  font-size: 18px;
  color: #00d4ff;
  text-align: center;
  font-weight: 600;
}

.map-container {
  overflow: hidden;
  position: relative;
  height: 100%;
  /* 移除所有边框和焦点样式 */
  outline: none;
}

/* 全局移除地图相关元素的边框 */
.center-panel *,
.map-container *,
#mapContainer,
#mapContainer * {
  outline: none !important;
  box-shadow: none !important;
}

/* 特别针对Canvas和WebGL元素 */
canvas,
canvas:focus,
canvas:active {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

.map-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  gap: 20px;
}

.map-placeholder p {
  font-size: 16px;
  margin: 0;
  color: #00d4ff;
}

.location-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
}

.location-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.location-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

.location-dot.red {
  background: #ef4444;
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.6);
}

.location-dot.green {
  background: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
}

.location-dot.blue {
  background: #3b82f6;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
}

/* 已移除所有响应式适配，使用固定布局 */
</style>
