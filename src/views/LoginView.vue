<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="grid-lines"></div>
      <div class="floating-particles">
        <div class="particle" v-for="i in 20" :key="i"></div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="login-content">
      <!-- 左侧3D模型区域 -->
      <div class="left-section">
        <div class="model-container">
          <!-- 3D建筑模型 -->
          <div class="building-model">
            <div class="building-base">
              <div class="building-tower tower-1"></div>
              <div class="building-tower tower-2"></div>
              <div class="building-tower tower-3"></div>
              <div class="building-tower tower-4"></div>
            </div>
            <div class="building-platform">
              <div class="platform-ring ring-1"></div>
              <div class="platform-ring ring-2"></div>
              <div class="platform-ring ring-3"></div>
            </div>
          </div>
          
          <!-- 浮动装饰元素 -->
          <div class="floating-elements">
            <div class="cube cube-1"></div>
            <div class="cube cube-2"></div>
            <div class="cube cube-3"></div>
            <div class="cube cube-4"></div>
            <div class="cube cube-5"></div>
            <div class="cube cube-6"></div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="right-section">
        <div class="login-form-container">
          <!-- 标题 -->
          <div class="login-header">
            <div class="logo-icon">
                             <el-icon size="40"><OfficeBuilding /></el-icon>
            </div>
            <h1 class="login-title">党建引领基层治理服务平台</h1>
          </div>

          <!-- 登录表单 -->
          <el-form 
            :model="loginForm" 
            :rules="loginRules" 
            ref="loginFormRef"
            class="login-form"
            size="large"
          >
                         <el-form-item prop="username">
               <el-input
                 v-model="loginForm.username"
                 placeholder="请输入账号"
                 prefix-icon="User"
                 class="login-input"
               >
                <template #prefix>
                  <!-- <el-icon><User /></el-icon> -->
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                show-password
                class="login-input"
              >
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item>
              <el-button 
                type="primary" 
                class="login-button"
                :loading="loading"
                @click="handleLogin"
              >
                立即登录
              </el-button>
            </el-form-item>


          </el-form>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { OfficeBuilding, User, Lock } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { loginApi, getCommunityUserInfoApi } from '@/api'
import type { LoginData } from '@/api'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref<FormInstance>()
const loading = ref(false)

// 登录表单数据
const loginForm = reactive<LoginData>({
  clientId: '980a7458cd984785d19206ce24a7bksa', // 大屏客户端标识
  username: '',
  password: ''
})



// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    loading.value = true
    
    // 实际登录请求
    try {
      console.log(loginForm);
      
      const result = await loginApi(loginForm)
      console.log(result);
      
      // 使用store管理用户状态
      userStore.setToken(result.data.access_token)
      userStore.setUserInfo(result.data.userInfo)
      
      // 登录成功后获取详细用户信息
      try {
        const userInfoResult = await userStore.fetchCommunityUserInfo()
        console.log('获取用户信息成功:', userInfoResult);
      } catch (userInfoError: any) {
        console.warn('获取用户信息失败:', userInfoError)
        // 不影响登录流程，只是警告
        ElMessage.warning('获取用户详情失败，但登录成功')
      }
      
      
      ElMessage.success('登录成功')
      // 跳转到大屏页面
      router.push('/bigscreen')
    } catch (apiError: any) {
      // API请求失败，错误信息已在响应拦截器中处理
      console.warn('登录失败:', apiError)
      return
    }
    
  } catch (error) {
    console.log('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}


</script>

<style scoped>
.login-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a1628 0%, #1e3a8a 50%, #0f172a 100%);
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(59, 130, 246, 0.6);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(odd) {
  animation-delay: -3s;
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0) rotate(0deg);
    opacity: 0;
  }
  50% { 
    transform: translateY(-100px) rotate(180deg);
    opacity: 1;
  }
}

/* 主要内容 */
.login-content {
  display: flex;
  width: 100%;
  height: 100%;
  max-width: 1400px;
  align-items: center;
  justify-content: center;
  gap: 100px;
  padding: 0 50px;
}

/* 左侧3D模型区域 */
.left-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.model-container {
  position: relative;
  width: 500px;
  height: 500px;
  perspective: 1000px;
}

.building-model {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  animation: rotate3d 20s linear infinite;
}

@keyframes rotate3d {
  0% { transform: rotateY(0deg) rotateX(10deg); }
  100% { transform: rotateY(360deg) rotateX(10deg); }
}

.building-base {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
}

.building-tower {
  position: absolute;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.tower-1 {
  width: 40px;
  height: 120px;
  left: 80px;
  top: 20px;
  animation: towerGlow 3s ease-in-out infinite;
}

.tower-2 {
  width: 35px;
  height: 100px;
  left: 130px;
  top: 40px;
  animation: towerGlow 3s ease-in-out infinite 0.5s;
}

.tower-3 {
  width: 45px;
  height: 140px;
  left: 30px;
  top: 0;
  animation: towerGlow 3s ease-in-out infinite 1s;
}

.tower-4 {
  width: 30px;
  height: 80px;
  left: 170px;
  top: 60px;
  animation: towerGlow 3s ease-in-out infinite 1.5s;
}

@keyframes towerGlow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
    transform: scale(1.05);
  }
}

.building-platform {
  position: absolute;
  top: 70%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.platform-ring {
  position: absolute;
  border: 2px solid rgba(59, 130, 246, 0.6);
  border-radius: 50%;
  transform-origin: center;
}

.ring-1 {
  width: 300px;
  height: 300px;
  left: -150px;
  top: -150px;
  animation: ringRotate 8s linear infinite;
}

.ring-2 {
  width: 250px;
  height: 250px;
  left: -125px;
  top: -125px;
  animation: ringRotate 6s linear infinite reverse;
}

.ring-3 {
  width: 200px;
  height: 200px;
  left: -100px;
  top: -100px;
  animation: ringRotate 10s linear infinite;
}

@keyframes ringRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 浮动立方体 */
.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.cube {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  transform-style: preserve-3d;
  animation: cubeFloat 4s ease-in-out infinite;
}

.cube-1 { top: 10%; left: 10%; animation-delay: 0s; }
.cube-2 { top: 20%; right: 15%; animation-delay: 0.5s; }
.cube-3 { bottom: 30%; left: 20%; animation-delay: 1s; }
.cube-4 { bottom: 20%; right: 10%; animation-delay: 1.5s; }
.cube-5 { top: 50%; left: 5%; animation-delay: 2s; }
.cube-6 { top: 30%; right: 5%; animation-delay: 2.5s; }

@keyframes cubeFloat {
  0%, 100% { 
    transform: translateY(0) rotateX(0deg) rotateY(0deg);
  }
  50% { 
    transform: translateY(-20px) rotateX(180deg) rotateY(180deg);
  }
}

/* 右侧登录区域 */
.right-section {
  flex: 0 0 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-form-container {
  width: 100%;
  max-width: 400px;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo-icon {
  color: #3b82f6;
  margin-bottom: 16px;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.login-form {
  width: 100%;
}

:deep(.login-input .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: none;
}

:deep(.login-input .el-input__wrapper:hover) {
  border-color: rgba(59, 130, 246, 0.4);
}

:deep(.login-input .el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

:deep(.login-input .el-input__inner) {
  color: #ffffff;
  background: transparent;
}

:deep(.login-input .el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.4);
}

:deep(.login-input .el-input__prefix-inner) {
  color: rgba(255, 255, 255, 0.6);
}

.login-button {
  width: 100%;
  height: 48px;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  margin-top: 20px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.login-button:hover {
  background: linear-gradient(45deg, #2563eb, #1e40af);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
}

/* 粒子动画随机位置 */
.particle:nth-child(1) { left: 10%; top: 20%; animation-duration: 8s; }
.particle:nth-child(2) { left: 20%; top: 80%; animation-duration: 6s; }
.particle:nth-child(3) { left: 30%; top: 40%; animation-duration: 7s; }
.particle:nth-child(4) { left: 40%; top: 70%; animation-duration: 9s; }
.particle:nth-child(5) { left: 50%; top: 10%; animation-duration: 5s; }
.particle:nth-child(6) { left: 60%; top: 60%; animation-duration: 8s; }
.particle:nth-child(7) { left: 70%; top: 30%; animation-duration: 6s; }
.particle:nth-child(8) { left: 80%; top: 90%; animation-duration: 7s; }
.particle:nth-child(9) { left: 90%; top: 50%; animation-duration: 9s; }
.particle:nth-child(10) { left: 5%; top: 95%; animation-duration: 5s; }
.particle:nth-child(11) { left: 15%; top: 5%; animation-duration: 8s; }
.particle:nth-child(12) { left: 25%; top: 75%; animation-duration: 6s; }
.particle:nth-child(13) { left: 35%; top: 25%; animation-duration: 7s; }
.particle:nth-child(14) { left: 45%; top: 85%; animation-duration: 9s; }
.particle:nth-child(15) { left: 55%; top: 15%; animation-duration: 5s; }
.particle:nth-child(16) { left: 65%; top: 65%; animation-duration: 8s; }
.particle:nth-child(17) { left: 75%; top: 35%; animation-duration: 6s; }
.particle:nth-child(18) { left: 85%; top: 75%; animation-duration: 7s; }
.particle:nth-child(19) { left: 95%; top: 25%; animation-duration: 9s; }
.particle:nth-child(20) { left: 12%; top: 55%; animation-duration: 5s; }
</style> 