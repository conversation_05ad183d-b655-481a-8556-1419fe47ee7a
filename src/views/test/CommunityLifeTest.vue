<template>
  <div class="community-life-test">
    <h2>社区生活面板测试</h2>
    
    <div class="test-container">
      <!-- 社区生活概况 -->
      <div class="test-section">
        <h3>社区圈子概况</h3>
        <CommunityLifeOverview 
          :overviewData="overviewData" 
          @resourceMap="handleResourceMap"
          @heatMap="handleHeatMap"
          @dataDetail="handleDataDetail"
        />
      </div>
      
      <!-- 社区圈子类型分布 -->
      <div class="test-section">
        <h3>社区圈子类型分布</h3>
        <EchartsCommunityLifeTypeBar 
          :chartData="typeData" 
          :loading="false"
        />
      </div>
      
      <!-- 社区圈子使用分布 -->
      <div class="test-section">
        <h3>社区圈子使用分布</h3>
        <EchartsCommunityLifeUsage 
          :chartData="usageData" 
          :loading="false"
        />
      </div>
      
      <!-- 社区圈子使用趋势 -->
      <div class="test-section">
        <h3>社区圈子使用趋势</h3>
        <EchartsCommunityLifeTrend 
          :chartData="trendData" 
          :loading="false"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import CommunityLifeOverview from '@/components/bigscreen/components/CommunityLifeOverview.vue'
import EchartsCommunityLifeTypeBar from '@/components/bigscreen/components/EchartsCommunityLifeTypeBar.vue'
import EchartsCommunityLifeUsage from '@/components/bigscreen/components/EchartsCommunityLifeUsage.vue'
import EchartsCommunityLifeTrend from '@/components/bigscreen/components/EchartsCommunityLifeTrend.vue'

// 测试数据
const overviewData = ref({
  totalCount: 23768,
  totalViews: 2376902
})

const typeData = ref([
  { name: '医学', value: 100 },
  { name: '数理', value: 100 },
  { name: '生物', value: 110 },
  { name: '地理', value: 90 },
  { name: '历史', value: 100 },
  { name: '艺术', value: 130 },
  { name: '文学', value: 115 },
  { name: '语言', value: 142 },
  { name: '宗教', value: 105 },
  { name: '哲学', value: 129 },
  { name: '社会科学', value: 108 }
])

const usageData = ref([
  { name: '文君街道', posts: 142, views: 142 },
  { name: '大同街道', posts: 138, views: 140 },
  { name: '临邛镇', posts: 135, views: 138 },
  { name: '羊安镇', posts: 132, views: 134 },
  { name: '牟礼镇', posts: 128, views: 130 },
  { name: '桑园镇', posts: 125, views: 128 },
  { name: '平乐镇', posts: 122, views: 125 },
  { name: '夹关镇', posts: 118, views: 120 }
])

const trendData = ref([
  { month: '1月', posts: 180, views: 180 },
  { month: '2月', posts: 200, views: 200 },
  { month: '3月', posts: 240, views: 220 },
  { month: '4月', posts: 220, views: 240 },
  { month: '5月', posts: 200, views: 220 },
  { month: '6月', posts: 180, views: 200 },
  { month: '7月', posts: 160, views: 180 },
  { month: '8月', posts: 140, views: 160 }
])

// 事件处理
const handleResourceMap = () => {
  ElMessage.info('资源地图分布功能')
}

const handleHeatMap = () => {
  ElMessage.info('辖区热力分布功能')
}

const handleDataDetail = () => {
  ElMessage.info('数据详情功能')
}
</script>

<style scoped>
.community-life-test {
  padding: 20px;
  background: #02162d;
  min-height: 100vh;
  color: #fff;
}

h2 {
  color: #00D4FF;
  text-align: center;
  margin-bottom: 30px;
}

h3 {
  color: #9D9DA6;
  margin-bottom: 15px;
  font-size: 16px;
}

.test-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.test-section {
  background: rgba(23, 81, 159, 0.1);
  border: 1px solid rgba(23, 81, 159, 0.3);
  border-radius: 8px;
  padding: 20px;
}
</style> 