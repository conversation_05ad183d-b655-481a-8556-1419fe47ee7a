<template>
  <div class="quick-test">
    <h2>快速热力图测试</h2>
    <div class="test-controls">
      <button @click="checkFunctions" class="btn primary">检查函数</button>
      <button @click="testHeatMap" class="btn success">测试热力图</button>
      <button @click="clearLog" class="btn secondary">清空日志</button>
    </div>
    
    <div class="log-container">
      <div v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
        {{ log.message }}
      </div>
    </div>
    
    <!-- 地图组件 -->
    <div class="map-wrapper">
      <MapComponent />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import MapComponent from '@/components/MapComponent.vue'

const logs = ref<Array<{ message: string, type: string }>>([])

const addLog = (message: string, type: string = 'info') => {
  logs.value.push({ message: `${new Date().toLocaleTimeString()}: ${message}`, type })
}

const clearLog = () => {
  logs.value = []
}

const checkFunctions = () => {
  addLog('开始检查函数状态...', 'info')
  
  const functions = [
    'addTotalHeatMap',
    'addHeatMapData',
    'clearHeatMap',
    'addResourceMapPoints',
    'mapComponentLoaded',
    'mapFunctionsPreloaded'
  ]
  
  functions.forEach(funcName => {
    const func = (window as any)[funcName]
    if (typeof func === 'function') {
      addLog(`✅ ${funcName}: 函数存在`, 'success')
    } else if (func === true) {
      addLog(`✅ ${funcName}: 标志为true`, 'success')
    } else {
      addLog(`❌ ${funcName}: ${typeof func} (${func})`, 'error')
    }
  })
}

const testHeatMap = () => {
  addLog('开始测试热力图...', 'info')
  
  if (typeof (window as any).addTotalHeatMap !== 'function') {
    addLog('❌ addTotalHeatMap函数不存在', 'error')
    return
  }
  
  const testData = [
    {
      districtName: '测试区域1',
      districtCode: '123456',
      longitude: '103.366654',
      latitude: '30.410004',
      total: 25
    },
    {
      districtName: '测试区域2',
      districtCode: '123457',
      longitude: '103.378597',
      latitude: '30.349588',
      total: 18
    }
  ]
  
  try {
    ;(window as any).addTotalHeatMap(testData)
    addLog('✅ 热力图测试成功', 'success')
  } catch (error: any) {
    addLog(`❌ 热力图测试失败: ${error.message}`, 'error')
  }
}

onMounted(() => {
  addLog('页面加载完成', 'info')
  
  // 延迟检查函数状态
  setTimeout(() => {
    checkFunctions()
  }, 2000)
})
</script>

<style scoped>
.quick-test {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

h2 {
  color: #333;
  margin-bottom: 20px;
}

.test-controls {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn.primary {
  background: #409eff;
  color: white;
}

.btn.success {
  background: #67c23a;
  color: white;
}

.btn.secondary {
  background: #909399;
  color: white;
}

.btn:hover {
  opacity: 0.8;
}

.log-container {
  height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
  padding: 10px;
  margin-bottom: 20px;
  background: #f9f9f9;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 4px;
  line-height: 1.4;
}

.log-item.success {
  color: #67c23a;
}

.log-item.error {
  color: #f56c6c;
}

.log-item.info {
  color: #409eff;
}

.map-wrapper {
  flex: 1;
  min-height: 400px;
  border: 1px solid #ddd;
}
</style> 