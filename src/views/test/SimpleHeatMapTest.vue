<template>
  <div class="simple-heat-test">
    <div class="test-header">
      <h2>社区导览热力分布测试</h2>
      <p>专门测试社区导览模块的辖区热力分布功能</p>
    </div>
    
    <div class="test-controls">
      <button @click="testRealAPI" class="test-btn primary">测试真实API</button>
      <button @click="testMockData" class="test-btn success">测试模拟数据</button>
      <button @click="clearHeatMap" class="test-btn secondary">清除热力图</button>
      <button @click="checkFunctions" class="test-btn info">检查函数状态</button>
    </div>
    
    <div class="test-results">
      <h3>测试结果：</h3>
      <div class="result-log">
        <div v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          <span class="timestamp">{{ log.time }}</span>
          <span class="message">{{ log.message }}</span>
        </div>
      </div>
    </div>
    
    <!-- 地图容器 -->
    <div class="map-container">
      <MapComponent />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import MapComponent from '@/components/MapComponent.vue'
import { getCommunityTourHeatMapApi } from '@/api/bigscreen'
import { getDistrictCode } from '@/utils/district'

const logs = ref<Array<{
  time: string
  message: string
  type: 'success' | 'error' | 'info' | 'warning'
}>>([])

const addLog = (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info') => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  logs.value.unshift({ time, message, type })
  console.log(`[${time}] ${message}`)
}

// 测试真实API
const testRealAPI = async () => {
  try {
    addLog('开始测试真实API...', 'info')
    
    // 检查函数是否存在
    if (!(window as any).addTotalHeatMap) {
      addLog('❌ addTotalHeatMap函数不存在', 'error')
      ElMessage.error('地图组件未加载完成')
      return
    }
    
    const areaCode = getDistrictCode()
    const timeType = '0' // 当天
    
    addLog(`请求参数: areaCode=${areaCode}, timeType=${timeType}, resourceType=2`, 'info')
    
    const response = await getCommunityTourHeatMapApi({
      areaCode,
      timeType,
      resourceType: 2
    }) as any
    
    addLog(`API响应: code=${response.code}, 数据条数=${response.data?.length || 0}`, 'info')
    
    if (response.code === 200) {
      if (!response.data || response.data.length === 0) {
        addLog('⚠️ API返回空数据', 'warning')
        ElMessage.warning('当前没有热力图数据')
        return
      }
      
      // 转换数据
      const convertedData = response.data.map((item: any) => ({
        districtName: item.districtName || '未知区域',
        districtCode: item.districtCode || '',
        longitude: item.longitude || '0',
        latitude: item.latitude || '0',
        total: item.count || item.total || 0
      }))
      
      addLog(`数据转换完成，有效数据: ${convertedData.length} 条`, 'success')
      
      // 调用热力图函数
      ;(window as any).addTotalHeatMap(convertedData)
      addLog(`✅ 热力图显示成功`, 'success')
      ElMessage.success(`热力图加载完成，共 ${convertedData.length} 个热力点`)
    } else {
      addLog(`❌ API调用失败: ${response.msg}`, 'error')
      ElMessage.error(`API调用失败: ${response.msg}`)
    }
  } catch (error: any) {
    addLog(`❌ 测试失败: ${error.message}`, 'error')
    ElMessage.error(`测试失败: ${error.message}`)
  }
}

// 测试模拟数据
const testMockData = () => {
  try {
    addLog('开始测试模拟数据...', 'info')
    
    if (!(window as any).addTotalHeatMap) {
      addLog('❌ addTotalHeatMap函数不存在', 'error')
      ElMessage.error('地图组件未加载完成')
      return
    }
    
    const mockData = [
      {
        districtName: '邛崃市中心',
        districtCode: '510183001',
        longitude: '103.366654',
        latitude: '30.410004',
        total: 25
      },
      {
        districtName: '临邛镇',
        districtCode: '510183002',
        longitude: '103.378597',
        latitude: '30.349588',
        total: 18
      },
      {
        districtName: '羊安镇',
        districtCode: '510183003',
        longitude: '103.348922',
        latitude: '30.361444',
        total: 32
      }
    ]
    
    addLog(`模拟数据: ${mockData.length} 条`, 'info')
    
    ;(window as any).addTotalHeatMap(mockData)
    addLog('✅ 模拟数据热力图显示成功', 'success')
    ElMessage.success(`模拟热力图加载完成，共 ${mockData.length} 个热力点`)
  } catch (error: any) {
    addLog(`❌ 模拟数据测试失败: ${error.message}`, 'error')
    ElMessage.error(`模拟数据测试失败: ${error.message}`)
  }
}

// 清除热力图
const clearHeatMap = () => {
  try {
    addLog('清除热力图...', 'info')
    
    if ((window as any).clearHeatMap) {
      ;(window as any).clearHeatMap()
      addLog('✅ 热力图清除成功', 'success')
      ElMessage.success('热力图已清除')
    } else {
      addLog('❌ clearHeatMap函数不存在', 'error')
      ElMessage.error('清除函数不存在')
    }
  } catch (error: any) {
    addLog(`❌ 清除失败: ${error.message}`, 'error')
    ElMessage.error(`清除失败: ${error.message}`)
  }
}

// 检查函数状态
const checkFunctions = () => {
  const functions = {
    addTotalHeatMap: typeof (window as any).addTotalHeatMap,
    clearHeatMap: typeof (window as any).clearHeatMap,
    mapComponentLoaded: (window as any).mapComponentLoaded,
    mapFunctionsPreloaded: (window as any).mapFunctionsPreloaded
  }
  
  addLog(`函数状态检查: ${JSON.stringify(functions)}`, 'info')
  
  Object.entries(functions).forEach(([name, type]) => {
    if (type === 'function') {
      addLog(`✅ ${name}: 可用`, 'success')
    } else {
      addLog(`❌ ${name}: 不可用 (${type})`, 'error')
    }
  })
}

onMounted(() => {
  addLog('测试页面已加载', 'info')
  
  // 延迟检查函数状态
  setTimeout(() => {
    checkFunctions()
  }, 2000)
})
</script>

<style scoped>
.simple-heat-test {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.test-header {
  margin-bottom: 20px;
}

.test-header h2 {
  color: #409eff;
  margin: 0 0 10px 0;
}

.test-controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.test-btn.primary {
  background: #409eff;
  color: white;
}

.test-btn.success {
  background: #67c23a;
  color: white;
}

.test-btn.secondary {
  background: #909399;
  color: white;
}

.test-btn.info {
  background: #909399;
  color: white;
}

.test-btn:hover {
  opacity: 0.8;
}

.test-results {
  margin: 20px 0;
  max-height: 200px;
  overflow-y: auto;
}

.result-log {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 10px;
  max-height: 150px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-size: 12px;
  line-height: 1.4;
}

.log-item.success {
  color: #67c23a;
}

.log-item.error {
  color: #f56c6c;
}

.log-item.warning {
  color: #e6a23c;
}

.log-item.info {
  color: #909399;
}

.timestamp {
  margin-right: 10px;
  font-weight: bold;
  min-width: 60px;
}

.message {
  flex: 1;
  word-break: break-all;
}

.map-container {
  flex: 1;
  min-height: 400px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}
</style>
