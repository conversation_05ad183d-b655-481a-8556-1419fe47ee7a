<template>
  <div class="map-load-test">
    <div class="test-header">
      <h2>地图组件加载测试</h2>
      <p>测试地图组件是否正确加载并绑定全局函数</p>
    </div>
    
    <div class="test-controls">
      <button @click="checkMapStatus" class="test-btn primary">检查地图状态</button>
      <button @click="testTotalHeatMap" class="test-btn success">测试Total热力图</button>
      <button @click="clearResults" class="test-btn secondary">清空结果</button>
    </div>
    
    <div class="test-results">
      <h3>测试结果:</h3>
      <div class="result-list">
        <div 
          v-for="(result, index) in testResults" 
          :key="index" 
          :class="['result-item', result.type]"
        >
          <span class="result-time">{{ result.time }}</span>
          <span class="result-message">{{ result.message }}</span>
        </div>
      </div>
    </div>
    
    <!-- 地图组件 -->
    <div class="map-container">
      <MapComponent />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import MapComponent from '@/components/MapComponent.vue'

// 测试结果
const testResults = ref<Array<{
  time: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
}>>([])

// 添加测试结果
const addTestResult = (message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  testResults.value.push({
    time,
    message,
    type
  })
  
  // 自动滚动到底部
  setTimeout(() => {
    const resultList = document.querySelector('.result-list')
    if (resultList) {
      resultList.scrollTop = resultList.scrollHeight
    }
  }, 100)
}

// 清空结果
const clearResults = () => {
  testResults.value = []
}

// 检查地图状态
const checkMapStatus = () => {
  addTestResult('开始检查地图组件状态...', 'info')
  
  // 检查地图组件加载标志
  const mapComponentLoaded = (window as any).mapComponentLoaded
  addTestResult(`地图组件加载标志: ${mapComponentLoaded}`, mapComponentLoaded ? 'success' : 'warning')
  
  // 检查各种全局函数
  const functions = [
    'addResourceMapPoints',
    'addHeatMapData', 
    'addTotalHeatMap',
    'clearHeatMap',
    'addCustomHeatMapData'
  ]
  
  functions.forEach(funcName => {
    const func = (window as any)[funcName]
    const exists = typeof func === 'function'
    addTestResult(`${funcName}: ${exists ? '✅ 存在' : '❌ 不存在'}`, exists ? 'success' : 'error')
  })
  
  // 检查window对象上所有包含Map的属性
  const mapRelatedKeys = Object.keys(window).filter(key => 
    key.toLowerCase().includes('map') || key.toLowerCase().includes('heat')
  )
  addTestResult(`Window对象上地图相关属性: ${mapRelatedKeys.join(', ')}`, 'info')
}

// 等待地图组件加载完成
const waitForMapComponent = (maxRetries = 10, retryDelay = 500): Promise<boolean> => {
  return new Promise((resolve) => {
    let retries = 0
    
    const checkMapComponent = () => {
      if ((window as any).mapComponentLoaded && (window as any).addTotalHeatMap) {
        addTestResult('✅ 地图组件已加载完成', 'success')
        resolve(true)
        return
      }
      
      retries++
      if (retries >= maxRetries) {
        addTestResult('⚠️ 地图组件加载超时', 'warning')
        resolve(false)
        return
      }
      
      addTestResult(`🔄 等待地图组件加载... (${retries}/${maxRetries})`, 'info')
      setTimeout(checkMapComponent, retryDelay)
    }
    
    checkMapComponent()
  })
}

// 测试Total热力图
const testTotalHeatMap = async () => {
  try {
    addTestResult('开始测试Total热力图功能...', 'info')
    
    // 等待地图组件加载
    const mapLoaded = await waitForMapComponent()
    if (!mapLoaded) {
      throw new Error('地图组件加载超时')
    }
    
    // 模拟数据
    const testData = [
      {
        districtName: '测试区域1',
        districtCode: '123456',
        longitude: '103.473283',
        latitude: '30.407823',
        total: 100
      },
      {
        districtName: '测试区域2', 
        districtCode: '123457',
        longitude: '103.523456',
        latitude: '30.457890',
        total: 50
      }
    ]
    
    // 调用Total热力图函数
    if ((window as any).addTotalHeatMap) {
      addTestResult('调用addTotalHeatMap函数...', 'info')
      ;(window as any).addTotalHeatMap(testData)
      addTestResult('Total热力图测试成功！', 'success')
      ElMessage.success('Total热力图测试成功！')
    } else {
      throw new Error('addTotalHeatMap函数不存在')
    }
    
  } catch (error: any) {
    const errorMsg = error.message || '测试失败'
    addTestResult(`测试失败: ${errorMsg}`, 'error')
    ElMessage.error(`Total热力图测试失败: ${errorMsg}`)
  }
}

// 组件挂载后自动检查状态
onMounted(() => {
  setTimeout(() => {
    addTestResult('页面加载完成，开始检查地图状态...', 'info')
    checkMapStatus()
  }, 1000)
})
</script>

<style scoped lang="scss">
.map-load-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  
  h2 {
    color: #333;
    margin-bottom: 10px;
  }
  
  p {
    color: #666;
    font-size: 14px;
  }
}

.test-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 30px;
  
  .test-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    
    &.primary {
      background: #409eff;
      color: white;
      
      &:hover {
        background: #337ecc;
      }
    }
    
    &.success {
      background: #67c23a;
      color: white;
      
      &:hover {
        background: #5daf34;
      }
    }
    
    &.secondary {
      background: #f56c6c;
      color: white;
      
      &:hover {
        background: #dd6161;
      }
    }
  }
}

.test-results {
  margin-bottom: 30px;
  
  h3 {
    color: #333;
    margin-bottom: 15px;
  }
  
  .result-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    background: #f9f9f9;
    
    .result-item {
      display: flex;
      margin-bottom: 8px;
      font-size: 14px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .result-time {
        color: #999;
        margin-right: 10px;
        min-width: 60px;
      }
      
      .result-message {
        flex: 1;
      }
      
      &.info .result-message {
        color: #333;
      }
      
      &.success .result-message {
        color: #67c23a;
        font-weight: bold;
      }
      
      &.warning .result-message {
        color: #e6a23c;
        font-weight: bold;
      }
      
      &.error .result-message {
        color: #f56c6c;
        font-weight: bold;
      }
    }
  }
}

.map-container {
  height: 400px;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}
</style>
