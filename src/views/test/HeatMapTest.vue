<template>
  <div class="heat-map-test">
    <div class="test-header">
      <h2>热力图功能测试</h2>
      <p>测试您的API数据在地图上的热力图显示效果</p>
    </div>
    
    <div class="test-controls">
      <div class="control-group">
        <label>测试数据类型：</label>
        <select v-model="selectedTestType" @change="handleTestTypeChange">
          <option value="api">真实API数据</option>
          <option value="mock">模拟数据</option>
        </select>
      </div>
      
      <div class="control-group">
        <button @click="testHeatMap" class="test-btn primary">显示热力图</button>
        <button @click="testTotalHeatMap" class="test-btn success">显示Total热力图</button>
        <button @click="clearHeatMap" class="test-btn secondary">清空热力图</button>
      </div>
    </div>
    
    <div class="test-data-preview">
      <h3>数据预览：</h3>
      <pre>{{ JSON.stringify(currentTestData, null, 2) }}</pre>
    </div>
    
    <div class="test-results">
      <h3>测试结果：</h3>
      <div v-for="(result, index) in testResults" :key="index" :class="['result-item', result.type]">
        <span class="timestamp">{{ result.timestamp }}</span>
        <span class="message">{{ result.message }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getCommunityTourHeatMapApi } from '@/api/bigscreen'
import { getDistrictCode } from '@/utils/district'

// 测试状态
const selectedTestType = ref('mock')
const currentTestData = ref<any>([])
const testResults = ref<Array<{
  timestamp: string
  message: string
  type: 'success' | 'error' | 'info'
}>>([])

// 模拟数据 - 使用您提供的数据格式
const mockData = [
  {
    districtName: '火车北站',
    districtCode: '510183002014',
    longitude: '103.473283',
    latitude: '30.407823',
    total: 172
  },
  {
    districtName: '大同镇',
    districtCode: '510183003',
    longitude: '103.523456',
    latitude: '30.457890',
    total: 64
  },
  {
    districtName: '安仁镇',
    districtCode: '510183004',
    longitude: '103.573789',
    latitude: '30.507123',
    total: 29
  },
  {
    districtName: '新津区',
    districtCode: '510183005',
    longitude: '103.623012',
    latitude: '30.556456',
    total: 85
  }
]

// 添加测试结果
const addTestResult = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  testResults.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // 限制结果数量
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10)
  }
}

// 处理测试类型变更
const handleTestTypeChange = () => {
  if (selectedTestType.value === 'mock') {
    currentTestData.value = mockData
    addTestResult('切换到模拟数据模式', 'info')
  } else {
    currentTestData.value = []
    addTestResult('切换到真实API数据模式', 'info')
  }
}

// 测试热力图功能
const testHeatMap = async () => {
  try {
    addTestResult('开始测试热力图功能...', 'info')

    // 首先等待地图组件加载完成
    addTestResult('等待地图组件加载完成...', 'info')
    const mapLoaded = await waitForMapComponent()
    if (!mapLoaded) {
      throw new Error('地图组件加载超时，请刷新页面重试')
    }
    addTestResult('地图组件加载完成！', 'success')

    let testData: any = []

    if (selectedTestType.value === 'mock') {
      // 使用模拟数据
      testData = mockData
      addTestResult(`使用模拟数据，共 ${testData.length} 个数据点`, 'info')
    } else {
      // 调用真实API
      addTestResult('调用真实API获取数据...', 'info')
      const areaCode = getDistrictCode()
      const response = await getCommunityTourHeatMapApi({
        areaCode,
        timeType: '2', // 近3月
        resourceType: 2 // 社区导览
      })

      if (response.code === 200) {
        testData = response.data
        addTestResult(`API调用成功，获得 ${testData.length} 个数据点`, 'success')
      } else {
        throw new Error(`API返回错误: ${response.msg}`)
      }
    }

    currentTestData.value = testData

    // 检查全局热力图函数
    if ((window as any).addCustomHeatMapData) {
      addTestResult('找到自定义热力图函数，开始显示热力图...', 'info')
      ;(window as any).addCustomHeatMapData(testData)
      addTestResult('热力图显示成功！', 'success')
      ElMessage.success('热力图测试成功！')
    } else if ((window as any).addHeatMapData) {
      addTestResult('找到通用热力图函数，开始显示热力图...', 'info')
      ;(window as any).addHeatMapData(testData)
      addTestResult('热力图显示成功！', 'success')
      ElMessage.success('热力图测试成功！')
    } else {
      throw new Error('未找到热力图函数，请确保地图组件已加载')
    }

  } catch (error: any) {
    const errorMsg = error.message || '测试失败'
    addTestResult(`测试失败: ${errorMsg}`, 'error')
    ElMessage.error(`热力图测试失败: ${errorMsg}`)
    console.error('热力图测试失败:', error)
  }
}

// 等待地图组件加载完成
const waitForMapComponent = (maxRetries = 10, retryDelay = 500): Promise<boolean> => {
  return new Promise((resolve) => {
    let retries = 0

    const checkMapComponent = () => {
      if ((window as any).mapComponentLoaded && (window as any).addTotalHeatMap) {
        console.log('✅ 地图组件已加载完成')
        resolve(true)
        return
      }

      retries++
      if (retries >= maxRetries) {
        console.warn('⚠️ 地图组件加载超时')
        resolve(false)
        return
      }

      console.log(`🔄 等待地图组件加载... (${retries}/${maxRetries})`)
      setTimeout(checkMapComponent, retryDelay)
    }

    checkMapComponent()
  })
}

// 测试Total热力图功能
const testTotalHeatMap = async () => {
  try {
    addTestResult('开始测试Total热力图功能...', 'info')

    // 首先等待地图组件加载完成
    addTestResult('等待地图组件加载完成...', 'info')
    const mapLoaded = await waitForMapComponent()
    if (!mapLoaded) {
      throw new Error('地图组件加载超时，请刷新页面重试')
    }
    addTestResult('地图组件加载完成！', 'success')

    let testData: any = []

    if (selectedTestType.value === 'mock') {
      // 使用模拟数据
      testData = mockData
      addTestResult(`使用模拟数据，共 ${testData.length} 个数据点`, 'info')
    } else {
      // 调用真实API
      addTestResult('调用真实API获取数据...', 'info')
      const areaCode = getDistrictCode()
      const response = await getCommunityTourHeatMapApi({
        areaCode,
        timeType: '2', // 近3月
        resourceType: 2 // 社区导览
      })

      if (response.code === 200) {
        testData = response.data
        addTestResult(`API调用成功，获得 ${testData.length} 个数据点`, 'success')
      } else {
        throw new Error(`API返回错误: ${response.msg}`)
      }
    }

    currentTestData.value = testData

    // 检查Total热力图函数
    if ((window as any).addTotalHeatMap) {
      addTestResult('找到Total热力图函数，开始显示热力图...', 'info')
      ;(window as any).addTotalHeatMap(testData)
      addTestResult('Total热力图显示成功！', 'success')
      ElMessage.success('Total热力图测试成功！')
    } else {
      throw new Error('未找到Total热力图函数，请确保地图组件已加载')
    }
    
  } catch (error: any) {
    const errorMsg = error.message || '测试失败'
    addTestResult(`测试失败: ${errorMsg}`, 'error')
    ElMessage.error(`热力图测试失败: ${errorMsg}`)
    console.error('热力图测试失败:', error)
  }
}

// 清空热力图
const clearHeatMap = () => {
  try {
    if ((window as any).clearHeatMap) {
      ;(window as any).clearHeatMap()
      addTestResult('热力图已清空', 'success')
      ElMessage.success('热力图已清空')
    } else {
      addTestResult('未找到清空热力图函数', 'error')
      ElMessage.error('未找到清空热力图函数')
    }
  } catch (error: any) {
    addTestResult(`清空热力图失败: ${error.message}`, 'error')
    ElMessage.error('清空热力图失败')
  }
}

// 组件挂载时初始化
onMounted(() => {
  handleTestTypeChange()
  addTestResult('热力图测试页面已加载', 'success')
})
</script>

<style lang="scss" scoped>
.heat-map-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  .test-header {
    text-align: center;
    margin-bottom: 30px;
    
    h2 {
      color: #333;
      margin-bottom: 10px;
    }
    
    p {
      color: #666;
      font-size: 14px;
    }
  }
  
  .test-controls {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 8px;
    
    .control-group {
      display: flex;
      align-items: center;
      gap: 10px;
      
      label {
        font-weight: bold;
        color: #333;
      }
      
      select {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
      }
    }
    
    .test-btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
      transition: all 0.3s;
      
      &.primary {
        background: #409eff;
        color: white;
        
        &:hover {
          background: #337ecc;
        }
      }
      
      &.secondary {
        background: #f56c6c;
        color: white;

        &:hover {
          background: #dd6161;
        }
      }

      &.success {
        background: #67c23a;
        color: white;

        &:hover {
          background: #5daf34;
        }
      }
    }
  }
  
  .test-data-preview {
    margin-bottom: 30px;
    
    h3 {
      color: #333;
      margin-bottom: 15px;
    }
    
    pre {
      background: #f8f8f8;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
      font-size: 12px;
      max-height: 300px;
      overflow-y: auto;
    }
  }
  
  .test-results {
    h3 {
      color: #333;
      margin-bottom: 15px;
    }
    
    .result-item {
      display: flex;
      gap: 15px;
      padding: 10px;
      margin-bottom: 5px;
      border-radius: 4px;
      font-size: 14px;
      
      .timestamp {
        font-weight: bold;
        min-width: 80px;
      }
      
      &.success {
        background: #f0f9ff;
        border-left: 4px solid #67c23a;
        color: #67c23a;
      }
      
      &.error {
        background: #fef0f0;
        border-left: 4px solid #f56c6c;
        color: #f56c6c;
      }
      
      &.info {
        background: #f4f4f5;
        border-left: 4px solid #909399;
        color: #909399;
      }
    }
  }
}
</style>
