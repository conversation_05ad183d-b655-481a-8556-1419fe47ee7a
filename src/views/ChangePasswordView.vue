<template>
  <div class="change-password-container">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="grid-lines"></div>
    </div>

    <!-- 主要内容 -->
    <div class="change-password-content">
      <div class="change-password-form-container">
        <!-- 标题 -->
        <div class="header">
          <div class="back-button" @click="goBack">
            <el-icon size="20"><ArrowLeft /></el-icon>
            <span>返回</span>
          </div>
          <h1 class="title">修改密码</h1>
        </div>

        <!-- 修改密码表单 -->
        <el-form
          :model="passwordForm"
          :rules="passwordRules"
          ref="passwordFormRef"
          class="password-form"
          label-width="100px"
          size="large"
        >
          <el-form-item label="原密码" prop="oldPassword">
            <el-input
              v-model="passwordForm.oldPassword"
              type="password"
              placeholder="请输入原密码"
              show-password
              class="password-input"
            >
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="passwordForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
              class="password-input"
            >
              <template #prefix>
                <el-icon><Key /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
              class="password-input"
            >
              <template #prefix>
                <el-icon><Key /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item>
            <div class="button-group">
              <el-button size="large" @click="resetForm">重置</el-button>
              <el-button
                type="primary"
                size="large"
                :loading="loading"
                @click="handleChangePassword"
                class="submit-button"
              >
                确认修改
              </el-button>
            </div>
          </el-form-item>
        </el-form>

        <!-- 密码安全提示 -->
        <div class="security-tips">
          <h3>密码安全提示</h3>
          <ul>
            <li>密码长度至少6位，建议8位以上</li>
            <li>建议包含大小写字母、数字和特殊字符</li>
            <li>不要使用过于简单的密码，如生日、姓名等</li>
            <li>定期更换密码，确保账号安全</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Lock, Key } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { changePasswordApi } from '@/api/auth'

const router = useRouter()
const passwordFormRef = ref<FormInstance>()
const loading = ref(false)

// 密码修改表单数据
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const passwordRules: FormRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value === passwordForm.oldPassword) {
          callback(new Error('新密码不能与原密码相同'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 重置表单
const resetForm = () => {
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  passwordFormRef.value?.clearValidate()
}

// 修改密码
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    
    // 确认操作
    await ElMessageBox.confirm(
      '确定要修改密码吗？修改后需要重新登录。',
      '确认修改',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value = true

    try {
      await changePasswordApi({
        oldPassword: passwordForm.oldPassword,
        newPassword: passwordForm.newPassword
      })

      ElMessage.success('密码修改成功，请重新登录')
      
      // 清除本地存储的用户信息
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      
      // 跳转到登录页面
      setTimeout(() => {
        router.push('/login')
      }, 1500)

    } catch (error: any) {
      // 错误已在拦截器中处理
      console.warn('密码修改失败:', error)
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.log('表单验证失败:', error)
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.change-password-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a1628 0%, #1e3a8a 50%, #0f172a 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* 主要内容 */
.change-password-content {
  width: 100%;
  max-width: 600px;
  padding: 20px;
}

.change-password-form-container {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.header {
  position: relative;
  text-align: center;
  margin-bottom: 40px;
}

.back-button {
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #3b82f6;
  cursor: pointer;
  font-size: 16px;
  transition: color 0.3s ease;
}

.back-button:hover {
  color: #2563eb;
}

.title {
  font-size: 28px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.password-form {
  margin-bottom: 30px;
}

:deep(.password-input .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: none;
}

:deep(.password-input .el-input__wrapper:hover) {
  border-color: rgba(59, 130, 246, 0.4);
}

:deep(.password-input .el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

:deep(.password-input .el-input__inner) {
  color: #ffffff;
  background: transparent;
}

:deep(.password-input .el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.4);
}

:deep(.password-input .el-input__prefix-inner) {
  color: rgba(255, 255, 255, 0.6);
}

:deep(.password-form .el-form-item__label) {
  color: #ffffff;
  font-weight: 500;
}

.button-group {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 30px;
}

.button-group .el-button {
  min-width: 120px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

.submit-button {
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.submit-button:hover {
  background: linear-gradient(45deg, #2563eb, #1e40af);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
}

/* 安全提示 */
.security-tips {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  padding: 20px;
}

.security-tips h3 {
  color: #3b82f6;
  font-size: 16px;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.security-tips ul {
  margin: 0;
  padding-left: 20px;
  color: rgba(255, 255, 255, 0.8);
}

.security-tips li {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.security-tips li:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .change-password-content {
    padding: 10px;
  }
  
  .change-password-form-container {
    padding: 30px 20px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .button-group {
    flex-direction: column;
    align-items: center;
  }
  
  .button-group .el-button {
    width: 100%;
    max-width: 300px;
  }
}
</style> 