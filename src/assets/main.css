@import './base.css';

/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 确保设备列表弹窗始终在最顶层 */
.device-list-modal {
  z-index: 999999 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
}

/* 确保所有Element Plus弹窗组件在最顶层 */
.el-overlay,
.el-overlay-dialog,
.el-dialog__wrapper,
.el-dialog,
.el-popper,
.el-select-dropdown,
.el-tooltip__popper {
  z-index: 999999 !important;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }


}
