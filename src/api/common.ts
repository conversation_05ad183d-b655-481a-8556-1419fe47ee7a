import request from './request'
import type { ResultData, PageResult } from './request'

// 分页查询参数
export interface PageParams {
  page: number
  pageSize: number
  keyword?: string
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

// 文件上传结果
export interface UploadResult {
  url: string
  filename: string
  size: number
  type: string
}

// 字典数据
export interface DictData {
  label: string
  value: string | number
  disabled?: boolean
  children?: DictData[]
}

/**
 * @name 通用模块
 */
// 文件上传
export const uploadFileApi = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  return request.post<UploadResult>('/common/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 批量文件上传
export const uploadFilesApi = (files: File[]) => {
  const formData = new FormData()
  files.forEach(file => {
    formData.append('files', file)
  })
  return request.post<UploadResult[]>('/common/upload/batch', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 文件下载
export const downloadFileApi = (url: string, filename?: string) => {
  return request.download('/common/download', { url, filename })
}

// 获取字典数据
export const getDictDataApi = (dictType: string) => {
  return request.get<DictData[]>('/common/dict', { dictType })
}

// 获取系统配置
export const getSystemConfigApi = () => {
  return request.get<Record<string, any>>('/common/config')
}

// 发送短信验证码
export const sendSmsCodeApi = (phone: string, type: 'login' | 'register' | 'reset') => {
  return request.post('/common/sms/send', { phone, type })
}

// 验证短信验证码
export const verifySmsCodeApi = (phone: string, code: string, type: string) => {
  return request.post<boolean>('/common/sms/verify', { phone, code, type })
}

// 获取省市区数据
export const getRegionDataApi = (parentId?: string) => {
  return request.get<Array<{
    id: string
    name: string
    code: string
    level: number
    parentId: string
    hasChildren: boolean
  }>>('/common/region', { parentId })
}

// 通用搜索接口
export const searchApi = <T = any>(params: {
  keyword: string
  type?: string
  filters?: Record<string, any>
} & Partial<PageParams>) => {
  return request.post<PageResult<T>>('/common/search', params)
} 