# API 请求接口使用指南

## 📁 文件结构

```
src/api/
├── request.ts      # HTTP请求工具类
├── auth.ts         # 认证相关接口
├── bigscreen.ts    # 大屏数据接口
├── common.ts       # 通用接口
├── index.ts        # 统一导出
└── README.md       # 使用说明
```

## 🚀 快速开始

### 1. 基础用法

```typescript
import { loginApi, getUserInfoApi } from '@/api'

// 登录
const result = await loginApi({
  account: '<EMAIL>',
  password: '123456'
})

// 获取用户信息
const userInfo = await getUserInfoApi()
```

### 2. 类型安全

所有接口都提供了完整的TypeScript类型定义：

```typescript
import type { LoginData, LoginResult } from '@/api'

const loginData: LoginData = {
  account: 'admin',
  password: '123456'
}

const result: ResultData<LoginResult> = await loginApi(loginData)
```

## 🔧 核心功能

### HTTP请求工具类 (request.ts)

提供了完整的请求拦截器、响应拦截器和错误处理：

```typescript
import request from '@/api/request'

// GET请求
const data = await request.get<UserInfo>('/api/user/info')

// POST请求
const result = await request.post('/api/user/create', userData)

// 文件下载
const blob = await request.download('/api/file/download', { fileId: '123' })
```

### 认证模块 (auth.ts)

```typescript
import { 
  loginApi, 
  logoutApi, 
  getUserInfoApi, 
  refreshTokenApi,
  changePasswordApi 
} from '@/api'

// 登录
await loginApi({ account: 'admin', password: '123456' })

// 登出
await logoutApi()

// 获取用户信息
const userInfo = await getUserInfoApi()

// 刷新Token
await refreshTokenApi()

// 修改密码
await changePasswordApi({
  oldPassword: '123456',
  newPassword: 'newPassword'
})
```

### 大屏数据模块 (bigscreen.ts)

```typescript
import { 
  getPopulationDataApi,
  getAgeDistributionApi,
  getSatisfactionDataApi,
  getActivityDataApi,
  getKeyIndicatorsApi 
} from '@/api'

// 获取人口统计数据
const population = await getPopulationDataApi()

// 获取年龄分布数据
const ageDistribution = await getAgeDistributionApi()

// 获取满意度数据
const satisfaction = await getSatisfactionDataApi()

// 获取活动数据（支持时间范围筛选）
const activities = await getActivityDataApi({
  startDate: '2024-01-01',
  endDate: '2024-12-31'
})
```

### 通用模块 (common.ts)

```typescript
import { 
  uploadFileApi,
  downloadFileApi,
  getDictDataApi,
  sendSmsCodeApi,
  searchApi 
} from '@/api'

// 文件上传
const uploadResult = await uploadFileApi(file)

// 获取字典数据
const dictData = await getDictDataApi('user_status')

// 发送短信验证码
await sendSmsCodeApi('13800138000', 'login')

// 通用搜索
const searchResult = await searchApi({
  keyword: '搜索关键词',
  page: 1,
  pageSize: 10
})
```

## ⚙️ 配置说明

### 1. 基础配置

在 `request.ts` 中可以配置：

```typescript
const config = {
  baseURL: 'https://api.example.com', // API基础地址
  timeout: 60000,                     // 请求超时时间
  withCredentials: true               // 跨域携带凭证
}
```

### 2. 请求拦截器

自动添加认证头：

```typescript
config.headers['x-access-token'] = localStorage.getItem('token')
```

### 3. 响应拦截器

- ✅ 自动处理成功响应
- ❌ 统一错误处理和提示
- 🔄 自动Token刷新（401错误）
- 📱 网络状态检测

## 🛠️ 实际使用示例

### 在Vue组件中使用

```vue
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getPopulationDataApi, loginApi } from '@/api'
import type { PopulationData, LoginData } from '@/api'

const population = ref<PopulationData>()
const loading = ref(false)

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const result = await getPopulationDataApi()
    population.value = result.data
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 登录
const handleLogin = async (formData: LoginData) => {
  try {
    const result = await loginApi(formData)
    localStorage.setItem('token', result.data.token)
    // 跳转到主页
  } catch (error) {
    // 错误已在拦截器中处理
  }
}

onMounted(() => {
  fetchData()
})
</script>
```

### 错误处理

```typescript
try {
  const result = await loginApi(loginData)
  // 处理成功逻辑
} catch (error: any) {
  // 错误已在响应拦截器中统一处理
  // 这里可以添加特殊的错误处理逻辑
  if (error.code === 'CUSTOM_ERROR') {
    // 自定义错误处理
  }
}
```

## 🌟 最佳实践

### 1. 使用类型定义

```typescript
// ✅ 推荐：使用类型定义
const result: ResultData<UserInfo> = await getUserInfoApi()

// ❌ 不推荐：不使用类型
const result = await getUserInfoApi()
```

### 2. 错误处理

```typescript
// ✅ 推荐：使用try-catch处理特殊错误
try {
  await loginApi(formData)
} catch (error) {
  // 特殊错误处理
}

// ✅ 推荐：一般情况下依赖拦截器处理
await getUserInfoApi() // 错误会自动显示消息
```

### 3. 加载状态

```typescript
// ✅ 推荐：管理加载状态
const loading = ref(false)

const fetchData = async () => {
  loading.value = true
  try {
    await getDataApi()
  } finally {
    loading.value = false
  }
}
```

## 🔍 调试技巧

1. **开发环境调试**：打开浏览器开发者工具的Network标签页查看请求
2. **Token检查**：在Application -> Local Storage中查看token
3. **错误日志**：在Console中查看详细错误信息

## 📝 环境变量配置

创建 `.env.development` 和 `.env.production` 文件：

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:3000/api

# .env.production  
VITE_API_BASE_URL=https://api.production.com
```

然后在配置中使用：

```typescript
const config = {
  baseURL: import.meta.env.VITE_API_BASE_URL,
  // ...
}
``` 