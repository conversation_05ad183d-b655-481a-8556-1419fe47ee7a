import axios from 'axios'
import type { AxiosInstance, InternalAxiosRequestConfig } from 'axios'
import { ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth'

// 崃建言采纳统计参数
export interface SuggestionAdoptParams {
  timeType: string  // 统计查询时间类型 0: 当天; 1: 近周; 2: 近3月; 3: 近6月; 4: 近1年
  areaCode: string  // 社区code
}

// 崃建言概览数据结构
export interface SuggestionOverviewData {
  receivedCount: number    // 已接收数量
  feedbackCount: number    // 已反馈数量  
  unFeedbackCount: number  // 未反馈数量
  convertCount: number     // 已转化数量
}

// 崃建言概览响应数据（经过响应拦截器处理后直接是data对象）
export type SuggestionOverviewResponse = SuggestionOverviewData

// 崃建言采纳统计数据项
export interface SuggestionAdoptItem {
  adoptionStatusLabel: string  // 采纳状态标签
  total: number               // 该状态的数量
}

// 崃建言采纳统计响应数据（经过响应拦截器处理后直接是数组）
export type SuggestionAdoptResponse = SuggestionAdoptItem[]

// 崃建言处理情况查询参数
export interface SuggestionListParams {
  areaCode: string   // 区域代码
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: number   // 时间类型
}

// 区域信息
export interface District {
  districtId: string
  code: string
  name: string
  longitude: string
  latitude: string
  level: number
  subjectType: string | null
  pid: string
  headChar: string
  hotFlag: string
  children: any[] | null
  useCount: number | null
  lifeCircleUseCount: number | null
  communityLineUseCount: number | null
}

// 崃建言处理情况列表项
export interface SuggestionListItem {
  feedbackCount: number    // 反馈数量
  receivedCount: number    // 接收数量
  district: District       // 区域信息
}

// 崃建言处理情况响应数据
export interface SuggestionListResponse {
  code: number
  msg: string
  data: any
  message: {
    list: SuggestionListItem[]
  }
}

// 社区游线概览查询参数
export interface TourInfoParams {
  areaCode: string   // 区域代码
  timeType: number   // 时间类型
}

// 社区游线概览响应数据
export interface TourInfoResponse {
  code: number
  msg: string
  data: any
  message: {
    goodsCount: number  // 商品数量（游线数量）
    viewCount: number   // 浏览数量
  }
}

// 社区游线列表查询参数
export interface TourListParams {
  areaCode: string   // 区域代码
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: string   // 时间类型
}

// 社区游线列表项
export interface TourListItem {
  goodsCount: number  // 游线数量
  viewCount?: number  // 浏览数量（可选，因为有些项目没有这个字段）
  district: District  // 区域信息
}

// 社区游线列表响应数据
export interface TourListResponse {
  code: number
  msg: string
  data: any
  message: {
    list: TourListItem[]
  }
}

// 热门游线查询参数
export interface TourHotsParams {
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: string   // 时间类型
  areaCode: string   // 区域代码
}

// 热门游线项
export interface TourHotItem {
  tourLineId: string         // 游线ID
  tourLineName: string       // 游线名称
  districtCode: string       // 区域代码
  districtAddress: string | null  // 区域地址
  travelDays: number | null      // 旅游天数
  tourLineIntroduction: string | null  // 游线介绍
  browseNumber: number       // 浏览数量
  createTime: string | null      // 创建时间
  updateTime: string | null      // 更新时间
  districtName: string       // 区域名称
}

// 热门游线响应数据
export interface TourHotsResponse {
  code: number
  msg: string
  data: any
  message: {
    list: {
      total: number
      rows: TourHotItem[]
      code: number
      msg: string
    }
  }
}

// 创建专门用于崃建言接口的axios实例
const suggestionRequest: AxiosInstance = axios.create({
  // baseURL: 'https://qlzhsq.qlzhsq.cn:30200/prod-api', //dev
  baseURL: 'https://qlzhsq.qlzhsq.cn:30400/prod-api', //uat
  timeout: 60000,
  withCredentials: true
})

// 建言接口的请求拦截器
suggestionRequest.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getToken()
    // console.log('🔍 崃建言API请求拦截器:')
    // console.log('  - 请求URL:', (config.baseURL || '') + (config.url || ''))
    // console.log('  - 获取到的token:', token ? token.substring(0, 20) + '...' : '无token')
    
    if (token) {
      config.headers = config.headers || {}
      config.headers['Authorization'] = `${token}`
      config.headers['clientId'] = `980a7458cd984785d19206ce24a7bksa`
     // console.log('✅ 已添加崃建言API的Authorization header')
    } else {
      // console.log('❌ 未添加Authorization header - 没有token')
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 建言接口的响应拦截器
suggestionRequest.interceptors.response.use(
  (response) => {
    const { data } = response
    console.log('📡 崃建言API响应:', data)
    
    // 检查响应格式
    if (data.code && data.code !== 200) {
      ElMessage.error(data.message || data.msg || '请求失败')
      return Promise.reject(data)
    }
    
    return data
  },
  (error) => {
    console.error('❌ 崃建言API请求失败:', error)
    
    if (error.message.indexOf('timeout') !== -1) {
      ElMessage.error('崃建言接口请求超时')
    } else if (error.message.indexOf('Network Error') !== -1) {
      ElMessage.error('崃建言接口网络错误')
    } else if (error.response) {
      const status = error.response.status
      switch (status) {
        case 401:
          ElMessage.error('崃建言接口：未授权访问')
          break
        case 403:
          ElMessage.error('崃建言接口：禁止访问')
          break
        case 404:
          ElMessage.error('崃建言接口：接口不存在')
          break
        case 500:
          ElMessage.error('崃建言接口：服务器错误')
          break
        default:
          ElMessage.error(`崃建言接口错误：${status}`)
      }
    } else {
      ElMessage.error('崃建言接口请求失败')
    }
    
    return Promise.reject(error)
  }
)

// 获取崃建言概览统计
export const getSuggestionOverviewApi = (params: SuggestionAdoptParams) => {
  console.log('🔄 调用崃建言概览API:', params)
  return suggestionRequest.post<SuggestionOverviewResponse>('/cockpit/statistics/suggestions/overview', params)
}

// 获取崃建言采纳统计
export const getSuggestionAdoptApi = (params: SuggestionAdoptParams) => {
  console.log('🔄 调用崃建言采纳统计API:', params)
  return suggestionRequest.post<SuggestionAdoptResponse>('/cockpit/statistics/suggestions/adopt', params)
}

// 获取崃建言处理情况列表
export const getSuggestionListApi = (params: SuggestionListParams): Promise<SuggestionListResponse> => {
  console.log('🔄 调用崃建言处理情况列表API:', params)
  return suggestionRequest.post('/cockpit/statistics/suggestionList', params)
}

// 获取社区游线概览
export const getTourInfoApi = (params: TourInfoParams): Promise<TourInfoResponse> => {
  console.log('🔄 调用社区游线概览API:', params)
  return suggestionRequest.post('/cockpit/statistics/tourInfo', params)
}

// 获取社区游线列表
export const getTourListApi = (params: TourListParams): Promise<TourListResponse> => {
  console.log('🔄 调用社区游线列表API:', params)
  return suggestionRequest.post('/cockpit/statistics/tourList', params)
}

// 获取热门游线列表
export const getTourHotsApi = (params: TourHotsParams): Promise<TourHotsResponse> => {
  console.log('🔄 调用热门游线列表API:', params)
  return suggestionRequest.post('/cockpit/statistics/tourHots', params)
}

// 社区好物热浏览查询参数
export interface StoreHotsParams {
  areaCode?: string  // 区域代码（可选）
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: string   // 时间类型
  type?: number      // 类型（可选，热门贴时为0）
}

// 社区好物热浏览项
export interface StoreHotItem {
  id: string                    // 好物ID
  name: string                  // 好物名称
  coverImages: string | null    // 封面图片
  brief: string | null          // 简介
  details: string | null        // 详情
  contactPhone: string | null   // 联系电话
  address: string | null        // 地址
  longitude: string | null      // 经度
  latitude: string | null       // 纬度
  districtCode: string          // 区域代码
  districtName: string          // 区域名称
  browseNumber: number          // 浏览数量
  sort: number | null           // 排序
  status: string | null         // 状态
  updateTime: string | null     // 更新时间
  createTime: string | null     // 创建时间
  applyStatus: string           // 申请状态
  remark: string | null         // 备注
  userId: string | null         // 用户ID
  price: number | null          // 价格
}

// 社区好物热浏览响应数据
export interface StoreHotsResponse {
  code: number
  msg: string
  data: any
  message: {
    list: {
      total: number
      rows: StoreHotItem[]
      code: number
      msg: string
    }
  }
}

// 社区好物概览接口参数
export interface StoreInfoParams {
  areaCode: string // 区域编码
  timeType: number // 时间类型
}

// 社区好物概览接口响应
export interface StoreInfoResponse {
  code: number
  msg: string
  data: null
  message: {
    goodsCount: number // 商品数量
    viewCount: number  // 浏览次数
  }
}

// 获取社区好物概览
export const getStoreInfoApi = (params: StoreInfoParams): Promise<StoreInfoResponse> => {
  console.log('🔄 调用社区好物概览API:', params)
  return suggestionRequest.post('/cockpit/statistics/storeInfo', params)
}

// 社区好物分布查询参数
export interface StoreListParams {
  areaCode: string   // 区域代码
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: string   // 时间类型
}

// 社区好物分布项
export interface StoreListItem {
  goodsCount: number     // 好物数量
  viewCount?: number     // 浏览数量（可选）
  district: District     // 区域信息
}

// 社区好物分布响应数据
export interface StoreListResponse {
  code: number
  msg: string
  data: any
  message: {
    list: StoreListItem[]
  }
}

// 获取社区好物热浏览概况
export const getStoreHotsApi = (params: StoreHotsParams): Promise<StoreHotsResponse> => {
  console.log('🔄 调用社区好物热浏览概况API:', params)
  return suggestionRequest.post('/cockpit/statistics/storeHots', params)
}

// 获取社区好物浏览及使用人数分布
export const getStoreListApi = (params: StoreListParams): Promise<StoreListResponse> => {
  console.log('🔄 调用社区好物分布API:', params)
  return suggestionRequest.post('/cockpit/statistics/storeList', params)
}

// 共享物品概览查询参数
export interface MarketInfoParams {
  areaCode: string   // 区域代码
  timeType: number   // 时间类型
  type: number       // 类型（共享物品为1）
}

// 共享物品概览响应数据
export interface MarketInfoResponse {
  code: number
  msg: string
  data: any
  message: {
    goodsCount: number  // 商品数量
    viewCount: number   // 浏览数量
  }
}

// 获取社区好物热门贴
export const getStoreHotPostsApi = (params: StoreHotsParams): Promise<StoreHotsResponse> => {
  console.log('🔄 调用社区好物热门贴API:', params)
  return suggestionRequest.post('/cockpit/statistics/storeHots', params)
}

// 共享热浏览概况查询参数
export interface MarketHotsParams {
  areaCode: string   // 区域代码
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: string   // 时间类型
  type: number       // 类型（共享物品为1）
}

// 共享热浏览概况响应数据（可以复用StoreHotsResponse的结构）
export type MarketHotsResponse = StoreHotsResponse

// 获取共享物品概览
export const getMarketInfoApi = (params: MarketInfoParams): Promise<MarketInfoResponse> => {
  console.log('🔄 调用共享物品概览API:', params)
  return suggestionRequest.post('/cockpit/statistics/marketInfo', params)
}

// 共享物品分布查询参数
export interface MarketListParams {
  areaCode: string   // 区域代码
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: string   // 时间类型
  type: number       // 类型（共享物品为1）
}

// 共享物品分布响应数据（可以复用StoreListResponse的结构）
export type MarketListResponse = StoreListResponse

// 获取共享热浏览概况
export const getMarketHotsApi = (params: MarketHotsParams): Promise<MarketHotsResponse> => {
  console.log('🔄 调用共享热浏览概况API:', params)
  return suggestionRequest.post('/cockpit/statistics/marketHots', params)
}

// 共享物品热门贴查询参数
export interface MarketHotPostsParams {
  areaCode: string   // 区域代码
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: string   // 时间类型
  type: number       // 类型（共享物品为1）
}

// 共享物品热门贴响应数据（复用MarketHotsResponse）
export type MarketHotPostsResponse = MarketHotsResponse

// 获取共享物品分布
export const getMarketListApi = (params: MarketListParams): Promise<MarketListResponse> => {
  console.log('🔄 调用共享物品分布API:', params)
  return suggestionRequest.post('/cockpit/statistics/marketList', params)
}

// 二手闲置概览查询参数
export interface SecondhandInfoParams {
  areaCode: string   // 区域代码
  timeType: string   // 时间类型
  type: number       // 类型（二手闲置为0）
}

// 二手闲置概览响应数据（复用MarketInfoResponse）
export type SecondhandInfoResponse = MarketInfoResponse

// 获取共享物品热门贴
export const getMarketHotPostsApi = (params: MarketHotPostsParams): Promise<MarketHotPostsResponse> => {
  console.log('🔄 调用共享物品热门贴API:', params)
  return suggestionRequest.post('/cockpit/statistics/marketHots', params)
}

// 二手闲置热浏览概况查询参数
export interface SecondhandHotsParams {
  areaCode: string   // 区域代码
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: string   // 时间类型
  type: number       // 类型（二手闲置为0）
}

// 二手闲置热浏览概况响应数据（复用MarketHotsResponse）
export type SecondhandHotsResponse = MarketHotsResponse

// 获取二手闲置概览
export const getSecondhandInfoApi = (params: SecondhandInfoParams): Promise<SecondhandInfoResponse> => {
  console.log('🔄 调用二手闲置概览API:', params)
  return suggestionRequest.post('/cockpit/statistics/marketInfo', params)
}

// 二手闲置分布查询参数
export interface SecondhandListParams {
  areaCode: string   // 区域代码
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: string   // 时间类型
  type: number       // 类型（二手闲置为0）
}

// 二手闲置分布响应数据（复用MarketListResponse）
export type SecondhandListResponse = MarketListResponse

// 获取二手闲置热浏览概况
export const getSecondhandHotsApi = (params: SecondhandHotsParams): Promise<SecondhandHotsResponse> => {
  console.log('🔄 调用二手闲置热浏览概况API:', params)
  return suggestionRequest.post('/cockpit/statistics/marketHots', params)
}

// 二手闲置热门贴查询参数
export interface SecondhandHotPostsParams {
  areaCode: string   // 区域代码
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: string   // 时间类型
  type: number       // 类型（二手闲置为0）
}

// 二手闲置热门贴响应数据（复用SecondhandHotsResponse）
export type SecondhandHotPostsResponse = SecondhandHotsResponse

// 获取二手闲置分布
export const getSecondhandListApi = (params: SecondhandListParams): Promise<SecondhandListResponse> => {
  console.log('🔄 调用二手闲置分布API:', params)
  return suggestionRequest.post('/cockpit/statistics/marketList', params)
}

// 获取二手闲置热门贴
export const getSecondhandHotPostsApi = (params: SecondhandHotPostsParams): Promise<SecondhandHotPostsResponse> => {
  console.log('🔄 调用二手闲置热门贴API:', params)
  return suggestionRequest.post('/cockpit/statistics/marketHots', params)
}

// 获取日期范围函数
export const getDateRange = (timeType: string) => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  
  let startDate: Date
  let endDate: Date = new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1)
  
  switch (timeType) {
    case '0': // 当天
      startDate = new Date(today)
      break
    case '1': // 近周
      startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case '2': // 近3月
      startDate = new Date(today.getFullYear(), today.getMonth() - 3, today.getDate())
      break
    case '3': // 近6月
      startDate = new Date(today.getFullYear(), today.getMonth() - 6, today.getDate())
      break
    case '4': // 近1年
      startDate = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate())
      break
    default:
      startDate = new Date(today)
  }
  
  return {
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0]
  }
}

// 获取区域代码函数
export const getAreaCode = () => {
  try {
    // 从 localStorage 中获取 communityUserInfo
    const communityUserInfoStr = localStorage.getItem('communityUserInfo')
    if (communityUserInfoStr) {
      const communityUserInfo = JSON.parse(communityUserInfoStr)
      const districtCode = communityUserInfo?.districtCode
      console.log('从 communityUserInfo 获取 districtCode:', districtCode)
      return districtCode || '510100'  // 如果没有 districtCode 则使用默认值
    } else {
      console.log('localStorage 中没有 communityUserInfo，使用默认区域代码')
      return '510100'
    }
  } catch (error) {
    console.error('获取区域代码失败:', error)
    return '510100'  // 发生错误时使用默认值
  }
} 

// 社区活动概览查询参数
export interface ActivityInfoParams {
  areaCode: string   // 区域代码
  timeType: string   // 时间类型
}

// 社区活动概览响应数据
export interface ActivityInfoResponse {
  code: number
  msg: string
  data: any
  message: {
    ended: number      // 已结束活动数量
    total: number      // 总活动数量
    ongoing: number    // 进行中活动数量
  }
}

// 获取社区活动概览
export const getActivityInfoApi = (params: ActivityInfoParams): Promise<ActivityInfoResponse> => {
  console.log('🔄 调用社区活动概览API:', params)
  return suggestionRequest.post('/cockpit/statistics/activityInfo', params)
}

// 社区活动热浏览概况查询参数
export interface ActivityHotsParams {
  areaCode: string   // 区域代码
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: string   // 时间类型
}

// 社区活动热浏览项
export interface ActivityHotItem {
  activityId: string         // 活动ID
  title: string              // 活动标题
  districtCode: string       // 区域代码
  districtName: string       // 区域名称
  userCount: number          // 参与人数
  signUp: number             // 报名人数
  maxParticipants?: number   // 最大参与人数
  activityStartTime?: string // 活动开始时间
  activityEndTime?: string   // 活动结束时间
  location?: string          // 活动地点
  status?: string            // 活动状态
}

// 社区活动热浏览概况响应数据
export interface ActivityHotsResponse {
  code: number
  msg: string
  data: any
  message: {
    list: {
      total: number
      rows: ActivityHotItem[]
      code: number
      msg: string
    }
  }
}

// 获取社区活动热浏览概况
export const getActivityHotsApi = (params: ActivityHotsParams): Promise<ActivityHotsResponse> => {
  console.log('🔄 调用社区活动热浏览概况API:', params)
  return suggestionRequest.post('/cockpit/statistics/activityHots', params)
}

// 社区活动分布查询参数
export interface ActivityListParams {
  areaCode: string   // 区域代码
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: string   // 时间类型
}

// 社区活动分布项
export interface ActivityListItem {
  total: number           // 活动总数
  ongoing: number         // 进行中活动数
  ended: number           // 已结束活动数
  participants: number    // 参与人数
  district: District      // 区域信息
}

// 社区活动分布响应数据
export interface ActivityListResponse {
  code: number
  msg: string
  data: any
  message: {
    list: ActivityListItem[]
  }
}

// 获取社区活动分布
export const getActivityListApi = (params: ActivityListParams): Promise<ActivityListResponse> => {
  console.log('🔄 调用社区活动分布API:', params)
  return suggestionRequest.post('/cockpit/statistics/activityList', params)
}

// 热门社区活动表格查询参数
export interface ActivityHotTableParams {
  areaCode: string   // 区域代码
  pageNum: number    // 页码
  pageSize: number   // 每页数量
  timeType: string   // 时间类型
}

// 热门社区活动表格响应数据（复用ActivityHotsResponse）
export type ActivityHotTableResponse = ActivityHotsResponse

// 获取热门社区活动表格数据
export const getActivityHotTableApi = (params: ActivityHotTableParams): Promise<ActivityHotTableResponse> => {
  console.log('🔄 调用热门社区活动表格API:', params)
  return suggestionRequest.post('/cockpit/statistics/activityHots', params)
}

// 社区空间概览查询参数
export interface SpaceInfoParams {
  areaCode: string   // 区域代码
  timeType: string   // 时间类型
}

// 社区空间概览响应数据
export interface SpaceInfoResponse {
  code: number
  msg: string
  data: any
  message: {
    goodsCount: number  // 社区空间数量
    viewCount: number   // 总浏览量
  }
}

// 获取社区空间概览
export const getSpaceInfoApi = (params: SpaceInfoParams): Promise<SpaceInfoResponse> => {
  console.log('🔄 调用社区空间概览API:', params)
  return suggestionRequest.post('/cockpit/statistics/spaceInfo', params)
}

/**
 * 社区空间热浏览概况
 */
export interface SpaceHotItem {
  id: string
  name: string
  coverImages?: string
  brief?: string
  details?: string
  contactPhone?: string
  address?: string
  longitude?: number
  latitude?: number
  districtCode: string
  districtName: string
  browseNumber: number
  sort?: number
  status?: string
  updateTime?: string
  createTime?: string
}

export interface SpaceHotResponse {
  total: number
  rows: SpaceHotItem[]
  code: number
  msg: string
}

export interface SpaceHotParams {
  areaCode: string
  pageNum: number
  pageSize: number
  timeType: string
}

/**
 * 获取社区空间热浏览概况
 */
export function getSpaceHotList(params: SpaceHotParams) {
  console.log('🔄 调用社区空间热浏览概况API:', params)
  return suggestionRequest.post('/cockpit/statistics/spaceHots', params)
}

/**
 * 社区空间浏览及使用人数分布
 */
export interface SpaceListParams {
  areaCode: string
  pageNum: number
  pageSize: number
  timeType: string
}

export interface SpaceListItem {
  goodsCount: number      // 社区空间数量
  viewCount?: number      // 浏览量（可选，有些区域可能没有）
  district: District      // 区域信息
}

export interface SpaceListResponse {
  code: number
  msg: string
  data: any
  message: {
    list: SpaceListItem[]
  }
}

/**
 * 获取社区空间浏览及使用人数分布
 */
export function getSpaceListApi(params: SpaceListParams): Promise<SpaceListResponse> {
  console.log('🔄 调用社区空间浏览及使用人数分布API:', params)
  return suggestionRequest.post('/cockpit/statistics/spaceList', params)
}

/**
 * 获取热门社区空间表格数据
 */
export function getSpaceHotTableApi(params: SpaceHotParams) {
  console.log('🔄 调用热门社区空间表格API:', params)
  return suggestionRequest.post('/cockpit/statistics/spaceHots', params)
}

/**
 * 社区匠人概览
 */
export interface CraftsmanInfoParams {
  areaCode: string   // 区域代码
  timeType: string   // 时间类型
}

export interface CraftsmanInfoResponse {
  code: number
  msg: string
  data: any
  message: {
    goodsCount: number  // 匠人数量
    viewCount: number   // 总浏览量
  }
}

/**
 * 获取社区匠人概览
 */
export function getCraftsmanInfoApi(params: CraftsmanInfoParams): Promise<CraftsmanInfoResponse> {
  console.log('🔄 调用社区匠人概览API:', params)
  return suggestionRequest.post('/cockpit/statistics/craftsmanInfo', params)
}

/**
 * 社区匠人热浏览概况
 */
export interface CraftsmanHotParams {
  areaCode: string
  pageNum: number
  pageSize: number
  timeType: string
}

export interface CraftsmanHotItem {
  id: string
  name: string
  coverImages?: string
  brief?: string
  details?: string
  contactPhone?: string
  address?: string
  longitude?: number
  latitude?: number
  districtCode: string
  districtName: string
  browseNumber: number
  sort?: number
  status?: string
  updateTime?: string
  createTime?: string
}

export interface CraftsmanHotResponse {
  code: number
  msg: string
  data: any
  message: {
    list: {
      total: number
      rows: CraftsmanHotItem[]
      code: number
      msg: string
    }
  }
}

/**
 * 获取社区匠人热浏览概况
 */
export function getCraftsmanHotListApi(params: CraftsmanHotParams): Promise<CraftsmanHotResponse> {
  console.log('🔄 调用社区匠人热浏览概况API:', params)
  return suggestionRequest.post('/cockpit/statistics/craftsmanHots', params)
}

// 社区匠人分布参数
export interface CraftsmanDistributionParams {
  timeType: string
  pageNum: number
  pageSize: number
  areaCode: string
}

// 社区匠人分布响应数据
export interface CraftsmanDistributionItem {
  goodsCount: number
  district: {
    districtId: string
    code: string
    name: string
    longitude: string
    latitude: string
    level: number
    subjectType: null
    pid: string
    headChar: string
    hotFlag: string
    children: null
    useCount: null
    lifeCircleUseCount: null
    communityLineUseCount: null
  }
  viewCount?: number
}

export interface CraftsmanDistributionResponse {
  code: number
  msg: string
  data: null
  message: {
    list: CraftsmanDistributionItem[]
  }
}

/**
 * 获取社区匠人浏览及关注人数分布
 */
export function getCraftsmanDistributionApi(params: CraftsmanDistributionParams): Promise<CraftsmanDistributionResponse> {
  console.log('🔄 调用社区匠人分布API:', params)
  return suggestionRequest.post('/cockpit/statistics/craftsmanList', params)
}

// 协商铃社区响应概况查询参数
export interface BellInfoParams {
  areaCode: string   // 区域代码
  timeType: number   // 时间类型
}

// 协商铃社区响应概况响应数据
export interface BellInfoResponse {
  code: number
  msg: string
  data: any
  message: {
    callCount: number    // 呼叫数量
    serviceCount: number // 服务数量
  }
}

// 获取协商铃社区响应概况
export const getBellInfoApi = (params: BellInfoParams): Promise<BellInfoResponse> => {
  console.log('🔄 调用协商铃社区响应概况API:', params)
  return suggestionRequest.post('/cockpit/statistics/bellInfo', params)
}

// 协商铃处理情况查询参数
export interface BellListParams {
  areaCode: string   // 区域代码
  timeType: number   // 时间类型
}

// 协商铃处理情况列表项
export interface BellListItem {
  serviceCount?: number  // 服务数量（可选，有些区域可能没有）
  callCount: number      // 来电数量
  district: District     // 区域信息
}

// 协商铃处理情况响应数据
export interface BellListResponse {
  code: number
  msg: string
  data: any
  message: {
    list: BellListItem[]
  }
}

// 获取协商铃处理情况列表
export const getBellListApi = (params: BellListParams): Promise<BellListResponse> => {
  console.log('🔄 调用协商铃处理情况API:', params)
  return suggestionRequest.post('/cockpit/statistics/bellList', params)
}

// AI智能客服热门问题查询参数
export interface AiHotsParams {
  areaCode: string   // 区域代码
  timeType: number   // 时间类型
}

// AI智能客服热门问题列表项
export interface AiHotItem {
  count: number      // 问题次数
  content: string    // 问题内容
}

// AI智能客服热门问题响应数据
export interface AiHotsResponse {
  code: number
  msg: string
  data: any
  message: {
    list: AiHotItem[]
  }
}

// 获取AI智能客服热门问题
export const getAiHotsApi = (params: AiHotsParams): Promise<AiHotsResponse> => {
  console.log('🔄 调用AI智能客服热门问题API:', params)
  return suggestionRequest.post('/cockpit/statistics/aiHots', params)
}

// 社区来电时段概况查询参数
export interface BellCallLineParams {
  areaCode: string   // 区域代码
  timeType: number   // 时间类型
}

// 社区来电时段概况列表项
export interface BellCallLineItem {
  todayServiceCount: number     // 今日服务数量
  hour: number                  // 小时
  yesterdayServiceCount: number // 昨日服务数量
  yesterdayCallCount: number    // 昨日来电数量
  todayCallCount: number        // 今日来电数量
}

// 社区来电时段概况响应数据
export interface BellCallLineResponse {
  code: number
  msg: string
  data: any
  message: {
    list: BellCallLineItem[]
  }
}

// 获取社区来电时段概况
export const getBellCallLineApi = (params: BellCallLineParams): Promise<BellCallLineResponse> => {
  console.log('🔄 调用社区来电时段概况API:', params)
  return suggestionRequest.post('/cockpit/statistics/bellCallLine', params)
}

// 微互助概况查询参数
export interface MutualAidInfoParams {
  areaCode: string   // 区域代码
  timeType: number   // 时间类型
}

// 微互助概况数据
export interface MutualAidInfoData {
  serviceOngoing: number  // 进行中的服务
  serviceEnded: number    // 已结束的服务
  demandEnded: number     // 已结束的需求
  demandTotal: number     // 总需求数
  demandOngoing: number   // 进行中的需求
  serviceTotal: number    // 总服务数
}

// 微互助概况响应数据
export interface MutualAidInfoResponse {
  code: number
  msg: string
  data: any
  message: MutualAidInfoData
}

// 获取微互助概况
export const getMutualAidInfoApi = (params: MutualAidInfoParams): Promise<MutualAidInfoResponse> => {
  console.log('🔄 调用微互助概况API:', params)
  return suggestionRequest.post('/cockpit/statistics/mutualAidInfo', params)
}

// 微互助达成情况分布查询参数
export interface MutualAidListParams {
  areaCode: string   // 区域代码
  timeType: number   // 时间类型
  type?: string      // 微互助类型：'0' 微心愿，'1' 微服务
}

// 微互助达成情况分布列表项
export interface MutualAidListItem {
  total: number          // 总数
  ongoing: number        // 进行中
  ended: number          // 已结束
  participants: number   // 参与人数
  district: {
    districtId: string
    code: string
    name: string
    longitude: string
    latitude: string
    level: number
    subjectType: any
    pid: string
    headChar: string
    hotFlag: string
    children: any
    useCount: any
    lifeCircleUseCount: any
    communityLineUseCount: any
  }
}

// 微互助达成情况分布响应数据
export interface MutualAidListResponse {
  code: number
  msg: string
  data: any
  message: {
    list: MutualAidListItem[]
  }
}

// 获取微互助达成情况分布
export const getMutualAidListApi = (params: MutualAidListParams): Promise<MutualAidListResponse> => {
  console.log('🔄 调用微互助达成情况分布API:', params)
  return suggestionRequest.post('/cockpit/statistics/mutualAidList', params)
}

// 微互助服务人数查询参数
export interface MutualAidServiceListParams {
  areaCode: string   // 区域代码
  timeType: number   // 时间类型
}

// 微互助服务人数列表项
export interface MutualAidServiceListItem {
  wishParticipants: number      // 微心愿参与人数
  serviceParticipants: number   // 微服务参与人数
  district: {
    districtId: string
    code: string
    name: string
    longitude: string
    latitude: string
    level: number
    subjectType: any
    pid: string
    headChar: string
    hotFlag: string
    children: any
    useCount: any
    lifeCircleUseCount: any
    communityLineUseCount: any
  }
}

// 微互助服务人数响应数据
export interface MutualAidServiceListResponse {
  code: number
  msg: string
  data: any
  message: {
    list: MutualAidServiceListItem[]
  }
}

// 获取微互助服务人数
export const getMutualAidServiceListApi = (params: MutualAidServiceListParams): Promise<MutualAidServiceListResponse> => {
  console.log('🔄 调用微互助服务人数API:', params)
  return suggestionRequest.post('/cockpit/statistics/mutualAidServiceList', params)
}

// 微心愿/微服务热力图查询参数
export interface MutualAidHotsParams {
  areaCode: string   // 区域代码
  timeType: number   // 时间类型
  pageNum: number    // 页码
  pageSize: number   // 每页数量
}

// 微心愿/微服务热力图列表项
export interface MutualAidHotsItem {
  voiceText: any
  videoId: any
  videoUrl: any
  id: string
  wishType: any
  title: string
  content: any
  voiceId: any
  voiceUrl: any
  attachments: any
  imageOssVos: any
  location: any
  registrationDeadline: any
  activityStartTime: any
  activityEndTime: any
  contactName: any
  contactPhone: any
  registrationCount: any
  participate: any
  reviewStatus: any
  districtCode: string
  districtName: string
  userId: any
  userName: any
  createTime: any
  status: any
  signUp: number
  handlerUserId: any
  handlerTime: any
  handlerUserName: any
  locationInfo: any
  userCount: number
}

// 微心愿/微服务热力图响应数据
export interface MutualAidHotsResponse {
  code: number
  msg: string
  data: any
  message: {
    list: {
      total: number
      rows: MutualAidHotsItem[]
      code: number
      msg: string
    }
  }
}

// 获取微心愿/微服务热力图
export const getMutualAidHotsApi = (params: MutualAidHotsParams): Promise<MutualAidHotsResponse> => {
  console.log('🔄 调用微心愿/微服务热力图API:', params)
  return suggestionRequest.post('/cockpit/statistics/mutualAidHots', params)
}