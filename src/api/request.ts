import axios from 'axios'
import type { 
  AxiosInstance, 
  AxiosRequestConfig, 
  AxiosResponse, 
  InternalAxiosRequestConfig 
} from 'axios'
import { ElMessage } from 'element-plus'
import { getToken, isTokenValid, clearAuthData } from '@/utils/auth'

// 请求响应参数（不含data）
export interface Result {
  code: number
  message: string
}

// 请求响应参数（包含data）
export interface ResultData<T = any> extends Result {
  data: T
}

// 分页响应参数
export interface PageResult<T = any> {
  code: number
  message: string
  data: {
    list: T[]
    total: number
    page: number
    pageSize: number
  }
}

// 从环境变量获取API地址，如果没有则使用默认值
// const URL = import.meta.env.VITE_API_BASE_URL || 'https://qlzhsq.qlzhsq.cn:30200/prod-api' // 开发
// const URL = import.meta.env.VITE_API_BASE_URL || 'https://qlzhsq.qlzhsq.cn:8130/prod-api' //演示
const URL = import.meta.env.VITE_API_BASE_URL || 'https://qlzhsq.qlzhsq.cn:30400/prod-api' //正式

enum RequestEnums {
  TIMEOUT = 60000,
  OVERDUE = 600, // 登录失效
  FAIL = 999, // 请求失败
  SUCCESS = 200, // 请求成功
}

const config = {
  // 默认地址请求地址，可在 .env.** 文件中修改
  baseURL: URL as string,
  // 设置超时时间
  timeout: RequestEnums.TIMEOUT as number,
  // 跨域时候允许携带凭证
  withCredentials: true
}

class RequestHttp {
  service: AxiosInstance
  public constructor(config: AxiosRequestConfig) {
    // 实例化axios
    this.service = axios.create(config)

    /**
     * @description 请求拦截器
     * 客户端发送请求 -> [请求拦截器] -> 服务器
     * token校验(JWT) : 接受服务器返回的 token,存储到 vuex/pinia/本地储存当中
     */
    this.service.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        // 获取token（暂时跳过验证）
        const token = getToken()
        // console.log('🔍 请求拦截器调试信息:')
        // console.log('  - 请求URL:', config.url)
        // console.log('  - 获取到的token:', token ? token.substring(0, 20) + '...' : '无token')
        // console.log('  - token长度:', token ? token.length : 0)
        
        // 暂时跳过token验证，只要有token就添加
        if (token) {
          config.headers = config.headers || {}
          // 使用Bearer token格式（JWT标准）
          config.headers['Authorization'] = `${token}`
          config.headers['clientId'] = `980a7458cd984785d19206ce24a7bksa`
          // console.log('✅ 已添加Authorization header')
          // console.log('  - Authorization:', `Bearer ${token.substring(0, 20)}...`)
          // console.log('  - 完整headers:', Object.keys(config.headers))
        } else {
          console.log('❌ 未添加Authorization header - 没有token')
        }
        return config
      },
      (error: any) => {
        // 请求报错
        Promise.reject(error)
      }
    )

    /**
     * @description 响应拦截器
     *  服务器换返回信息 -> [拦截统一处理] -> 客户端JS获取到信息
     */
    this.service.interceptors.response.use(
      (response: AxiosResponse) => {
        const { data } = response
        const { code, msg } = data

        // 特殊处理401认证失败
        if (data.code === 401) {
          ElMessage.error(msg || '认证失败，无法访问系统资源')
          // 清除所有认证数据
          clearAuthData()
          // 跳转登录页面
          window.location.hash = '/login'
          return Promise.reject(data)
        }

        // 全局错误信息拦截（防止下载文件的时候返回数据流，没有 code 直接报错）
        if (data.code && data.code !== RequestEnums.SUCCESS) {
          ElMessage.error(msg)
          return Promise.reject(data)
        }

        // 成功请求（在页面上除非特殊情况，否则不用处理失败逻辑）
        return data
      },
      (error: any) => {
        const { response } = error
        // 请求超时 && 网络错误单独判断，没有 response
        if (error.message.indexOf('timeout') !== -1) {
          ElMessage.error('请求超时！请您稍后重试')
        }
        if (error.message.indexOf('Network Error') !== -1) {
          ElMessage.error('网络错误！请您稍后重试')
        }

        // 根据服务器响应的错误状态码，做不同的处理
        if (response) {
          console.log(response,'response')
          this.handleCode(response.code)
        }

        if (!window.navigator.onLine) {
          ElMessage.error('网络连接失败')
          // 可以跳转到错误页面，也可以不做操作
          // return router.replace({ path: '/500' })
        }
        return Promise.reject(error)
      }
    )
  }

  /**
   * @description 常用请求方法封装
   */
  get<T>(url: string, params?: object, _object = {}): Promise<ResultData<T>> {
    return this.service.get(url, { params, ..._object })
  }

  // 新增：直接传递查询参数的GET方法（用于解决params[key]=value格式问题）
  getWithDirectParams<T>(url: string, params?: object, _object = {}): Promise<ResultData<T>> {
    // 构建查询字符串
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      const queryString = searchParams.toString()
      if (queryString) {
        url = url + (url.includes('?') ? '&' : '?') + queryString
      }
    }
    return this.service.get(url, _object)
  }
  post<T>(url: string, params?: object | string, _object = {}): Promise<ResultData<T>> {
    return this.service.post(url, params, _object)
  }
  put<T>(url: string, params?: object, _object = {}): Promise<ResultData<T>> {
    return this.service.put(url, params, _object)
  }
  delete<T>(url: string, params?: any, _object = {}): Promise<ResultData<T>> {
    return this.service.delete(url, { params, ..._object })
  }
  download(url: string, params?: object, _object = {}): Promise<BlobPart> {
    return this.service.post(url, params, { ..._object, responseType: 'blob' })
  }

  /**
   * @description 处理请求状态码
   */
  private handleCode(code: number): void {
    switch (code) {
      case 401:
        ElMessage.error('登录失效！请您重新登录')
        // 清除所有认证数据
        clearAuthData()
        // 跳转登录页面
        window.location.hash = '/login'
        break
      case 403:
        ElMessage.error('当前账号无权限访问！')
        break
      case 404:
        ElMessage.error('你所访问的资源不存在！')
        break
      case 405:
        ElMessage.error('请求方式错误！请您稍后重试')
        break
      case 408:
        ElMessage.error('请求超时！请您稍后重试')
        break
      case 500:
        ElMessage.error('服务异常！')
        break
      case 502:
        ElMessage.error('网关错误！')
        break
      case 503:
        ElMessage.error('服务不可用！')
        break
      case 504:
        ElMessage.error('网关超时！')
        break
      default:
        ElMessage.error('请求失败！')
    }
  }
}

export default new RequestHttp(config) 