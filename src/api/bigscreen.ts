import request from './request'
import type { ResultData } from './request'
import axios from 'axios'

// 人口统计数据
export interface PopulationData {
  male: number
  female: number
  total: number
}

// 年龄分布数据
export interface AgeDistributionData {
  age0_18: number
  age19_35: number
  age36_50: number
  age51_65: number
  age65_plus: number
}

// 满意度数据
export interface SatisfactionData {
  value: number
  level: string
}

// 活动参与数据
export interface ActivityData {
  month: string
  count: number
}

// 关键指标数据
export interface KeyIndicatorsData {
  totalPopulation: number
  satisfactionRate: number
  monthlyActivities: number
  facilityUtilization: number
}

// 社区地图数据
export interface CommunityMapData {
  communities: {
    id: string
    name: string
    latitude: number
    longitude: number
    population: number
    status: 'normal' | 'warning' | 'danger'
  }[]
}

/**
 * @name 大屏数据模块
 */
// 获取人口统计数据
export const getPopulationDataApi = () => {
  return request.get<PopulationData>('/bigscreen/population')
}

// 获取年龄分布数据
export const getAgeDistributionApi = () => {
  return request.get<AgeDistributionData>('/bigscreen/age-distribution')
}

// 获取满意度数据
export const getSatisfactionDataApi = () => {
  return request.get<SatisfactionData>('/bigscreen/satisfaction')
}

// 获取活动参与数据
export const getActivityDataApi = (params?: { startDate?: string; endDate?: string }) => {
  return request.get<ActivityData[]>('/bigscreen/activity', params)
}

// 获取关键指标数据
export const getKeyIndicatorsApi = () => {
  return request.get<KeyIndicatorsData>('/bigscreen/key-indicators')
}

// 获取社区地图数据
export const getCommunityMapDataApi = () => {
  return request.get<CommunityMapData>('/bigscreen/community-map')
}

// 获取实时数据（WebSocket或轮询）
export const getRealTimeDataApi = () => {
  return request.get<{
    population: PopulationData
    satisfaction: SatisfactionData
    keyIndicators: KeyIndicatorsData
    timestamp: string
  }>('/bigscreen/realtime')
}

// 物联设备运行统计数据接口
export interface DeviceTypeRunStatisticsParams {
  areaCode: string
}

export interface DeviceTypeRunStatisticsResponse {
  Total: number // 智能设备总数量
  Online: number // 设备在线数量
  LastMonthOnlineGrowthRate: number // 在线上月同比
  ByArea: Array<{
    Name: string // 区域名称
    Count: number // 设备数量
  }>
  DeviceTypeList: Array<{
    DeviceType: number // 设备类型编号
    DeviceTypeName: string // 设备类型名称
    Online: number // 在线数量
    Offline: number // 离线数量
  }>
}

// 获取物联设备运行统计数据
export const getDeviceTypeRunStatisticsApi = (params: DeviceTypeRunStatisticsParams) => {
  // 由于该API使用不同的服务器地址，创建独立的axios实例
  return axios.get<DeviceTypeRunStatisticsResponse>(
    'https://qlzhsq.qlzhsq.cn:8131/api/DCEventRYB/DeviceTypeRunStatistics',
    { 
      params,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  ).then((response: any) => response.data)
}

// 设备预警数据接口
export interface DeviceDCEParams {
  areaCode: string
  timeStart: string // 开始时间，格式为 YYYY-M-D
}

export interface DeviceDCEResponse {
  Statistics: {
    Total: number // 今日预警总数
    Complete: number // 已处理数量
    UnComplete: number // 待处理数量
  }
  List: Array<{
    DCEId: number // 预警ID
    TotalEventName: string // 预警类型
    ParkName: string // 预警小区
    EventDate: string // 预警时间
    CompleteState: string // 状态
  }>
  UnCompleteGroup: Array<{
    Name: string // 区域名称
    Count: number // 待处理数量
  }>
}

// 获取设备预警数据
export const getDeviceDCEApi = (params: DeviceDCEParams) => {
  return axios.get<DeviceDCEResponse>(
    'https://qlzhsq.qlzhsq.cn:8131/api/DCEventRYB/DCEOverView',
    { 
      params,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  ).then((response: any) => response.data)
}

// 设备预警统计数据接口（风险预警点位排名和类型排名）
export interface DCEStatisticsParams {
  areaCode: string
}

export interface DCEStatisticsResponse {
  Top3Device: Array<{
    DeviceName: string // 设备名称
    Count: number // 预警数量
  }>
  ByType: Array<{
    TotalEventName: string // 预警类型名称
    Count: number // 预警数量
  }>
}

// 获取设备预警统计数据
export const getDCEStatisticsApi = (params: DCEStatisticsParams) => {
  return axios.get<DCEStatisticsResponse>(
    'https://qlzhsq.qlzhsq.cn:8131/api/DCEventRYB/DCEGroupRanking',
    { 
      params,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  ).then((response: any) => response.data)
}

// 设备列表相关接口
export interface DCDeviceListParams {
  AreaCode: string
  DeviceId?: string
  Rows?: string
  Page?: string
  DateRangeMin?: string
  DateRangeMax?: string
  ParkId?: number | null
}

export interface DCDeviceListResponse {
  state: number
  message: {
    PageTotal: number
    Total: number
    Rows: Array<{
      RowNumber: number
      DcetId: string
      TargetName: string
      TotalEventType: number
      TotalEventName: string
      Photo: string
      Video: string | null
      BottomImageSource: string | null
      BottomImage: string | null
      Name: string
      EventDate: string
      AssignPerson: string | null
      IsRead: number
      MessageState: number
    }>
  }
}

export interface ParkListParams {
  areaCode: string
}

export interface ParkListItem {
  ParkId: number
  Name: string
}

export interface ParkListResponse {
  state: number
  message: ParkListItem[]
}

// 获取设备类型统计
export interface GetDeviceTypeStatisticsParams {
  areaCode: string
  deviceType?: number | null // deviceType可以为空
}

export interface GetDeviceTypeStatisticsResponse {
  state: number
  message: {
    Count: number
    List: Array<{
      Id: number
      DeviceSN: string
      DeviceName: string
      Position: string
      AreaName: string
      DeviceState: number
    }>
  }
}

export const getDeviceTypeStatisticsApi = (params: GetDeviceTypeStatisticsParams) => {
  // 构建查询参数，如果deviceType为空则不传递
  const queryParams: any = {
    areaCode: params.areaCode
  }

  // 只有当deviceType有值时才添加到参数中
  if (params.deviceType !== null && params.deviceType !== undefined) {
    queryParams.deviceType = params.deviceType
  }

  return axios.get<GetDeviceTypeStatisticsResponse>(
    'https://qlzhsq.qlzhsq.cn:8131/api/DCEventRYB/GetDeviceTypeStatistics',
    {
      params: queryParams,
      timeout: 10000,
      headers: {
        'Accept': 'application/json, text/plain, */*'
      }
    }
  ).then((response: any) => response.data)
}

// 获取设备列表
export const getDCDeviceListApi = (params: DCDeviceListParams) => {
  return axios.post<DCDeviceListResponse>(
    'https://qlzhsq.qlzhsq.cn:8131/api/DCEventRYB/DCEList',
    params,
    {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*'
      }
    }
  ).then((response: any) => response.data)
}

// 获取小区列表
export const getParkListApi = (params: ParkListParams) => {
  return axios.get<ParkListResponse>(
    'https://qlzhsq.qlzhsq.cn:8131/api/DCEventRYB/GetParkList',
    {
      params,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*'
      }
    }
  ).then((response: any) => response.data)
}

// 获取辖区热力分布数据
export const getSpecialStatisticsByAreaApi = (params: SpecialStatisticsByAreaParams) => {
  return axios.post<SpecialStatisticsByAreaResponse>(
    'https://qlzhsq.qlzhsq.cn:8131/api/DCEventRYB/SpecialStatictiscByArea',
    params,
    {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*'
      }
    }
  )
}

// 家庭医生统计接口参数
export interface FamilyDoctorParams {
  areaCode: string
}

// 家庭医生统计接口
export interface FamilyDoctorDistrictItem {
  districtId: string
  code: string
  name: string
  longitude: string
  latitude: string
  level: number
  subjectType: null
  pid: string
  headChar: string
  hotFlag: string
  children: null
  useCount: number
  lifeCircleUseCount: null
  communityLineUseCount: null
}

export interface FamilyDoctorResponse {
  code: number
  msg: string
  data: null
  message: {
    districtList: FamilyDoctorDistrictItem[]
  }
}

// 获取家庭医生统计
export function getFamilyDoctorApi(params: FamilyDoctorParams): Promise<FamilyDoctorResponse> {
  return request.post('/cockpit/statistics/familyDoctor', params) as unknown as Promise<FamilyDoctorResponse>
}

// 实用工具数据接口
export interface PracticalToolsParams {
  areaCode: string
  timeType: number // 时间类型：0-今日，1-近一月，2-近一年
}

export interface PracticalToolsFileTypeItem {
  dictCode: string
  dictSort: number
  dictLabel: string
  dictValue: string
  dictType: string
  cssClass: string
  listClass: string
  isDefault: string
  remark: string
  createTime: string
  useCount: number
  appletMenus: null
}

export interface PracticalToolsDistrictItem {
  districtId: string
  code: string
  name: string
  longitude: string
  latitude: string
  level: number
  subjectType: null
  pid: string
  headChar: string
  hotFlag: string
  children: null
  useCount: number
  lifeCircleUseCount: null
  communityLineUseCount: null
}

export interface PracticalToolsResponse {
  code: number
  msg: string
  data: {
    watermarkCameraPublicCount: number
    districtList: PracticalToolsDistrictItem[]
    watermarkCameraCount: number
    idCameraWorkCount: number
    fileTypeList: PracticalToolsFileTypeItem[]
    idCameraCount: number
    watermarkCameraWorkCount: number
    idCameraPublicCount: number
    fileCount: number
  }
  message: null
}

// 获取实用工具数据
export const getPracticalToolsApi = (params: PracticalToolsParams) => {
  return request.post<PracticalToolsResponse>('/cockpit/statistics/practicalTools', params)
}

// 社区游线相关接口
export interface TourInfoParams {
  areaCode: string
  timeType: number // 时间类型：0-今日，1-近一月，2-近一年
}

export interface TourInfoResponse {
  code: number
  msg: string
  data: null
  message: {
    goodsCount: number // 社区游线数量
    viewCount: number // 总浏览量
  }
}

// 社区游线概览接口
export const getTourInfoApi = (params: TourInfoParams) => {
  return request.post<TourInfoResponse>('/cockpit/statistics/tourInfo', params)
}

// 社区游线热浏览概况接口
export interface TourHotsParams {
  areaCode: string
  timeType: number
}

export interface TourHotItem {
  tourLineId: string
  tourLineName: string
  districtCode: string
  districtAddress: string | null
  travelDays: number | null
  tourLineIntroduction: string | null
  browseNumber: number
  createTime: string | null
  updateTime: string | null
  districtName: string
}

export interface TourHotsResponse {
  code: number
  msg: string
  data: null
  message: {
    list: {
      total: number
      rows: TourHotItem[]
      code: number
      msg: string
    }
  }
}

// 社区游线热浏览概况
export const getTourHotsApi = (params: TourHotsParams) => {
  return request.post<TourHotsResponse>('/cockpit/statistics/tourHots', params)
}

// 社区游线浏览及参与人数分布接口
export interface TourListParams {
  areaCode: string
  timeType: number
}

export interface TourDistrictItem {
  districtId: string
  code: string
  name: string
  longitude: string
  latitude: string
  level: number
  subjectType: null
  pid: string
  headChar: string
  hotFlag: string
  children: null
  useCount: null
  lifeCircleUseCount: null
  communityLineUseCount: null
}

export interface TourListItem {
  goodsCount: number
  district: TourDistrictItem
  viewCount?: number
}

export interface TourListResponse {
  code: number
  msg: string
  data: null
  message: {
    list: TourListItem[]
  }
}

// 社区游线浏览及参与人数分布
export const getTourListApi = (params: TourListParams) => {
  return request.post<TourListResponse>('/cockpit/statistics/tourList', params)
}

// 文件和人员信息统计接口
export interface FileAndResLogStatisticsParams {
  cityCode: string
  token: string
}

export interface FileAndResLogStatisticsResponse {
  code: number
  message: string
  data: string // 加密数据，解密后包含 fileTotal 和 personnelAutoFillTotal
}

// 获取文件和人员信息统计数据
export const getFileAndResLogStatisticsApi = (params: FileAndResLogStatisticsParams) => {
  return axios.get<FileAndResLogStatisticsResponse>(
    'https://qlwarehouse.muulin.cn:40300/proxy/statistics/open/findFileAndResLogStatistics',
    { 
      params,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  ).then((response: any) => response.data)
}

// 标签统计接口
export interface LabelStatisticsParams {
  cityCode: string
  token: string
}

export interface LabelStatisticsItem {
  tagName: string // 标签名称
  count: number   // 数量
}

export interface LabelStatisticsResponse {
  code: number
  message: string
  data: string // 加密数据，解密后包含标签统计列表
}

// 获取标签统计数据
export const getLabelStatisticsApi = (params: LabelStatisticsParams) => {
  return axios.get<LabelStatisticsResponse>(
    'https://qlwarehouse.muulin.cn:40300/proxy/statistics/open/findLabelStatistcs',
    { 
      params,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  ).then((response: any) => response.data)
}

// 多种标签人群接口
export interface MultipleLabelsParams {
  cityCode: string
  token: string
}

export interface MultipleLabelsResponse {
  code: number
  message: string
  data: string // 加密数据，解密后包含多种标签人群数量
}

// 获取多种标签人群数据
export const getMultipleLabelsApi = (params: MultipleLabelsParams) => {
  return axios.get<MultipleLabelsResponse>(
    'https://qlwarehouse.muulin.cn:40300/proxy/statistics/open/findMultipleLabels',
    { 
      params,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  ).then((response: any) => response.data)
}

// 社区文件资源列表接口
export interface ResResidentsLogListParams {
  cityCode: string
  token: string
  pageNumber: number
  pageSize: number
}

export interface ResResidentsLogListItem {
  id: string
  name: string
  type: string
  createTime: string
  updateTime: string
  status: string
  description?: string
}

export interface ResResidentsLogListResponse {
  code: number
  message: string
  data: string // 加密数据，解密后包含文件资源列表
}

// 获取社区文件资源列表数据
export const getResResidentsLogListApi = (params: ResResidentsLogListParams) => {
  return axios.get<ResResidentsLogListResponse>(
    'https://qlwarehouse.muulin.cn:40300/proxy/statistics/open/findResResidentsLogList',
    { 
      params,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  ).then((response: any) => response.data)
}

// 数据使用预测列表接口
export interface AnticipateListParams {
  cityCode: string
  token: string
}

export interface AnticipateListItem {
  id: string
  title: string
  description: string
  count: number
  type: string
  createTime: string
  updateTime: string
}

export interface AnticipateListResponse {
  code: number
  message: string
  data: string // 加密数据，解密后包含预测数据列表
}

// 获取数据使用预测列表数据
export const getAnticipateListApi = (params: AnticipateListParams) => {
  return axios.get<AnticipateListResponse>(
    'https://qlwarehouse.muulin.cn:40300/proxy/statistics/open/findAnticipateList',
    { 
      params,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  ).then((response: any) => response.data)
}

// 人口结构分析接口
export interface AgeSummaryParams {
  cityCode: string
  token: string
}

export interface AgeSummaryItem {
  quantity: number      // 人口数量
  sectionId: number     // 年龄段ID
  startValue: number    // 年龄段起始值
  endValue: number      // 年龄段结束值
  name: string          // 年龄段名称
}

export interface AgeSummaryResponse {
  code: number
  message: string
  data: string // 加密数据，解密后包含年龄分组统计数据
}

// 获取人口结构分析数据
export const getAgeSummaryApi = (params: AgeSummaryParams) => {
  return axios.get<AgeSummaryResponse>(
    'https://qlwarehouse.muulin.cn:40300/proxy/statistics/open/findAgeSummary',
    { 
      params,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  ).then((response: any) => response.data)
}

// 家庭医生服务概况接口
export interface FamilyDoctorOverviewParams {
  timeType: string
  areaCode: string
}

export interface FamilyDoctorOverviewData {
  doctorCount: number      // 家庭医生数量
  residentCount: number    // 居民数量
  contractCount: number    // 签约数量
}

export interface FamilyDoctorOverviewResponse {
  code: number
  message: string
  data: FamilyDoctorOverviewData
}

// 获取家庭医生服务概况数据
export const getFamilyDoctorOverviewApi = (params: FamilyDoctorOverviewParams) => {
  return request.post<FamilyDoctorOverviewResponse>('/cockpit/statistics/family/overview', params)
}

// 社区导览接口
export interface CommunityTourParams {
  areaCode: string
  timeType: number // 时间类型：0-当天，1-一周，2-近三月，3-近六月，4-近一年
}

export interface CommunityTourDistrictItem {
  districtId: string
  code: string
  name: string
  longitude: string
  latitude: string
  level: number
  subjectType: null
  pid: string
  headChar: string
  hotFlag: string
  children: null
  useCount: number
  lifeCircleUseCount: number
  communityLineUseCount: number
}

export interface CommunityTourTypeItem {
  typeId: string
  parentId: number | string
  typeName: string
  typeIcon: string | null
  mapIcon: string | null
  fontColor: string | null
  bgColor: string | null
  showFlag: string
  sort: number
  lifeCircleCount: number | null
  children: CommunityTourTypeItem[]
}

export interface CommunityTourResponse {
  code: number
  msg: string
  data: null
  message: {
    districtList: CommunityTourDistrictItem[]
    totalNumber: number
    lifeCircleTypeList: CommunityTourTypeItem[]
  }
}

// 获取社区导览数据
export const getCommunityTourApi = (params: CommunityTourParams) => {
  return request.post<CommunityTourResponse>('/cockpit/statistics/communityTour', params)
}

// 社区导览数据详情接口
export interface CommunityTourDataDetailParams {
  timeType: string // 统计查询条件：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 社区code
  key?: string // 查询关键字，通用查询
  pageSize: number // 分页大小
  pageNum: number // 当前页数
  orderByColumn?: string // 排序列
  isAsc?: string // 排序方向：asc或desc
}

export interface CommunityTourDataDetailItem {
  name?: string | null // 资源名称
  createTime?: string | null // 创建时间
  total?: number // 总数
  districtName?: string // 区域名称
  districtCode?: string // 区域代码
  longitude?: string // 经度
  latitude?: string // 纬度
}

export interface CommunityTourDataDetailResponse {
  code: number
  msg: string | null
  total: number
  rows: CommunityTourDataDetailItem[]
}

// 获取社区导览数据详情
export const getCommunityTourDataDetailApi = (params: CommunityTourDataDetailParams) => {
  return request.getWithDirectParams<CommunityTourDataDetailResponse>('/cockpit/statistics/communityTour/map/resource/page', params)
}

// 社区导览热力图数据接口
export interface CommunityTourHeatMapParams {
  timeType: string // 统计查询条件：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 社区code
  resourceType: number // 资源类型：2-社区导览
}

export interface CommunityTourHeatMapItem {
  districtName: string // 区域名称
  districtCode: string // 区域代码
  longitude: string // 经度
  latitude: string // 纬度
  count: number // 数量统计
}

export interface CommunityTourHeatMapResponse {
  code: number
  msg: string | null
  data: CommunityTourHeatMapItem[]
}

// 获取社区导览热力图数据
export const getCommunityTourHeatMapApi = (params: CommunityTourHeatMapParams) => {
  return request.post<CommunityTourHeatMapResponse>('/cockpit/statistics/communityTour/map/resource', params)
}

// 实用工具数据详情接口
export interface ToolsDataDetailParams {
  timeType: string // 时间类型：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 区域代码
  key?: string // 查询关键字，通用查询
  toMidnight?: string // 只写，可选
  pageSize: number // 分页大小
  pageNum: number // 当前页数
  orderByColumn?: string // 排序列，可选
  isAsc?: string // 排序方向：desc或asc，可选
}

export interface ToolsDataDetailItem {
  name?: string | null // 工具名称
  createTime?: string | null // 创建时间
  total?: number // 总数
  districtName?: string // 区域名称
  districtCode?: string // 区域代码
  longitude?: string // 经度
  latitude?: string // 纬度
}

export interface ToolsDataDetailResponse {
  code: number
  msg: string
  total: number
  rows: ToolsDataDetailItem[]
}

// 获取实用工具数据详情
export const getToolsDataDetailApi = (params: ToolsDataDetailParams) => {
  return request.getWithDirectParams<ToolsDataDetailResponse>('/cockpit/statistics/tools/map/resource/page', params)
}

// 家庭医生服务概况数据详情接口
export interface FamilyServiceDataDetailParams {
  timeType: string // 时间类型：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 区域代码
  key?: string // 查询关键字，通用查询
  pageSize: number // 分页大小
  pageNum: number // 当前页数
  orderByColumn?: string // 排序列，可选
  isAsc?: string // 排序方向：desc或asc，可选
}

export interface FamilyServiceDataDetailItem {
  name?: string // 服务名称 (如"家庭医生")
  createTime?: string // 创建时间
  total?: number // 总数
  districtName?: string // 区域名称
  districtCode?: string // 区域代码
  longitude?: string // 经度
  latitude?: string // 纬度
  doctorName?: string // 医生姓名
}

export interface FamilyServiceDataDetailResponse {
  code: number
  msg: string
  total: number
  rows: FamilyServiceDataDetailItem[]
}

// 获取家庭医生服务概况数据详情
export const getFamilyServiceDataDetailApi = (params: FamilyServiceDataDetailParams) => {
  return request.getWithDirectParams<FamilyServiceDataDetailResponse>('/cockpit/statistics/family/map/resource/page', params)
}

// 家庭医生资源地图分布接口
export interface FamilyMapResourceParams {
  timeType: string // 时间类型：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 区域代码
  resourceType: string // 资源类型：1-地图资源，2-地图热力
}

export interface FamilyMapResourceItem {
  name?: string | null // 名称 (通常是"家庭医生")
  createTime?: string | null // 创建时间
  total?: string | null // 总数
  districtName?: string // 区域名称
  districtCode?: string // 区域代码
  longitude?: string // 经度
  latitude?: string // 纬度
  doctorName?: string // 医生姓名
}

export interface FamilyMapResourceResponse {
  code: number
  msg: string
  data: FamilyMapResourceItem[]
}

// 获取家庭医生资源地图分布
export const getFamilyMapResourceApi = (params: FamilyMapResourceParams) => {
  return request.post<FamilyMapResourceResponse>('/cockpit/statistics/family/map/resource', params)
}

// 社区响应概况数据详情接口
export interface CommunityResponseDataDetailParams {
  timeType: string // 时间类型：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 区域代码
  key?: string // 查询关键字，通用查询
  toMidnight?: string // 只写，可选
  pageSize: number // 分页大小
  pageNum: number // 当前页数
  orderByColumn?: string // 排序列，可选
  isAsc?: string // 排序方向：desc或asc，可选
}

export interface CommunityResponseDataDetailItem {
  districtName?: string // 区域名称
  districtCode?: string // 区域代码
  callerName?: string | null // 呼叫者姓名
  handlerStatus?: string // 处理状态：0-未处理，1-已处理
  handlerUserName?: string | null // 处理人姓名
}

export interface CommunityResponseDataDetailResponse {
  code: number
  msg: string | null
  total: number
  rows: CommunityResponseDataDetailItem[]
}

// 获取社区响应概况数据详情
export const getCommunityResponseDataDetailApi = (params: CommunityResponseDataDetailParams) => {
  return request.getWithDirectParams<CommunityResponseDataDetailResponse>('/cockpit/statistics/call/map/resource/page', params)
}

// 社区匠人数据详情接口
export interface CraftsmanDataDetailParams {
  timeType: string // 时间类型：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 区域代码
  key?: string // 查询关键字，通用查询
  toMidnight?: string // 只写，可选
  pageSize: number // 分页大小
  pageNum: number // 当前页数
  orderByColumn?: string // 排序列，可选
  isAsc?: string // 排序方向：desc或asc，可选
}

export interface CraftsmanDataDetailItem {
  id?: string
  name?: string // 匠人名称
  createTime?: string // 创建时间
  districtName?: string // 区域名称
  districtCode?: string // 区域代码
  browseNumber?: number // 浏览次数
  description?: string // 描述
  category?: string // 分类
  status?: string // 状态
  publishTime?: string // 发布时间
  updateTime?: string // 更新时间
}

export interface CraftsmanDataDetailResponse {
  code: number
  msg: string | null
  total: number
  rows: CraftsmanDataDetailItem[]
}

// 获取社区匠人数据详情
export const getCraftsmanDataDetailApi = (params: CraftsmanDataDetailParams) => {
  return request.getWithDirectParams<CraftsmanDataDetailResponse>('/cockpit/statistics/craftsman/map/resource/page', params)
}

// 崃建言数据详情接口
export interface SuggestionDataDetailParams {
  timeType: string // 时间类型：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 区域代码
  key?: string // 查询关键字，通用查询
  toMidnight?: string // 只写，可选
  pageSize: number // 分页大小
  pageNum: number // 当前页数
  orderByColumn?: string // 排序列，可选
  isAsc?: string // 排序方向：desc或asc，可选
}

export interface SuggestionDataDetailItem {
  name?: string | null // 名称
  createTime?: string // 创建时间
  total?: string | null // 总数
  districtName?: string // 区域名称
  districtCode?: string // 区域代码
  longitude?: string // 经度
  latitude?: string // 纬度
  questionnaireId?: string // 问卷ID
  title?: string // 建议标题
  type?: string // 类型
  content?: string // 建议内容
  stratagem?: string // 策略/处理方案
  contactName?: string // 联系人姓名
  contactPhone?: string // 联系人电话
}

export interface SuggestionDataDetailResponse {
  code: number
  msg: string | null
  total: number
  rows: SuggestionDataDetailItem[]
}

// 获取崃建言数据详情
export const getSuggestionDataDetailApi = (params: SuggestionDataDetailParams) => {
  return request.getWithDirectParams<SuggestionDataDetailResponse>('/cockpit/statistics/suggestions/map/resource/page', params)
}

// 崃建言资源地图分布接口
export interface SuggestionMapResourceParams {
  timeType: string // 时间类型：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 区域代码
  resourceType: string // 资源类型：1-地图资源，2-地图热力
}

export interface SuggestionMapResourceItem {
  name?: string | null // 名称 (通常是"崃建言")
  createTime?: string | null // 创建时间
  total?: string | null // 总数
  districtName?: string // 区域名称
  districtCode?: string // 区域代码
  longitude?: string // 经度
  latitude?: string // 纬度
}

export interface SuggestionMapResourceResponse {
  code: number
  msg: string | null
  data: SuggestionMapResourceItem[]
}

// 获取崃建言资源地图分布
export const getSuggestionMapResourceApi = (params: SuggestionMapResourceParams) => {
  return request.post<SuggestionMapResourceResponse>('/cockpit/statistics/suggestions/map/resource', params)
}







// 信息公告接口
export interface NoticeStatisticsParams {
  timeType: string // 统计查询条件：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 社区code
}

export interface NoticeTypeItem {
  name: string // 热词
  total: number // 总数
}

export interface NoticeOverviewItem {
  name: string // 名称
  browseNumber: number // 浏览数
  total: number // 总数
}

export interface NoticeFileItem {
  districtName: string // 辖区名称
  title: string // 标题
  time: string // 发布时间
}

export interface NoticeStatisticsResponse {
  code: number
  msg: string
  data: {
    total: number // 总数
    scatters: NoticeTypeItem[] // 信息公告类型分布
    overviews: NoticeOverviewItem[] // 信息公告浏览量趋势
    noticeFiles: NoticeFileItem[] // 最新公告动态
  }
  message: object
}

export const getNoticeStatisticsApi = (params: NoticeStatisticsParams) => {
  return request.post<NoticeStatisticsResponse>('/cockpit/statistics/notice', params)
}

// 信息公告资源地图接口
export interface NoticeMapResourceParams {
  timeType: string // 统计查询条件：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 社区code
  resourceType: number // 1: 资源地图分布, 2: 辖区热力分布
}

export interface NoticeMapResourceResponse {
  code: number
  msg: string
  data: any // 具体数据结构根据实际返回调整
  message: object
}

export const getNoticeMapResourceApi = (params: NoticeMapResourceParams) => {
  return request.post<NoticeMapResourceResponse>('/cockpit/statistics/notice/map/resource', params)
}

// 数据详情分页接口
export interface NoticeMapResourcePageParams {
  timeType: string // 统计查询条件：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 社区code
  resourceType: string // 1: 地图资源, 2: 地区热力
  pageSize: number // 分页大小
  pageNum: number // 当前页数
  orderByColumn?: string // 排序列
  isAsc?: string // 排序方向：asc/desc
}

export interface NoticeMapResourcePageItem {
  name: string // 标题
  typeName: string // 类型名称
  createTime: string // 创建时间
  total: number | null // 总数（可能为null）
  districtName: string // 辖区名称
  districtCode: string // 辖区代码
  longitude: string // 经度
  latitude: string // 纬度
}

export interface NoticeMapResourcePageResponse {
  code: number
  msg: string
  total: number // 总数直接在根级别
  rows: NoticeMapResourcePageItem[] // 数据列表使用rows字段
}

export const getNoticeMapResourcePageApi = (params: NoticeMapResourcePageParams) => {
  return request.getWithDirectParams<NoticeMapResourcePageResponse>('/cockpit/statistics/notice/map/resource/page', params)
}

// 协商铃资源地图接口
export interface CallMapResourceParams {
  timeType: string // 统计查询条件：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 社区code
  resourceType: number // 1: 资源地图分布, 2: 辖区热力分布
}

export interface CallMapResourceResponse {
  code: number
  msg: string
  data: any // 具体数据结构根据实际返回调整
  message: object
}

export const getCallMapResourceApi = (params: CallMapResourceParams) => {
  return request.post<CallMapResourceResponse>('/cockpit/statistics/call/map/resource', params)
}

// 社区匠人资源地图接口
export interface CraftsmanMapResourceParams {
  timeType: string // 统计查询条件：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 社区code
  resourceType: number // 1: 资源地图分布, 2: 辖区热力分布
}

export interface CraftsmanMapResourceResponse {
  code: number
  msg: string
  data: any // 具体数据结构根据实际返回调整
  message: object
}

export const getCraftsmanMapResourceApi = (params: CraftsmanMapResourceParams) => {
  return request.post<CraftsmanMapResourceResponse>('/cockpit/statistics/craftsman/map/resource', params)
}

// 实用工具资源地图接口
export interface ToolsMapResourceParams {
  timeType: string // 统计查询条件：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 社区code
  resourceType: number // 1: 资源地图分布, 2: 辖区热力分布
}

export interface ToolsMapResourceResponse {
  code: number
  msg: string
  data: any // 具体数据结构根据实际返回调整
  message: object
}

export const getToolsMapResourceApi = (params: ToolsMapResourceParams) => {
  return request.post<ToolsMapResourceResponse>('/cockpit/statistics/tools/map/resource', params)
}

// 社区好物资源地图接口
export interface StoreMapResourceParams {
  timeType: string // 统计查询条件：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 社区code
  resourceType: number // 1: 资源地图分布, 2: 辖区热力分布
}

export interface StoreMapResourceResponse {
  code: number
  msg: string
  data: any // 具体数据结构根据实际返回调整
  message: object
}

export const getStoreMapResourceApi = (params: StoreMapResourceParams) => {
  return request.post<StoreMapResourceResponse>('/cockpit/statistics/storeInfo/map/resource', params)
}

// 社区活动资源地图接口
export interface ActivityMapResourceParams {
  timeType: string // 统计查询条件：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 社区code
  resourceType: number // 1: 资源地图分布, 2: 辖区热力分布
}

export interface ActivityMapResourceResponse {
  code: number
  msg: string
  data: any // 具体数据结构根据实际返回调整
  message: object
}

export const getActivityMapResourceApi = (params: ActivityMapResourceParams) => {
  return request.post<ActivityMapResourceResponse>('/cockpit/statistics/activity/map/resource', params)
}

// 社区活动数据详情接口
export interface ActivityDataDetailParams {
  timeType: string // 统计查询条件：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 社区code
  key?: string // 查询关键字，通用查询
  toMidnight?: string // string <date-time>
  pageSize: number // integer <int32> 分页大小
  pageNum: number // integer <int32> 当前页数
  orderByColumn: string // 排序列
  isAsc: string // 排序的方向desc或者asc
}

export interface ActivityDataDetailResponse {
  code: number
  msg: string
  data: {
    rows: any[]
    total: number
  }
  message: object
}

export const getActivityDataDetailApi = (params: ActivityDataDetailParams) => {
  return request.get<ActivityDataDetailResponse>('/cockpit/statistics/activity/map/resource/page', params)
}

// 社区好物数据详情接口
export interface StoreDataDetailParams {
  timeType: string // 统计查询条件：0-当天，1-近周，2-近3月，3-近6月，4-近1年
  areaCode: string // 社区code
  key?: string // 查询关键字，通用查询
  toMidnight?: string // string <date-time>
  pageSize: number // integer <int32> 分页大小
  pageNum: number // integer <int32> 当前页数
  orderByColumn: string // 排序列
  isAsc: string // 排序的方向desc或者asc
}

export interface StoreDataDetailResponse {
  code: number
  msg: string
  data: {
    rows: any[]
    total: number
  }
  message: object
}

export const getStoreDataDetailApi = (params: StoreDataDetailParams) => {
  return request.get<StoreDataDetailResponse>('/cockpit/statistics/storeInfo/map/resource/page', params)
}

// 辖区热力分布接口
export interface SpecialStatisticsByAreaParams {
  AreaCode: string // 区域代码
  DateRangeMin: string // 开始日期，格式：2025-07-01
  DateRangeMax: string // 结束日期，格式：2025-07-18
}

export interface SpecialStatisticsByAreaItem {
  name: string | null // 名称
  createTime: string | null // 创建时间
  districtName: string // 辖区名字
  districtCode: string // 辖区代码
  total: number // 计数
  longitude: string // 经度
  latitude: string // 纬度
}

export interface SpecialStatisticsByAreaResponse {
  code: number
  msg: string
  data: SpecialStatisticsByAreaItem[]
}

