import request from './request'
import type { ResultData } from './request'

// 常量定义
const CLIENT_ID = "980a7458cd984785d19206ce24a7bksa"

// 登录接口参数
export interface LoginData {
  clientId: string
  username: string
  password: string
}

// 登录接口返回数据
export interface LoginResult {
  access_token: string
  token?: string
  userInfo: {
    id: string
    username: string
    nickname: string
    avatar: string
    email: string
    phone: string
    roles: string[]
  }
}

// 用户信息
export interface UserInfo {
  id: string
  username: string
  nickname: string
  avatar: string
  email: string
  phone: string
  roles: string[]
  permissions?: string[]
  defaultPassword?: number  // 是否为默认密码：0-默认密码需要修改，1-已修改过密码
}

/**
 * @name 认证模块
 */
// 用户登录
export const loginApi = (params: LoginData) => {
  // 如果没有提供clientId，使用默认值
  const loginParams = {
    ...params,
    clientId: params.clientId || CLIENT_ID
  }
  return request.post<LoginResult>('/auth/screen/login', loginParams)
}

// 用户登出
export const logoutApi = () => {
  return request.post('/auth/logout')
}

// 获取用户信息
export const getUserInfoApi = () => {
  return request.get<UserInfo>('/auth/userinfo')
}

// 获取社区用户信息
export const getCommunityUserInfoApi = () => {
  const params = {
    clientId: CLIENT_ID
  }
  return request.get<UserInfo>('/system/communityUser/info', params)
}

// 刷新token
export const refreshTokenApi = () => {
  return request.post<{ token: string }>('/auth/refresh')
}

// 修改密码
export const changePasswordApi = (params: { password: string; newPassword: string }) => {
  return request.post('/system/communityUser/updatePwd', params)
}

// 忘记密码 - 发送验证码
export const sendResetCodeApi = (params: { username: string; type: 'phone' | 'email' }) => {
  return request.post('/auth/reset/send-code', params)
}

// 忘记密码 - 验证验证码
export const verifyResetCodeApi = (params: { username: string; code: string }) => {
  return request.post<{ resetToken: string }>('/auth/reset/verify-code', params)
}

// 忘记密码 - 重置密码
export const resetPasswordApi = (params: { resetToken: string; newPassword: string }) => {
  return request.post('/auth/reset/password', params)
} 

/**
 * 授权接口参数
 */
export interface AuthorizeParams {
  cityCode: string
  code: string
}

/**
 * 授权接口响应
 */
export interface AuthorizeResponse {
  code: number
  message: string
  data?: any
}

/**
 * 调用授权接口
 * @param params 授权参数
 * @returns 授权结果
 */
export const authorizeApi = async (params: AuthorizeParams): Promise<AuthorizeResponse> => {
  const url = `https://qlwarehouse.muulin.cn:40300/kangbank/open/authorize?cityCode=${params.cityCode}&code=${params.code}`
  
  console.log('调用授权接口:', url)
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    const result = await response.json()
    
    console.log('授权接口响应:', result)
    
    return result
  } catch (error) {
    console.error('授权接口调用失败:', error)
    throw error
  }
} 