/**
 * 精治数仓社区概览接口
 */
import request from './request'

/**
 * 社区统计数据响应接口
 */
export interface CommunityStatisticsResponse {
  code: number
  message: string
  data?: any
}

/**
 * 社区生活概况请求参数
 */
export interface CommunityLifeInfoParams {
  areaCode: string
  timeType: number
}

/**
 * 社区生活概况响应接口
 */
export interface CommunityLifeInfoResponse {
  code: number
  message: string
  data?: any
}

/**
 * 社区图书概况请求参数
 */
export interface CommunityBookInfoParams {
  areaCode: string
  timeType: number
}

/**
 * 社区图书概况响应接口
 */
export interface CommunityBookInfoResponse {
  code: number
  message: string
  data?: any
}

/**
 * 调用精治数仓社区概览接口
 * @param token 授权token
 * @param cityCode 城市编码
 * @returns 社区统计数据
 */
export const getCommunityStatisticsApi = async (
  token: string,
  cityCode: string = '510183002014'
): Promise<CommunityStatisticsResponse> => {
  const url = `https://qlwarehouse.muulin.cn:40300/proxy/statistics/open/findCommunityStatistics?cityCode=${cityCode}&token=${token}`

  console.log('📊 调用精治数仓社区概览接口:', url)
  console.log('🔑 使用token:', token)

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    console.log('📊 精治数仓接口响应:', result)

    return result
  } catch (error) {
    console.error('❌ 精治数仓接口调用失败:', error)
    throw error
  }
}

/**
 * 获取社区生活概况信息
 * @param params 请求参数
 * @returns 社区生活概况数据
 */
export const getCommunityLifeInfoApi = async (
  params: CommunityLifeInfoParams
): Promise<CommunityLifeInfoResponse> => {
  console.log('📊 调用社区生活概况接口:', params)

  try {
    const response = await request.post('/cockpit/statistics/groupInfo', params)

    console.log('📊 社区生活概况接口响应:', response)

    return response
  } catch (error) {
    console.error('❌ 社区生活概况接口调用失败:', error)
    throw error
  }
}

/**
 * 获取社区图书概况信息
 * @param params 请求参数
 * @returns 社区图书概况数据
 */
export const getCommunityBookInfoApi = async (
  params: CommunityBookInfoParams
): Promise<CommunityBookInfoResponse> => {
  console.log('📚 调用社区图书概况接口:', params)

  try {
    const response = await request.post('/cockpit/statistics/bookInfo', params)

    console.log('📚 社区图书概况接口响应:', response)

    return response
  } catch (error) {
    console.error('❌ 社区图书概况接口调用失败:', error)
    throw error
  }
}

/**
 * 社区圈子地图资源请求参数
 */
export interface CommunityGroupMapResourceParams {
  areaCode: string
  timeType: number
  resourceType: number
}

/**
 * 社区圈子地图资源响应接口
 */
export interface CommunityGroupMapResourceResponse {
  code: number
  msg: string
  data?: any[]
  message?: string | null
}

/**
 * 获取社区圈子地图资源分布
 * @param params 请求参数
 * @returns 社区圈子地图资源数据
 */
export const getCommunityGroupMapResourceApi = async (
  params: CommunityGroupMapResourceParams
): Promise<CommunityGroupMapResourceResponse> => {
  console.log('🗺️ 调用社区圈子地图资源接口:', params)

  try {
    const response = await request.post('/cockpit/statistics/group/map/resource', params)

    console.log('🗺️ 社区圈子地图资源接口响应:', response)

    return response
  } catch (error) {
    console.error('❌ 社区圈子地图资源接口调用失败:', error)
    throw error
  }
}

/**
 * 社区圈子数据详情请求参数
 */
export interface CommunityGroupDataDetailParams {
  timeType: string  // 获取时间类型：0: 当天; 1: 近周; 2: 近3月; 3: 近6月; 4: 近1年
  areaCode: string  // 社区code
  key?: string      // 查询关键字，通用查询
  toMidnight?: string  // <date-time>
  pageSize: number  // 分页大小
  pageNum: number   // 当前页数
  orderByColumn?: string  // 排序列
  isAsc?: string    // 排序方向 desc或者asc
}

/**
 * 社区圈子数据详情项
 */
export interface CommunityGroupDataDetailItem {
  id: string
  name: string
  type: string
  status: string
  createTime: string
  updateTime: string
  [key: string]: any
}

/**
 * 社区圈子数据详情响应接口
 */
export interface CommunityGroupDataDetailResponse {
  code: number
  msg: string
  data?: {
    list: CommunityGroupDataDetailItem[]
    total: number
    pageNum: number
    pageSize: number
  }
  message?: string | null
}

/**
 * 获取社区圈子数据详情
 * @param params 请求参数
 * @returns 社区圈子数据详情
 */
export const getCommunityGroupDataDetailApi = async (
  params: CommunityGroupDataDetailParams
): Promise<CommunityGroupDataDetailResponse> => {
  console.log('🔍 调用社区圈子数据详情接口:', params)

  try {
    const response = await request.getWithDirectParams('/cockpit/statistics/group/map/resource/page', params)

    console.log('🔍 社区圈子数据详情接口响应:', response)

    return response
  } catch (error) {
    console.error('❌ 社区圈子数据详情接口调用失败:', error)
    throw error
  }
}

/**
 * 社区图书地图资源请求参数
 */
export interface CommunityBookMapResourceParams {
  timeType: string
  areaCode: string
  resourceType: number  // 1: 资源地图分布, 2: 辖区热力分布
}

/**
 * 社区图书地图资源响应接口
 */
export interface CommunityBookMapResourceResponse {
  code: number
  msg: string
  data?: any[]
  message?: string | null
}

/**
 * 获取社区图书地图资源分布
 * @param params 请求参数
 * @returns 社区图书地图资源数据
 */
export const getCommunityBookMapResourceApi = async (
  params: CommunityBookMapResourceParams
): Promise<CommunityBookMapResourceResponse> => {
  console.log('📚 调用社区图书地图资源接口:', params)

  try {
    const response = await request.post('/cockpit/statistics/book/map/resource', params)

    console.log('📚 社区图书地图资源接口响应:', response)

    return response
  } catch (error) {
    console.error('❌ 社区图书地图资源接口调用失败:', error)
    throw error
  }
}

/**
 * 社区图书数据详情请求参数
 */
export interface CommunityBookDataDetailParams {
  timeType: string  // 获取时间类型：0: 当天; 1: 近周; 2: 近3月; 3: 近6月; 4: 近1年
  areaCode: string  // 社区code
  key?: string      // 查询关键字，通用查询
  toMidnight?: string  // <date-time>
  pageSize: number  // 分页大小
  pageNum: number   // 当前页数
  orderByColumn?: string  // 排序列
  isAsc?: string    // 排序方向 desc或者asc
}

/**
 * 社区图书数据详情项
 */
export interface CommunityBookDataDetailItem {
  name: string
  createTime: string
  total: number | null
  districtName: string
  districtCode: string
  longitude: string
  latitude: string
  typeName: string
  [key: string]: any
}

/**
 * 社区图书数据详情响应接口
 */
export interface CommunityBookDataDetailResponse {
  code: number
  msg: string
  total: number
  rows: CommunityBookDataDetailItem[]
}

/**
 * 获取社区图书数据详情
 * @param params 请求参数
 * @returns 社区图书数据详情
 */
export const getCommunityBookDataDetailApi = async (
  params: CommunityBookDataDetailParams
): Promise<CommunityBookDataDetailResponse> => {
  console.log('📚 调用社区图书数据详情接口:', params)

  try {
    const response = await request.getWithDirectParams('/cockpit/statistics/book/map/resource/page', params)

    console.log('📚 社区图书数据详情接口响应:', response)

    return response
  } catch (error) {
    console.error('❌ 社区图书数据详情接口调用失败:', error)
    throw error
  }
}
