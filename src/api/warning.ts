import axios from 'axios'
import type { AxiosInstance, InternalAxiosRequestConfig } from 'axios'
import { ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth'

// 三色预警接口参数
export interface WarningOverviewParams {
  DateRangeMin: string
  DateRangeMax: string
  AreaCode: string
}

// 三色预警数据项
export interface WarningItem {
  Blue: number    // 蓝色预警数量
  Yellow: number  // 黄色预警数量
  Red: number     // 红色预警数量
}

// 三色预警概况响应数据
export interface WarningOverviewResponse {
  state: number
  message: WarningItem[]
}

// 三色预警趋势接口参数
export interface WarningTrendParams {
  DateRangeMin: string
  DateRangeMax: string
  AreaCode: string
  WarningType?: number  // 可选的预警类型
}

// 三色预警趋势数据项
export interface WarningTrendItem {
  Month: string   // 月份
  Red: number     // 红色预警数量
  Yellow: number  // 黄色预警数量
  Blue: number    // 蓝色预警数量
}

// 三色预警趋势响应数据
export interface WarningTrendResponse {
  state: number
  message: WarningTrendItem[]
}

// 三色预警统计接口参数
export interface WarningStatisticsParams {
  DateRangeMin: string
  DateRangeMax: string
  AreaCode: string
  WarningType?: number  // 可选的预警类型
}

// 三色预警统计数据项
export interface WarningStatisticsItem {
  Name: string          // 社区名称
  Red: number           // 红色预警数量
  Yellow: number        // 黄色预警数量
  Blue: number          // 蓝色预警数量
}

// 三色预警统计响应数据
export interface WarningStatisticsResponse {
  state: number
  message: WarningStatisticsItem[]
}

// 创建专门用于三色预警接口的axios实例
const warningRequest: AxiosInstance = axios.create({
  baseURL: 'https://qlzhsq.qlzhsq.cn:8131/api',
  timeout: 60000,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json, text/plain, */*',
  }
})

// 三色预警接口的请求拦截器
warningRequest.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getToken()
    // console.log('🔍 三色预警API请求拦截器:')
    // console.log('  - 请求URL:', (config.baseURL || '') + (config.url || ''))
    // console.log('  - 获取到的token:', token ? token.substring(0, 20) + '...' : '无token')
    
    // 初始化headers
    config.headers = config.headers || {}
    
    // 添加认证相关headers
    if (token) {
      config.headers['Authorization'] = `${token}`
      config.headers['clientId'] = `428a8310cd442757ae699df5d894f051`
      console.log('✅ 已添加三色预警API的Authorization header')
    } else {
      console.log('❌ 未添加Authorization header - 没有token')
    }
    
    // 确保Content-Type正确设置
    if (!config.headers['Content-Type']) {
      config.headers['Content-Type'] = 'application/json'
    }
    
    console.log('✅ 已设置基础headers')
    console.log('  - Content-Type:', config.headers['Content-Type'])
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 三色预警接口的响应拦截器
warningRequest.interceptors.response.use(
  (response) => {
    const { data } = response
    console.log('📡 三色预警API响应:', data)
    
    // 检查响应格式
    if (data.code && data.code !== 200) {
      ElMessage.error(data.message || data.msg || '请求失败')
      return Promise.reject(data)
    }
    
    return data
  },
  (error) => {
    console.error('❌ 三色预警API请求失败:', error)
    
    if (error.message.indexOf('timeout') !== -1) {
      ElMessage.error('三色预警接口请求超时')
    } else if (error.message.indexOf('Network Error') !== -1) {
      ElMessage.error('三色预警接口网络错误')
    } else if (error.response) {
      const status = error.response.status
      switch (status) {
        case 401:
          ElMessage.error('三色预警接口：未授权访问')
          break
        case 403:
          ElMessage.error('三色预警接口：禁止访问')
          break
        case 404:
          ElMessage.error('三色预警接口：接口不存在')
          break
        case 500:
          ElMessage.error('三色预警接口：服务器错误')
          break
        default:
          ElMessage.error(`三色预警接口错误：${status}`)
      }
    } else {
      ElMessage.error('三色预警接口请求失败')
    }
    
    return Promise.reject(error)
  }
)

// 获取三色预警概况
export const getWarningOverviewApi = (params: WarningOverviewParams) => {
  return warningRequest.post<any>('/DCEventRYB/StatisticsOverView', params, {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json, text/plain, */*',
    }
  })
}

// 获取三色预警趋势
export const getWarningTrendApi = (params: WarningTrendParams): Promise<WarningTrendResponse> => {
  return warningRequest.post('/DCEventRYB/StatisticsByMonth', params, {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json, text/plain, */*',
    }
  })
}

// 获取三色预警统计
export const getWarningStatisticsApi = (params: WarningStatisticsParams): Promise<WarningStatisticsResponse> => {
  return warningRequest.post('/DCEventRYB/StatisticsByCommunity', params, {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json, text/plain, */*',
    }
  })
}

// 设备类型统计接口参数
export interface DeviceTypeStatisticsParams {
  areaCode: string
  deviceType?: string  // 设备类型，可选参数
}

// 设备信息项
export interface DeviceItem {
  Id: number
  DeviceSN: string
  DeviceName: string
  Position: string  // 格式: "经度,纬度"
  AreaName: string
  DeviceState: number  // 1为正常，其他为异常
  DeviceType: number
  DeviceTypeName: string
}

// 设备类型统计响应数据
export interface DeviceTypeStatisticsResponse {
  state: number
  message: {
    Count: number
    List: DeviceItem[]
  }
}

// 获取设备类型统计
export const getDeviceTypeStatisticsApi = (params: DeviceTypeStatisticsParams): Promise<DeviceTypeStatisticsResponse> => {
  const queryParams = new URLSearchParams()
  queryParams.append('areaCode', params.areaCode)

  // deviceType 如果有值且不为空字符串才添加到参数中
  if (params.deviceType && params.deviceType.trim() !== '') {
    queryParams.append('deviceType', params.deviceType)
  }
  // 如果 deviceType 为 null、undefined 或空字符串，则不传这个参数

  return warningRequest.get(`/DCEventRYB/GetDeviceTypeStatistics?${queryParams.toString()}`, {
    headers: {
      'Accept': 'application/json, text/plain, */*',
    }
  })
}