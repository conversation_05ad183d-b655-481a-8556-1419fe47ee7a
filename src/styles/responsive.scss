// 小屏幕小于2000函数计算px 转换位vw 保留3位小数
@function two($width) {
  @return floor(($width * 100 / 3840 * 1.6) * 1000) / 1000 * 1vw;
}

//大屏幕大于2000函数计算px 转换位vw 保留3位小数
@function one($width) {
  @return floor(($width * 100 / 3840) * 1000) / 1000 * 1vw;
}

// 响应式混合器
@mixin responsive-size($property, $value) {
  #{$property}: one($value);
  
  @media (max-width: 2000px) {
    #{$property}: two($value);
  }
}

@mixin responsive-font($size) {
  font-size: one($size);
  
  @media (max-width: 2000px) {
    font-size: two($size);
  }
}

@mixin responsive-spacing($property, $value) {
  #{$property}: one($value);
  
  @media (max-width: 2000px) {
    #{$property}: two($value);
  }
} 