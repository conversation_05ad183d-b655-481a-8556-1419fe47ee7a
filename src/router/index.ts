import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login', // 默认重定向到登录页面
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresAuth: false },
    },
    {
      path: '/change-password',
      name: 'changePassword',
      component: () => import('../views/ChangePasswordView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/bigscreen',
      name: 'bigscreen',
      component: () => import('../views/BigScreenView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/home',
      name: 'home',
      component: HomeView,
      meta: { requiresAuth: true },
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/test/heatmap',
      name: 'heatmapTest',
      component: () => import('../views/test/HeatMapTest.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/test/simple-heatmap',
      name: 'simpleHeatmapTest',
      component: () => import('../views/test/SimpleHeatMapTest.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/test/mapload',
      name: 'mapLoadTest',
      component: () => import('../views/test/MapLoadTest.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/test/quick-heatmap',
      name: 'quickHeatmapTest',
      component: () => import('../views/test/QuickHeatMapTest.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/test/community-life',
      name: 'communityLifeTest',
      component: () => import('../views/test/CommunityLifeTest.vue'),
      meta: { requiresAuth: true },
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  const isAuthenticated = !!token

  // 如果路由需要认证
  if (to.meta.requiresAuth) {
    if (isAuthenticated) {
      // 已登录，允许访问
      next()
    } else {
      // 未登录，重定向到登录页面
      next('/login')
    }
  } else {
    // 不需要认证的路由
    if (to.path === '/login' && isAuthenticated) {
      // 已登录用户访问登录页面，重定向到大屏页面
      next('/bigscreen')
    } else {
      next()
    }
  }
})

export default router
