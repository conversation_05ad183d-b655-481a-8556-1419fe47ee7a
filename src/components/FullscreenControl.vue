<template>
  <!-- <div class="fullscreen-control">
    <button 
      @click="toggleFullscreen" 
      :class="{ active: fullscreenStore.isFullscreen }"
    >
      {{ fullscreenStore.isFullscreen ? '退出全屏模式' : '进入全屏模式' }}
    </button>
    <div v-if="fullscreenStore.isFullscreen" class="fullscreen-info">
      <p>全屏页面: {{ fullscreenStore.fullscreenPage }}</p>
      <p>Left1显示页面: {{ fullscreenStore.getLeft1Page() }}</p>
      <p>Left2显示页面: {{ fullscreenStore.getLeft2Page() }}</p>
      <button @click="switchPage">切换全屏页面</button>
    </div>
    <div class="debug-info">
      <h4>调试信息:</h4>
      <p>全屏状态: {{ fullscreenStore.isFullscreen ? '是' : '否' }}</p>
      <p>Left1下一页按钮应该{{ fullscreenStore.isFullscreen ? '隐藏' : '显示' }}</p>
      <p>Left2上一页按钮应该{{ fullscreenStore.isFullscreen ? '隐藏' : '显示' }}</p>
    </div>
  </div> -->
</template>

<script setup lang="ts">
import { useFullscreenStore } from '@/stores/fullscreen'

const fullscreenStore = useFullscreenStore()

const toggleFullscreen = () => {
  fullscreenStore.setFullscreen(!fullscreenStore.isFullscreen)
  console.log('切换全屏状态:', fullscreenStore.isFullscreen)
  if (fullscreenStore.isFullscreen) {
    fullscreenStore.setFullscreenPage(1)
  }
}

const switchPage = () => {
  fullscreenStore.switchFullscreenPage()
  console.log('切换全屏页面:', fullscreenStore.fullscreenPage)
}
</script>

<style lang="scss" scoped>
.fullscreen-control {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  padding: 16px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  border: 1px solid #24324b;
  
  .control-btn {
    padding: 8px 16px;
    background: #166dc8;
    border: none;
    border-radius: 4px;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: #1890ff;
    }
    
    &.active {
      background: #52c41a;
    }
  }
  
  .fullscreen-info {
    margin-top: 12px;
    
    .info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 12px;
      color: #ffffff;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      span:first-child {
        color: rgba(255, 255, 255, 0.7);
        margin-right: 12px;
      }
      
      span:last-child {
        color: #166dc8;
        font-weight: bold;
      }
    }
  }
}

.debug-info {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #ccc;
  font-size: 12px;
}

.debug-info h4 {
  margin: 0 0 8px 0;
  color: #ffd700;
}

.debug-info p {
  margin: 4px 0;
}
</style> 