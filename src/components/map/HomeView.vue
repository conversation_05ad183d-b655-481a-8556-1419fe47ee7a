<script setup lang="ts">
import "mars3d-cesium/Build/Cesium/Widgets/widgets.css"
import "mars3d/mars3d.css"
import * as mars3d from "mars3d"
import {onMounted,ref,onUnmounted} from "vue";
import { queryArcgisLayer,addDemoGraphic,removeDemoGraphicById, back, eventTarget} from './hook'
import * as Cesium from 'mars3d-cesium'
import "./style.css"

let map: mars3d.Map
let graphicLayer: mars3d.layer.GraphicLayer
let heatMapLayer: mars3d.layer.GraphicLayer // 添加热力图图层

const color = "#02162d"

// 添加全局town_code状态
let currentTownCode: string = ''

interface PointData {
  id:number,
  title:string
  position:[number,number,number]
  originalData?: any // 保存原始数据
}

// 点位信息
const pointDataList=[
  {
    id:1,
    title:'邛崃市',
    position:[103.366654,30.410004, 781]
  },
  {
    id:2,
    title:'邛崃市1',
    position:[103.178597,30.349588, 781]
  },
  {
    id:3,
    title:'邛崃市2',
    position:[103.548922,30.361444, 781]
  },
  {
    id:4,
    title:'dian123123123',
    position:[103.318458,30.371272, 0]
  }
]


onMounted(async ()=>{
   map = new mars3d.Map("mars3dContainer", {
     scene: {
       center:{
        lng:103.412165,
        lat:30.386399,
        alt:45000,
        heading:0, 
        pitch: -90
       },
       showSun: false,
       showMoon: false,
       showSkyBox: false,
       showSkyAtmosphere: false,
       fog: false,
       backgroundColor: color, // 天空背景色
       globe: {
         baseColor: color, // 地球地面背景色
         showGroundAtmosphere: false,
         enableLighting: false
       }
     },
     control: {
       baseLayerPicker: false
     },
     basemaps: [
     {
      "name": "邛崃市",
      "type": "arcgis",
      "url": "https://api.cdmbc.cn:4432/gateway/gis/1/2e73507f614948998198c0a1cb47c5ea",
      "queryParameters": { "blankTile": false, "AppKey": "700023938926772224" },
      "crs": "EPSG:4326", 
      "show": true
    } 
     ],
   })
  // 创建DIV数据图层
  graphicLayer = new mars3d.layer.GraphicLayer()
  map.addLayer(graphicLayer)
  
  // 创建热力图图层
  heatMapLayer = new mars3d.layer.GraphicLayer()
  map.addLayer(heatMapLayer)
  
  // 首次加载区域
  changeLayer('8780d3715f054a0b948bb3a744d8ba34/0/query')

  // 只有在MapComponent不存在时才绑定全局函数，避免冲突
  if (!document.getElementById('mapContainer')) {
    console.log('🔗 HomeView: 绑定全局撒点函数和热力图函数')
    ;(window as any).addResourceMapPoints = addResourceMapPoints
    ;(window as any).clearResourceMapPoints = clearResourceMapPoints
    ;(window as any).addWarningResourceMapPoints = addWarningResourceMapPoints
    ;(window as any).addSuggestionResourceMapPoints = addSuggestionResourceMapPoints
    ;(window as any).addFamilyResourceMapPoints = addFamilyResourceMapPoints
    
    // 绑定热力图函数
    ;(window as any).addTotalHeatMap = addTotalHeatMap
    ;(window as any).addHeatMapData = addHeatMapData
    ;(window as any).clearHeatMap = clearHeatMap
    ;(window as any).addCustomHeatMapData = addCustomHeatMapData
    
    // 设置地图组件加载完成标志
    ;(window as any).mapComponentLoaded = true
    ;(window as any).mapFunctionsPreloaded = true
    
    console.log('🔗 HomeView: 全局函数绑定完成，包括热力图函数')
    
    // 添加调试函数到全局，方便控制台测试
    ;(window as any).debugTestHeatMapHomeView = () => {
      const testData = [
        {
          districtName: '邛崃市中心',
          districtCode: '510183001',
          longitude: '103.366654',
          latitude: '30.410004',
          total: 25
        },
        {
          districtName: '临邛镇',
          districtCode: '510183002',
          longitude: '103.378597',
          latitude: '30.349588',
          total: 18
        },
        {
          districtName: '羊安镇',
          districtCode: '510183003',
          longitude: '103.348922',
          latitude: '30.361444',
          total: 32
        }
      ]
      console.log('🧪 HomeView调试测试热力图，数据:', testData)
      if ((window as any).addTotalHeatMap) {
        ;(window as any).addTotalHeatMap(testData)
        console.log('✅ HomeView调试热力图测试成功')
      } else {
        console.error('❌ addTotalHeatMap函数不存在')
      }
    }
  } else {
    console.log('🔗 HomeView: 检测到MapComponent存在，跳过全局函数绑定')
  }

  // const fallbackLayer = new mars3d.layer.ArcGisLayer({
  //   url: "https://api.cdmbc.cn:4432/gateway/gis/1/0e759114ef1d492a873e727d4eb0de73",
  //   wkid: 4490,
  //   queryParameters:{
  //     AppKey:'700023938926772224',
  //     blankTile:'false'
  //   },
  //   show: true
  // });
  // map.addLayer(fallbackLayer);

  eventTarget.on("getInfo", function(info) {
    console.log("town_code:", info.town_code)
    
    // 当获取到town_code时，更新全局状态并触发接口重新请求
    if (info.town_code && info.town_code !== currentTownCode) {
      currentTownCode = info.town_code
      console.log('🗺️ 地图点击获取到新的town_code:', currentTownCode)
      
      // 更新localStorage中的communityUserInfo.districtCode
      updateCommunityUserInfoDistrictCode(currentTownCode)
      
      // 触发左右组件所有接口重新请求
      updateAllComponentsWithTownCode(currentTownCode)
    }
  })
})

// 更新localStorage中的communityUserInfo.districtCode
function updateCommunityUserInfoDistrictCode(townCode: string) {
  try {
    const communityUserInfoStr = localStorage.getItem('communityUserInfo')
    if (communityUserInfoStr) {
      const communityUserInfo = JSON.parse(communityUserInfoStr)
      
      // 保存原来的districtCode用于日志
      const oldDistrictCode = communityUserInfo.districtCode
      
      // 更新districtCode为新的town_code
      communityUserInfo.districtCode = townCode
      
      // 保存回localStorage
      localStorage.setItem('communityUserInfo', JSON.stringify(communityUserInfo))
      
      console.log('✅ 已更新localStorage中的districtCode:', {
        old: oldDistrictCode,
        new: townCode
      })
    } else {
      console.warn('⚠️ localStorage中没有找到communityUserInfo，无法更新districtCode')
    }
  } catch (error) {
    console.error('❌ 更新localStorage中的districtCode失败:', error)
  }
}

// 重置localStorage中的communityUserInfo.districtCode为默认值
function resetCommunityUserInfoDistrictCode() {
  try {
    const communityUserInfoStr = localStorage.getItem('communityUserInfo')
    if (communityUserInfoStr) {
      const communityUserInfo = JSON.parse(communityUserInfoStr)
      
      // 保存原来的districtCode用于日志
      const oldDistrictCode = communityUserInfo.districtCode
      
      // 重置districtCode为默认值510183
      communityUserInfo.districtCode = '510183'
      
      // 保存回localStorage
      localStorage.setItem('communityUserInfo', JSON.stringify(communityUserInfo))
      
      console.log('✅ 已重置localStorage中的districtCode为默认值:', {
        old: oldDistrictCode,
        new: '510183'
      })
    } else {
      console.warn('⚠️ localStorage中没有找到communityUserInfo，无法重置districtCode')
    }
  } catch (error) {
    console.error('❌ 重置localStorage中的districtCode失败:', error)
  }
}

// 触发所有组件接口重新请求的函数
function updateAllComponentsWithTownCode(townCode: string) {
  console.log('🔄 开始使用town_code更新所有组件接口:', townCode)
  
  // 触发Left1Component的接口更新
  if ((window as any).updateLeft1WithTownCode) {
    (window as any).updateLeft1WithTownCode(townCode)
  }
  
  // 触发Left2Component的接口更新
  if ((window as any).updateLeft2WithTownCode) {
    (window as any).updateLeft2WithTownCode(townCode)
  }
  
  // 触发Right1Component的接口更新
  if ((window as any).updateRight1WithTownCode) {
    (window as any).updateRight1WithTownCode(townCode)
  }
  
  // 触发Right2Component的接口更新
  if ((window as any).updateRight2WithTownCode) {
    (window as any).updateRight2WithTownCode(townCode)
  }
}

function changeLayer(id:string){
  queryArcgisLayer(id,map,clickMap)
  
  // 当点击"成都市-乡镇界"按钮时，恢复默认区域代码并刷新所有接口
  if (id === '8780d3715f054a0b948bb3a744d8ba34/0/query') {
    console.log('🔄 点击成都市-乡镇界，恢复默认区域代码')
    
    // 重置currentTownCode
    currentTownCode = ''
    
    // 恢复localStorage中的districtCode为默认值510183
    resetCommunityUserInfoDistrictCode()
    
    // 刷新所有组件接口，使用默认区域代码510183
    updateAllComponentsWithTownCode('510183')
  }
}
// 撒点
function addPoint(){
  pointDataList.forEach(item=>{
    const point=addDemoGraphic(graphicLayer,item,pointClickCallback)

  })
  // console.log(graphicLayer)
}

// 资源地图分布撒点
function addResourceMapPoints(resourceData: any) {
  console.log('开始资源地图分布撒点，数据:', resourceData)
  
  // 先清除现有的点位
  removeAllGraphic()
  
  // 转换数据格式为地图点位格式
  const resourcePoints = convertResourceDataToPoints(resourceData)

  // 确保图层已清空后再撒点
  if (graphicLayer && graphicLayer.graphics && graphicLayer.graphics.length > 0) {
    console.warn('⚠️ 图层未完全清空，强制再次清除')
    graphicLayer.clear()
  }

  // 撒点
  resourcePoints.forEach(point => {
    addDemoGraphic(graphicLayer, point, resourcePointClickCallback)
  })
  
  console.log(`已添加 ${resourcePoints.length} 个资源分布点位`)
  
  // 提示用户撒点完成
  if (resourcePoints.length > 0) {
    console.log(`✅ 资源地图分布撒点完成，共 ${resourcePoints.length} 个点位`)
  }
}

// 清除资源地图分布点位
function clearResourceMapPoints() {
  removeAllGraphic()
  console.log('✅ 已清除所有资源地图分布点位')
}

// 三色预警资源地图分布撒点
function addWarningResourceMapPoints(warningDeviceData: any) {
  // console.log('🎯 开始三色预警资源地图分布撒点，数据:', warningDeviceData)

  // 先清除现有的点位
  removeAllGraphic()

  // 转换设备数据为地图点位格式
  const warningPoints = convertWarningDeviceDataToPoints(warningDeviceData)
  console.log('🔄 数据转换完成，准备撒点:', warningPoints.length, '个点位')

  // 确保图层已清空后再撒点
  if (graphicLayer && graphicLayer.graphics && graphicLayer.graphics.length > 0) {
    console.warn('⚠️ 图层未完全清空，强制再次清除')
    graphicLayer.clear()
  }

  // 撒点
  let successCount = 0
  warningPoints.forEach((point, index) => {
    try {
      const graphic = addDemoGraphic(graphicLayer, point, warningDevicePointClickCallback)
      if (graphic) {
        successCount++
        // 只打印前5个点位的详细信息

      }
    } catch (error) {
      console.error(`❌ 添加点位${index + 1}失败:`, error, point)
    }
  })

  console.log(`📊 撒点结果: 尝试${warningPoints.length}个，成功${successCount}个`)

  // 检查图层中的实际点位数量
  if (graphicLayer && graphicLayer.graphics) {
    console.log(`🗺️ 图层中实际点位数量: ${graphicLayer.graphics.length}`)
  }

  // 提示用户撒点完成
  if (warningPoints.length > 0) {
    console.log(`✅ 三色预警资源地图分布撒点完成，共 ${successCount} 个点位`)
  } else {
    console.warn('⚠️ 没有有效的点位数据进行撒点')
  }
}

// 崃建言资源地图分布撒点
function addSuggestionResourceMapPoints(suggestionData: any) {
  console.log('💡 开始崃建言资源地图分布撒点，数据:', suggestionData)

  // 先清除现有的点位
  removeAllGraphic()

  // 转换崃建言数据为地图点位格式
  const suggestionPoints = convertSuggestionDataToPoints(suggestionData)
  console.log('🔄 数据转换完成，准备撒点:', suggestionPoints.length, '个点位')

  // 确保图层已清空后再撒点
  if (graphicLayer && graphicLayer.graphics && graphicLayer.graphics.length > 0) {
    console.warn('⚠️ 图层未完全清空，强制再次清除')
    graphicLayer.clear()
  }

  // 撒点
  let successCount = 0
  suggestionPoints.forEach((point, index) => {
    try {
      const graphic = addDemoGraphic(graphicLayer, point, suggestionPointClickCallback)
      if (graphic) {
        successCount++
      }
    } catch (error) {
      console.error(`❌ 添加崃建言点位${index + 1}失败:`, error, point)
    }
  })

  console.log(`📊 崃建言撒点结果: 尝试${suggestionPoints.length}个，成功${successCount}个`)

  // 检查图层中的实际点位数量
  if (graphicLayer && graphicLayer.graphics) {
    console.log(`🗺️ 图层中实际点位数量: ${graphicLayer.graphics.length}`)
  }

  // 提示用户撒点完成
  if (suggestionPoints.length > 0) {
    // console.log(`✅ 崃建言资源地图分布撒点完成，共 ${successCount} 个点位`)
  } else {
    console.warn('⚠️ 没有有效的崃建言点位数据进行撒点')
  }
}

// 家庭医生资源地图分布撒点
function addFamilyResourceMapPoints(familyData: any) {
  console.log('👨‍⚕️ 开始家庭医生资源地图分布撒点，数据:', familyData)

  // 先清除现有的点位
  removeAllGraphic()

  // 转换家庭医生数据为地图点位格式
  const familyPoints = convertFamilyDataToPoints(familyData)
  console.log('🔄 数据转换完成，准备撒点:', familyPoints.length, '个点位')

  // 确保图层已清空后再撒点
  if (graphicLayer && graphicLayer.graphics && graphicLayer.graphics.length > 0) {
    console.warn('⚠️ 图层未完全清空，强制再次清除')
    graphicLayer.clear()
  }

  // 撒点
  let successCount = 0
  familyPoints.forEach((point, index) => {
    try {
      const graphic = addDemoGraphic(graphicLayer, point, familyPointClickCallback)
      if (graphic) {
        successCount++
      }
    } catch (error) {
      console.error(`❌ 添加家庭医生点位${index + 1}失败:`, error, point)
    }
  })

  console.log(`📊 家庭医生撒点结果: 尝试${familyPoints.length}个，成功${successCount}个`)

  // 检查图层中的实际点位数量
  if (graphicLayer && graphicLayer.graphics) {
    console.log(`🗺️ 图层中实际点位数量: ${graphicLayer.graphics.length}`)
  }

  // 提示用户撒点完成
  if (familyPoints.length > 0) {
    console.log(`✅ 家庭医生资源地图分布撒点完成，共 ${successCount} 个点位`)
  } else {
    console.log('📊 家庭医生资源地图分布撒点完成，共 0 个点位')
  }
}

// 测试资源撒点功能
function testResourceMapPoints() {
  console.log('🧪 测试资源地图分布撒点功能')
  
  // 模拟API返回的测试数据
  const testData = [
    {
      id: 1,
      name: '社区服务中心',
      longitude: 103.366654,
      latitude: 30.410004,
      type: '服务设施',
      description: '提供各类社区服务'
    },
    {
      id: 2,
      name: '社区图书馆',
      longitude: 103.378597,
      latitude: 30.349588,
      type: '文化设施',
      description: '社区图书阅览服务'
    },
    {
      id: 3,
      name: '社区卫生站',
      longitude: 103.386654,
      latitude: 30.400004,
      type: '医疗设施',
      description: '基础医疗服务'
    },
    {
      id: 4,
      name: '社区活动中心',
      longitude: 103.356654,
      latitude: 30.420004,
      type: '活动设施',
      description: '社区活动场所'
    },
    {
      id: 5,
      name: '社区便民服务站',
      longitude: 103.396654,
      latitude: 30.390004,
      type: '便民设施',
      description: '便民服务设施'
    }
  ]
  
  // 调用撒点函数
  addResourceMapPoints(testData)
}

// 将资源数据转换为地图点位格式
function convertResourceDataToPoints(resourceData: any) {
  console.log('转换资源数据为地图点位格式:', resourceData)
  
  // 处理不同的数据结构
  let dataArray = []
  
  // 如果数据是对象，尝试找到数组部分
  if (resourceData && typeof resourceData === 'object') {
    if (Array.isArray(resourceData)) {
      dataArray = resourceData
    } else if (resourceData.data && Array.isArray(resourceData.data)) {
      dataArray = resourceData.data
    } else if (resourceData.list && Array.isArray(resourceData.list)) {
      dataArray = resourceData.list
    } else if (resourceData.points && Array.isArray(resourceData.points)) {
      dataArray = resourceData.points
    } else if (resourceData.locations && Array.isArray(resourceData.locations)) {
      dataArray = resourceData.locations
    } else {
      console.warn('未能识别的数据结构，尝试提取属性值')
      // 尝试提取对象的属性值
      const values = Object.values(resourceData)
      const arrayValue = values.find(v => Array.isArray(v))
      if (arrayValue) {
        dataArray = arrayValue as any[]
      }
    }
  }
  
  // 如果没有找到数组数据或数据为空，直接返回空数组，不使用默认测试数据
  if (!Array.isArray(dataArray) || dataArray.length === 0) {
    console.log('📊 资源数据格式不正确或为空，不添加任何点位')
    return []
  }
  
  // 转换数据格式
  return dataArray.map((item: any, index: number) => {
    // 尝试多种可能的字段名
    const title = item.name || item.title || item.resourceName || item.placeName || `资源点位${index + 1}`
    
    // 尝试多种可能的坐标字段名
    let longitude = item.longitude || item.lng || item.lon || item.x
    let latitude = item.latitude || item.lat || item.y
    
    // 如果没有坐标，使用默认坐标并添加随机偏移
    if (!longitude || !latitude) {
      longitude = 103.366654 + (Math.random() - 0.5) * 0.1
      latitude = 30.410004 + (Math.random() - 0.5) * 0.1
    }
    
    return {
      id: item.id || index + 1,
      title: title,
      position: [
        parseFloat(longitude),
        parseFloat(latitude),
        781
      ],
      // 保存原始数据以供点击回调使用
      originalData: item
    }
  })
}

// 将三色预警设备数据转换为地图点位格式
function convertWarningDeviceDataToPoints(deviceData: any) {
  // console.log('🔄 转换三色预警设备数据为地图点位格式:', deviceData)
  // console.log('📊 输入数据类型:', typeof deviceData, '是否为数组:', Array.isArray(deviceData))

  // 处理不同的数据结构
  let dataArray = []

  // 如果数据是数组，直接使用
  if (Array.isArray(deviceData)) {
    dataArray = deviceData
    //console.log('✅ 使用数组数据，长度:', dataArray.length)
  } else {
    console.log('📊 三色预警设备数据格式不正确或为空，不添加任何点位')
    return []
  }

  // 如果数据为空，直接返回空数组
  if (dataArray.length === 0) {
    console.log('📊 三色预警设备数据为空，不添加任何点位')
    return []
  }

  // 转换数据格式
  const convertedPoints = dataArray.map((device: any, index: number) => {
    // 直接使用longitude和latitude字段
    const parsedLng = parseFloat(device.longitude)
    const parsedLat = parseFloat(device.latitude)

    // 调试前几个点位的坐标


    // 根据设备状态确定点位颜色
    const deviceStateColor = device.deviceState === 1 ? '#00ff00' : '#ff0000'

    return {
      id: device.id || index + 1,
      title: device.name || `预警设备${index + 1}`,
      position: [
        parsedLng,
        parsedLat,
        781
      ],
      // 保存原始设备数据以供点击回调使用
      originalData: {
        ...device,
        deviceStateColor,
        deviceTypeName: device.deviceTypeName,
        areaName: device.areaName,
        deviceSN: device.deviceSN
      }
    }
  })

  // 过滤无效坐标
  const validPoints = convertedPoints.filter((point: any) => !isNaN(point.position[0]) && !isNaN(point.position[1]))

  console.log(`📈 数据转换结果: 原始${dataArray.length}条 → 转换${convertedPoints.length}条 → 有效${validPoints.length}条`)

  // 检查坐标分布情况
  if (validPoints.length > 0) {
    const lngRange = {
      min: Math.min(...validPoints.map(p => p.position[0])),
      max: Math.max(...validPoints.map(p => p.position[0]))
    }
    const latRange = {
      min: Math.min(...validPoints.map(p => p.position[1])),
      max: Math.max(...validPoints.map(p => p.position[1]))
    }
   
  }

  return validPoints
}

// 三色预警设备点位点击回调
function warningDevicePointClickCallback(data: any) {
  console.log("三色预警设备点位数据:", data)

  // 显示设备详细信息
  if (data.originalData) {
    console.log("原始设备数据:", data.originalData)

    // 可以在这里添加弹窗显示设备详细信息
    // 比如设备名称、设备类型、区域名称、设备状态等
    const deviceInfo = data.originalData
    console.log(`设备信息:
      设备名称: ${deviceInfo.DeviceName}
      设备类型: ${deviceInfo.DeviceTypeName}
      所属区域: ${deviceInfo.AreaName}
      设备状态: ${deviceInfo.DeviceState === 1 ? '正常' : '异常'}
      设备编号: ${deviceInfo.DeviceSN}
    `)
  }

  // 这里可以添加设备点位的具体处理逻辑
  // 比如显示设备详情弹窗、高亮选中的点位等
}

// 资源点位点击回调
function resourcePointClickCallback(data: any) {
  console.log("资源点位数据:", data)
  
  // 显示资源详细信息
  if (data.originalData) {
    console.log("原始资源数据:", data.originalData)
    
    // 可以在这里添加弹窗显示详细信息
    // 或者触发其他业务逻辑
  }
  
  // 这里可以添加资源点位的具体处理逻辑
  // 比如显示资源详情弹窗、高亮选中的点位等
}

// point回调  
function pointClickCallback(data:PointData){
  console.log("点位数据:", data)
}
function removeAllGraphic(){
  if (graphicLayer) {
    const beforeCount = graphicLayer.graphics ? graphicLayer.graphics.length : 0
    console.log(`🧹 准备清除点位，当前点位数量: ${beforeCount}`)

    graphicLayer.clear()

    const afterCount = graphicLayer.graphics ? graphicLayer.graphics.length : 0
    console.log(`✅ 点位清除完成，剩余点位数量: ${afterCount}`)

    if (afterCount > 0) {
      console.warn('⚠️ 警告：仍有点位未被清除')
    }
  } else {
    console.warn('⚠️ graphicLayer 未初始化，无法清除点位')
  }
}
// 点击行政区划回调
function clickMap(data:any){
  // 不需要处理，由eventTarget统一处理
}

// 转换崃建言数据为地图点位格式
function convertSuggestionDataToPoints(suggestionData: any[]): PointData[] {
  if (!Array.isArray(suggestionData)) {
    console.warn('⚠️ 崃建言数据不是数组格式:', suggestionData)
    return []
  }

  // 如果数据为空，直接返回空数组，不使用默认测试数据
  if (suggestionData.length === 0) {
    console.log('📊 崃建言数据为空，不添加任何点位')
    return []
  }

  return suggestionData
    .filter(item => {
      // 过滤掉没有坐标信息的数据
      const hasCoords = item.longitude && item.latitude &&
                       !isNaN(parseFloat(item.longitude)) &&
                       !isNaN(parseFloat(item.latitude))

      if (!hasCoords) {
        console.warn('⚠️ 崃建言数据缺少有效坐标:', item)
      }

      return hasCoords
    })
    .map((item, index) => {
      const longitude = parseFloat(item.longitude)
      const latitude = parseFloat(item.latitude)

      return {
        id: index + 1,
        title: `${item.name || '崃建言'} - ${item.districtName || '未知区域'}`,
        position: [longitude, latitude, 0] as [number, number, number],
        // 保存原始数据用于点击回调
        originalData: item
      }
    })
}

// 转换家庭医生数据为地图点位格式
function convertFamilyDataToPoints(familyData: any[]): PointData[] {
  if (!Array.isArray(familyData)) {
    console.warn('⚠️ 家庭医生数据不是数组格式:', familyData)
    return []
  }

  // 如果数据为空，直接返回空数组，不使用默认测试数据
  if (familyData.length === 0) {
    console.log('📊 家庭医生数据为空，不添加任何点位')
    return []
  }

  return familyData
    .filter(item => {
      // 过滤掉没有坐标信息的数据
      const hasCoords = item.longitude && item.latitude &&
                       !isNaN(parseFloat(item.longitude)) &&
                       !isNaN(parseFloat(item.latitude))

      if (!hasCoords) {
        console.warn('⚠️ 家庭医生数据缺少有效坐标:', item)
      }

      return hasCoords
    })
    .map((item, index) => {
      const longitude = parseFloat(item.longitude)
      const latitude = parseFloat(item.latitude)

      return {
        id: index + 1000, // 使用不同的ID范围避免冲突
        title: `${item.doctorName || item.name || '家庭医生'} - ${item.districtName || '未知区域'}`,
        position: [longitude, latitude, 0] as [number, number, number],
        // 保存原始数据用于点击回调
        originalData: item
      }
    })
}

// 崃建言点位点击回调
function suggestionPointClickCallback(pointData: any) {
  console.log('🖱️ 点击崃建言点位:', pointData)

  const originalData = pointData.originalData
  if (originalData) {
    console.log('📋 崃建言详细信息:', {
      名称: originalData.name,
      区域: originalData.districtName,
      区域代码: originalData.districtCode,
      经度: originalData.longitude,
      纬度: originalData.latitude,
      创建时间: originalData.createTime,
      总数: originalData.total
    })
  }
}

// 家庭医生点位点击回调
function familyPointClickCallback(pointData: any) {
  console.log('🖱️ 点击家庭医生点位:', pointData)

  const originalData = pointData.originalData
  if (originalData) {
    console.log('👨‍⚕️ 家庭医生详细信息:', {
      医生姓名: originalData.doctorName || originalData.name,
      区域: originalData.districtName,
      区域代码: originalData.districtCode,
      经度: originalData.longitude,
      纬度: originalData.latitude,
      创建时间: originalData.createTime,
      总数: originalData.total
    })
  }
}

// 热力图相关函数

// 添加基于total数据的热力图显示
function addTotalHeatMap(data: Array<{
  districtName: string
  districtCode?: string
  longitude: string
  latitude: string
  total: number
}>) {
  console.log('🔥 HomeView addTotalHeatMap 被调用，数据:', data)

  if (!heatMapLayer || !map) {
    console.error('❌ 热力图图层或地图未初始化', {
      heatMapLayer: !!heatMapLayer,
      map: !!map
    })
    return
  }

  // 清空现有热力图数据
  heatMapLayer.clear()

  if (!data || data.length === 0) {
    console.warn('⚠️ 没有热力图数据')
    return
  }

  console.log(`🔥 开始添加热力图，共 ${data.length} 个数据点`)

  // 计算数据范围，用于确定圆圈大小和颜色
  const totals = data.map(item => item.total)
  const maxTotal = Math.max(...totals)
  const minTotal = Math.min(...totals)

  console.log(`📊 数据范围: ${minTotal} - ${maxTotal}`)

  data.forEach((item, index) => {
    const longitude = parseFloat(item.longitude)
    const latitude = parseFloat(item.latitude)

    // 跳过无效坐标
    if (isNaN(longitude) || isNaN(latitude)) {
      console.warn(`⚠️ 跳过无效坐标: ${item.districtName}`, item)
      return
    }

    // 根据total数量计算圆圈大小和颜色强度
    const normalizedValue = maxTotal > minTotal ? (item.total - minTotal) / (maxTotal - minTotal) : 0.5
    const radius = 20 + normalizedValue * 40 // 20-60像素范围
    const opacity = 0.4 + normalizedValue * 0.4 // 0.4-0.8透明度范围

    // 根据数量确定颜色（红色系热力图）
    let color = '#ff4444' // 默认红色
    if (normalizedValue < 0.3) {
      color = '#ffaa00' // 橙色（低值）
    } else if (normalizedValue < 0.7) {
      color = '#ff6600' // 深橙色（中值）
    } else {
      color = '#ff0000' // 红色（高值）
    }

    // 创建热力图圆圈
    const circleGraphic = new mars3d.graphic.CircleEntity({
      position: [longitude, latitude, 0],
      style: {
        radius: radius,
        color: color,
        opacity: opacity,
        outline: true,
        outlineColor: color,
        outlineOpacity: 0.9,
        outlineWidth: 2,
        clampToGround: true
      },
      attr: {
        districtName: item.districtName,
        districtCode: item.districtCode || '',
        total: item.total,
        type: 'total-heatmap'
      }
    })

    // 使用 DivGraphic 创建带红色背景的文本标签
    const labelGraphic = new mars3d.graphic.DivGraphic({
      position: [longitude, latitude, 0],
      style: {
        html: `<div style="
          background-color: #ff0000; 
          color: #ffffff; 
          padding: 8px; 
          border-radius: 50%; 
          font-size: 12px; 
          font-weight: bold; 
          text-align: center;
          border: 2px solid #000000;
          box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 1;
        ">${item.total}</div>`,
        offsetX: -10, // 水平偏移
        offsetY: -10, // 垂直偏移
        clampToGround: true
      },
      attr: {
        districtName: item.districtName,
        total: item.total,
        type: 'total-heatmap-label'
      }
    })

    // 绑定点击事件显示详细信息
    circleGraphic.on(mars3d.EventType.click, () => {
      console.log(`🔥 点击热力图区域: ${item.districtName}, 数量: ${item.total}`)
    })

    heatMapLayer.addGraphic(circleGraphic)
    heatMapLayer.addGraphic(labelGraphic)
  })

  console.log(`✅ 热力图添加完成，共 ${data.length} 个热力点`)
}

// 添加通用热力图数据
function addHeatMapData(data: any) {
  console.log('🔥 HomeView addHeatMapData 被调用，数据:', data)
  // 转换为addTotalHeatMap格式
  if (Array.isArray(data)) {
    const convertedData = data.map(item => ({
      districtName: item.districtName || item.name || '未知区域',
      districtCode: item.districtCode || item.code || '',
      longitude: item.longitude || '0',
      latitude: item.latitude || '0',
      total: item.count || item.total || item.number || 1
    }))
    addTotalHeatMap(convertedData)
  }
}

// 清除热力图
function clearHeatMap() {
  console.log('🧹 HomeView 清除热力图')
  if (heatMapLayer) {
    heatMapLayer.clear()
  }
}

// 自定义热力图数据
function addCustomHeatMapData(data: any) {
  console.log('🔥 HomeView addCustomHeatMapData 被调用，数据:', data)
  addHeatMapData(data)
}

// 组件卸载时清理
onUnmounted(() => {
  // 清理全局函数绑定
  if ((window as any).addResourceMapPoints) {
    delete (window as any).addResourceMapPoints
  }
  if ((window as any).clearResourceMapPoints) {
    delete (window as any).clearResourceMapPoints
  }
  if ((window as any).addWarningResourceMapPoints) {
    delete (window as any).addWarningResourceMapPoints
  }
  if ((window as any).addSuggestionResourceMapPoints) {
    delete (window as any).addSuggestionResourceMapPoints
  }
  if ((window as any).addFamilyResourceMapPoints) {
    delete (window as any).addFamilyResourceMapPoints
  }
  
  // 清理热力图函数
  if ((window as any).addTotalHeatMap) {
    delete (window as any).addTotalHeatMap
  }
  if ((window as any).addHeatMapData) {
    delete (window as any).addHeatMapData
  }
  if ((window as any).clearHeatMap) {
    delete (window as any).clearHeatMap
  }
  if ((window as any).addCustomHeatMapData) {
    delete (window as any).addCustomHeatMapData
  }
  if ((window as any).mapComponentLoaded) {
    delete (window as any).mapComponentLoaded
  }
  if ((window as any).mapFunctionsPreloaded) {
    delete (window as any).mapFunctionsPreloaded
  }
})


</script>

<template>
  <div style="height: 100%;">
    <div id="mars3dContainer" class="mars3d-container"></div>
    <div class="mb-4">
      <!-- <el-button type="primary" @click="changeLayer('5e249977ce1b454eab17999b9122871e/0/query')">成都市-区县界</el-button> -->
      <el-button type="primary" @click="changeLayer('8780d3715f054a0b948bb3a744d8ba34/0/query')">邛崃市-乡镇界</el-button>
      <!-- <el-button type="primary" @click="changeLayer('5f651de6590d4583ad2943027c86cb9e/0/query')">成都市-村社区界</el-button> -->
      <!-- <el-button type="primary" @click="changeLayer('61e0f08c05244354bd6baeaa5cec4bb9/0/query')">成都市-管理网格</el-button> -->
      <!-- <el-button type="primary" @click="changeLayer('ea77647c00c64bd782cb38799e2b88dd/2/query')">成都市-四级2</el-button> -->
      <!-- <el-button type="primary" @click="changeLayer('ea77647c00c64bd782cb38799e2b88dd/3/query')">成都市-四级3</el-button> -->
      <!-- <el-button type="primary" @click="changeLayer('ea77647c00c64bd782cb38799e2b88dd/4/query')">成都市-四级4</el-button> -->
      <!-- <el-button type="primary" @click="addPoint()">撒点</el-button> -->
      <!-- <el-button type="primary" @click="removeDemoGraphicById(graphicLayer,2)">删除邛崃1</el-button> -->
      <!-- <el-button type="primary" @click="removeAllGraphic">点位全部删除</el-button> -->
      <el-button type="primary" @click="back()">返回上一级</el-button>
      <!-- <el-button type="success" @click="testResourceMapPoints()">测试资源撒点</el-button> -->
      <el-button type="danger" @click="clearHeatMap()">清除热力分布</el-button>
      <el-button type="warning" @click="clearResourceMapPoints()">清除资源点位</el-button>
    </div>


  </div>

</template>
<style>
.mars3d-container{
  width: 100%;
  height: 100%;
}
.mb-4{
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
}
</style>
