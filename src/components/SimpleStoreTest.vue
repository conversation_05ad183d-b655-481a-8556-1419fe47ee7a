<template>
  <div style="padding: 20px; border: 2px solid #409eff; margin: 20px;">
    <h4>🧪 简单测试 - 社区好物概览</h4>
    
    <div style="margin: 10px 0;">
      <label>时间类型：</label>
      <select v-model="timeType" @change="onTimeChange" style="margin-left: 10px;">
        <option value="0">今日</option>
        <option value="1">近一月</option>
        <option value="2">近一年</option>
      </select>
    </div>

    <div style="margin: 10px 0;">
      <label>区域编码：</label>
      <input v-model="areaCode" style="margin-left: 10px;" />
    </div>

    <div style="margin: 10px 0;">
      <button @click="callApi" :disabled="loading">
        {{ loading ? '请求中...' : '调用API' }}
      </button>
    </div>

    <div style="margin: 10px 0; background: #f5f5f5; padding: 10px; border-radius: 4px;">
      <strong>调试信息：</strong>
      <div id="debug-info" style="font-family: monospace; font-size: 12px; white-space: pre-wrap;"></div>
    </div>

    <div v-if="result" style="margin: 10px 0; background: #e7f3ff; padding: 10px; border-radius: 4px;">
      <strong>API返回结果：</strong>
      <pre>{{ JSON.stringify(result, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getStoreInfoApi, type StoreInfoParams } from '@/api/suggestion'

const timeType = ref(0)
const areaCode = ref('510100')
const loading = ref(false)
const result = ref<any>(null)

// 添加调试信息到页面
const addDebugInfo = (info: string) => {
  const debugElement = document.getElementById('debug-info')
  if (debugElement) {
    debugElement.textContent += new Date().toLocaleTimeString() + ': ' + info + '\n'
  }
  console.log('🧪', info)
}

// 时间类型改变处理
const onTimeChange = (event: Event) => {
  const target = event.target as HTMLSelectElement
  const newValue = parseInt(target.value)
  addDebugInfo(`时间类型改变: ${newValue}`)
  addDebugInfo(`准备自动调用API...`)
  callApi()
}

// 调用API
const callApi = async () => {
  addDebugInfo(`开始调用API`)
  addDebugInfo(`区域编码: ${areaCode.value}`)
  addDebugInfo(`时间类型: ${timeType.value}`)
  
  if (!areaCode.value) {
    addDebugInfo('❌ 区域编码为空')
    return
  }

  loading.value = true
  result.value = null

  try {
    const params: StoreInfoParams = {
      areaCode: areaCode.value,
      timeType: timeType.value
    }
    
    addDebugInfo(`发送请求参数: ${JSON.stringify(params)}`)
    
    const response = await getStoreInfoApi(params)
    
    addDebugInfo(`收到响应: ${JSON.stringify(response)}`)
    
    result.value = response
    
    if (response.code === 200) {
      addDebugInfo('✅ API调用成功')
    } else {
      addDebugInfo(`❌ API返回错误: ${response.msg}`)
    }
    
  } catch (error: any) {
    addDebugInfo(`❌ API调用异常: ${error.message || error}`)
    console.error('API调用异常:', error)
  } finally {
    loading.value = false
  }
}

// 组件挂载时调用一次
import { onMounted } from 'vue'
onMounted(() => {
  addDebugInfo('组件已挂载，准备初始化调用API')
  callApi()
})
</script> 