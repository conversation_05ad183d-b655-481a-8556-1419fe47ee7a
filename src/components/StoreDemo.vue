<template>
  <div class="store-demo">
    <h2>社区好物概览组件演示</h2>
    
    <div class="demo-section">
      <h3>基础用法</h3>
      <CommunityStore 
        :area-code="currentAreaCode" 
        @refresh="handleRefresh"
      />
    </div>

    <div class="demo-section">
      <h3>自动刷新模式</h3>
      <CommunityStore 
        :area-code="currentAreaCode"
        :auto-refresh="true"
        :refresh-interval="60"
      />
    </div>

    <div class="demo-controls">
      <h3>控制面板</h3>
      <el-form inline>
        <el-form-item label="区域编码:">
          <el-input 
            v-model="currentAreaCode" 
            placeholder="请输入区域编码"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="updateAreaCode">更新区域</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import CommunityStore from './CommunityStore.vue'

// 当前区域编码
const currentAreaCode = ref('510100')

// 处理刷新事件
const handleRefresh = () => {
  ElMessage.success('数据已刷新')
}

// 更新区域编码
const updateAreaCode = () => {
  if (!currentAreaCode.value.trim()) {
    ElMessage.warning('请输入有效的区域编码')
    return
  }
  ElMessage.info('区域编码已更新')
}
</script>

<style scoped lang="scss">
.store-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    color: #2c3e50;
    margin-bottom: 30px;
    text-align: center;
  }

  .demo-section {
    margin-bottom: 40px;

    h3 {
      color: #34495e;
      margin-bottom: 15px;
      padding-bottom: 5px;
      border-bottom: 2px solid #3498db;
    }
  }

  .demo-controls {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;

    h3 {
      margin-top: 0;
      color: #495057;
    }
  }
}
</style> 