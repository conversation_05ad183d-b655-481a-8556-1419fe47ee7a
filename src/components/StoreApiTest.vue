<template>
  <div style="padding: 20px; background: #f0f2f5; border-radius: 8px;">
    <h3>🔧 社区好物概览接口测试</h3>
    
    <div style="background: white; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
      <h4>📋 接口信息</h4>
      <ul>
        <li><strong>接口地址:</strong> POST /prod-api/cockpit/statistics/storeInfo</li>
        <li><strong>参数:</strong> areaCode (区域编码), timeType (时间类型)</li>
        <li><strong>期望返回:</strong> {"code":200,"msg":"操作成功","data":null,"message":{"goodsCount":5,"viewCount":12}}</li>
      </ul>
    </div>

    <div style="background: white; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
      <h4>🎛️ 测试参数</h4>
      <div style="margin-bottom: 10px;">
        <label>区域编码: </label>
        <input v-model="testAreaCode" placeholder="请输入区域编码" style="width: 200px; padding: 5px;" />
      </div>
      <div style="margin-bottom: 10px;">
        <label>时间类型: </label>
        <select v-model="testTimeType" style="width: 150px; padding: 5px;">
          <option :value="0">0 - 今日</option>
          <option :value="1">1 - 近一月</option>
          <option :value="2">2 - 近一年</option>
        </select>
      </div>
      <button @click="testApiCall" :disabled="isLoading" style="padding: 8px 16px; background: #409eff; color: white; border: none; border-radius: 4px;">
        {{ isLoading ? '测试中...' : '🚀 测试接口' }}
      </button>
    </div>

    <div v-if="debugLogs.length > 0" style="background: #f5f5f5; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
      <h4>📝 调试日志</h4>
      <div style="font-family: 'Courier New', monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
        <div v-for="(log, index) in debugLogs" :key="index" :style="{ color: log.startsWith('❌') ? '#f56c6c' : log.startsWith('✅') ? '#67c23a' : '#606266' }">
          {{ log }}
        </div>
      </div>
    </div>

    <div v-if="apiResponse" style="background: white; padding: 15px; border-radius: 6px;">
      <h4>📊 接口返回结果</h4>
      <pre style="background: #f8f8f8; padding: 10px; border-radius: 4px; overflow-x: auto;">{{ JSON.stringify(apiResponse, null, 2) }}</pre>
      
      <div v-if="apiResponse.code === 200" style="margin-top: 10px; padding: 10px; background: #f0f9ff; border-left: 4px solid #409eff;">
        <strong>✅ 解析结果:</strong>
        <div>商品数量: {{ apiResponse.message?.goodsCount || 'N/A' }}</div>
        <div>浏览次数: {{ apiResponse.message?.viewCount || 'N/A' }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getStoreInfoApi, type StoreInfoParams } from '@/api/suggestion'

const testAreaCode = ref('510100')
const testTimeType = ref(0)
const isLoading = ref(false)
const debugLogs = ref<string[]>([])
const apiResponse = ref<any>(null)

const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  debugLogs.value.push(`[${timestamp}] ${message}`)
  console.log(`🔧 ${message}`)
}

const testApiCall = async () => {
  // 清空之前的结果
  debugLogs.value = []
  apiResponse.value = null
  isLoading.value = true

  addLog('🚀 开始测试社区好物概览接口')
  addLog(`📋 测试参数: areaCode="${testAreaCode.value}", timeType=${testTimeType.value}`)

  try {
    // 构建请求参数
    const params: StoreInfoParams = {
      areaCode: testAreaCode.value,
      timeType: testTimeType.value
    }

    addLog(`📤 发送请求参数: ${JSON.stringify(params)}`)
    
    // 调用接口
    const response = await getStoreInfoApi(params)
    
    addLog(`📥 收到响应: ${JSON.stringify(response)}`)
    apiResponse.value = response

    // 检查响应状态
    if (response.code === 200) {
      addLog('✅ 接口调用成功')
      if (response.message) {
        addLog(`✅ 数据解析成功: 商品数量=${response.message.goodsCount}, 浏览次数=${response.message.viewCount}`)
      }
    } else {
      addLog(`❌ 接口返回错误: 状态码=${response.code}, 错误信息="${response.msg}"`)
    }

  } catch (error: any) {
    addLog(`❌ 接口调用异常: ${error.message || error}`)
    
    // 详细错误信息
    if (error.response) {
      addLog(`❌ HTTP错误: 状态码=${error.response.status}`)
      addLog(`❌ 错误数据: ${JSON.stringify(error.response.data)}`)
    }
    
    console.error('接口测试异常:', error)
  } finally {
    isLoading.value = false
    addLog('🔚 接口测试完成')
  }
}

// 页面加载时自动测试一次
import { onMounted } from 'vue'
onMounted(() => {
  addLog('🎯 组件已加载，准备自动测试接口')
  setTimeout(() => {
    testApiCall()
  }, 1000)
})
</script> 