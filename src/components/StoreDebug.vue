<template>
  <div class="store-debug">
    <h3>社区好物概览 - 调试工具</h3>
    
    <el-card>
      <template #header>
        <div class="card-header">
          <span>接口测试</span>
        </div>
      </template>
      
      <el-form inline>
        <el-form-item label="区域编码:">
          <el-input v-model="testParams.areacode" placeholder="请输入区域编码" style="width: 200px" />
        </el-form-item>
        <el-form-item label="时间类型:">
          <el-select v-model="testParams.timetype" style="width: 150px">
            <el-option label="今日" :value="0" />
            <el-option label="近一月" :value="1" />
            <el-option label="近一年" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testApi" :loading="loading">
            测试接口
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card style="margin-top: 20px;" v-if="apiResult">
      <template #header>
        <div class="card-header">
          <span>接口返回结果</span>
        </div>
      </template>
      
      <div class="result-section">
        <h4>原始返回数据:</h4>
        <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
      </div>
    </el-card>

    <el-card style="margin-top: 20px;" v-if="error">
      <template #header>
        <div class="card-header">
          <span style="color: #f56c6c;">错误信息</span>
        </div>
      </template>
      
      <div class="error-section">
        <p><strong>错误详情:</strong></p>
        <pre style="color: #f56c6c;">{{ error }}</pre>
      </div>
    </el-card>

    <!-- 网络请求详情 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>网络请求详情</span>
        </div>
      </template>
      
      <div class="network-info">
        <p><strong>请求URL:</strong> POST /prod-api/cockpit/statistics/storeInfo</p>
        <p><strong>请求参数:</strong></p>
        <pre>{{ JSON.stringify(testParams, null, 2) }}</pre>
        <p><strong>期望返回格式:</strong></p>
        <pre>{
  "code": 200,
  "msg": "操作成功",
  "data": null,
  "message": {
    "goodsCount": 5,
    "viewCount": 12
  }
}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getStoreInfoApi, type StoreInfoParams } from '@/api'

// 测试参数
const testParams = ref<StoreInfoParams>({
  areacode: '510100',
  timetype: 0
})

// 响应式数据
const loading = ref(false)
const apiResult = ref<any>(null)
const error = ref<string>('')

// 测试API接口
const testApi = async () => {
  if (!testParams.value.areacode.trim()) {
    ElMessage.warning('请输入区域编码')
    return
  }

  loading.value = true
  apiResult.value = null
  error.value = ''

  console.log('🧪 开始测试API接口')
  console.log('🧪 请求参数:', testParams.value)

  try {
    const response = await getStoreInfoApi(testParams.value)
    
    console.log('🧪 API返回原始数据:', response)
    apiResult.value = response

    // 检查返回的数据结构
    if (response.data) {
      console.log('✅ response.data 存在:', response.data)
      if (response.data.code === 200) {
        console.log('✅ 接口调用成功')
        ElMessage.success('接口调用成功！')
      } else {
        console.log('❌ 接口返回错误码:', response.data.code)
        ElMessage.error(`接口返回错误: ${response.data.msg}`)
      }
    } else {
      console.log('⚠️  response.data 不存在，直接检查 response')
      if (response.code === 200) {
        console.log('✅ 接口调用成功（直接返回格式）')
        ElMessage.success('接口调用成功！')
      } else {
        console.log('❌ 接口返回错误码:', response.code)
        ElMessage.error(`接口返回错误: ${(response as any).msg || '未知错误'}`)
      }
    }

  } catch (err: any) {
    console.error('🧪 API调用失败:', err)
    error.value = err.message || err.toString()
    
    if (err.response) {
      console.error('🧪 HTTP错误响应:', err.response)
      error.value += `\n\n状态码: ${err.response.status}\n响应数据: ${JSON.stringify(err.response.data, null, 2)}`
    }
    
    ElMessage.error('接口调用失败，请查看错误详情')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.store-debug {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;

  h3 {
    color: #2c3e50;
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
  }

  .result-section,
  .error-section,
  .network-info {
    h4 {
      color: #34495e;
      margin-bottom: 10px;
    }

    pre {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      border: 1px solid #ddd;
      overflow-x: auto;
      font-size: 12px;
      line-height: 1.4;
    }

    p {
      margin: 8px 0;
      
      strong {
        color: #2c3e50;
      }
    }
  }

  .error-section pre {
    background: #fef2f2;
    border-color: #fecaca;
  }
}
</style> 