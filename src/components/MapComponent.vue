<template>
  <div class="map-container">
    <!-- 地图容器 -->
    <div id="mapContainer" class="map-wrapper"></div>
    
    <!-- 控制按钮 -->
          <div class="map-controls">
        <el-button-group>
          <el-button 
            type="primary" 
            @click="loadLayer('town')"
            :class="{ 'active-layer': currentLayer === 'town' }"
          >乡镇界</el-button>
  
        </el-button-group>
        
        <el-button-group>
          <!-- <el-button type="success" @click="addSamplePoints">添加点位</el-button> -->
          <!-- <el-button type="warning" @click="clearPoints">清空点位</el-button> -->
          <el-button type="info" @click="goBack">返回上级</el-button>
        </el-button-group>
        
        <!-- 当前图层信息 -->
        <div class="layer-info">
          <span class="layer-label">当前图层:</span>
          <span class="layer-name">{{ getLayerName(currentLayer) }}</span>
        </div>
      </div>

    <!-- 区域信息弹窗 -->
    <el-dialog v-model="areaDialogVisible" title="区域信息" width="500px">
      <div class="area-info">
        <p v-for="(value, key) in areaInfo" :key="key">
          <strong>{{ key }}:</strong> {{ value }}
        </p>
      </div>
      <template #footer>
        <el-button @click="areaDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 点位信息弹窗 -->
    <el-dialog v-model="pointDialogVisible" title="点位信息" width="400px">
      <div class="point-info">
        <p><strong>ID:</strong> {{ selectedPoint?.id }}</p>
        <p><strong>名称:</strong> {{ selectedPoint?.title }}</p>
        <p><strong>坐标:</strong> {{ selectedPoint?.position?.join(', ') }}</p>
      </div>
      <template #footer>
        <el-button @click="pointDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { MapUtils, type PointData, type MapConfig } from '@/utils/map-utils'

// 响应式数据
const areaDialogVisible = ref(false)
const pointDialogVisible = ref(false)
const areaInfo = ref<any>({})
const selectedPoint = ref<PointData | null>(null)
const currentLayer = ref<string>('town') // 当前活跃的图层

// 地图实例
let mapUtils: MapUtils | null = null

// 示例点位数据
const samplePoints: PointData[] = [
  { id: 1, title: '邛崃市', position: [103.366654, 30.410004, 781] },
  { id: 2, title: '邛崃市1', position: [103.178597, 30.349588, 781] },
  { id: 3, title: '邛崃市2', position: [103.548922, 30.361444, 781] },
  { id: 4, title: '测试点', position: [103.318458, 30.371272, 0] }
]

// 图层映射
const layerMappings = {
  district: '5e249977ce1b454eab17999b9122871e/0/query',
  town: '8780d3715f054a0b948bb3a744d8ba34/0/query',
  village: '5f651de6590d4583ad2943027c86cb9e/0/query',
  grid: '61e0f08c05244354bd6baeaa5cec4bb9/0/query'
}



// 热力图数据转换函数 - 复制 convertDataToPoints 但显示数值
const convertHeatMapDataToPoints = (data: any): PointData[] => {
  if (!data) return []

  let dataArray: any[] = []

  // 处理不同的数据格式 - 和 convertDataToPoints 完全一样
  if (Array.isArray(data)) {
    dataArray = data
  } else if (data && typeof data === 'object') {
    // 尝试从对象中提取数组
    const values = Object.values(data)
    const arrayValue = values.find(v => Array.isArray(v))
    if (arrayValue) {
      dataArray = arrayValue as any[]
    }
  }

  if (!Array.isArray(dataArray) || dataArray.length === 0) {
    return []
  }

  // 转换为点位格式 - 和 convertDataToPoints 几乎一样，但 title 显示数值
  return dataArray
    .filter(item => {
      // 过滤掉没有坐标信息的数据
      const hasCoords = (item.longitude || item.lng) && (item.latitude || item.lat) &&
                       !isNaN(parseFloat(item.longitude || item.lng)) &&
                       !isNaN(parseFloat(item.latitude || item.lat))
      return hasCoords
    })
    .map((item, index) => {
      const longitude = parseFloat(item.longitude || item.lng || 0)
      const latitude = parseFloat(item.latitude || item.lat || 0)
      const total = item.total || item.count || 0

      return {
        id: index + 1,
        title: total.toString(), // 关键差异：显示数值而不是名称
        position: [longitude, latitude, 0] as [number, number, number]
      }
    })
}

// 热力图撒点 - 完全复制 addResourceMapPoints 的成功模式
const addTotalHeatMapPoints = (heatMapData: any) => {
  if (!mapUtils) {
    console.error('❌ MapComponent: mapUtils未初始化')
    return
  }
  console.log('🔥 MapComponent: 开始热力图处理，数据:', heatMapData)

  // 调用MapUtils的热力图方法而不是撒点方法
  mapUtils.addTotalHeatMap(heatMapData)
  console.log(`✅ MapComponent: 热力图处理完成`)
}

// 热力图函数将在 onMounted 中绑定，确保 mapUtils 已初始化

// 提前绑定全局函数，确保其他组件能够访问
const bindGlobalFunctions = () => {
  console.log('🔗 提前绑定全局函数...')

  ;(window as any).addResourceMapPoints = addResourceMapPoints
  ;(window as any).clearResourceMapPoints = clearResourceMapPoints
  ;(window as any).addWarningResourceMapPoints = addWarningResourceMapPoints
  ;(window as any).addSuggestionResourceMapPoints = addSuggestionResourceMapPoints
  ;(window as any).addFamilyResourceMapPoints = addFamilyResourceMapPoints
  ;(window as any).addHeatMapData = addHeatMapData
  ;(window as any).clearHeatMap = clearHeatMap
  ;(window as any).addCustomHeatMapData = addCustomHeatMapData
  ;(window as any).addTotalHeatMap = addTotalHeatMapPoints

  // 设置预加载标志
  ;(window as any).mapFunctionsPreloaded = true

  console.log('🔗 全局函数预绑定完成:', {
    addTotalHeatMap: typeof (window as any).addTotalHeatMap,
    addHeatMapData: typeof (window as any).addHeatMapData,
    mapFunctionsPreloaded: (window as any).mapFunctionsPreloaded
  })
}

// 注意：bindGlobalFunctions() 将在所有函数定义完成后调用

// 初始化地图
onMounted(async () => {
  const config: MapConfig = {
    containerId: 'mapContainer',
    backgroundColor: '#02162d',
    basemaps: [
      {
        name: "邛崃市",
        type: "arcgis",
        url: "https://api.cdmbc.cn:4432/gateway/gis/1/2e73507f614948998198c0a1cb47c5ea",
        queryParameters: { blankTile: false, AppKey: "700023938926772224" },
        crs: "EPSG:4326",
        show: true
      }
    ],
    onAreaClick: (data) => {
      areaInfo.value = data
      areaDialogVisible.value = true
    },
    onPointClick: (data) => {
      selectedPoint.value = data
      pointDialogVisible.value = true
    }
  }

  mapUtils = new MapUtils(config)
  await mapUtils.initMap()

  // 将mapUtils实例绑定到全局，供热力图函数使用
  ;(window as any).mapUtilsInstance = mapUtils

  console.log('🗺️ 地图初始化完成')

  // 立即绑定全局函数，供其他组件调用 - 参考 HomeView.vue 的绑定方式
  ;(window as any).addResourceMapPoints = addResourceMapPoints
  ;(window as any).clearResourceMapPoints = clearResourceMapPoints
  ;(window as any).addWarningResourceMapPoints = addWarningResourceMapPoints
  ;(window as any).addSuggestionResourceMapPoints = addSuggestionResourceMapPoints
  ;(window as any).addFamilyResourceMapPoints = addFamilyResourceMapPoints
  ;(window as any).addHeatMapData = addHeatMapData
  ;(window as any).clearHeatMap = clearHeatMap
  ;(window as any).addCustomHeatMapData = addCustomHeatMapData

  // 绑定热力图函数 - 确保 mapUtils 已初始化
  ;(window as any).addTotalHeatMap = addTotalHeatMapPoints
  console.log('🔥 热力图函数已绑定:', typeof (window as any).addTotalHeatMap)

  // 立即测试函数是否可调用
  console.log('🧪 测试热力图函数绑定状态:', {
    functionExists: typeof (window as any).addTotalHeatMap === 'function',
    mapUtilsExists: !!mapUtils,
    mapExists: !!mapUtils?.getMap()
  })

  // 设置地图组件加载完成标志
  ;(window as any).mapComponentLoaded = true

  console.log('🗺️ 地图组件已加载完成，全局函数已绑定:', {
    addResourceMapPoints: typeof (window as any).addResourceMapPoints,
    addHeatMapData: typeof (window as any).addHeatMapData,
    addTotalHeatMap: typeof (window as any).addTotalHeatMap,
    mapComponentLoaded: (window as any).mapComponentLoaded
  })

  // 触发一个自定义事件，通知其他组件地图已加载完成
  window.dispatchEvent(new CustomEvent('mapComponentReady', {
    detail: { mapComponentLoaded: true }
  }))

  // 异步加载默认图层，不阻塞全局函数绑定
  loadLayer('town').then(() => {
    console.log('🎯 默认乡镇界图层加载完成')
  }).catch(error => {
    console.error('❌ 默认图层加载失败:', error)
  })

  console.log('🔗 全局函数绑定完成:', {
    addHeatMapData: typeof (window as any).addHeatMapData,
    clearHeatMap: typeof (window as any).clearHeatMap
  })

  // 添加调试函数到全局，方便控制台测试
  ;(window as any).debugTestHeatMap = () => {
    const testData = [
      {
        districtName: '邛崃市中心',
        districtCode: '510183001',
        longitude: '103.366654',
        latitude: '30.410004',
        total: 25
      },
      {
        districtName: '临邛镇',
        districtCode: '510183002',
        longitude: '103.378597',
        latitude: '30.349588',
        total: 18
      },
      {
        districtName: '羊安镇',
        districtCode: '510183003',
        longitude: '103.348922',
        latitude: '30.361444',
        total: 32
      }
    ]
    console.log('🧪 调试测试热力图，数据:', testData)
    if ((window as any).addTotalHeatMap) {
      ;(window as any).addTotalHeatMap(testData)
      console.log('✅ 调试热力图测试成功')
    } else {
      console.error('❌ addTotalHeatMap函数不存在')
    }
  }

  // 添加简单的函数状态检查
  ;(window as any).checkHeatMapFunction = () => {
    console.log('🔍 检查热力图函数状态:', {
      addTotalHeatMap: typeof (window as any).addTotalHeatMap,
      mapUtils: !!mapUtils,
      map: !!mapUtils?.getMap(),
      functionCallable: typeof (window as any).addTotalHeatMap === 'function'
    })
  }

  // 添加清除热力图的调试函数
  ;(window as any).debugClearHeatMap = () => {
    console.log('🧪 调试清除热力图')
    if ((window as any).clearHeatMap) {
      ;(window as any).clearHeatMap()
      console.log('✅ 调试清除热力图成功')
    } else {
      console.error('❌ clearHeatMap函数不存在')
    }
  }

})

// 加载图层
const loadLayer = async (layerType: keyof typeof layerMappings) => {
  if (!mapUtils) return
  
  try {
    const geoId = layerMappings[layerType]
    await mapUtils.loadAdministrativeLayer(geoId, null, layerType)
    currentLayer.value = layerType // 更新当前图层状态
    mapUtils.setCurrentLayerType(layerType) // 同步图层类型到地图工具
    console.log(`✅ 已加载图层: ${layerType}`)
  } catch (error) {
    console.error('加载图层失败:', error)
  }
}

// 添加示例点位
const addSamplePoints = () => {
  if (!mapUtils) return
  mapUtils.addPoints(samplePoints)
}

// 清空点位
const clearPoints = () => {
  if (!mapUtils) return
  mapUtils.clearAllPoints()
}

// 返回上级
const goBack = async () => {
  if (!mapUtils) return
  await mapUtils.back()
  // 同步当前图层状态
  currentLayer.value = mapUtils.getCurrentLayerType() as keyof typeof layerMappings
}

// 获取图层名称
const getLayerName = (layerType: string) => {
  const layerNames = {
    district: '区县界',
    town: '乡镇界',
    village: '村社区界',
    grid: '管理网格'
  }
  return layerNames[layerType as keyof typeof layerNames] || '未知图层'
}

// 资源地图分布撒点
const addResourceMapPoints = (resourceData: any) => {
  if (!mapUtils) return
  console.log('🗺️ MapComponent: 开始资源地图分布撒点，数据:', resourceData)

  // 先清除现有点位
  mapUtils.clearAllPoints()

  // 转换数据格式
  const points = convertDataToPoints(resourceData)

  // 添加点位
  if (points.length > 0) {
    mapUtils.addPoints(points)
    console.log(`✅ MapComponent: 资源地图分布撒点完成，共 ${points.length} 个点位`)
  } else {
    console.log('📊 MapComponent: 资源地图分布撒点完成，共 0 个点位')
  }
}

// 清除资源地图分布点位
const clearResourceMapPoints = () => {
  if (!mapUtils) return
  mapUtils.clearAllPoints()
  console.log('✅ MapComponent: 已清除所有资源地图分布点位')

  // 通知所有组件清除激活状态
  if ((window as any).clearActiveMapPointTypeLeft1) {
    (window as any).clearActiveMapPointTypeLeft1()
  }
  if ((window as any).clearActiveMapPointTypeLeft2) {
    (window as any).clearActiveMapPointTypeLeft2()
  }
  if ((window as any).clearActiveMapPointTypeRight1) {
    (window as any).clearActiveMapPointTypeRight1()
  }
  if ((window as any).clearActiveMapPointTypeRight2) {
    (window as any).clearActiveMapPointTypeRight2()
  }
}

// 三色预警资源地图分布撒点
const addWarningResourceMapPoints = (warningDeviceData: any) => {
  if (!mapUtils) return
  console.log('🎯 MapComponent: 开始三色预警资源地图分布撒点，数据:', warningDeviceData)

  // 先清除现有点位
  mapUtils.clearAllPoints()

  // 转换数据格式
  const points = convertDataToPoints(warningDeviceData)

  // 添加点位
  if (points.length > 0) {
    mapUtils.addPoints(points)
    console.log(`✅ MapComponent: 三色预警资源地图分布撒点完成，共 ${points.length} 个点位`)
  } else {
    console.log('📊 MapComponent: 三色预警资源地图分布撒点完成，共 0 个点位')
  }
}

// 崃建言资源地图分布撒点
const addSuggestionResourceMapPoints = (suggestionData: any) => {
  if (!mapUtils) return
  console.log('💡 MapComponent: 开始崃建言资源地图分布撒点，数据:', suggestionData)
  console.log('🧹 MapComponent: 清除所有现有点位（包括其他类型的点位）')

  // 先清除现有点位
  mapUtils.clearAllPoints()

  // 转换数据格式
  const points = convertDataToPoints(suggestionData)

  // 添加点位
  if (points.length > 0) {
    mapUtils.addPoints(points)
    console.log(`✅ MapComponent: 崃建言资源地图分布撒点完成，共 ${points.length} 个点位`)
    console.log('📍 MapComponent: 当前地图上显示的是崃建言点位')
  } else {
    console.log('📊 MapComponent: 崃建言资源地图分布撒点完成，共 0 个点位')
    console.log('🗺️ MapComponent: 地图上已清空所有点位')
  }
}

// 家庭医生资源地图分布撒点
const addFamilyResourceMapPoints = (familyData: any) => {
  if (!mapUtils) return
  console.log('👨‍⚕️ MapComponent: 开始家庭医生资源地图分布撒点，数据:', familyData)
  console.log('🧹 MapComponent: 清除所有现有点位（包括其他类型的点位）')

  // 先清除现有点位
  mapUtils.clearAllPoints()

  // 转换数据格式
  const points = convertDataToPoints(familyData)

  // 添加点位
  if (points.length > 0) {
    mapUtils.addPoints(points)
    console.log(`✅ MapComponent: 家庭医生资源地图分布撒点完成，共 ${points.length} 个点位`)
    console.log('📍 MapComponent: 当前地图上显示的是家庭医生点位')
  } else {
    console.log('📊 MapComponent: 家庭医生资源地图分布撒点完成，共 0 个点位')
    console.log('🗺️ MapComponent: 地图上已清空所有点位')
  }
}



// 通用数据转换函数
const convertDataToPoints = (data: any): PointData[] => {
  if (!data) return []

  let dataArray: any[] = []

  // 处理不同的数据格式
  if (Array.isArray(data)) {
    dataArray = data
  } else if (data && typeof data === 'object') {
    // 尝试从对象中提取数组
    const values = Object.values(data)
    const arrayValue = values.find(v => Array.isArray(v))
    if (arrayValue) {
      dataArray = arrayValue as any[]
    }
  }

  if (!Array.isArray(dataArray) || dataArray.length === 0) {
    return []
  }

  // 转换为点位格式
  return dataArray
    .filter(item => {
      // 过滤掉没有坐标信息的数据
      const hasCoords = (item.longitude || item.lng) && (item.latitude || item.lat) &&
                       !isNaN(parseFloat(item.longitude || item.lng)) &&
                       !isNaN(parseFloat(item.latitude || item.lat))
      return hasCoords
    })
    .map((item, index) => {
      const longitude = parseFloat(item.longitude || item.lng || 0)
      const latitude = parseFloat(item.latitude || item.lat || 0)

      return {
        id: index + 1,
        title: item.name || item.title || item.doctorName || item.deviceName || `点位${index + 1}`,
        position: [longitude, latitude, 0] as [number, number, number]
      }
    })
}

// 转换为热力图数据格式
const convertToHeatMapData = (data: any) => {
  console.log('🔄 转换热力图数据格式:', data)

  let dataArray: any[] = []

  // 处理不同的数据格式
  if (Array.isArray(data)) {
    dataArray = data
  } else if (data && typeof data === 'object') {
    // 尝试从对象中提取数组
    const possibleArrayKeys = ['data', 'list', 'rows', 'items', 'result', 'message']
    for (const key of possibleArrayKeys) {
      const arrayValue = data[key]
      if (Array.isArray(arrayValue)) {
        dataArray = arrayValue
        break
      }
    }

    // 如果没有找到数组，尝试直接使用对象的值
    if (dataArray.length === 0) {
      const arrayValue = Object.values(data).find(value => Array.isArray(value))
      if (arrayValue) {
        dataArray = arrayValue as any[]
      }
    }
  }

  if (!Array.isArray(dataArray) || dataArray.length === 0) {
    console.warn('⚠️ 没有找到有效的数组数据:', data)
    return []
  }

  console.log('📊 找到数据数组，长度:', dataArray.length, '前3项:', dataArray.slice(0, 3))

  // 转换为热力图格式
  return dataArray
    .filter(item => {
      // 过滤掉没有坐标信息的数据
      const hasCoords = (item.longitude || item.lng) && (item.latitude || item.lat) &&
                       !isNaN(parseFloat(item.longitude || item.lng)) &&
                       !isNaN(parseFloat(item.latitude || item.lat))
      const hasCount = (item.count !== undefined || item.total !== undefined || item.number !== undefined)

      if (!hasCoords) {
        console.warn('⚠️ 跳过无效坐标数据:', item)
      }
      if (!hasCount) {
        console.warn('⚠️ 跳过无数量数据:', item)
      }

      return hasCoords && hasCount
    })
    .map(item => {
      const longitude = item.longitude || item.lng || '0'
      const latitude = item.latitude || item.lat || '0'
      const count = item.count || item.total || item.number || 1

      return {
        districtName: item.districtName || item.name || item.areaName || '未知区域',
        districtCode: item.districtCode || item.code || item.areaCode || '',
        longitude: longitude.toString(),
        latitude: latitude.toString(),
        count: parseInt(count) || 1
      }
    })
}

// 添加热力图数据
const addHeatMapData = (data: any) => {
  console.log('🔥 接收到热力图数据:', data)

  if (!mapUtils) {
    console.error('❌ 地图工具未初始化')
    return
  }

  // 清空现有的撒点数据
  mapUtils.clearAllPoints()

  // 转换数据格式
  const heatMapData = convertToHeatMapData(data)

  if (heatMapData.length === 0) {
    console.warn('⚠️ 没有有效的热力图数据')
    return
  }

  // 添加热力图数据
  mapUtils.addHeatMapData(heatMapData)
}

// 清空热力图
const clearHeatMap = () => {
  console.log('🔥 清空热力图')

  if (!mapUtils) {
    console.error('❌ 地图工具未初始化')
    return
  }

  mapUtils.clearHeatMap()
}

// 专门处理您的API数据格式的热力图函数
const addCustomHeatMapData = (apiData: any) => {
  console.log('🔥 接收到自定义热力图数据:', apiData)

  if (!mapUtils) {
    console.error('❌ 地图工具未初始化')
    return
  }

  // 清空现有的撒点数据
  mapUtils.clearAllPoints()

  // 直接处理您的数据格式
  let dataArray: any[] = []

  if (Array.isArray(apiData)) {
    dataArray = apiData
  } else if (apiData && typeof apiData === 'object') {
    // 检查是否有嵌套的数组结构
    if (apiData.message && Array.isArray(apiData.message)) {
      dataArray = apiData.message
    } else if (apiData.data && Array.isArray(apiData.data)) {
      dataArray = apiData.data
    } else if (apiData.list && Array.isArray(apiData.list)) {
      dataArray = apiData.list
    }
  }

  if (!Array.isArray(dataArray) || dataArray.length === 0) {
    console.warn('⚠️ 没有找到有效的热力图数据数组')
    return
  }

  console.log(`📊 找到 ${dataArray.length} 条热力图数据`)

  // 转换为热力图格式，专门处理您的数据结构
  const heatMapData = dataArray
    .filter(item => {
      // 检查必要字段
      const hasCoords = item.longitude && item.latitude &&
                       !isNaN(parseFloat(item.longitude)) &&
                       !isNaN(parseFloat(item.latitude))
      // 支持多种数量字段：total, count, number
      const hasCount = (item.total !== undefined && item.total !== null) ||
                      (item.count !== undefined && item.count !== null) ||
                      (item.number !== undefined && item.number !== null)

      if (!hasCoords) {
        console.warn('⚠️ 跳过无效坐标的数据项:', item)
      }
      if (!hasCount) {
        console.warn('⚠️ 跳过无数量字段的数据项:', item)
      }

      return hasCoords && hasCount
    })
    .map(item => ({
      districtName: item.districtName || '未知区域',
      districtCode: item.districtCode || '',
      longitude: item.longitude.toString(),
      latitude: item.latitude.toString(),
      count: parseInt(item.total || item.count || item.number) || 0
    }))

  if (heatMapData.length === 0) {
    console.warn('⚠️ 转换后没有有效的热力图数据')
    return
  }

  console.log(`✅ 成功转换 ${heatMapData.length} 条热力图数据:`, heatMapData.slice(0, 3))

  // 添加热力图数据到地图
  mapUtils.addHeatMapData(heatMapData)
}

// 所有函数定义完成后，立即执行全局函数绑定
bindGlobalFunctions()

// 立即测试函数是否正确绑定
console.log('🧪 立即测试函数绑定状态:', {
  windowAddTotalHeatMap: typeof (window as any).addTotalHeatMap,
  windowFunctionExists: (window as any).addTotalHeatMap !== undefined,
  functionIsCallable: typeof (window as any).addTotalHeatMap === 'function'
})

// 组件销毁时清理地图
onBeforeUnmount(() => {
  // 清理全局函数绑定
  if ((window as any).addResourceMapPoints) {
    delete (window as any).addResourceMapPoints
  }
  if ((window as any).clearResourceMapPoints) {
    delete (window as any).clearResourceMapPoints
  }
  if ((window as any).addWarningResourceMapPoints) {
    delete (window as any).addWarningResourceMapPoints
  }
  if ((window as any).addSuggestionResourceMapPoints) {
    delete (window as any).addSuggestionResourceMapPoints
  }
  if ((window as any).addFamilyResourceMapPoints) {
    delete (window as any).addFamilyResourceMapPoints
  }
  if ((window as any).addHeatMapData) {
    delete (window as any).addHeatMapData
  }
  if ((window as any).clearHeatMap) {
    delete (window as any).clearHeatMap
  }
  if ((window as any).addCustomHeatMapData) {
    delete (window as any).addCustomHeatMapData
  }
  if ((window as any).addTotalHeatMap) {
    delete (window as any).addTotalHeatMap
  }
  if ((window as any).mapComponentLoaded) {
    delete (window as any).mapComponentLoaded
  }

  if ((window as any).clearActiveMapPointType) {
    delete (window as any).clearActiveMapPointType
  }

  if (mapUtils) {
    mapUtils.destroy()
  }
})
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  /* 移除焦点边框 */
  outline: none;
}

.map-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  /* 移除焦点边框 */
  outline: none;
}

/* 移除地图容器的所有焦点和选中状态边框 */
.map-container *,
.map-wrapper *,
#mapContainer,
#mapContainer * {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 移除Canvas元素的边框 */
.map-container canvas,
.map-wrapper canvas,
#mapContainer canvas {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 移除Cesium相关的选中样式 */
.cesium-widget,
.cesium-widget-canvas,
.cesium-viewer,
.cesium-viewer-canvas {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 移除所有可能的蓝色边框和焦点样式 */
* {
  -webkit-tap-highlight-color: transparent !important;
}

*:focus,
*:active,
*:hover {
  outline: none !important;
  border-color: transparent !important;
  box-shadow: none !important;
}

/* 特别针对地图容器内的所有元素 */
#mapContainer *,
#mapContainer *:focus,
#mapContainer *:active,
#mapContainer *:hover {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

.map-controls {
  position: absolute;
  top: 100px;
  left: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  background: rgba(0, 0, 0, 0.7);
  padding: 15px;
  border-radius: 8px;
}

/* 活跃图层按钮样式 */
.map-controls .active-layer {
  background-color: #409eff !important;
  border-color: #409eff !important;
  color: #fff !important;
}

/* 图层信息样式 */
.layer-info {
  margin-top: 10px;
  padding: 8px 12px;
  background: rgba(64, 158, 255, 0.1);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 4px;
  font-size: 12px;
}

.layer-label {
  color: #909399;
  margin-right: 5px;
}

.layer-name {
  color: #409eff;
  font-weight: bold;
}

.area-info, .point-info {
  max-height: 400px;
  overflow-y: auto;
}

.area-info p, .point-info p {
  margin: 8px 0;
  padding: 4px 0;
  border-bottom: 1px solid #eee;
}

.area-info p:last-child, .point-info p:last-child {
  border-bottom: none;
}
</style> 