<template>
  <div class="change-password-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="change-password-dialog" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">修改密码</h3>
        <button class="close-btn" @click="handleClose">×</button>
      </div>
      
      <div class="dialog-content">
        <el-form 
          ref="formRef" 
          :model="form" 
          :rules="rules" 
          label-width="100px"
          class="password-form"
        >
          <el-form-item label="旧密码" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入旧密码"
              show-password
              autocomplete="off"
            />
          </el-form-item>
          
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="form.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
              autocomplete="off"
            />
          </el-form-item>
          
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="form.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
              autocomplete="off"
            />
          </el-form-item>
        </el-form>
      </div>
      
      <div class="dialog-footer">
        <el-button @click="handleClose" class="cancel-btn">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading" class="submit-btn">
          确认修改
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElForm } from 'element-plus'
import { changePasswordApi } from '@/api/auth'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<InstanceType<typeof ElForm>>()
const loading = ref(false)

// 表单数据
const form = reactive({
  password: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const rules = {
  password: [
    { required: true, message: '请输入旧密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== form.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

// 点击遮罩层关闭
const handleOverlayClick = (e: Event) => {
  if (e.target === e.currentTarget) {
    handleClose()
  }
}

// 重置表单
const resetForm = () => {
  form.password = ''
  form.newPassword = ''
  form.confirmPassword = ''
  formRef.value?.clearValidate()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    await changePasswordApi({
      password: form.password,
      newPassword: form.newPassword
    })
    
    ElMessage.success('密码修改成功')
    emit('success')
    handleClose()
  } catch (error: any) {
    console.error('修改密码失败:', error)
    ElMessage.error(error.message || '修改密码失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.change-password-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.change-password-dialog {
  background: #060E1F;
  border: 1px solid #20497F;
  border-radius: 8px;
  width: 500px;
  max-width: 90vw;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  color: white;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #20497F;
}

.dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #ffffff;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;
}

.close-btn:hover {
  color: #00d4ff;
}

.dialog-content {
  padding: 24px;
}

.password-form {
  /* Element Plus 表单样式覆盖 */
}

.password-form :deep(.el-form-item__label) {
  color: #ffffff !important;
}

.password-form :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid #20497F !important;
  box-shadow: none !important;
}

.password-form :deep(.el-input__wrapper:hover) {
  border-color: #00d4ff !important;
}

.password-form :deep(.el-input__wrapper.is-focus) {
  border-color: #00d4ff !important;
  box-shadow: 0 0 0 1px rgba(0, 212, 255, 0.2) !important;
}

.password-form :deep(.el-input__inner) {
  color: #ffffff !important;
  background: transparent !important;
}

.password-form :deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.5) !important;
}

.password-form :deep(.el-form-item__error) {
  color: #ff6b6b !important;
}

.password-form :deep(.el-icon) {
  color: rgba(255, 255, 255, 0.7) !important;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #20497F;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid #20497F !important;
  color: #ffffff !important;
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: #00d4ff !important;
}

.submit-btn {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%) !important;
  border: none !important;
  color: #ffffff !important;
}

.submit-btn:hover {
  background: linear-gradient(135deg, #00b8e6 0%, #0088bb 100%) !important;
}

.submit-btn.is-loading {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%) !important;
  opacity: 0.8;
}
</style> 