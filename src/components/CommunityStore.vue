<template>
  <div class="community-store">
    <div class="store-header">
      <h3 class="store-title">社区好物概览</h3>
      <div class="time-selector">
        <el-select v-model="currentTimeType" @change="handleTimeChange" size="small">
          <el-option label="今日" :value="0" />
          <el-option label="近一月" :value="1" />
          <el-option label="近一年" :value="2" />
        </el-select>
      </div>
    </div>
    
    <div class="store-content" v-loading="loading">
      <div class="data-cards">
        <div class="data-card goods-card">
          <div class="card-icon">
            <el-icon><ShoppingBag /></el-icon>
          </div>
          <div class="card-info">
            <div class="card-title">商品数量</div>
            <div class="card-value">{{ storeData?.goodsCount || 0 }}</div>
            <div class="card-unit">件</div>
          </div>
        </div>
        
        <div class="data-card views-card">
          <div class="card-icon">
            <el-icon><View /></el-icon>
          </div>
          <div class="card-info">
            <div class="card-title">浏览次数</div>
            <div class="card-value">{{ storeData?.viewCount || 0 }}</div>
            <div class="card-unit">次</div>
          </div>
        </div>
      </div>
      
      <!-- 数据为空时的提示 -->
      <div v-if="!loading && !storeData" class="empty-data">
        <el-empty description="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { ShoppingBag, View } from '@element-plus/icons-vue'
import { getStoreInfoApi, type StoreInfoParams, type StoreInfoResponse } from '@/api/suggestion'

// 组件属性
interface Props {
  areaCode?: string // 区域编码，可以从外部传入
  autoRefresh?: boolean // 是否自动刷新
  refreshInterval?: number // 刷新间隔（秒）
}

const props = withDefaults(defineProps<Props>(), {
  areaCode: '510100', // 默认区域编码
  autoRefresh: false,
  refreshInterval: 30
})

// 响应式数据
const loading = ref(false)
const currentTimeType = ref(0) // 当前时间类型：0-今日，1-近一月，2-近一年
const storeData = ref<StoreInfoResponse['message'] | null>(null)

// 自动刷新定时器
let refreshTimer: number | null = null

// 获取社区好物数据
const fetchStoreData = async () => {
  console.log('🚀 fetchStoreData 函数被调用')
  console.log('🚀 props.areaCode:', props.areaCode)
  console.log('🚀 检查区域编码是否为空...')
  
  if (!props.areaCode) {
    console.log('❌ 区域编码为空，终止执行')
    ElMessage.warning('区域编码不能为空')
    return
  }

  console.log('✅ 区域编码检查通过')
  console.log('📡 开始获取社区好物数据...')
  console.log('📡 参数:', { areaCode: props.areaCode, timeType: currentTimeType.value })
  
  loading.value = true
  
  try {
    const params: StoreInfoParams = {
      areaCode: props.areaCode,
      timeType: currentTimeType.value
    }
    
    console.log('📡 调用API:', params)
    const response = await getStoreInfoApi(params)
    console.log('📡 API返回:', response)
    
    // suggestionRequest 直接返回 StoreInfoResponse 格式
    if (response.code === 200) {
      storeData.value = response.message
      console.log('✅ 数据获取成功:', response.message)
    } else {
      const errorMsg = response.msg || '获取数据失败'
      ElMessage.error(errorMsg)
      storeData.value = null
      console.log('❌ 数据获取失败:', errorMsg)
    }
  } catch (error) {
    console.error('获取社区好物数据失败:', error)
    ElMessage.error('获取数据失败，请稍后重试')
    storeData.value = null
  } finally {
    loading.value = false
  }
}

// 时间类型改变处理
const handleTimeChange = (value: number) => {
  console.log('🔄 社区好物概览时间变更:', value)
  console.log('🔄 当前时间类型:', currentTimeType.value)
  console.log('🔄 当前区域编码:', props.areaCode)
  console.log('🔄 区域编码是否为空:', !props.areaCode)
  
  try {
    console.log('🔄 开始调用 fetchStoreData...')
    fetchStoreData()
    console.log('🔄 fetchStoreData 调用完成')
  } catch (error) {
    console.error('🔄 fetchStoreData 调用出错:', error)
  }
}

// 设置自动刷新
const setupAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  
  if (props.autoRefresh) {
    refreshTimer = setInterval(() => {
      fetchStoreData()
    }, props.refreshInterval * 1000)
  }
}

// 监听区域编码变化
watch(() => props.areaCode, (newAreaCode) => {
  if (newAreaCode) {
    fetchStoreData()
  }
}, { immediate: false })

// 监听自动刷新配置变化
watch(() => [props.autoRefresh, props.refreshInterval], () => {
  setupAutoRefresh()
}, { immediate: true })

// 组件挂载时获取数据
onMounted(() => {
  fetchStoreData()
})

// 组件卸载时清理定时器
import { onUnmounted } from 'vue'
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})

// 暴露方法给父组件
defineExpose({
  refresh: fetchStoreData,
  getData: () => storeData.value
})
</script>

<style scoped lang="scss">
.community-store {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  min-height: 200px;

  .store-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .store-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
    }

    .time-selector {
      :deep(.el-select) {
        .el-input__wrapper {
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          
          .el-input__inner {
            color: white;
            
            &::placeholder {
              color: rgba(255, 255, 255, 0.7);
            }
          }
        }
      }
    }
  }

  .store-content {
    .data-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      gap: 20px;

      .data-card {
        background: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        padding: 20px;
        display: flex;
        align-items: center;
        gap: 15px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .card-icon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
        }

        &.goods-card .card-icon {
          background: rgba(52, 152, 219, 0.3);
          color: #3498db;
        }

        &.views-card .card-icon {
          background: rgba(46, 204, 113, 0.3);
          color: #2ecc71;
        }

        .card-info {
          flex: 1;

          .card-title {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 5px;
          }

          .card-value {
            font-size: 28px;
            font-weight: bold;
            line-height: 1;
            margin-bottom: 2px;
          }

          .card-unit {
            font-size: 12px;
            opacity: 0.7;
          }
        }
      }
    }

    .empty-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 120px;
      
      :deep(.el-empty) {
        .el-empty__description {
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .community-store {
    padding: 15px;

    .store-header {
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;

      .store-title {
        font-size: 18px;
      }
    }

    .data-cards {
      grid-template-columns: 1fr;
      gap: 15px;

      .data-card {
        padding: 15px;

        .card-icon {
          width: 40px;
          height: 40px;
          font-size: 20px;
        }

        .card-info .card-value {
          font-size: 24px;
        }
      }
    }
  }
}
</style> 