<template>
  <div class="left1-component">
    <!-- 标题栏 -->
    <div class="header-bar">
      <div class="header-back" @click="handleGoBack">
        <span class="back-text">上一页</span>
      </div>
      <div class="header-title">
        <div class="title-icon">
          <img src="@/assets/common/icon1.png" alt="title-icon">
        </div>
        <span class="title-text">{{ currentPageTitle }}</span>
      </div>
      <div class="header-back" @click="handleGoNext" :style="{ visibility: (fullscreenStore.screenMode === 'wide' || fullscreenStore.isFullscreen) ? 'hidden' : 'visible' }">
        <span class="back-text">下一页</span>
      </div>
    </div>
    
    <!-- 第一页：智能物联 -->
    <template v-if="currentPage === 1">
      <!-- 智能物联子模块选项卡 -->
      <div class="iot-tabs">
        <div 
          class="tab-item" 
          :class="{ active: iotActiveTab === 'warning' }"
          @click="handleIotTabChange('warning')"
        >
          三色预警
        </div>
        <div 
          class="tab-item" 
          :class="{ active: iotActiveTab === 'equipment' }"
          @click="handleIotTabChange('equipment')"
        >
          物联设备运行概况
        </div>
        <div 
          class="tab-item" 
          :class="{ active: iotActiveTab === 'deviceWarning' }"
          @click="handleIotTabChange('deviceWarning')"
        >
          设备预警概况
        </div>
      </div>
      
      <!-- 三色预警模块 -->
      <template v-if="iotActiveTab === 'warning'">
        <div class="module-box">
          <ModuleTitle title="三色预警概况" />
          <DateGroup :index="timeRange" :dateData="timeOptions" @change="change"/>
          <WarningData :warningData="warningData" />
            <div class="action-buttons">
              <button class="action-btn bg1" @click="handleWarningResourceMap">资源地图分布</button>
              <button class="action-btn bg2" @click="handleWarningHeatMap">辖区热力分布</button>
              <button class="action-btn bg3" @click="handleWarningDataDetail">数据详情</button>
          </div>
        </div>
        
        <div class="module-box">
          <ModuleTitle class="module-title" title="三色预警趋势" />
          <div class="trend-controls">
            <DateGroup :index="timeRangeCurve" :dateData="curveTimeOptions" @change="changeCurve"/>
            <div class="warning-type-select">
              <el-select 
                v-model="selectedWarningType" 
                placeholder="请选择预警类型"
                @change="handleWarningTypeChange"
                size="small"
                class="warning-select"
              >
                <el-option
                  v-for="item in warningTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <EchartsWarningCurve :chartData="data.curveData" />
        </div>
        
        <div class="module-box">
          <ModuleTitle class="module-title" title="三色预警统计" />
          <DateGroup :index="timeRangeHorizontal" :dateData="horizontalTimeOptions" @change="changeHorizontal"/>
          <EchartsWarningHorizontal :chartData="data.horizontalData" />
        </div>
      </template>
      
      <!-- 物联设备运行概况模块 -->
      <template v-else-if="iotActiveTab === 'equipment'">
        <div class="module-box">
          <ModuleTitle title="设备概况" />
          <div class="device-overview">
            <div class="device-card">
              <div class="device-icon">
                <img src="@/assets/images/run/ai.png" alt="设备图标">
              </div>
              <div class="device-info">
                <div class="device-title">智能设备数量</div>
                <div class="device-count">{{ deviceOverviewData.total }} <span class="unit">台</span></div>
              </div>
            </div>
            <div class="device-card">
              <div class="device-icon">
                <img src="@/assets/images/run/online.png" alt="在线图标">
              </div>
              <div class="device-info">
                <div class="device-title">设备在线数量</div>
                <div class="device-count">{{ deviceOverviewData.online }} <span class="unit">台</span></div>
              </div>
            </div>
            <div class="device-card">
              <div class="device-icon">
                <img src="@/assets/images/run/chart.png" alt="图表图标">
              </div>
              <div class="device-info">
                <div class="device-title">在线上月同比</div>
                <div class="device-count">{{ deviceOverviewData.growthRate }} <span class="unit">%</span></div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="感知设备分布" />
          <div class="device-distribution">
            <div class="distribution-item" v-for="(item, index) in deviceDistribution" :key="index">
              <div class="item-name">{{ item.name }}</div>
              <div class="item-count">{{ item.count }}</div>
            </div>
          </div>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="设备信息" />
          <div class="device-table">
            <div class="table-header">
              <div class="col-device">智能设备数量</div>
              <div class="col-online">在线数</div>
              <div class="col-error">异常情况</div>
              <div class="col-action">操作</div>
              <div class="col-location">分布</div>
            </div>
            <div class="table-body">
              <div class="table-row" v-for="(item, index) in deviceList" :key="index">
                <div class="col-device">
                  <div class="device-cell">
                    <div class="device-cell-icon">
                      <img :src="item.icon" alt="" @error="handleImageError" @load="handleImageLoad">
                    </div>
                    <div class="device-info">
                      <div class="device-name">{{ item.name }}</div>
                      <div class="device-total">{{ item.total }}台</div>
                    </div>
                  </div>
                </div>
                <div class="col-online">
                  <div class="online-cell">
                    <img src="@/assets/images/run/signal.png" alt="">
                    <span>{{ item.online }}</span>
                  </div>
                </div>
                <div class="col-error">
                  <span :class="['error-count', { 'has-error': item.offline > 0 }]">{{ item.offline }}</span>
                </div>
                <div class="col-action">
                  <button class="action-btn" @click="toPage(item)">查看</button>
                </div>
                <div class="col-location" @click="distributed(item)">
                  <img src="@/assets/images/run/local.png" alt="" class="location-icon">
                </div>
              </div>
              <!-- 无数据提示 -->
              <div v-if="!deviceLoading && deviceList.length === 0" class="no-data">
                暂无设备数据
              </div>
            </div>
          </div>
        </div>
      </template>
      
      <!-- 设备预警概况模块 -->
      <template v-else-if="iotActiveTab === 'deviceWarning'">
        <div class="module-box">
          <div class="title-box">
            <ModuleTitle title="预警详情" />
            <DateGroup :index="warningTimeRange" :dateData="warningTimeOptions" @change="changeWarningTime" />
          </div>
          <div class="warning-overview">
            <div class="warning-card">
              <div class="warning-icon">
                <img src="@/assets/images/warn/danger.png" alt="预警图标">
              </div>
              <div class="warning-info">
                <div class="warning-count">{{ warningStatistics.total }}</div>
                <div class="warning-title">今日预警</div>
              </div>
            </div>
            <div class="warning-card">
              <div class="warning-icon">
                <img src="@/assets/images/warn/processed.png" alt="已处理图标">
              </div>
              <div class="warning-info">
                <div class="warning-count">{{ warningStatistics.complete }}</div>
                <div class="warning-title">已处理</div>
              </div>
            </div>
            <div class="warning-card">
              <div class="warning-icon">
                <img src="@/assets/images/warn/pending.png" alt="待处理图标">
              </div>
              <div class="warning-info">
                <div class="warning-count">{{ warningStatistics.unComplete }}</div>
                <div class="warning-title">待处理</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="预警消息" />
          <div class="warning-table">
            <el-table 
              v-loading="warningLoading" 
              element-loading-text="加载中..." 
              element-loading-background="rgba(0, 0, 0, 0.6)"
              stripe 
              :data="warningList.slice(0, 5)" 
              :max-height="200"
              size="small"
            >
              <el-table-column label="预警类型" align="center" width="90">
                <template #default="scope">
                  <div class="warning-type-text">{{ scope.row.type }}</div>
                </template>
              </el-table-column>
              <el-table-column label="预警小区" align="center" width="80">
                <template #default="scope">
                  <div class="warning-area-text">{{ scope.row.area }}</div>
                </template>
              </el-table-column>
              <el-table-column label="预警时间" align="center">
                <template #default="scope">
                  <div class="warning-time-text">{{ scope.row.time }}</div>
                </template>
              </el-table-column>
              <el-table-column label="状态" align="center" width="60">
                <template #default="scope">
                  <div 
                    class="warning-status-text" 
                    :class="scope.row.status === '待处理' ? 'status-pending' : 'status-completed'"
                  >
                    {{ scope.row.status }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="60">
                <template #default="scope">
                  <el-button size="small" @click="showWarningDetail(scope.row)">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!-- 无数据提示 -->
            <div v-if="!warningLoading && warningList.length === 0" class="no-data">
              暂无预警数据
            </div>
          </div>
        </div>

        <div class="module-box">
          <ModuleTitle title="待处理事件分布" />
          <div class="pending-chart">
            <EchartsColumnar :chartData="{
              columnarDataBlue: pendingChart.map(item => item.value),
              labelData: pendingChart.map(item => item.name),
              columnarDataYellow: [],
              curveData: []
            }" />
          </div>
        </div>

        <div class="module-box">
          <ModuleTitle title="风险预警点位排名" />
          <div class="device-ranking-list">
            <div 
              v-for="(item, index) in deviceRankingData.slice(0, 3)" 
              :key="index"
              class="ranking-block"
            >
              <div class="ranking-content">
                <p class="device-name">{{ item.DeviceName }}</p>
                <p class="device-count">{{ item.Count }}</p>
              </div>
            </div>
            <div v-if="deviceRankingData.length === 0" class="no-data">
              暂无排名数据
            </div>
          </div>
        </div>

        <div class="module-box">
          <ModuleTitle title="风险预警类型排名" />
          <div class="type-ranking-list">
            <div 
              v-for="(item, index) in warningTypeRankingData.slice(0, 5)" 
              :key="index"
              class="ranking-block"
            >
              <div class="ranking-content">
                <p class="type-name">{{ item.TotalEventName }}</p>
                <p class="type-count">{{ item.Count }}</p>
              </div>
            </div>
            <div v-if="warningTypeRankingData.length === 0" class="no-data">
              暂无排名数据
            </div>
          </div>
        </div>
      </template>
    </template>
    
    <!-- 第二页：崃建言 -->
    <template v-else-if="currentPage === 2">
      <!-- 崃建言概览 -->
      <div class="module-box">
        <ModuleTitle title="崃建言概览" />
        <DateGroup :index="overviewTimeRange" :dateData="timeOptions" @change="changeOverview"/>
        <SuggestionOverview 
          :overviewData="overviewData" 
          :titleData="{receivedTitle:'已接收', feedbackTitle:'已反馈', receivedUnit:'条', feedbackUnit:'条'}" 
        />
         <div class="action-buttons" style="margin-top: 20px;">
           <button class="action-btn bg1" @click="handleSuggestionResourceMap">资源地图分布</button>
            <button class="action-btn bg2" @click="handleSuggestionHeatMap">辖区热力分布</button>
            <button class="action-btn bg3" @click="handleSuggestionDataDetail">数据详情</button>
          </div>
      </div>
      
      <!-- 崃建言采纳情况 -->
      <div class="module-box">
        <ModuleTitle title="崃建言采纳情况" />
        <DateGroup :index="adoptionTimeRange" :dateData="adoptionTimeOptions" @change="changeAdoption"/>
        <EchartsAdoptionRate :chartData="adoptionData" :loading="false"/>
      </div>
      
      <!-- 崃建言处理情况 -->
      <div class="module-box">
        <ModuleTitle title="崃建言处理情况" />
        <DateGroup :index="processTimeRange" :dateData="processTimeOptions" @change="changeProcess"/>
        <EchartsProcessStatus :chartData="processData" :loading="false"/>
      </div>
    </template>

    <!-- 第三页：社区服务 -->
    <template v-else-if="currentPage === 3">
      <!-- 社区服务选项卡 -->
      <TabsComponent 
        v-model="serviceActiveTab" 
        :tabs="[
          { label: '精治数仓', value: 'precision' },
          { label: '家庭医生', value: 'doctor' },
          { label: '实用工具', value: 'tools' }
        ]"
        @change="handleServiceTabChange" 
      />

      <!-- 精治数仓内容 -->
      <template v-if="serviceActiveTab === 'precision'">
        <div class="module-box">
          <ModuleTitle title="社区概览" />
          <div class="community-overview">
            <div class="overview-grid">
              <div class="overview-card">
                <img class="card-icon" src="@/assets/sc/icon1.png" alt="社区数量" />
                <div class="card-info">
                  <div class="card-title">社区数量</div>
                  <div class="card-count">{{ communityData.communityCount }}</div>
                </div>
              </div>
              <div class="overview-card">
                <img class="card-icon" src="@/assets/sc/icon2.png" alt="人口数量" />
                <div class="card-info">
                  <div class="card-title">人口数量</div>
                  <div class="card-count">{{ communityData.populationCount }}</div>
                </div>
              </div>
              <div class="overview-card">
                <img class="card-icon" src="@/assets/sc/icon3.png" alt="标签数量" />
                <div class="card-info">
                  <div class="card-title">标签数量</div>
                  <div class="card-count">{{ communityData.tagCount }}</div>
                </div>
              </div>
              <div class="overview-card">
                <img class="card-icon" src="@/assets/sc/icon4.png" alt="企业数量" />
                <div class="card-info">
                  <div class="card-title">企业数量</div>
                  <div class="card-count">{{ communityData.enterpriseCount }}</div>
                </div>
              </div>
              <div class="overview-card">
                <img class="card-icon" src="@/assets/sc/icon5.png" alt="关系数量" />
                <div class="card-info">
                  <div class="card-title">关系数量</div>
                  <div class="card-count">{{ communityData.relationCount }}</div>
                </div>
              </div>
              <div class="overview-card">
                  <img class="card-icon" src="@/assets/sc/icon6.png" alt="房屋数量" />
                <div class="card-info">
                  <div class="card-title">房屋数量</div>
                  <div class="card-count">{{ communityData.houseCount }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="data-statistics">
            <div class="stat-item">
              <div class="stat-number">{{ fileAndResLogData.fileTotal.toLocaleString() }}</div>
              <div class="stat-label">文件总数(个)</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ fileAndResLogData.resLogTotal.toLocaleString() }}</div>
              <div class="stat-label">人员信息主动补全(条)</div>
            </div>
           
          </div>
          <!-- 社区文件资源列表 -->
          <div class="data-source">
            <div class="source-item" v-for="(source, index) in dataSourceList" :key="index">
              <img class="source-icon" src="@/assets/sc/icon7.png" alt="数据来源" />
              <div class="source-text">{{ source.text }}</div>
            </div>
          </div>
        </div>


        <div class="module-box">
          <div class="tag-statistics-title">
          <ModuleTitle title="标签统计" />
                         <div class="tag-statistics-title-right" style="display: flex;align-items: center;">
               <div class="tag-title">多重标签人群</div>
                 <div class="tag-count">{{ multiTagCount }}</div>
                 <div class="tag-title">人</div>
             </div>
          </div>
        
          <div class="tag-statistics">
            <div class="tag-grid">
            <div class="tag-item" v-for="(tag, index) in tagStatistics" :key="index">
              <div class="tag-info">
                <div class="tag-name">{{ tag.name }}</div>
                <div class="tag-count">{{ tag.count }}</div>
              </div>
            </div>
            </div>
           
          </div>
        </div>

        <div class="module-box">
           <ModuleTitle title="数据使用" />
          <div class="data-source">
             <div class="source-item" v-for="(item, index) in anticipateDataList" :key="index">
               <img class="source-icon" src="@/assets/sc/icon7.png" alt="数据使用" />
               <div class="source-text">
                 <p>{{ item.title }}</p>
                 <p>{{ item.count }}</p>
            </div>
          </div>
           </div>
         </div>

         <div class="module-box">
           <ModuleTitle title="人口结构分析" />
           <EchartsColumnar :chartData="ageSummaryChartData" />
        </div>
      </template>

      <!-- 家庭医生内容 -->
      <template v-else-if="serviceActiveTab === 'doctor'">
        <div class="module-box">
          <ModuleTitle title="服务概况" />
          <DateGroup 
            :index="doctorOverviewTimeRange"
            :dateData="doctorOverviewTimeOptions"
            @change="changeDoctorOverviewTime"
          />
          <DoctorServiceOverview :serviceData="doctorServiceData" />
          <div class="action-buttons" style="margin-top: 20px;">
            <button class="action-btn bg1" @click="handleFamilyServiceResourceMap">资源地图分布</button>
            <button class="action-btn bg2" @click="handleFamilyServiceHeatMap">辖区热力分布</button>
            <button class="action-btn bg3" @click="handleFamilyServiceDataDetail">数据详情</button>
          </div>
        </div>
        
        <div class="module-box">
          <ModuleTitle title="家庭医生" />
          <EchartsDoctorDistribution :chartData="doctorDistributionData" :loading="warningLoading" />
        </div>
        
        <div class="module-box">
          <ModuleTitle title="在管糖尿病患者签约数" />
          <EchartsDiabetesSignup :chartData="diabetesSignupData" :loading="warningLoading" />
        </div>
        
        <div class="module-box">
          <ModuleTitle title="家庭医生签约率" />
          <DoctorSignupRate :rateData="signupRateData" :loading="warningLoading" />
        </div>
      </template>

      <!-- 实用工具内容 -->
      <template v-else-if="serviceActiveTab === 'tools'">
        <div class="module-box">
          <ModuleTitle title="实用工具使用数" />
          <DateGroup 
            :index="toolsTimeRange"
            :dateData="toolsTimeOptions"
            @change="changeToolsTime"
          />
          <ToolsUsageCards :toolsData="toolsUsageData" />
          <div class="action-buttons" style="margin-top: 20px;">
            <button class="action-btn bg1" @click="handleToolsResourceMap">资源地图分布</button>
            <button class="action-btn bg2" @click="handleToolsHeatMap">辖区热力分布</button>
            <button class="action-btn bg3" @click="handleToolsDataDetail">数据详情</button>
          </div>
        </div>

        <div class="module-box">
          <ModuleTitle title="政策类型使用分布" />
          <EchartsPolicyDistribution :chartData="policyDistributionData" />
        </div>

        <div class="module-box">
          <ModuleTitle title="工具使用分布" />
          <EchartsToolsDistribution :chartData="toolsDistributionData" />
        </div>

        <div class="module-box">
          <ModuleTitle title="用户群体分布" />
          <EchartsUserGroupDistribution :chartData="userGroupData" />
        </div>
      </template>
    </template>

    <!-- 第四页：社区经济 -->
    <template v-else-if="currentPage === 4">
      <!-- 社区经济选项卡 -->
      <TabsComponent 
        v-model="economyActiveTab" 
        :tabs="[
          { label: '社区导览', value: 'navigation' },
          { label: '社区游线', value: 'tour' }
        ]"
        @change="handleEconomyTabChange" 
      />

      <!-- 社区导览内容 -->
      <template v-if="economyActiveTab === 'navigation'">
        <div class="module-box">
          <ModuleTitle title="使用总人次" />
          <DateGroup 
            :index="navigationTimeRange"
            :dateData="navigationTimeOptions"
            @change="changeNavigationTime"
          />
          <div class="total-usage-card">
            <div class="usage-icon">
              <img :src="communityX1" alt="使用总人次" />
            </div>
            <div class="usage-content">
              <div class="usage-title">使用总人次</div>
              <div class="usage-number">{{ navigationData.totalNumber.toLocaleString() }}</div>
              <div class="usage-unit">次</div>
            </div>
          </div>
          <!-- 社区导览操作按钮 -->
          <div class="action-buttons" style="margin-top: 20px;">
            <button class="action-btn bg1" @click="handleNavigationResourceMap">资源地图分布</button>
            <button class="action-btn bg2" @click="handleNavigationHeatMap">辖区热力分布</button>
            <button class="action-btn bg3" @click="handleNavigationDataDetail">数据详情</button>
          </div>
        </div>

        <div class="module-box">
          <ModuleTitle title="资源分布" />
          <EchartsResourcePie 
            :chartData="navigationData.resourcePieChartData" 
            :loading="false"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="使用数量" />
          <EchartsColumnar 
            :chartData="navigationData.chartData2" 
            :loading="false"
            :height="200"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="使用热度排行" />
          <EchartsColumnar 
            :chartData="navigationData.heatRankingChartData" 
            :loading="false"
            :height="200"
          />
        </div>
      </template>

      <!-- 社区游线内容 -->
      <template v-else-if="economyActiveTab === 'tour'">
        <div class="module-box">
          <ModuleTitle title="社区游线概览" />
          <DateGroup :index="tourTimeRange" :dateData="tourTimeOptions" @change="changeTourInfo"/>
          <ReuseOverview 
            :overviewData="{
              itemsCount: tourOverviewData.totalItems,
              clicksCount: tourOverviewData.totalClicks
            }" 
            :titleData="{
              itemsTitle: '游线总数',
              clicksTitle: '总浏览量', 
              itemsUnit: '条',
              clicksUnit: '次'
            }"
            :loading="tourIsLoadingInfo"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="社区游线热浏览概况" />
          <DateGroup :index="tourHotTimeRange" :dateData="tourTimeOptions" @change="changeTourHots"/>
          <EchartsHotReuse 
            :chartData="tourHotReuseData" 
            :loading="tourIsLoadingHotList"
            :dataKeys="{
              nameKey: 'tourLineName',
              valueKey: 'browseNumber'
            }"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="社区游线浏览及参与人数分布" />
          <DateGroup :index="tourListTimeRange" :dateData="tourTimeOptions" @change="changeTourList"/>
          <EchartsReuseDistribution 
            :chartData="tourDistributionData" 
            :loading="tourIsLoadingList"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="热门社区游线" />
          <DateGroup :index="tourHotListTimeRange" :dateData="tourTimeOptions" @change="changeTourHotList"/>
          <ReuseTable 
            :tableData="tourTableData"
            :columns="[
              { prop: 'districtName', label: '区域名称' },
              { prop: 'tourLineName', label: '游线名称' },
              { prop: 'browseNumber', label: '浏览次数' }
            ]"
            :loading="tourIsLoadingHotList"
          />
        </div>
      </template>
    </template>

    <!-- 设备列表弹窗 -->
    <DeviceListDialog 
      v-model:visible="deviceDialogVisible"
      :device-type-name="selectedDevice.typeName"
      :device-type="selectedDevice.type"
      :area-code="selectedDevice.areaCode"
    />
    
    <!-- 预警详情弹窗 -->
    <WarningEventDetailDialog
      v-model:visible="warningDetailVisible"
      :event-detail="selectedWarningEvent"
    />

    <!-- 三色预警数据详情弹窗 -->
    <WarningDataDetailDialog
      v-model:visible="warningDataDetailVisible"
      :area-code="getDistrictCode()"
      :time-range="timeRange"
      @view-detail="handleViewWarningDetail"
    />

    <!-- 社区导览数据详情弹窗 -->
    <CommunityTourDataDetailDialog
      v-model:visible="communityTourDataDetailVisible"
      :area-code="getDistrictCode()"
      :time-range="navigationTimeRange"
    />

    <!-- 实用工具数据详情弹窗 -->
    <ToolsDataDetailDialog
      v-model:visible="toolsDataDetailVisible"
      :area-code="getDistrictCode()"
      :time-range="toolsTimeRange"
    />

    <!-- 家庭医生服务概况数据详情弹窗 -->
    <FamilyServiceDataDetailDialog
      v-model:visible="familyServiceDataDetailVisible"
      :area-code="getDistrictCode()"
      :time-range="doctorOverviewTimeRange"
    />

    <!-- 崃建言数据详情弹窗 -->
    <SuggestionDataDetailDialog
      v-model:visible="suggestionDataDetailVisible"
      :area-code="getDistrictCode()"
      :time-range="overviewTimeRange"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, reactive, ref, computed, watch } from 'vue'
import ModuleTitle from './components/ModuleTitle.vue'
import DateGroup from './components/DateGroup.vue'
import WarningData from './components/WarningData.vue'
import EchartsWarningCurve from './components/EchartsWarningCurve.vue'
import EchartsWarningHorizontal from './components/EchartsWarningHorizontal.vue'
import TabsComponent from './components/TabsComponent.vue'
import DoctorServiceOverview from './components/DoctorServiceOverview.vue'
import EchartsDoctorDistribution from './components/EchartsDoctorDistribution.vue'
import EchartsDiabetesSignup from './components/EchartsDiabetesSignup.vue'
import DoctorSignupRate from './components/DoctorSignupRate.vue'
import ToolsUsageCards from './components/ToolsUsageCards.vue'
import EchartsPolicyDistribution from './components/EchartsPolicyDistribution.vue'
import EchartsToolsDistribution from './components/EchartsToolsDistribution.vue'
import EchartsUserGroupDistribution from './components/EchartsUserGroupDistribution.vue'
import DeviceListDialog from './components/DeviceListDialog.vue'
import WarningEventDetailDialog from './components/WarningEventDetailDialog.vue'
import WarningDataDetailDialog from './components/WarningDataDetailDialog.vue'
import CommunityTourDataDetailDialog from './components/CommunityTourDataDetailDialog.vue'
import ToolsDataDetailDialog from './components/ToolsDataDetailDialog.vue'
import FamilyServiceDataDetailDialog from './components/FamilyServiceDataDetailDialog.vue'
import SuggestionDataDetailDialog from './components/SuggestionDataDetailDialog.vue'
import SuggestionOverview from './components/SuggestionOverview.vue'
import EchartsAdoptionRate from './components/EchartsAdoptionRate.vue'
import EchartsProcessStatus from './components/EchartsProcessStatus.vue'
import ReuseOverview from './components/ReuseOverview.vue'
import EchartsHotReuse from './components/EchartsHotReuse.vue'
import EchartsReuseDistribution from './components/EchartsReuseDistribution.vue'
import ReuseTable from './components/ReuseTable.vue'
import EchartsColumnar from '@/views/viewsDemo/views/sidebar/compoments/EchartsColumnar.vue'
import EchartsResourcePie from './components/EchartsResourcePie.vue'
import { getWarningOverviewApi, getWarningTrendApi, getWarningStatisticsApi, getDeviceTypeStatisticsApi } from '@/api/warning'
import type { WarningTrendParams, WarningStatisticsParams, DeviceTypeStatisticsParams } from '@/api/warning'
import { getDeviceTypeRunStatisticsApi, getDeviceDCEApi, getDCEStatisticsApi, getFamilyDoctorApi, getPracticalToolsApi, getFileAndResLogStatisticsApi, getLabelStatisticsApi, getMultipleLabelsApi, getResResidentsLogListApi, getAnticipateListApi, getAgeSummaryApi, getFamilyDoctorOverviewApi, getCommunityTourApi, getSuggestionMapResourceApi, getFamilyMapResourceApi, getCraftsmanMapResourceApi, getToolsMapResourceApi, getCommunityTourHeatMapApi, getSpecialStatisticsByAreaApi } from '@/api/bigscreen'
import type { DeviceTypeRunStatisticsResponse, DeviceDCEResponse, DCEStatisticsParams, FamilyDoctorResponse, FamilyDoctorParams, PracticalToolsParams, FileAndResLogStatisticsParams, LabelStatisticsParams, MultipleLabelsParams, ResResidentsLogListParams, ResResidentsLogListItem, AnticipateListParams, AnticipateListItem, AgeSummaryParams, AgeSummaryItem, FamilyDoctorOverviewParams, CommunityTourParams, SuggestionMapResourceParams, FamilyMapResourceParams, CraftsmanMapResourceParams, ToolsMapResourceParams, SpecialStatisticsByAreaParams } from '@/api/bigscreen'
import { getDistrictCode, getDateRange } from '@/utils/district'
import { decryptResponseData } from '@/utils/crypto'
import { getSuggestionOverviewApi, getSuggestionAdoptApi, getSuggestionListApi, getAreaCode, getTourInfoApi, getTourHotsApi, getTourListApi } from '@/api/suggestion'
import type { TourInfoParams, TourHotsParams, TourListParams } from '@/api/suggestion'
import { ElMessage } from 'element-plus'
import { useFullscreenStore } from '@/stores/fullscreen'

// 导入图片
import toolX1 from '@/assets/tool/x1.png'
import toolX2 from '@/assets/tool/x2.png'
import toolX3 from '@/assets/tool/x3.png'
import communityX1 from '@/assets/images/community/x1.png'

// 导入设备图标
import deviceIcon1 from '@/assets/images/device/x1.png'
import deviceIcon2 from '@/assets/images/device/x2.png'
import deviceIcon3 from '@/assets/images/device/x3.png'
import deviceIcon4 from '@/assets/images/device/x4.png'
import deviceIcon5 from '@/assets/images/device/x5.png'
import deviceIcon6 from '@/assets/images/device/x6.png'
import deviceIcon7 from '@/assets/images/device/x7.png'
import deviceIcon8 from '@/assets/images/device/x8.png'
import deviceIcon9 from '@/assets/images/device/x9.png'
import deviceIcon10 from '@/assets/images/device/x10.png'
import deviceIcon11 from '@/assets/images/device/x11.png'
import deviceIcon12 from '@/assets/images/device/x12.png'
import deviceIcon13 from '@/assets/images/device/x13.png'
import deviceIcon14 from '@/assets/images/device/x14.png'
import deviceIcon15 from '@/assets/images/device/x15.png'
import deviceIcon16 from '@/assets/images/device/x16.png'
import deviceIcon17 from '@/assets/images/device/x17.png'
import deviceIcon18 from '@/assets/images/device/x18.png'
import deviceIcon19 from '@/assets/images/device/x19.png'
import deviceIcon20 from '@/assets/images/device/x20.png'
import deviceIcon21 from '@/assets/images/device/x21.png'

// 设备图标数组
const deviceIcons = [
  deviceIcon1, deviceIcon2, deviceIcon3, deviceIcon4, deviceIcon5,
  deviceIcon6, deviceIcon7, deviceIcon8, deviceIcon9, deviceIcon10,
  deviceIcon11, deviceIcon12, deviceIcon13, deviceIcon14, deviceIcon15,
  deviceIcon16, deviceIcon17, deviceIcon18, deviceIcon19, deviceIcon20,
  deviceIcon21
]

  // 全屏状态管理
  const fullscreenStore = useFullscreenStore()
  
  // 页面切换状态
  const currentPage = ref(1) // 1: 智能物联, 2: 崃建言, 3: 社区服务, 4: 社区经济
  const currentPageTitle = computed(() => {
    switch (currentPage.value) {
      case 1: return '智能物联'
      case 2: return '崃建言'
      case 3: return '社区服务'
      case 4: return '社区经济'
      default: return '智能物联'
    }
  })

  // 跟踪当前激活的地图撒点类型
  const activeMapPointType = ref('') // 'family', 'suggestion', 'warning', 'resource', 'navigation', 'tools'

  // 智能物联子模块tab状态
  const iotActiveTab = ref('warning') // warning: 三色预警, equipment: 物联设备运行概况, deviceWarning: 设备预警概况

  // 社区服务子模块tab状态
  const serviceActiveTab = ref('precision') // precision: 精治数仓, doctor: 家庭医生, tools: 实用工具

  // 社区经济子模块tab状态
  const economyActiveTab = ref('navigation') // navigation: 社区导览, tour: 社区游线

const activeTab = ref('precision')
const timeRange = ref('0')
const timeRangeCurve = ref('2')
const timeRangeHorizontal = ref('2')

const timeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]

const curveTimeOptions = timeOptions.filter(i => ['2','3','4'].includes(i.value))
const horizontalTimeOptions = timeOptions.filter(i => ['2','3','4'].includes(i.value))

// 预警类型选择
const selectedWarningType = ref('')
const warningTypeOptions = [
  { label: '用水量异常', value: '521' },
  { label: '烟雾探测', value: '502' },
  { label: '高空抛物', value: '531' },
  { label: '电梯监控', value: '543' },
  { label: '水位超限', value: '501' },
  { label: '可燃气体', value: '542' },
  { label: '消防占道', value: '567' },
  { label: '人员跌倒', value: '540' },
  { label: '火焰监测', value: '503' },
  { label: '区域入侵', value: '505' },
  { label: '徘徊行为', value: '568' },
  { label: '雨量监测', value: '565' }
]

// 模拟数据
const warningData = reactive({
  red: 5,
  yellow: 12,
  blue: 8
})

const data = reactive({
  curveData: {
    red: [2, 5, 3, 8, 6, 4],
    yellow: [8, 12, 15, 10, 18, 14],
    blue: [6, 8, 10, 12, 9, 11],
    label: ['7月', '8月', '9月', '10月', '11月', '12月']
  },
  horizontalData: {
    red: [3, 2, 1, 4],
    yellow: [8, 6, 4, 10],
    blue: [5, 7, 3, 8],
    label: ['阳光社区', '和谐社区', '幸福社区', '安康社区']
  }
})

// 崃建言相关数据和时间选项
const adoptionTimeOptions = timeOptions.filter(i => ['2','3','4'].includes(i.value))
const processTimeOptions = timeOptions.filter(i => ['2','3','4'].includes(i.value))

// 时间范围选择
const overviewTimeRange = ref('0')
const adoptionTimeRange = ref('2')
const processTimeRange = ref('2')

// 崃建言数据
const overviewData = ref({
  receivedCount: 156,
  feedbackCount: 142
})

// 社区服务相关数据 - 初始化为0，等待从API获取真实数据
const communityData = ref({
  communityCount: 0,
  populationCount: 0,
  tagCount: 0,
  enterpriseCount: 0,
  relationCount: 0,
  houseCount: 0,
  documentCount: 0,
  memberCount: 0,
  multiTagCount: 0
})

// 文件和人员信息统计数据
const fileAndResLogData = ref({
  fileTotal: 0,        // 文件总数
  resLogTotal: 0       // 人员信息主动补全数量
})

// 标签统计数据 - 从API获取
const tagStatistics = ref<Array<{name: string, count: number}>>([])

// 多重标签人群数据
const multiTagCount = ref(0)

// 社区文件资源列表数据 - 从API获取
const dataSourceList = ref<Array<{text: string}>>([
  { text: '公安户籍数据' },
  { text: '民政低保数据' },
  { text: '卫健医疗数据' },
  { text: '教育学籍数据' },
  { text: '住建房产数据' },
  { text: '人社就业数据' }
])

// 数据使用预测数据 - 从API获取
const anticipateDataList = ref<Array<{
  title: string
  count: number
}>>([
  {
    title: '未来一个月将新增满80岁老人',
    count: 44
  }
])

// 人口结构分析数据 - 从API获取
const ageSummaryData = ref<Array<{
  name: string
  value: number
}>>([
  { name: '0-6岁', value: 375 },
  { name: '7-14岁', value: 172 },
  { name: '15-24岁', value: 305 },
  { name: '25-34岁', value: 325 },
  { name: '35-49岁', value: 1652 },
  { name: '50-59岁', value: 2823 },
  { name: '60-74岁', value: 3358 },
  { name: '75-79岁', value: 161 },
  { name: '80岁以上', value: 162 }
])

// 人口结构分析图表数据 - 转换为EchartsColumnar组件需要的格式
const ageSummaryChartData = computed(() => {
  return {
    labelData: ageSummaryData.value.map(item => item.name),
    columnarDataBlue: ageSummaryData.value.map(item => item.value),
    columnarDataYellow: []
  }
})

const adoptionData = ref([
  { name: '采纳', value: 65, color: '#018FFF' },
  { name: '部分采纳', value: 23, color: '#E9B902' },
  { name: '留作参考', value: 12, color: '#5D7092' }
])

const processData = ref([
  { district: { name: '阳光社区' }, receivedCount: 25, feedbackCount: 22 },
  { district: { name: '和谐社区' }, receivedCount: 18, feedbackCount: 16 },
  { district: { name: '幸福社区' }, receivedCount: 32, feedbackCount: 28 },
  { district: { name: '安康社区' }, receivedCount: 28, feedbackCount: 25 },
  { district: { name: '美好社区' }, receivedCount: 21, feedbackCount: 19 },
  { district: { name: '温馨社区' }, receivedCount: 19, feedbackCount: 17 },
  { district: { name: '文明社区' }, receivedCount: 24, feedbackCount: 21 },
  { district: { name: '繁荣社区' }, receivedCount: 16, feedbackCount: 14 }
])
const toPage = (item: Record<string, any>, customAreaCode?: string) => {
  // 获取区域代码
  const areaCode = customAreaCode || JSON.parse(localStorage.getItem('communityUserInfo') || '{}').districtCode || getDistrictCode()
  
  // 设置弹窗数据
  selectedDevice.value = {
    typeName: item.name || item.DeviceTypeName || '设备列表',
    type: item.deviceType || item.DeviceType || 0,
    areaCode: areaCode
  }
  
  // 打开弹窗
  deviceDialogVisible.value = true
  
  console.log('打开设备列表弹窗:', selectedDevice.value)
}

// 显示预警详情
const showWarningDetail = (item: any) => {
  selectedWarningEvent.value = item
  warningDetailVisible.value = true
  console.log('打开预警详情弹窗:', item)
}
const change = async (index: string) => {
  timeRange.value = index
  // 根据时间范围更新概况数据
  await updateWarningData(index)
}

const changeCurve = async (index: string) => {
  timeRangeCurve.value = index
  // 如果选择了预警类型，使用类型数据，否则使用时间范围数据
  if (selectedWarningType.value) {
    updateCurveDataByType(selectedWarningType.value)
  } else {
    await updateCurveData(index)
  }
}

const changeHorizontal = async (index: string) => {
  timeRangeHorizontal.value = index
  // 根据时间范围更新统计数据
  await updateHorizontalData(index)
}

// 预警类型变化处理
const handleWarningTypeChange = async (value: string) => {
  console.log('选择的预警类型:', value)
  // 根据选择的预警类型更新趋势图数据，使用真实API
  await updateCurveData(timeRangeCurve.value)
}

// 家庭医生数据
const doctorServiceData = ref({
  doctorCount: 0,
  residentCount: 0,
  signupCount: 0
})

// 家庭医生服务概况时间选择
const doctorOverviewTimeRange = ref('0')
const doctorOverviewTimeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]

const doctorDistributionData = ref([
  { name: '黄忠镇', value: 85 },
  { name: '平乐镇', value: 72 },
  { name: '夹关镇', value: 45 },
  { name: '火井镇', value: 38 },
  { name: '临济镇', value: 32 },
  { name: '天台山镇', value: 28 },
  { name: '南宝山镇', value: 15 },
  { name: '水口镇', value: 25 },
  { name: '文君街道', value: 135 },
  { name: '临邛街道', value: 88 }
])

const diabetesSignupData = ref([
  { name: '黄忠镇', value: 913 },
  { name: '平乐镇', value: 771 },
  { name: '夹关镇', value: 516 },
  { name: '火井镇', value: 440 },
  { name: '临济镇', value: 328 },
  { name: '天台山镇', value: 305 },
  { name: '南宝山镇', value: 253 },
  { name: '水口镇', value: 449 },
  { name: '文君街道', value: 3055 },
  { name: '临邛街道', value: 2812 }
])

const signupRateData = ref([
  { name: '天台山镇', rate: 0 },
  { name: '临济镇', rate: 0 },
  { name: '高埂街道', rate: 0 },
  { name: '火井镇', rate: 0 },
  { name: '平乐镇', rate: 0 }
])

// 社区经济 - 社区导览数据
const navigationData = reactive<{
  totalNumber: number
  chartData1: {
    columnarDataBlue: number[]
    columnarDataYellow: number[]
    curveData: number[]
    labelData: string[]
  }
  chartData2: {
    columnarDataBlue: number[]
    columnarDataYellow: number[]
    curveData: number[]
    labelData: string[]
  }
  pieChartData: any[]
  pieChartLegendData: any[]
  pieChartSerie1Data: any[]
  pieChartSerie2Data: any[]
  resourcePieChartData: any[] // 新的饼图数据格式
  heatRankingChartData: {
    columnarDataBlue: number[]
    columnarDataYellow: number[]
    curveData: number[]
    labelData: string[]
  }
}>({
  totalNumber: 0,
  chartData1: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  },
  chartData2: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  },
  pieChartData: [],
  pieChartLegendData: [],
  pieChartSerie1Data: [],
  pieChartSerie2Data: [],
  resourcePieChartData: [], // 新的饼图数据格式
  heatRankingChartData: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  }
})

// 社区导览时间选择
const navigationTimeRange = ref('2') // 默认近三月
const navigationTimeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]

const navigationTimer = ref()

// 社区经济 - 社区游线数据
const tourTimeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]

const tourTimeRange = ref('1')
const tourHotTimeRange = ref('1')
const tourListTimeRange = ref('1')
const tourHotListTimeRange = ref('1')

const tourOverviewData = ref<any>({})
const tourTableData = ref<any[]>([])
const tourHotReuseData = ref<any[]>([])
const tourDistributionData = ref<any[]>([])

const tourIsLoadingInfo = ref(true)
const tourIsLoadingList = ref(true)
const tourIsLoadingHotList = ref(true)

// 实用工具数据
const toolsTimeRange = ref('1')
const toolsTimeOptions = [
  { label: '今日', value: '0' },
  { label: '近一月', value: '1' },
  { label: '近一年', value: '2' }
]

const toolsUsageData = ref([
  {
    title: '政策文件',
    img: toolX1,
    unit: '次',
    count: 0,
    color: 'blue'
  },
  {
    title: '水印相机',
    img: toolX2,
    unit: '次',
    count: 0,
    color: 'green'
  },
  {
    title: '证件相机',
    img: toolX3,
    unit: '次',
    count: 0,
    color: 'orange'
  }
])

const policyDistributionData = ref([
  { name: '就业创业', value: 120 },
  { name: '教育培训', value: 85 },
  { name: '医疗保健', value: 95 },
  { name: '社会保障', value: 150 },
  { name: '住房保障', value: 65 }
])

const toolsDistributionData = ref([
  { name: '黄忠镇', value: 45 },
  { name: '平乐镇', value: 32 },
  { name: '夹关镇', value: 28 },
  { name: '火井镇', value: 38 },
  { name: '临济镇', value: 25 },
  { name: '天台山镇', value: 18 },
  { name: '文君街道', value: 85 },
  { name: '临邛街道', value: 52 }
])

const userGroupData = ref([
  { name: '居民端证件照', value: 125 },
  { name: '工作端证件照', value: 85 },
  { name: '居民端水印相机', value: 95 },
  { name: '工作端水印相机', value: 65 }
])

  // 物联设备相关数据
  const deviceOverviewData = reactive({
    total: 0,
    online: 0,
    growthRate: 0
  })

  const deviceDistribution = ref<Array<{ name: string; count: number }>>([])

  const deviceList = ref<Array<{
    name: string
    icon: string
    online: number
    offline: number
    total: number
    deviceType: number
  }>>([])

  const deviceLoading = ref(false)

  // 设备预警相关数据
  const warningLoading = ref(false)
  const warningTimeRange = ref('2') // 默认近一年
  const warningStatistics = reactive({
    total: 0,
    complete: 0,
    unComplete: 0
  })
  const warningList = ref<Array<{
    id: number
    type: string
    area: string
    time: string
    status: string
  }>>([])
  const pendingChart = ref<Array<{
    name: string
    value: number
  }>>([])

  // 新增：风险预警点位排名和类型排名数据
  const deviceRankingData = ref<Array<{
    DeviceName: string
    Count: number
  }>>([])
  const warningTypeRankingData = ref<Array<{
    TotalEventName: string
    Count: number
  }>>([])

  // 时间范围选项
  const warningTimeOptions = [
    { label: '今日', value: '0' },
    { label: '近一月', value: '1' },
    { label: '近一年', value: '2' }
  ]

  // 设备列表弹窗相关
  const deviceDialogVisible = ref(false)
  const selectedDevice = ref({
    typeName: '',
    type: 0,
    areaCode: ''
  })

  // 预警详情弹窗相关
  const warningDetailVisible = ref(false)
  const selectedWarningEvent = ref<any>(null)

  // 三色预警数据详情弹窗相关
  const warningDataDetailVisible = ref(false)

  // 社区导览数据详情弹窗相关
  const communityTourDataDetailVisible = ref(false)

  // 实用工具数据详情弹窗相关
  const toolsDataDetailVisible = ref(false)

  // 家庭医生服务概况数据详情弹窗相关
  const familyServiceDataDetailVisible = ref(false)

  // 崃建言数据详情弹窗相关
  const suggestionDataDetailVisible = ref(false)

  const riskRanking = ref([
    { name: '高埂-1单元背面', count: 20271 },
    { name: '智慧小区_博盛康都_电梯-20标', count: 454 },
    { name: '智慧小区_博盛康都_电梯-22标2单元客梯', count: 402 }
  ])

  // 精准数仓数据已在前面声明，此处删除重复声明

const changeToolsTime = (index: string) => {
  toolsTimeRange.value = index
  // 根据时间范围更新实用工具数据
  updateToolsData(index)
}

  const changeWarningTime = async (index: string) => {
    warningTimeRange.value = index
    // 根据时间范围更新预警数据
    // console.log('切换预警时间范围:', index)
    await getDeviceWarningData()
  }

  // 图片加载错误处理
  const handleImageError = (event: Event) => {
    const img = event.target as HTMLImageElement
    // console.error('图片加载失败:', img.src)
  }

  // 图片加载成功处理
  const handleImageLoad = (event: Event) => {
    const img = event.target as HTMLImageElement
    // console.log('图片加载成功:', img.src)
  }

  // 获取设备预警数据
  const getDeviceWarningData = async (customAreaCode?: string) => {
    try {
      warningLoading.value = true
      // console.log('🔄 调用设备预警API')
      
      // 获取区域代码
      const areaCode = customAreaCode || getDistrictCode()
      
      // 根据时间范围计算timeStart日期
      const getTimeStart = (timeRange: string): string => {
        const now = new Date()
        let startDate: Date
        
        switch (timeRange) {
          case '0': // 今日
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
            break
          case '1': // 近一月
            startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
            break
          case '2': // 近一年
            startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
            break
          default:
            startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
        }
        
        // 格式化为 YYYY-M-D 格式
        const year = startDate.getFullYear()
        const month = startDate.getMonth() + 1
        const day = startDate.getDate()
        return `${year}-${month}-${day}`
      }
      
      const timeStart = getTimeStart(warningTimeRange.value)
      
      const params = {
        areaCode,
        timeStart
      }
      
      // console.log('📤 设备预警API请求参数:', params)
      
      // // 调用API
       const response = await getDeviceDCEApi(params)
      
      // console.log('📡 设备预警API响应:', response)
      // console.log('==== 设备预警API返回的原始数据 ====')
      // console.log(JSON.stringify(response, null, 2))
      // console.log('============================================')
      
      // 解析API返回的数据结构
      const apiResponse = response as any
      
      if (apiResponse && apiResponse.state === 1 && apiResponse.message) {
        const data = apiResponse.message
        
        // 更新预警统计数据
        warningStatistics.total = data.Statistics?.Total || 0
        warningStatistics.complete = data.Statistics?.Complete || 0
        warningStatistics.unComplete = data.Statistics?.UnComplete || 0
        
        // 更新预警消息列表
        warningList.value = data.List?.map((item: any) => ({
          id: item.DCEId,
          type: item.TotalEventName,
          area: item.ParkName,
          time: item.EventDate,
          status: item.CompleteState
        })) || []
        
        // 更新待处理事件分布
        pendingChart.value = data.UnCompleteGroup?.map((item: any) => ({
          name: item.Name,
          value: item.Count
        })) || []
        
        console.log('✅ 设备预警数据更新成功:', {
          统计数据: warningStatistics,
          预警列表: warningList.value.length,
          分布数据: pendingChart.value.length
        })
      } else {
        throw new Error('API返回数据格式异常或状态不正确')
      }
      
      // 获取设备预警统计数据（风险预警点位排名和类型排名）
       await getDeviceWarningStatistics(customAreaCode)
      
    } catch (error) {
      console.error('❌ 获取设备预警数据失败:', error)
      
      // 使用模拟数据作为降级
      warningStatistics.total = 156
      warningStatistics.complete = 132
      warningStatistics.unComplete = 24
      
      warningList.value = [
        { id: 1, type: '电梯监控', area: '博盛康郡', time: '2024-12-20 10:30', status: '待处理' },
        { id: 2, type: '烟雾探测', area: '阳光小区', time: '2024-12-20 09:15', status: '已处理' },
        { id: 3, type: '高空抛物', area: '和谐家园', time: '2024-12-20 08:45', status: '待处理' }
      ]
      
      pendingChart.value = [
        { name: '博盛康郡', value: 8 },
        { name: '阳光小区', value: 6 },
        { name: '和谐家园', value: 5 },
        { name: '幸福社区', value: 3 },
        { name: '绿色家园', value: 2 }
      ]
      
      // 设置模拟的排名数据
      deviceRankingData.value = [
        { DeviceName: '电梯监控1', Count: 15 },
        { DeviceName: '烟雾探测器2', Count: 12 },
        { DeviceName: '高空抛物监控3', Count: 8 }
      ]
      
      warningTypeRankingData.value = [
        { TotalEventName: '电梯监控', Count: 45 },
        { TotalEventName: '烟雾探测', Count: 32 },
        { TotalEventName: '高空抛物', Count: 28 },
        { TotalEventName: '水位超限', Count: 22 },
        { TotalEventName: '可燃气体', Count: 18 }
      ]
    } finally {
      warningLoading.value = false
    }
  }

  // 获取设备预警统计数据（风险预警点位排名和类型排名）
  const getDeviceWarningStatistics = async (customAreaCode?: string) => {
    try {
      console.log('🔄 调用设备预警统计API')
      
      // 获取区域代码
      const areaCode = customAreaCode || getDistrictCode()
      
      const params: DCEStatisticsParams = {
        areaCode
      }
      
      console.log('📤 设备预警统计API请求参数:', params)
      
      // 调用API
      const response = await getDCEStatisticsApi(params)
      
      console.log('📡 设备预警统计API响应:', response)
      
      // 解析API返回的数据结构
      const apiResponse = response as any
      
      if (apiResponse && apiResponse.state === 1 && apiResponse.message) {
        const data = apiResponse.message
        
        // 更新风险预警点位排名
        deviceRankingData.value = data.Top3Device || []
        
        // 更新风险预警类型排名
        warningTypeRankingData.value = data.ByType || []
        
        console.log('✅ 设备预警统计数据更新成功:', {
          点位排名: deviceRankingData.value.length,
          类型排名: warningTypeRankingData.value.length
        })
      } else {
        throw new Error('统计API返回数据格式异常或状态不正确')
      }
    } catch (error) {
      console.error('❌ 获取设备预警统计数据失败:', error)
      
      // 使用模拟数据作为降级
      deviceRankingData.value = [
        { DeviceName: '电梯监控1', Count: 15 },
        { DeviceName: '烟雾探测器2', Count: 12 },
        { DeviceName: '高空抛物监控3', Count: 8 }
      ]
      
      warningTypeRankingData.value = [
        { TotalEventName: '电梯监控', Count: 45 },
        { TotalEventName: '烟雾探测', Count: 32 },
        { TotalEventName: '高空抛物', Count: 28 },
        { TotalEventName: '水位超限', Count: 22 },
        { TotalEventName: '可燃气体', Count: 18 }
      ]
    }
  }

  // 获取设备信息 - 调用新的设备信息接口
  const getDeviceInformation = async (areaCode: string) => {
    try {
      console.log('🔄 调用设备信息API')

      console.log('📤 设备信息API请求参数:', { areaCode, deviceType: null })

      // 调用设备信息接口，deviceType为空
      const response = await getDeviceTypeStatisticsApi({
        areaCode,
        deviceType: undefined
      })

      console.log('📡 设备信息API响应:', response)
   
      // 解析API返回的数据结构
      const apiResponse = response as any

      if (apiResponse && apiResponse.state === 1 && apiResponse.message) {
        const data = apiResponse.message

        console.log('📊 设备信息API数据结构:', {
          Count: data.Count,
          ListLength: data.List ? data.List.length : 0,
          SampleDevice: data.List && data.List.length > 0 ? data.List[0] : null
        })

        // 如果API返回了设备列表，处理设备信息
        if (data.List && Array.isArray(data.List) && data.List.length > 0) {
          // 按设备类型名称分组统计
          const deviceTypeMap = new Map()

          data.List.forEach((device: any) => {
            const deviceTypeName = device.DeviceTypeName || '未知设备类型'
            const deviceType = Number(device.DeviceType) || 0
            const isOnline = device.DeviceState === 2 // 根据实际数据，DeviceState=2表示在线

            if (deviceTypeMap.has(deviceTypeName)) {
              const existing = deviceTypeMap.get(deviceTypeName)
              existing.total += 1
              existing.online += isOnline ? 1 : 0
              existing.offline += isOnline ? 0 : 1
            } else {
              const iconIndex = deviceTypeMap.size % 21
              deviceTypeMap.set(deviceTypeName, {
                name: deviceTypeName,
                icon: deviceIcons[iconIndex],
                online: isOnline ? 1 : 0,
                offline: isOnline ? 0 : 1,
                total: 1,
                deviceType: deviceType
              })
            }
          })

          // 转换为数组格式
          const groupedDeviceList = Array.from(deviceTypeMap.values())

          console.log('✅ 设备信息统计成功:', {
            原始设备数: data.List.length,
            设备类型数: groupedDeviceList.length,
            设备类型统计: groupedDeviceList.map(item => ({
              类型: item.name,
              总数: item.total,
              在线: item.online,
              离线: item.offline
            }))
          })

          // 更新设备列表显示（替换现有的模拟数据）
          deviceList.value = groupedDeviceList

        } else {
          console.warn('⚠️ 设备信息API返回的设备列表为空')
        }

      } else {
        console.warn('⚠️ 设备信息API返回数据格式异常:', apiResponse)
      }
    } catch (error) {
      console.error('❌ 获取设备信息失败:', error)
      // 不影响主要功能，只记录错误
    }
  }

  // 获取物联设备运行统计数据
  const getDeviceRunStatistics = async (customAreaCode?: string) => {
    try {
      deviceLoading.value = true
      console.log('🔄 调用物联设备运行统计API')
      
      // 获取区域代码
      const areaCode = customAreaCode || getDistrictCode()
      
      console.log('📤 物联设备API请求参数:', { areaCode })
      
      // 调用API - 先获取设备运行统计
      const response = await getDeviceTypeRunStatisticsApi({ areaCode })

      // 同时调用设备信息接口获取详细的设备信息
      // await getDeviceInformation(areaCode)
      
      // console.log('📡 物联设备API响应:', response)
      // console.log('==== 物联设备API返回的原始数据 ====')
      // console.log(JSON.stringify(response, null, 2))
      // console.log('============================================')
      
      // 解析API返回的数据结构
      const apiResponse = response as any
      
      if (apiResponse && apiResponse.state === 1 && apiResponse.message) {
        const data = apiResponse.message
        
        // 更新设备概况数据
        deviceOverviewData.total = data.Total || 0
        deviceOverviewData.online = data.Online || 0
        deviceOverviewData.growthRate = data.LastMonthOnlineGrowthRate || 0
        
        // 更新设备分布数据
        deviceDistribution.value = data.ByArea?.map((item: any) => ({
          name: item.Name,
          count: item.Count
        })) || []
        
        // 更新设备列表数据，添加默认图标
        deviceList.value = data.DeviceTypeList?.map((item: any, index: number) => {
          const iconIndex = index % 21
          return {
            name: item.DeviceTypeName,
            icon: deviceIcons[iconIndex],
            online: item.Online,
            offline: item.Offline,
            total: item.Count, // 添加设备总数
            deviceType: item.DeviceType
          }
        }) || []
        
        console.log('✅ 物联设备数据更新成功:', {
          总数: deviceOverviewData.total,
          在线: deviceOverviewData.online,
          增长率: deviceOverviewData.growthRate,
          分布数据: deviceDistribution.value.length,
          设备列表: deviceList.value.length
        })
      } else {
        throw new Error('API返回数据格式异常或状态不正确')
      }
    } catch (error) {
      console.error('❌ 获取物联设备数据失败:', error)
      // ElMessage.warning('获取物联设备数据失败，使用模拟数据')
      
      // 使用模拟数据作为备用
      deviceOverviewData.total = 721
      deviceOverviewData.online = 626
      deviceOverviewData.growthRate = 15
      
      deviceDistribution.value = [
        { name: '天台山镇', count: 58 },
        { name: '孔明街道', count: 98 },
        { name: '桑园镇', count: 86 },
        { name: '火井镇', count: 90 },
        { name: '临邛街道', count: 151 },
        { name: '买关镇', count: 66 },
        { name: '平乐镇', count: 10 },
        { name: '羊安街道', count: 104 },
        { name: '文君街道', count: 58 }
      ]
      
      deviceList.value = [
        { name: '烟雾传感器', icon: deviceIcons[0], online: 359, offline: 20, total: 379, deviceType: 1 },
        { name: '水表', icon: deviceIcons[1], online: 113, offline: 2, total: 115, deviceType: 2 },
        { name: '高空抛物监控', icon: deviceIcons[2], online: 54, offline: 0, total: 54, deviceType: 3 },
        { name: '电梯监控摄像头', icon: deviceIcons[3], online: 46, offline: 12, total: 58, deviceType: 4 },
        { name: '城市内涝监测', icon: deviceIcons[4], online: 0, offline: 6, total: 6, deviceType: 5 },
        { name: '烟气传感器', icon: deviceIcons[5], online: 27, offline: 31, total: 58, deviceType: 6 },
        { name: '占道经营', icon: deviceIcons[6], online: 10, offline: 0, total: 10, deviceType: 7 },
        { name: '广播设备', icon: deviceIcons[7], online: 0, offline: 20, total: 20, deviceType: 8 },
        { name: 'NVR网络硬盘录像机', icon: deviceIcons[8], online: 1, offline: 2, total: 3, deviceType: 9 },
        { name: '火情监测摄像头', icon: deviceIcons[9], online: 10, offline: 0, total: 10, deviceType: 10 },
        { name: '消防通道监测摄像头', icon: deviceIcons[10], online: 2, offline: 0, total: 2, deviceType: 11 }
      ]
    } finally {
      deviceLoading.value = false
    }
  }

const updateToolsData = async (rangeType: string, customAreaCode?: string) => {
  try {
    console.log('🔄 调用实用工具API，时间范围:', rangeType)
    
    // 获取区域代码
    const areaCode = customAreaCode || getDistrictCode()
    
    // 时间类型映射：'0'-今日，'1'-近一月，'2'-近一年
    const timeTypeMap: Record<string, number> = {
      '0': 0, // 今日
      '1': 1, // 近一月
      '2': 2  // 近一年
    }
    
    const timeType = timeTypeMap[rangeType] || 1
    
    const params: PracticalToolsParams = {
      areaCode,
      timeType
    }
    
    console.log('📤 实用工具API请求参数:', params)
    
    // 调用API
    const response = await getPracticalToolsApi(params)
    
    // console.log('📡 实用工具API响应:', response)
    // console.log('==== 实用工具API返回的原始数据 ====')
    // console.log(JSON.stringify(response, null, 2))
    // console.log('============================================')
    
    if (response && response.code === 200 && response.data) {
      const data = response.data as any
      
      // 更新使用数统计
      toolsUsageData.value[0].count = data.fileCount || 0
      toolsUsageData.value[1].count = data.watermarkCameraCount || 0
      toolsUsageData.value[2].count = data.idCameraCount || 0
      
      // 更新政策类型分布
      if (data.fileTypeList && Array.isArray(data.fileTypeList)) {
        policyDistributionData.value = data.fileTypeList.map((item: any) => ({
          name: item.dictLabel || item.name,
          value: item.useCount || 0
        }))
      }
      
      // 更新工具使用分布（按区域）
      if (data.districtList && Array.isArray(data.districtList)) {
        toolsDistributionData.value = data.districtList.map((item: any) => ({
          name: item.name,
          value: item.useCount || 0
        }))
      }
      
      // 更新用户群体分布
      userGroupData.value = [
        { name: '居民端证件照', value: data.idCameraPublicCount || 0 },
        { name: '工作端证件照', value: data.idCameraWorkCount || 0 },
        { name: '居民端水印相机', value: data.watermarkCameraPublicCount || 0 },
        { name: '工作端水印相机', value: data.watermarkCameraWorkCount || 0 }
      ]
      
      console.log('✅ 实用工具数据更新成功:', {
        政策文件: data.fileCount,
        水印相机: data.watermarkCameraCount,
        证件相机: data.idCameraCount,
        政策类型: policyDistributionData.value.length,
        区域分布: toolsDistributionData.value.length
      })
    } else {
      console.warn('⚠️ 实用工具API返回数据格式异常，使用模拟数据')
      throw new Error('API返回数据格式异常')
    }
  } catch (error) {
    console.error('❌ 获取实用工具数据失败:', error)
    // ElMessage.warning('获取实用工具数据失败，使用模拟数据')
    
    // 使用模拟数据作为备用
    const mockData = {
      '0': { file: 0, watermark: 0, id: 0 },
      '1': { file: 2, watermark: 5, id: 3 },
      '2': { file: 15, watermark: 28, id: 22 }
    }
    
    const selectedData = mockData[rangeType as keyof typeof mockData] || mockData['1']
    toolsUsageData.value[0].count = selectedData.file
    toolsUsageData.value[1].count = selectedData.watermark
    toolsUsageData.value[2].count = selectedData.id
    
    // 使用模拟的政策类型分布数据
    policyDistributionData.value = [
      { name: '就业创业', value: 120 },
      { name: '教育培训', value: 85 },
      { name: '医疗保健', value: 95 },
      { name: '社会保障', value: 150 },
      { name: '住房保障', value: 65 }
    ]
    
    // 使用模拟的工具使用分布数据
    toolsDistributionData.value = [
      { name: '黄忠镇', value: 45 },
      { name: '平乐镇', value: 32 },
      { name: '夹关镇', value: 28 },
      { name: '火井镇', value: 38 },
      { name: '临济镇', value: 25 },
      { name: '天台山镇', value: 18 },
      { name: '文君街道', value: 85 },
      { name: '临邛街道', value: 52 }
    ]
    
    // 使用模拟的用户群体分布数据
    userGroupData.value = [
      { name: '居民端证件照', value: 125 },
      { name: '工作端证件照', value: 85 },
      { name: '居民端水印相机', value: 95 },
      { name: '工作端水印相机', value: 65 }
    ]
  }
}

// 社区经济 - 社区导览数据获取
const getNavigationData = async (timeType: string = '2', customAreaCode?: string) => {
  try {
    console.log('🔄 调用社区导览API，时间范围:', timeType)
    
    // 清空现有数据
    navigationData.totalNumber = 0
    navigationData.chartData1 = {
      columnarDataBlue: [],
      columnarDataYellow: [],
      curveData: [],
      labelData: []
    }
    navigationData.chartData2 = {
      columnarDataBlue: [],
      columnarDataYellow: [],
      curveData: [],
      labelData: []
    }
    navigationData.pieChartData = []
    navigationData.pieChartLegendData = []
    navigationData.pieChartSerie1Data = []
    navigationData.pieChartSerie2Data = []
    navigationData.resourcePieChartData = []
    navigationData.heatRankingChartData = {
      columnarDataBlue: [],
      columnarDataYellow: [],
      curveData: [],
      labelData: []
    }
    
    const areaCode = customAreaCode || getDistrictCode()
    const params: CommunityTourParams = {
      areaCode: areaCode.substring(0, 6), // 使用前6位作为区域代码
      timeType: parseInt(timeType)
    }
    
    console.log('📡 社区导览API参数:', params)
    
    const response = await getCommunityTourApi(params)
    
    console.log('📡 社区导览API响应:', response)
    
    if (response && response.code === 200 && response.message) {
      const message = response.message as any
      const { districtList, totalNumber, lifeCircleTypeList } = message
      
      // 1. 使用总人次
      navigationData.totalNumber = totalNumber || 0
      
      // 2. 资源分布数据 - 使用lifeCircleTypeList (柱状图显示)
      lifeCircleTypeList.forEach((item: any) => {
        if (item.lifeCircleCount && item.lifeCircleCount > 0) {
          navigationData.chartData1.columnarDataBlue.push(item.lifeCircleCount)
          navigationData.chartData1.labelData.push(item.typeName)
        }
      })
      
      // 同时为饼图等其他组件保留数据
      const resourceColors = ['#CEC60E', '#8815D4', '#C408E7', '#04EFD4', '#11B8D5', '#F16154', '#166DC8']
      const resourceBlurryColors = [
        'rgba(206, 198, 14, 0.7)', 'rgba(136, 21, 212, 0.7)', 'rgba(196, 8, 231, 0.7)', 
        'rgba(4, 239, 212, 0.7)', 'rgba(17, 184, 213, 0.7)', 'rgba(241, 97, 84, 0.7)', 'rgba(22, 109, 200, 0.7)'
    ]
    
      lifeCircleTypeList.forEach((item: any, index: number) => {
        if (item.lifeCircleCount && item.lifeCircleCount > 0) {
          const colorIndex = index % resourceColors.length
          
      navigationData.pieChartData.push({
            value: item.lifeCircleCount,
            name: item.typeName
      })
      
      navigationData.pieChartSerie1Data.push({
            value: item.lifeCircleCount,
            name: item.typeName,
        itemStyle: {
              color: resourceBlurryColors[colorIndex]
        }
      })
      
      navigationData.pieChartSerie2Data.push({
            value: item.lifeCircleCount,
            name: item.typeName,
        itemStyle: {
              color: resourceColors[colorIndex]
        }
      })
      
      // 为新的饼图组件添加数据
      navigationData.resourcePieChartData.push({
        name: item.typeName,
        value: item.lifeCircleCount,
        color: resourceColors[colorIndex]
      })
        }
    })
    
      // 3. 使用数量数据 - 使用districtList的useCount
      districtList.forEach((item: any) => {
        navigationData.chartData2.columnarDataBlue.push(item.useCount || 0)
        navigationData.chartData2.labelData.push(item.name)
      })
      
      // 4. 使用热度排行数据 - 使用districtList的communityLineUseCount
      // 先按热度排序，取前10名
      const sortedDistricts = [...districtList].sort((a: any, b: any) => (b.communityLineUseCount || 0) - (a.communityLineUseCount || 0))
      const top10Districts = sortedDistricts.slice(0, 10)
      
      top10Districts.forEach((item: any) => {
        navigationData.heatRankingChartData.columnarDataBlue.push(item.communityLineUseCount || 0)
        navigationData.heatRankingChartData.labelData.push(item.name)
      })
      
      console.log('✅ 社区导览数据更新成功')
      console.log('  - 使用总人次:', navigationData.totalNumber)
      console.log('  - 资源分布项目数:', navigationData.chartData1.labelData.length)
      console.log('  - 使用数量区域数:', navigationData.chartData2.labelData.length)
      console.log('  - 使用热度排行区域数:', navigationData.heatRankingChartData.labelData.length)
      
    } else {
      throw new Error('API返回数据格式异常')
    }
    
  } catch (error) {
    console.error('❌ 获取社区导览数据失败:', error)
    // 保持数据为空，不设置降级数据
  }
}

// 处理社区导览时间变更
const changeNavigationTime = async (timeType: string) => {
  console.log('社区导览时间变更:', timeType)
  navigationTimeRange.value = timeType
  await getNavigationData(timeType)
}

// 社区经济 - 社区游线数据获取
const getTourData = async (customAreaCode?: string) => {
  try {
    console.log('获取社区游线数据')
    
    const areaCode = customAreaCode || getDistrictCode()
    const timeType = 1 // 默认近一月，数字类型
    
    // 1. 获取概览数据
    try {
      const tourInfoResponse = await getTourInfoApi({ areaCode, timeType }) as any
      console.log('社区游线概览API响应:', tourInfoResponse)
      
      if (tourInfoResponse.code === 200 && tourInfoResponse.message) {
        tourOverviewData.value = {
          totalItems: tourInfoResponse.message.goodsCount || 0,
          totalClicks: tourInfoResponse.message.viewCount || 0
        }
      } else {
        throw new Error('概览API返回数据格式错误')
      }
    } catch (error) {
      console.error('获取社区游线概览失败:', error)
      // ElMessage.warning('获取社区游线概览失败，使用模拟数据')
      tourOverviewData.value = {
        totalItems: 85,
        totalClicks: 125847
      }
    }
    
    // 2. 获取热浏览数据
    try {
      const tourHotsResponse = await getTourHotsApi({ 
        areaCode, 
        timeType: String(timeType), // 转换为字符串类型
        pageNum: 1,
        pageSize: 10
      }) as any
      console.log('社区游线热浏览API响应:', tourHotsResponse)
      
      if (tourHotsResponse.code === 200 && tourHotsResponse.message?.list?.rows) {
        // 转换为图表数据格式，使用districtName作为显示名称
        tourHotReuseData.value = tourHotsResponse.message.list.rows.map((item: any) => ({
          tourLineName: item.districtName || item.tourLineName, // 优先使用区域名称
          browseNumber: item.browseNumber
        }))
        
        // 同时更新热门游线表格数据
        tourTableData.value = tourHotsResponse.message.list.rows.map((item: any) => ({
          districtName: item.districtName,
          tourLineName: item.tourLineName,
          browseNumber: item.browseNumber
        }))
      } else {
        throw new Error('热浏览API返回数据格式错误')
      }
    } catch (error) {
      console.error('获取社区游线热浏览失败:', error)
      // ElMessage.warning('获取社区游线热浏览失败，使用模拟数据')
      tourHotReuseData.value = [
        { tourLineName: '文化古街游', browseNumber: 2850 },
        { tourLineName: '美食体验游', browseNumber: 2156 },
        { tourLineName: '生态休闲游', browseNumber: 1923 },
        { tourLineName: '历史文化游', browseNumber: 1654 },
        { tourLineName: '民俗风情游', browseNumber: 1432 }
      ]
      
      tourTableData.value = [
        { districtName: '阳光社区', tourLineName: '文化古街游', browseNumber: 2850 },
        { districtName: '和谐社区', tourLineName: '美食体验游', browseNumber: 2156 },
        { districtName: '幸福社区', tourLineName: '生态休闲游', browseNumber: 1923 },
        { districtName: '安康社区', tourLineName: '历史文化游', browseNumber: 1654 },
        { districtName: '美好社区', tourLineName: '民俗风情游', browseNumber: 1432 }
      ]
    }
    
    // 3. 获取分布数据
    try {
      const tourListResponse = await getTourListApi({ 
        areaCode, 
        timeType: String(timeType), // 转换为字符串类型
        pageNum: 1,
        pageSize: 20
      }) as any
      console.log('社区游线分布API响应:', tourListResponse)
      
      if (tourListResponse.code === 200 && tourListResponse.message?.list) {
        // 转换为图表数据格式
        tourDistributionData.value = tourListResponse.message.list.map((item: any) => ({
          name: item.district.name,
          goods: item.goodsCount,
          views: item.viewCount || 0
        }))
      } else {
        throw new Error('分布API返回数据格式错误')
      }
    } catch (error) {
      console.error('获取社区游线分布失败:', error)
      // ElMessage.warning('获取社区游线分布失败，使用模拟数据')
      tourDistributionData.value = [
        { name: '阳光社区', goods: 12, views: 15847 },
        { name: '和谐社区', goods: 10, views: 12356 },
        { name: '幸福社区', goods: 9, views: 11245 },
        { name: '安康社区', goods: 8, views: 9876 },
        { name: '美好社区', goods: 7, views: 8654 }
      ]
    }
    
    tourIsLoadingInfo.value = false
    tourIsLoadingList.value = false
    tourIsLoadingHotList.value = false
    
    console.log('社区游线数据获取完成')
  } catch (error) {
    console.error('获取社区游线数据失败:', error)
    tourIsLoadingInfo.value = false
    tourIsLoadingList.value = false
    tourIsLoadingHotList.value = false
  }
}

// 社区导览时间范围变更处理
const changeNavigationDate = (index: string) => {
  navigationTimeRange.value = index
  getNavigationData(index)
}

// 社区游线时间范围变更处理
const changeTourInfo = async (index: string | number, customAreaCode?: string) => {
  console.log('changeTourInfo:', index)
  tourIsLoadingInfo.value = true
  
  try {
    const areaCode = customAreaCode || getDistrictCode()
    const timeType = Number(index)
    
    const tourInfoResponse = await getTourInfoApi({ areaCode, timeType }) as any
    console.log('社区游线概览API响应:', tourInfoResponse)
    
    if (tourInfoResponse.code === 200 && tourInfoResponse.message) {
      tourOverviewData.value = {
        totalItems: tourInfoResponse.message.goodsCount || 0,
        totalClicks: tourInfoResponse.message.viewCount || 0
      }
    }
  } catch (error) {
    console.error('更新社区游线概览失败:', error)
    ElMessage.warning('更新社区游线概览失败')
  } finally {
    tourIsLoadingInfo.value = false
  }
}

const changeTourHots = async (index: string | number, customAreaCode?: string) => {
  console.log('changeTourHots:', index)
  tourIsLoadingHotList.value = true
  
  try {
    const areaCode = customAreaCode || getDistrictCode()
    const timeType = Number(index)
    
    const tourHotsResponse = await getTourHotsApi({ 
      areaCode, 
      timeType: String(timeType), // 转换为字符串类型
      pageNum: 1,
      pageSize: 10
    }) as any
    console.log('社区游线热浏览API响应:', tourHotsResponse)
    
    if (tourHotsResponse.code === 200 && tourHotsResponse.message?.list?.rows) {
      // 转换为图表数据格式，使用districtName作为显示名称
      tourHotReuseData.value = tourHotsResponse.message.list.rows.map((item: any) => ({
        tourLineName: item.districtName || item.tourLineName, // 优先使用区域名称
        browseNumber: item.browseNumber
      }))
      
      // 同时更新热门游线表格数据
      tourTableData.value = tourHotsResponse.message.list.rows.map((item: any) => ({
        districtName: item.districtName,
        tourLineName: item.tourLineName,
        browseNumber: item.browseNumber
      }))
    }
  } catch (error) {
    console.error('更新社区游线热浏览失败:', error)
    ElMessage.warning('更新社区游线热浏览失败')
  } finally {
    tourIsLoadingHotList.value = false
  }
}

const changeTourList = async (index: string | number, customAreaCode?: string) => {
  console.log('changeTourList:', index)
  tourIsLoadingList.value = true
  
  try {
    const areaCode = customAreaCode || getDistrictCode()
    const timeType = Number(index)
    
    const tourListResponse = await getTourListApi({ 
      areaCode, 
      timeType: String(timeType), // 转换为字符串类型
      pageNum: 1,
      pageSize: 20
    }) as any
    console.log('社区游线分布API响应:', tourListResponse)
    
    if (tourListResponse.code === 200 && tourListResponse.message?.list) {
      // 转换为图表数据格式
      tourDistributionData.value = tourListResponse.message.list.map((item: any) => ({
        name: item.district.name,
        goods: item.goodsCount,
        views: item.viewCount || 0
      }))
    }
  } catch (error) {
    console.error('更新社区游线分布失败:', error)
    ElMessage.warning('更新社区游线分布失败')
  } finally {
    tourIsLoadingList.value = false
  }
}

const changeTourHotList = async (index: string | number, customAreaCode?: string) => {
  console.log('changeTourHotList:', index)
  // 热门游线数据和热浏览数据使用同一个API
  await changeTourHots(index, customAreaCode)
}

const handleTabChange = (tabValue: string) => {
  activeTab.value = tabValue
  console.log('切换到选项卡:', tabValue)
  // 这里可以根据不同的选项卡加载不同的数据
}

const handleGoBack = () => {
  console.log('返回上一页')
  
  // 宽屏模式和全屏模式下的逻辑 - 与Left2联动
  if (fullscreenStore.screenMode === 'wide' || fullscreenStore.isFullscreen) {
    console.log('Left1', fullscreenStore.screenMode, '模式下执行上一页逻辑')
    // 在宽屏模式和全屏模式下，调用全屏状态管理来切换左侧页面
    fullscreenStore.switchLeftPage()
    console.log('Left1', fullscreenStore.screenMode, '模式切换左侧页面，当前左侧页面:', fullscreenStore.leftPage)
    return
  }
  
  if (currentPage.value === 1) {
    currentPage.value = 4
    console.log('切换到社区经济页面')
  } else if (currentPage.value === 2) {
    currentPage.value = 1
    console.log('切换到智能物联页面')
  } else if (currentPage.value === 3) {
    currentPage.value = 2
    console.log('切换到崃建言页面')
  } else if (currentPage.value === 4) {
    currentPage.value = 3
    console.log('切换到社区服务页面')
  }
  
  // 页面切换后的数据初始化（如果需要的话）
  initLeft1PageData(currentPage.value)
}

const handleGoNext = () => {
  // 宽屏模式和全屏模式下不允许切换，由Left2控制
  if (fullscreenStore.screenMode === 'wide' || fullscreenStore.isFullscreen) {
    return
  }
  
  if (currentPage.value === 1) {
    currentPage.value = 2
    console.log('切换到崃建言页面')
  } else if (currentPage.value === 2) {
    currentPage.value = 3
    console.log('切换到社区服务页面')
  } else if (currentPage.value === 3) {
    currentPage.value = 4
    console.log('切换到社区经济页面')
  } else {
    currentPage.value = 1
    console.log('切换到智能物联页面')
  }
  
  // 页面切换后的数据初始化（如果需要的话）
  initLeft1PageData(currentPage.value)
}

const handleIotTabChange = async (tab: string) => {
  iotActiveTab.value = tab
  console.log('切换智能物联tab:', tab)
  
  // 根据不同的tab加载对应的数据
  if (tab === 'warning') {
    // 三色预警数据
    await updateWarningData(timeRange.value)
    await updateCurveData(timeRangeCurve.value)
    await updateHorizontalData(timeRangeHorizontal.value)
  } else if (tab === 'equipment') {
    // 物联设备运行概况数据
    await getDeviceRunStatistics()
  } else if (tab === 'deviceWarning') {
    // 设备预警概况数据
    await getDeviceWarningData()
  }
}

const handleServiceTabChange = async (tab: string) => {
  serviceActiveTab.value = tab
  console.log('切换社区服务tab:', tab)
  
  // 根据不同的tab加载对应的数据
  if (tab === 'precision') {
    // 精治数仓数据已经在onMounted中初始化了
    console.log('精治数仓tab已激活')
  } else if (tab === 'doctor') {
    // 家庭医生数据
    console.log('家庭医生tab已激活，重新加载数据')
    await getFamilyDoctorData()
  } else if (tab === 'tools') {
    // 实用工具数据
    await updateToolsData(toolsTimeRange.value)
  }
}

const handleEconomyTabChange = async (tab: string) => {
  economyActiveTab.value = tab
  console.log('切换社区经济tab:', tab)
  
  // 根据不同的tab加载对应的数据
  if (tab === 'navigation') {
    // 社区导览数据
    await getNavigationData(navigationTimeRange.value)
  } else if (tab === 'tour') {
    // 社区游线数据
    await getTourData()
  }
}

// 崃建言时间范围变更处理
const changeOverview = async (index: string | number) => {
  overviewTimeRange.value = String(index)
  await updateOverviewData(String(index))
}

const changeAdoption = async (index: string | number) => {
  adoptionTimeRange.value = String(index)
  await updateAdoptionData(String(index))
}

const changeProcess = async (index: string | number) => {
  processTimeRange.value = String(index)
  await updateProcessData(String(index))
}



// 崃建言数据更新函数
const updateOverviewData = async (timeType: string) => {
  try {
    // 调用崃建言概览API
    const response = await getSuggestionOverviewApi({
      timeType: timeType,
      areaCode: getAreaCode()
    })
    
    console.log('崃建言概览API响应:', response)
    
    if (response && response.data) {
      // 根据真实API返回的数据结构处理
      const data = response.data
      overviewData.value = {
        receivedCount: data.receivedCount || 0,
        feedbackCount: data.feedbackCount || 0
      }
      
      console.log('概览数据详情:', {
        已接收: data.receivedCount,
        已反馈: data.feedbackCount,
        未反馈: data.unFeedbackCount,
        已转化: data.convertCount
      })
    } else {
      // 如果API返回格式不符合预期，使用模拟数据
      const mockData = {
        '0': { receivedCount: 12, feedbackCount: 10 },
        '1': { receivedCount: 58, feedbackCount: 52 },
        '2': { receivedCount: 156, feedbackCount: 142 },
        '3': { receivedCount: 285, feedbackCount: 268 },
        '4': { receivedCount: 542, feedbackCount: 515 }
      }
      overviewData.value = mockData[timeType as keyof typeof mockData] || mockData['0']
    }
  } catch (error) {
    console.error('获取崃建言概览统计失败:', error)
    ElMessage.warning('获取崃建言概览统计失败，使用模拟数据')
    
    // 使用模拟数据作为后备
    const mockData = {
      '0': { receivedCount: 12, feedbackCount: 10 },
      '1': { receivedCount: 58, feedbackCount: 52 },
      '2': { receivedCount: 156, feedbackCount: 142 },
      '3': { receivedCount: 285, feedbackCount: 268 },
      '4': { receivedCount: 542, feedbackCount: 515 }
    }
    overviewData.value = mockData[timeType as keyof typeof mockData] || mockData['0']
  }
}

const updateAdoptionData = async (timeType: string) => {
  try {
    // 调用崃建言采纳统计API
    const response = await getSuggestionAdoptApi({
      timeType: timeType,
      areaCode: getAreaCode()
    })
    
    console.log('崃建言采纳统计API响应:', response)
    
    if (response && response.data && Array.isArray(response.data)) {
      // 根据真实API返回的数据结构处理
      const adoptionItems = response.data
      
      // 初始化计数器
      let adoptedCount = 0
      let partialAdoptedCount = 0
      let referenceCount = 0
      
      // 遍历数据，根据标签分类统计
      adoptionItems.forEach((item: any) => {
        switch (item.adoptionStatusLabel) {
          case '已采纳':
            adoptedCount = item.total
            break
          case '已转化':
            partialAdoptedCount = item.total
            break
          case '留作参考':
            referenceCount = item.total
            break
          default:
            console.log('未知的采纳状态标签:', item.adoptionStatusLabel)
        }
      })
      
      adoptionData.value = [
        { name: '采纳', value: adoptedCount, color: '#018FFF' },
        { name: '部分采纳', value: partialAdoptedCount, color: '#E9B902' },
        { name: '留作参考', value: referenceCount, color: '#5D7092' }
      ]
    } else {
      // 如果API返回格式不符合预期，使用模拟数据
      const mockData = {
        '2': [
          { name: '采纳', value: 65, color: '#018FFF' },
          { name: '部分采纳', value: 23, color: '#E9B902' },
          { name: '留作参考', value: 12, color: '#5D7092' }
        ],
        '3': [
          { name: '采纳', value: 58, color: '#018FFF' },
          { name: '部分采纳', value: 28, color: '#E9B902' },
          { name: '留作参考', value: 14, color: '#5D7092' }
        ],
        '4': [
          { name: '采纳', value: 62, color: '#018FFF' },
          { name: '部分采纳', value: 25, color: '#E9B902' },
          { name: '留作参考', value: 13, color: '#5D7092' }
        ]
      }
      adoptionData.value = mockData[timeType as keyof typeof mockData] || mockData['2']
    }
  } catch (error) {
    console.error('获取崃建言采纳统计失败:', error)
    // ElMessage.warning('获取崃建言采纳统计失败，使用模拟数据')
    
    // 使用模拟数据作为后备
    const mockData = {
      '2': [
        { name: '采纳', value: 65, color: '#018FFF' },
        { name: '部分采纳', value: 23, color: '#E9B902' },
        { name: '留作参考', value: 12, color: '#5D7092' }
      ],
      '3': [
        { name: '采纳', value: 58, color: '#018FFF' },
        { name: '部分采纳', value: 28, color: '#E9B902' },
        { name: '留作参考', value: 14, color: '#5D7092' }
      ],
      '4': [
        { name: '采纳', value: 62, color: '#018FFF' },
        { name: '部分采纳', value: 25, color: '#E9B902' },
        { name: '留作参考', value: 13, color: '#5D7092' }
      ]
    }
    adoptionData.value = mockData[timeType as keyof typeof mockData] || mockData['2']
  }
}

const updateProcessData = async (timeType: string) => {
  try {
    // 调用崃建言处理情况列表API
    const response = await getSuggestionListApi({
      areaCode: getAreaCode(),
      pageNum: 1,
      pageSize: 50, // 获取更多数据以便展示
      timeType: parseInt(timeType)
    })
    
    console.log('崃建言处理情况API响应:', response)
    
    if (response && response.message && response.message.list) {
      // 使用真实API返回的数据
      const listData = response.message.list
      
      // 按receivedCount降序排列，显示全部数据
      const filteredData = listData
        .sort((a: any, b: any) => b.receivedCount - a.receivedCount)
      
      processData.value = filteredData.map((item: any) => ({
        district: { name: item.district.name },
        receivedCount: item.receivedCount,
        feedbackCount: item.feedbackCount
      }))
      
      console.log('处理情况数据详情:', processData.value)
    } else {
      throw new Error('API返回数据格式不正确')
    }
  } catch (error) {
    console.error('获取崃建言处理情况列表失败:', error)
    // ElMessage.warning('获取崃建言处理情况列表失败，使用模拟数据')
    
    // 使用模拟数据作为后备
    const baseData = [
      { district: { name: '阳光社区' }, receivedCount: 25, feedbackCount: 22 },
      { district: { name: '和谐社区' }, receivedCount: 18, feedbackCount: 16 },
      { district: { name: '幸福社区' }, receivedCount: 32, feedbackCount: 28 },
      { district: { name: '安康社区' }, receivedCount: 28, feedbackCount: 25 },
      { district: { name: '美好社区' }, receivedCount: 21, feedbackCount: 19 },
      { district: { name: '温馨社区' }, receivedCount: 19, feedbackCount: 17 },
      { district: { name: '文明社区' }, receivedCount: 24, feedbackCount: 21 },
      { district: { name: '繁荣社区' }, receivedCount: 16, feedbackCount: 14 }
    ]
    
    // 显示全部数据，按receivedCount降序排列
    processData.value = baseData.sort((a, b) => b.receivedCount - a.receivedCount)
  }
}

const updateWarningData = async (rangeType: string, customAreaCode?: string) => {
  try {
    console.log('🔄 调用三色预警概况API，时间范围:', rangeType)
    
    // 获取日期范围
    const dateRange = getDateRange(rangeType)
    
    // 获取区域代码
    const areaCode = customAreaCode || getDistrictCode()
    
    // 构建API参数
    const params = {
      DateRangeMin: dateRange.DateRangeMin,
      DateRangeMax: dateRange.DateRangeMax,
      AreaCode: areaCode
    }
    
    // console.log('📤 三色预警API请求参数:', params)
    
    // 调用API
    const response = await getWarningOverviewApi(params)
    
    // console.log('📡 三色预警API响应:', response)
    // console.log('==== 三色预警API返回的原始数据 ====')
    // console.log(JSON.stringify(response, null, 2))
    // console.log('============================================')
    
    // 由于API拦截器返回的是data，所以response就是实际的数据对象
    const responseData = response as any
    if (responseData && responseData.state === 1 && responseData.message && Array.isArray(responseData.message) && responseData.message.length > 0) {
      // 获取第一个数据项
      const data = responseData.message[0]
      
      // 直接使用新的数据格式
      warningData.red = data.Red || 0
      warningData.yellow = data.Yellow || 0
      warningData.blue = data.Blue || 0
      
      console.log('✅ 三色预警数据更新成功:', {
        红色: warningData.red,
        黄色: warningData.yellow,
        蓝色: warningData.blue
      })
    } else {
      console.warn('⚠️ 三色预警API返回数据格式异常，使用模拟数据')
      throw new Error('API返回数据格式异常')
    }
  } catch (error) {
    console.error('❌ 获取三色预警数据失败:', error)
    // ElMessage.warning('获取三色预警数据失败，使用模拟数据')
    
    // 使用模拟数据作为备用
    const mockData = {
      '0': { red: 2, yellow: 5, blue: 3 },
      '1': { red: 8, yellow: 15, blue: 12 },
      '2': { red: 25, yellow: 48, blue: 32 },
      '3': { red: 45, yellow: 82, blue: 58 },
      '4': { red: 88, yellow: 156, blue: 124 }
    }
    
    const selectedData = mockData[rangeType as keyof typeof mockData] || mockData['0']
    warningData.red = selectedData.red
    warningData.yellow = selectedData.yellow
    warningData.blue = selectedData.blue
  }
}

const updateCurveData = async (rangeType: string, customAreaCode?: string) => {
  try {
    // 获取区域代码
    const areaCode = customAreaCode || JSON.parse(localStorage.getItem('communityUserInfo') || '{}').districtCode || getDistrictCode()
    
    // 根据时间范围计算日期
    const dateRange = getDateRange(rangeType)
    
    // 构建API参数
    const params: WarningTrendParams = {
      DateRangeMin: dateRange.DateRangeMin,
      DateRangeMax: dateRange.DateRangeMax,
      AreaCode: areaCode
    }
    
    // 如果选择了预警类型，添加到参数中
    if (selectedWarningType.value) {
      params.WarningType = parseInt(selectedWarningType.value)
    }
    
    console.log('🔍 调用三色预警趋势API:', params)
    
    // 调用API
    const response = await getWarningTrendApi(params) as any
    
    if (response && response.state === 1 && response.message) {
      // 处理API返回的数据
      const trendData = response.message
      
      // 转换数据格式
      const labels: string[] = []
      const redData: number[] = []
      const yellowData: number[] = []
      const blueData: number[] = []
      
      trendData.forEach((item: any) => {
        // 将 "2025-3" 格式转换为 "3月" 格式
        const monthMatch = item.Month.match(/-(\d+)$/)
        const monthLabel = monthMatch ? `${monthMatch[1]}月` : item.Month
        
        labels.push(monthLabel)
        redData.push(item.Red || 0)
        yellowData.push(item.Yellow || 0)
        blueData.push(item.Blue || 0)
      })
      
      // 更新图表数据
      data.curveData = {
        red: redData,
        yellow: yellowData,
        blue: blueData,
        label: labels
      }
      
      console.log('✅ 三色预警趋势数据更新成功:', data.curveData)
    } else {
      console.warn('⚠️ 三色预警趋势API返回数据格式异常，使用模拟数据')
      throw new Error('API返回数据格式异常')
    }
  } catch (error) {
    console.error('❌ 获取三色预警趋势数据失败:', error)
    // ElMessage.warning('获取三色预警趋势数据失败，使用模拟数据')
    
    // 使用模拟数据作为备用
    const mockData = {
      '2': {
        red: [2, 5, 3, 8, 6, 4],
        yellow: [8, 12, 15, 10, 18, 14],
        blue: [6, 8, 10, 12, 9, 11],
        label: ['7月', '8月', '9月', '10月', '11月', '12月']
      },
      '3': {
        red: [3, 6, 4, 9, 12, 8, 5],
        yellow: [12, 18, 22, 16, 25, 20, 14],
        blue: [8, 12, 15, 18, 14, 16, 10],
        label: ['6月', '7月', '8月', '9月', '10月', '11月', '12月']
      },
      '4': {
        red: [5, 8, 12, 15, 18, 22, 16, 14, 19, 23, 17, 12],
        yellow: [18, 25, 32, 28, 35, 42, 38, 30, 45, 52, 38, 28],
        blue: [12, 18, 25, 22, 28, 35, 30, 24, 32, 38, 28, 20],
        label: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      }
    }
    
    data.curveData = mockData[rangeType as keyof typeof mockData] || mockData['2']
  }
}

const updateHorizontalData = async (rangeType: string, customAreaCode?: string) => {
  try {
    // 获取区域代码
    const areaCode = customAreaCode || JSON.parse(localStorage.getItem('communityUserInfo') || '{}').districtCode || getDistrictCode()
    
    // 根据时间范围计算日期
    const dateRange = getDateRange(rangeType)
    
    // 构建API参数
    const params: WarningStatisticsParams = {
      DateRangeMin: dateRange.DateRangeMin,
      DateRangeMax: dateRange.DateRangeMax,
      AreaCode: areaCode
    }
    
    // 如果选择了预警类型，添加到参数中
    if (selectedWarningType.value) {
      params.WarningType = parseInt(selectedWarningType.value)
    }
    
    console.log('🔍 调用三色预警统计API:', params)
    
    // 调用API
    const response = await getWarningStatisticsApi(params) as any
    
    if (response && response.state === 1 && response.message) {
      // 处理API返回的数据
      const statisticsData = response.message
      
      // 转换数据格式
      const labels: string[] = []
      const redData: number[] = []
      const yellowData: number[] = []
      const blueData: number[] = []
      
      statisticsData.forEach((item: any) => {
        labels.push(item.Name || '未知社区')
        redData.push(item.Red || 0)
        yellowData.push(item.Yellow || 0)
        blueData.push(item.Blue || 0)
      })
      
      // 更新图表数据
      data.horizontalData = {
        red: redData,
        yellow: yellowData,
        blue: blueData,
        label: labels
      }
      
      console.log('✅ 三色预警统计数据更新成功:', data.horizontalData)
    } else {
      console.warn('⚠️ 三色预警统计API返回数据格式异常，使用模拟数据')
      throw new Error('API返回数据格式异常')
    }
  } catch (error) {
    console.error('❌ 获取三色预警统计数据失败:', error)
    // ElMessage.warning('获取三色预警统计数据失败，使用模拟数据')
    
    // 使用模拟数据作为备用
    const mockData = {
      '2': {
        red: [3, 2, 1, 4],
        yellow: [8, 6, 4, 10],
        blue: [5, 7, 3, 8],
        label: ['阳光社区', '和谐社区', '幸福社区', '安康社区']
      },
      '3': {
        red: [5, 3, 2, 6, 4],
        yellow: [12, 9, 6, 15, 8],
        blue: [8, 10, 5, 12, 7],
        label: ['阳光社区', '和谐社区', '幸福社区', '安康社区', '美好社区']
      },
      '4': {
        red: [8, 6, 4, 10, 7, 5],
        yellow: [20, 15, 12, 25, 18, 14],
        blue: [15, 18, 10, 22, 16, 12],
        label: ['阳光社区', '和谐社区', '幸福社区', '安康社区', '美好社区', '温馨社区']
      }
    }
    
    data.horizontalData = mockData[rangeType as keyof typeof mockData] || mockData['2']
  }
}

// 根据预警类型更新趋势数据
const updateCurveDataByType = (warningType: string) => {
  if (!warningType) return
  
  // 根据不同预警类型模拟不同的数据
  const typeDataMap: Record<string, any> = {
    '521': { // 用水量异常
      red: [1, 3, 2, 5, 4, 2],
      yellow: [5, 8, 10, 7, 12, 9],
      blue: [3, 5, 7, 8, 6, 7],
      label: ['7月', '8月', '9月', '10月', '11月', '12月']
    },
    '502': { // 烟雾探测
      red: [3, 6, 4, 9, 7, 5],
      yellow: [10, 15, 18, 12, 20, 16],
      blue: [8, 12, 15, 18, 14, 16],
      label: ['7月', '8月', '9月', '10月', '11月', '12月']
    },
    '531': { // 高空抛物
      red: [2, 4, 3, 6, 5, 3],
      yellow: [6, 9, 12, 8, 15, 11],
      blue: [4, 6, 8, 10, 7, 9],
      label: ['7月', '8月', '9月', '10月', '11月', '12月']
    },
    '543': { // 电梯监控
      red: [4, 7, 5, 10, 8, 6],
      yellow: [12, 18, 22, 16, 25, 20],
      blue: [9, 14, 18, 22, 17, 19],
      label: ['7月', '8月', '9月', '10月', '11月', '12月']
    },
    '501': { // 水位超限
      red: [1, 2, 1, 4, 3, 2],
      yellow: [4, 6, 8, 5, 10, 7],
      blue: [2, 4, 5, 6, 4, 5],
      label: ['7月', '8月', '9月', '10月', '11月', '12月']
    },
    '542': { // 可燃气体
      red: [5, 8, 6, 11, 9, 7],
      yellow: [15, 22, 26, 19, 30, 24],
      blue: [11, 16, 20, 24, 19, 21],
      label: ['7月', '8月', '9月', '10月', '11月', '12月']
    },
    '567': { // 消防占道
      red: [2, 3, 2, 5, 4, 3],
      yellow: [7, 10, 13, 9, 16, 12],
      blue: [5, 7, 9, 11, 8, 10],
      label: ['7月', '8月', '9月', '10月', '11月', '12月']
    },
    '540': { // 人员跌倒
      red: [3, 5, 4, 7, 6, 4],
      yellow: [9, 13, 16, 12, 19, 15],
      blue: [6, 9, 11, 13, 10, 12],
      label: ['7月', '8月', '9月', '10月', '11月', '12月']
    },
    '503': { // 火焰监测
      red: [4, 6, 5, 8, 7, 5],
      yellow: [11, 16, 19, 14, 22, 18],
      blue: [8, 12, 14, 16, 13, 15],
      label: ['7月', '8月', '9月', '10月', '11月', '12月']
    },
    '505': { // 区域入侵
      red: [6, 9, 7, 12, 10, 8],
      yellow: [18, 26, 31, 23, 35, 28],
      blue: [13, 19, 23, 27, 21, 25],
      label: ['7月', '8月', '9月', '10月', '11月', '12月']
    },
    '568': { // 徘徊行为
      red: [2, 4, 3, 6, 5, 3],
      yellow: [8, 11, 14, 10, 17, 13],
      blue: [5, 8, 10, 12, 9, 11],
      label: ['7月', '8月', '9月', '10月', '11月', '12月']
    },
    '565': { // 雨量监测
      red: [1, 2, 1, 3, 2, 1],
      yellow: [3, 5, 6, 4, 8, 6],
      blue: [2, 3, 4, 5, 3, 4],
      label: ['7月', '8月', '9月', '10月', '11月', '12月']
    }
  }
  
  // 更新趋势图数据
  data.curveData = typeDataMap[warningType] || data.curveData
  
  console.log(`已更新预警类型 ${warningType} 的趋势数据`)
}

// 处理三色预警数据详情
const handleWarningDataDetail = () => {
  console.log('🔍 点击三色预警数据详情，当前时间范围:', timeRange.value)
  warningDataDetailVisible.value = true
}

// 处理社区导览数据详情
const handleNavigationDataDetail = () => {
  console.log('🔍 点击社区导览数据详情，当前时间范围:', navigationTimeRange.value)
  communityTourDataDetailVisible.value = true
}

// 处理社区导览资源地图分布
const handleNavigationResourceMap = async (customAreaCode?: string | Event) => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = 'navigation'
    console.log('🎯 Left1Component设置激活撒点类型为: navigation')

    // 修复：如果传入的是事件对象，则忽略它
    let areaCode: string
    if (typeof customAreaCode === 'string') {
      areaCode = customAreaCode
    } else {
      areaCode = getDistrictCode()
    }

    console.log('🔍 点击社区导览资源地图分布，当前时间范围:', navigationTimeRange.value, '区域代码:', areaCode)

    const response = await getCraftsmanMapResourceApi({
      areaCode,
      timeType: navigationTimeRange.value,
      resourceType: 1
    })

    console.log('📊 社区导览资源地图分布API响应:', response)

    if (response.code === 200) {
      // 通知地图组件进行撒点 - 使用通用的 addResourceMapPoints 函数
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data)
        ElMessage.success(`社区导览资源地图分布撒点完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个点位`)
      } else {
        console.error('❌ 地图撒点函数未找到')
        ElMessage.error('地图撒点函数未找到')
      }
    } else {
      console.warn('⚠️ 社区导览资源地图分布API返回异常:', response)
      ElMessage.warning('获取社区导览资源地图分布数据异常')
    }
  } catch (error) {
    console.error('❌ 获取社区导览资源地图分布数据失败:', error)
    ElMessage.error('获取社区导览资源地图分布数据失败')
  }
}

// 等待地图组件加载完成
const waitForMapComponent = (maxRetries = 20, retryDelay = 200): Promise<boolean> => {
  return new Promise((resolve) => {
    let retries = 0

    const checkMapComponent = () => {
      const mapLoaded = (window as any).mapComponentLoaded
      const functionsPreloaded = (window as any).mapFunctionsPreloaded
      const totalHeatMapFunc = (window as any).addTotalHeatMap

      console.log(`🔍 检查地图组件状态 (${retries + 1}/${maxRetries}):`, {
        mapComponentLoaded: mapLoaded,
        mapFunctionsPreloaded: functionsPreloaded,
        addTotalHeatMap: typeof totalHeatMapFunc,
        windowKeys: Object.keys(window).filter(key => key.includes('map') || key.includes('Map') || key.includes('heat') || key.includes('Heat')).slice(0, 10)
      })

      // 如果函数已预加载，就可以使用了
      if ((mapLoaded && totalHeatMapFunc) || (functionsPreloaded && totalHeatMapFunc)) {
        console.log('✅ 地图组件已加载完成')
        resolve(true)
        return
      }

      retries++
      if (retries >= maxRetries) {
        console.warn('⚠️ 地图组件加载超时，当前状态:', {
          mapComponentLoaded: mapLoaded,
          addTotalHeatMap: typeof totalHeatMapFunc,
          allWindowKeys: Object.keys(window).filter(key => key.includes('map') || key.includes('Map') || key.includes('add'))
        })
        resolve(false)
        return
      }

      setTimeout(checkMapComponent, retryDelay)
    }

    checkMapComponent()
  })
}

// 处理社区导览辖区热力分布
const handleNavigationHeatMap = async () => {
  try {
    console.log('🔍 点击社区导览辖区热力分布，当前时间范围:', navigationTimeRange.value)

    const areaCode = getDistrictCode()
    const timeType = navigationTimeRange.value

    console.log('📊 请求社区导览热力图数据:', { areaCode, timeType, resourceType: 2 })

    const response = await getCommunityTourHeatMapApi({
      areaCode,
      timeType,
      resourceType: 2 // 社区导览
    }) as any

    console.log('📊 社区导览热力图API响应:', response)

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照资源地图分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log('✅ 调用热力图函数，原始数据:', response.data)

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0
        }))

        console.log('✅ 转换后的数据:', convertedData)
        ;(window as any).addTotalHeatMap(convertedData)
        ElMessage.success(`社区导览辖区热力分布加载完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个热力点`)
      } else {
        console.error('❌ 地图热力图函数未找到')
        ElMessage.error('地图热力图函数未找到')
      }
    } else {
      console.warn('⚠️ 社区导览热力图API返回异常:', response)
      ElMessage.warning('获取社区导览热力图数据异常')
    }
  } catch (error: any) {
    console.error('❌ 获取社区导览热力图数据失败:', error)
    ElMessage.error(`获取社区导览热力图数据失败: ${error.message}`)
  }
}

// 处理实用工具数据详情
const handleToolsDataDetail = () => {
  console.log('🔍 点击实用工具数据详情，当前时间范围:', toolsTimeRange.value)
  toolsDataDetailVisible.value = true
}

// 处理实用工具资源地图分布
const handleToolsResourceMap = async (customAreaCode?: string | Event) => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = 'tools'
    console.log('🎯 Left1Component设置激活撒点类型为: tools')

    // 修复：如果传入的是事件对象，则忽略它
    let areaCode: string
    if (typeof customAreaCode === 'string') {
      areaCode = customAreaCode
    } else {
      areaCode = getDistrictCode()
    }

    console.log('🔍 点击实用工具资源地图分布，当前时间范围:', toolsTimeRange.value, '区域代码:', areaCode)

    const response = await getToolsMapResourceApi({
      areaCode,
      timeType: toolsTimeRange.value,
      resourceType: 1
    })

    console.log('📊 实用工具资源地图分布API响应:', response)

    if (response.code === 200) {
      // 通知地图组件进行撒点 - 使用通用的 addResourceMapPoints 函数
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data)
        ElMessage.success(`实用工具资源地图分布撒点完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个点位`)
      } else {
        console.error('❌ 地图撒点函数未找到')
        ElMessage.error('地图撒点函数未找到')
      }
    } else {
      console.warn('⚠️ 实用工具资源地图分布API返回异常:', response)
      ElMessage.warning('获取实用工具资源地图分布数据异常')
    }
  } catch (error) {
    console.error('❌ 获取实用工具资源地图分布数据失败:', error)
    ElMessage.error('获取实用工具资源地图分布数据失败')
  }
}

// 处理实用工具辖区热力分布
const handleToolsHeatMap = async () => {
  try {
    console.log('🔍 点击实用工具辖区热力分布，当前时间范围:', toolsTimeRange.value)

    const areaCode = getDistrictCode()
    const timeType = toolsTimeRange.value

    console.log('📊 请求实用工具热力图数据:', { areaCode, timeType, resourceType: 2 })

    // 使用专门的实用工具接口，而不是社区导览接口
    const response = await getToolsMapResourceApi({
      areaCode,
      timeType,
      resourceType: 2 // 2: 辖区热力分布
    }) as any

    console.log('📊 实用工具热力图API响应:', response)

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照社区导览热力分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log('✅ 调用热力图函数，原始数据:', response.data)

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0
        }))

        console.log('✅ 转换后的数据:', convertedData)
        ;(window as any).addTotalHeatMap(convertedData)
        ElMessage.success(`实用工具辖区热力分布加载完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个热力点`)
      } else {
        console.error('❌ 地图热力图函数未找到')
        ElMessage.error('地图热力图函数未找到')
      }
    } else {
      console.warn('⚠️ 实用工具热力图API返回异常:', response)
      ElMessage.warning('获取实用工具热力图数据异常')
    }
  } catch (error: any) {
    console.error('❌ 获取实用工具热力图数据失败:', error)
    ElMessage.error(`获取实用工具热力图数据失败: ${error.message}`)
  }
}

// 处理家庭医生服务概况数据详情
const handleFamilyServiceDataDetail = () => {
  console.log('🔍 点击家庭医生服务概况数据详情，当前时间范围:', doctorOverviewTimeRange.value)
  familyServiceDataDetailVisible.value = true
}

// 处理家庭医生服务概况资源地图分布
const handleFamilyServiceResourceMap = async (customAreaCode?: string | Event) => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = 'family'
    console.log('🎯 设置激活撒点类型为: family')

    // 修复：如果传入的是事件对象，则忽略它
    let areaCode: string
    if (typeof customAreaCode === 'string') {
      areaCode = customAreaCode
    } else {
      areaCode = getDistrictCode()
    }
    console.log('🔍 点击家庭医生服务概况资源地图分布，当前时间范围:', doctorOverviewTimeRange.value, '区域代码:', areaCode)

    const params: FamilyMapResourceParams = {
      timeType: doctorOverviewTimeRange.value,
      areaCode: areaCode,
      resourceType: '1' // 1-地图资源
    }

    console.log('🗺️ 家庭医生资源地图分布查询参数:', params)

    const response = await getFamilyMapResourceApi(params)

    console.log('🗺️ 家庭医生资源地图分布API响应:', response)

    let familyData: any[] = []

    // 根据实际API响应结构处理数据
    if (response && (response as any).code === 200) {
      // 直接响应格式 - API直接返回 {code: 200, msg: "操作成功", data: [...]}
      familyData = (response as any).data || []
      console.log('✅ 家庭医生资源地图分布数据获取成功，共', familyData.length, '条数据')
    } else if (response && response.data && response.data.code === 200) {
      // 嵌套响应格式 - 响应被包装在data中
      familyData = response.data.data || []
      console.log('✅ 家庭医生资源地图分布数据获取成功（嵌套格式），共', familyData.length, '条数据')
    } else {
      // 处理错误情况
      const errorCode = (response as any)?.code || response?.data?.code
      const errorMsg = (response as any)?.msg || response?.data?.msg
      console.warn('⚠️ API返回错误代码:', errorCode, errorMsg)
      throw new Error(`API返回错误: ${errorMsg || '未知错误'}`)
    }

    // 通知地图组件进行撒点
    if ((window as any).addFamilyResourceMapPoints) {
      (window as any).addFamilyResourceMapPoints(familyData)
      ElMessage.success(`家庭医生资源地图分布撒点完成，共 ${familyData.length} 个点位`)
    } else {
      console.warn('⚠️ 地图组件撒点函数不可用')
      ElMessage.warning('地图组件不可用，请确保地图已正确加载')
    }

  } catch (error) {
    console.error('❌ 获取家庭医生资源地图分布数据失败:', error)
    console.log('📊 API调用失败，不使用模拟数据，清除地图点位')

    // 清除地图点位，不使用模拟数据
    if ((window as any).addFamilyResourceMapPoints) {
      (window as any).addFamilyResourceMapPoints([])
      console.log('✅ 家庭医生资源地图分布撒点完成，共 0 个点位')
    }
  }
}

// 处理家庭医生服务概况辖区热力分布
const handleFamilyServiceHeatMap = async () => {
  try {
    console.log('🔍 点击家庭医生服务概况辖区热力分布，当前时间范围:', doctorOverviewTimeRange.value)

    const areaCode = getDistrictCode()
    const timeType = doctorOverviewTimeRange.value

    console.log('📊 请求家庭医生热力图数据:', { areaCode, timeType, resourceType: 2 })

    // 使用专门的家庭医生接口，而不是社区导览接口
    const response = await getFamilyMapResourceApi({
      areaCode,
      timeType,
      resourceType: '2' // 2: 辖区热力分布 (注意：家庭医生接口使用字符串类型)
    }) as any

    console.log('📊 家庭医生热力图API响应:', response)

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照社区导览热力分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log('✅ 调用热力图函数，原始数据:', response.data)

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0
        }))

        console.log('✅ 转换后的数据:', convertedData)
        ;(window as any).addTotalHeatMap(convertedData)
        ElMessage.success(`家庭医生服务辖区热力分布加载完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个热力点`)
      } else {
        console.error('❌ 地图热力图函数未找到')
        ElMessage.error('地图热力图函数未找到')
      }
    } else {
      console.warn('⚠️ 家庭医生热力图API返回异常:', response)
      ElMessage.warning('获取家庭医生热力图数据异常')
    }
  } catch (error: any) {
    console.error('❌ 获取家庭医生热力图数据失败:', error)
    ElMessage.error(`获取家庭医生热力图数据失败: ${error.message}`)
  }
}

// 处理崃建言数据详情
const handleSuggestionDataDetail = () => {
  console.log('🔍 点击崃建言数据详情，当前时间范围:', overviewTimeRange.value)
  suggestionDataDetailVisible.value = true
}

// 处理崃建言资源地图分布
const handleSuggestionResourceMap = async (customAreaCode?: string | Event) => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = 'suggestion'
    console.log('🎯 设置激活撒点类型为: suggestion')

    // 修复：如果传入的是事件对象，则忽略它
    let areaCode: string
    if (typeof customAreaCode === 'string') {
      areaCode = customAreaCode
    } else {
      areaCode = getDistrictCode()
    }
    console.log('🔍 点击崃建言资源地图分布，当前时间范围:', overviewTimeRange.value, '区域代码:', areaCode)

    const params: SuggestionMapResourceParams = {
      timeType: overviewTimeRange.value,
      areaCode: areaCode,
      resourceType: '1' // 1-地图资源
    }

    console.log('🗺️ 崃建言资源地图分布查询参数:', params)

    const response = await getSuggestionMapResourceApi(params)

    console.log('🗺️ 崃建言资源地图分布API响应:', response)

    let suggestionData: any[] = []

    // 根据实际API响应结构处理数据
    if (response && (response as any).code === 200) {
      // 直接响应格式 - API直接返回 {code: 200, msg: "操作成功", data: [...]}
      suggestionData = (response as any).data || []
      console.log('✅ 崃建言地图数据获取成功，共', suggestionData.length, '条数据')
    } else if (response && response.data && response.data.code === 200) {
      // 嵌套响应格式 - 响应被包装在data中
      suggestionData = response.data.data || []
      console.log('✅ 崃建言地图数据获取成功（嵌套格式），共', suggestionData.length, '条数据')
    } else {
      // 处理错误情况
      const errorCode = (response as any)?.code || response?.data?.code
      const errorMsg = (response as any)?.msg || response?.data?.msg
      console.warn('⚠️ API返回错误代码:', errorCode, errorMsg)
      throw new Error(`API返回错误: ${errorMsg || '未知错误'}`)
    }

    // 如果API返回数据为空，使用模拟数据
    if (suggestionData.length === 0) {
      console.log('📝 API返回数据为空，使用模拟数据')
      suggestionData = [
        {
          name: '崃建言',
          createTime: null,
          total: null,
          districtName: '文君街道',
          districtCode: '510183002',
          longitude: '103.473283',
          latitude: '30.407823'
        },
        {
          name: '崃建言',
          createTime: null,
          total: null,
          districtName: '临邛街道',
          districtCode: '510183001',
          longitude: '103.469783',
          latitude: '30.41298'
        },
        {
          name: '崃建言',
          createTime: null,
          total: null,
          districtName: '平乐镇',
          districtCode: '510183104',
          longitude: '103.338966',
          latitude: '30.345812'
        }
      ]
    }

    // 通知地图组件进行撒点
    if ((window as any).addSuggestionResourceMapPoints) {
      (window as any).addSuggestionResourceMapPoints(suggestionData)
       ElMessage.success(`崃建言资源地图分布撒点完成，共 ${suggestionData.length} 个点位`)
    } else {
      console.warn('⚠️ 地图组件撒点函数不可用')
      // ElMessage.warning('地图组件不可用，请确保地图已正确加载')
    }

  } catch (error) {
    console.error('❌ 获取崃建言资源地图分布数据失败:', error)
    console.log('📊 API调用失败，不使用模拟数据，清除地图点位')

    // 清除地图点位，不使用模拟数据
    if ((window as any).addSuggestionResourceMapPoints) {
      (window as any).addSuggestionResourceMapPoints([])
      console.log('✅ 崃建言资源地图分布撒点完成，共 0 个点位')
    }
  }
}

// 处理崃建言辖区热力分布
const handleSuggestionHeatMap = async () => {
  try {
    console.log('🔍 点击崃建言辖区热力分布，当前时间范围:', overviewTimeRange.value)

    const areaCode = getDistrictCode()
    const timeType = overviewTimeRange.value

    console.log('📊 请求崃建言热力图数据:', { areaCode, timeType, resourceType: 2 })

    // 使用专门的崃建言接口，而不是社区导览接口
    const response = await getSuggestionMapResourceApi({
      areaCode,
      timeType,
      resourceType: 2 // 2: 辖区热力分布
    }) as any

    console.log('📊 崃建言热力图API响应:', response)

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照社区导览热力分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log('✅ 调用热力图函数，原始数据:', response.data)

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0
        }))

        console.log('✅ 转换后的数据:', convertedData)
        ;(window as any).addTotalHeatMap(convertedData)
        ElMessage.success(`崃建言辖区热力分布加载完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个热力点`)
      } else {
        console.error('❌ 地图热力图函数未找到')
        ElMessage.error('地图热力图函数未找到')
      }
    } else {
      console.warn('⚠️ 崃建言热力图API返回异常:', response)
      ElMessage.warning('获取崃建言热力图数据异常')
    }
  } catch (error: any) {
    console.error('❌ 获取崃建言热力图数据失败:', error)
    ElMessage.error(`获取崃建言热力图数据失败: ${error.message}`)
  }
}

// 处理查看预警详情
const handleViewWarningDetail = (warningData: any) => {
  console.log('🔍 查看预警详情:', warningData)
  selectedWarningEvent.value = warningData
  warningDetailVisible.value = true
  // 关闭数据详情弹窗
  warningDataDetailVisible.value = false
}

// 处理三色预警资源地图分布
const handleWarningResourceMap = async () => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = 'warning'
    console.log('🎯 设置激活撒点类型为: warning')

    console.log('🔍 点击三色预警资源地图分布')

    // 获取区域代码
    const areaCode = JSON.parse(localStorage.getItem('communityUserInfo') || '{}').districtCode || getDistrictCode()

    const params: DeviceTypeStatisticsParams = {
      areaCode
      // deviceType 不传，让接口不包含这个参数
    }

    console.log('📤 三色预警资源地图分布API请求参数:', params)

    const response = await getDeviceTypeStatisticsApi(params)

    console.log('📡 三色预警资源地图分布API响应:', response)

    if (response && response.state === 1 && response.message && response.message.List) {
      const deviceList = response.message.List
      // console.log('✅ 获取三色预警资源地图分布数据成功，设备总数:', response.message.Count)
      // console.log('📍 设备列表:', deviceList)

      // 转换设备数据为地图点位格式
      const mapPoints = deviceList.map((device: any) => {
        const [longitude, latitude] = device.Position.split(',')
        return {
          id: device.Id,
          name: device.DeviceName,
          longitude: parseFloat(longitude),
          latitude: parseFloat(latitude),
          deviceSN: device.DeviceSN,
          areaName: device.AreaName,
          deviceState: device.DeviceState,
          deviceType: device.DeviceType,
          deviceTypeName: device.DeviceTypeName,
          // 根据设备状态设置点位颜色
          color: device.DeviceState === 1 ? '#00ff00' : '#ff0000', // 1为绿色(正常)，其他为红色(异常)
          // 根据设备类型设置图标
          icon: getDeviceIcon(device.DeviceType)
        }
      }).filter((point: any) => !isNaN(point.longitude) && !isNaN(point.latitude)) // 过滤无效坐标

      // console.log('🗺️ 转换后的地图点位数据:', mapPoints)
      // console.log(`📊 有效点位数量: ${mapPoints.length}/${deviceList.length}`)

      // 通知地图组件进行撒点
      if ((window as any).addWarningResourceMapPoints) {
        (window as any).addWarningResourceMapPoints(mapPoints)
        console.log('✅ 已调用地图组件的三色预警撒点方法')
      } else {
        console.warn('⚠️ 地图组件的 addWarningResourceMapPoints 方法未找到')
      }

      // 不显示弹窗，只进行地图撒点操作
    } else {
      console.warn('⚠️ 三色预警资源地图分布API返回数据格式异常:', response)
    }
  } catch (error) {
    console.error('❌ 获取三色预警资源地图分布数据失败:', error)
  }
}

// 处理三色预警辖区热力分布
const handleWarningHeatMap = async () => {
  try {
    console.log('🔍 点击三色预警辖区热力分布')

    // 获取区域代码
    const areaCode = JSON.parse(localStorage.getItem('communityUserInfo') || '{}').districtCode || getDistrictCode()

    // 获取日期范围
    const dateRange = getDateRange(timeRange.value)

    const params: SpecialStatisticsByAreaParams = {
      AreaCode: areaCode,
      DateRangeMin: dateRange.DateRangeMin,
      DateRangeMax: dateRange.DateRangeMax
    }

    console.log('📤 三色预警辖区热力分布API请求参数:', params)

    const response = await getSpecialStatisticsByAreaApi(params)

    console.log('📡 三色预警辖区热力分布API响应:', response)

    if (response && (response as any).data.code === 200 && (response as any).data.data) {
      const heatMapData = (response as any).data.data
   
      // 检查数据结构
      if (heatMapData.length > 0) {
        console.log('📋 第一条数据示例:', heatMapData[0])
      }

      // 转换热力数据为地图热力图格式
      const heatPoints = heatMapData
        .filter((item: any) => {
          const hasCoords = item.longitude && item.latitude
          const hasTotal = item.total > 0
      
          return hasCoords && hasTotal
        })
        .map((item: any) => ({
          longitude: parseFloat(item.longitude),
          latitude: parseFloat(item.latitude),
          count: item.total,
          districtName: item.districtName,
          districtCode: item.districtCode
        }))

      // console.log(`📊 有效热力点位数量: ${heatPoints.length}/${heatMapData.length}`)

      if (heatPoints.length === 0) {
        console.warn('⚠️ 没有有效的热力点位数据')
        ElMessage.warning('没有有效的三色预警热力分布数据')
        return
      }

      // 转换数据格式：将count字段转换为total字段，保持与其他热力图一致
      const formattedHeatPoints: any[] = heatPoints.map((point: any) => ({
        ...point,
        total: point.count // 将count转换为total字段
      }))

      console.log('🔥 最终热力图数据:', formattedHeatPoints.slice(0, 3)) // 只显示前3条

      // 通知地图组件进行热力图展示 - 使用通用的热力图方法
      if ((window as any).addTotalHeatMap) {
        (window as any).addTotalHeatMap(formattedHeatPoints)
        console.log('✅ 已调用地图组件的三色预警热力图方法')
        ElMessage.success(`三色预警辖区热力分布加载完成，共 ${formattedHeatPoints.length} 个热力点`)
      } else {
        console.warn('⚠️ 地图组件的 addTotalHeatMap 方法未找到')
        ElMessage.error('地图热力图函数未找到')
      }

      // 不显示弹窗，只进行地图热力图操作
    } else {
      console.warn('⚠️ 三色预警辖区热力分布API返回数据格式异常:', response)
      ElMessage.warning('获取三色预警辖区热力分布数据异常')
    }
  } catch (error) {
    console.error('❌ 获取三色预警辖区热力分布数据失败:', error)
  }
}

// 处理设备分布点击事件
const distributed = async (item: any) => {
  try {
    console.log('🔍 点击设备分布，设备信息:', item)

    // 获取区域代码
    const areaCode = JSON.parse(localStorage.getItem('communityUserInfo') || '{}').districtCode || getDistrictCode()

    // 获取当前设备的deviceType，转换为字符串
    const deviceType = String(item.deviceType)

    const params = {
      areaCode,
      deviceType
    }

    console.log('📤 设备分布API请求参数:', params)

    const response = await getDeviceTypeStatisticsApi(params)

    console.log('📡 设备分布API响应:', response)

    if (response && response.state === 1 && response.message && response.message.List) {
      const deviceList = response.message.List
      console.log('✅ 获取设备分布数据成功，设备总数:', response.message.Count)
      console.log('📍 设备列表:', deviceList)

      // 转换设备数据为地图点位格式
      const mapPoints = deviceList
        .filter((device: any) => device.Position && device.Position.includes(','))
        .map((device: any) => {
          const [longitude, latitude] = device.Position.split(',')
          return {
            id: device.Id,
            name: device.DeviceName,
            longitude: parseFloat(longitude),
            latitude: parseFloat(latitude),
            deviceType: device.DeviceType,
            deviceTypeName: device.DeviceTypeName,
            areaName: device.AreaName,
            deviceState: device.DeviceState,
            icon: getDeviceIcon(device.DeviceType)
          }
        })
        .filter((point: any) => !isNaN(point.longitude) && !isNaN(point.latitude))

      console.log(`📊 有效点位数量: ${mapPoints.length}/${deviceList.length}`)

      // 通知地图组件进行撒点
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(mapPoints)
        console.log('✅ 已调用地图组件的设备分布撒点方法')
        ElMessage.success(`${item.name}设备分布撒点完成，共 ${mapPoints.length} 个点位`)
      } else {
        console.warn('⚠️ 地图组件的 addResourceMapPoints 方法未找到')
        ElMessage.error('地图撒点函数未找到')
      }

    } else {
      console.warn('⚠️ 设备分布API返回数据格式异常:', response)
      ElMessage.warning('获取设备分布数据异常')
    }
  } catch (error) {
    console.error('❌ 获取设备分布数据失败:', error)
    ElMessage.error('获取设备分布数据失败')
  }
}

// 根据设备类型获取对应图标
const getDeviceIcon = (deviceType: number) => {
  const iconMap: Record<number, string> = {
    23: 'high-throw', // 高空抛物监控设备
    55: 'smoke',      // 烟雾传感器
    // 可以根据需要添加更多设备类型的图标映射
  }
  return iconMap[deviceType] || 'default'
}

onMounted(async () => {
  // 初始化社区概览数据
  initCommunityData()
  
  // 定期检查localStorage中的数据更新
  const checkDataUpdates = () => {
    const storedData = localStorage.getItem('communityOverviewData')
    if (storedData) {
      try {
        const overviewData = JSON.parse(storedData)
        // 检查数据是否有变化
        const currentTotal = communityData.value.communityCount + communityData.value.populationCount
        const newTotal = (overviewData.courtyardTotal || 0) + (overviewData.populationTotal || 0)
        
        if (currentTotal !== newTotal) {
          console.log('🔄 检测到数据更新，重新加载社区数据')
          updateCommunityDataFromOverview(overviewData)
        }
      } catch (error) {
        console.error('❌ 检查数据更新失败:', error)
      }
    }
  }
  
  // 每5秒检查一次数据更新
  setInterval(checkDataUpdates, 5000)
  
  // 立即检查一次数据更新
  setTimeout(checkDataUpdates, 1000)
  
  // 初始化实用工具数据
  updateToolsData(toolsTimeRange.value)
  
  // 初始化三色预警数据
  await updateWarningData(timeRange.value)
  
  // 初始化趋势数据
  await updateCurveData(timeRangeCurve.value)
  
  // 初始化统计数据
  await updateHorizontalData(timeRangeHorizontal.value)
  
  // 初始化物联设备数据
  await getDeviceRunStatistics()
  
  // 如果默认显示设备预警概况tab，获取预警数据
  if (iotActiveTab.value === 'deviceWarning') {
    await getDeviceWarningData()
  }
  
  // 初始化崃建言数据
  await updateOverviewData(overviewTimeRange.value)
  await updateAdoptionData(adoptionTimeRange.value)
  await updateProcessData(processTimeRange.value)
  
  // 初始化社区经济数据
  await getNavigationData(navigationTimeRange.value)
  await getTourData()
  
  // 初始化家庭医生数据
  await getFamilyDoctorData()
  
  // 初始化家庭医生服务概况数据
  await getFamilyDoctorOverview(doctorOverviewTimeRange.value)
  
  // 初始化文件和人员信息统计数据
  await getFileAndResLogStatistics()
  
  // 初始化标签统计数据
  await getResidentsTagStatistics()
  
  // 初始化多种标签人群数据
  await getMultipleLabelsData()
  
  // 初始化社区文件资源列表数据
  await getResResidentsLogList()
  
  // 初始化数据使用预测数据
  await getAnticipateList()
  
  // 初始化人口结构分析数据
  await getAgeSummary()
  
  // 监听来自BigScreenView的社区数据更新事件
  communityDataUpdateHandler = (event: Event) => {
    const customEvent = event as CustomEvent
    console.log('🔄 Left1Component接收到社区数据更新事件:', customEvent.detail)
    
    // 检查是否是原始概览数据
    if (customEvent.detail && customEvent.detail.courtyardTotal !== undefined) {
      updateCommunityDataFromOverview(customEvent.detail)
    } else {
      // 如果是已映射的数据，直接使用
      communityData.value = { ...customEvent.detail }
      console.log('✅ Left1Component社区数据已更新:', communityData.value)
    }
  }
  
  window.addEventListener('updateCommunityData', communityDataUpdateHandler)
  
  // 创建全局更新函数，方便直接调用
  const globalUpdateFunction = (data: any) => {
    console.log('🔄 通过全局函数更新Left1Component社区数据:', data)
    
    // 如果传入的是原始概览数据，需要先映射
    if (data.courtyardTotal !== undefined) {
      updateCommunityDataFromOverview(data)
    } else {
      // 如果传入的是已经映射好的数据，直接使用
      communityData.value = { ...data }
    }
    
    console.log('✅ Left1Component社区数据已更新:', communityData.value)
  }
  ;(window as any).updateLeft1CommunityData = globalUpdateFunction
  
  // 创建全局文件统计函数
  ;(window as any).getFileAndResLogStatistics = getFileAndResLogStatistics
  
  // 创建全局标签统计函数
  ;(window as any).getResidentsTagStatistics = getResidentsTagStatistics
  
  // 创建全局多种标签人群函数
  ;(window as any).getMultipleLabelsData = getMultipleLabelsData
  
  // 创建全局社区文件资源列表函数
  ;(window as any).getResResidentsLogList = getResResidentsLogList
  
  // 创建全局数据使用预测函数
  ;(window as any).getAnticipateList = getAnticipateList
  
  // 创建全局人口结构分析函数
  ;(window as any).getAgeSummary = getAgeSummary
  
  // 创建全局家庭医生服务概况函数
  ;(window as any).getFamilyDoctorOverview = () => getFamilyDoctorOverview(doctorOverviewTimeRange.value)
  
  // 创建全局设备列表弹窗函数
  ;(window as any).toPage = toPage
  
  console.log('Left1Component 组件已挂载')
  
  // 注册全局更新函数
  ;(window as any).updateLeft1WithTownCode = updateAllInterfacesWithTownCode

  // 注册清除激活状态函数
  ;(window as any).clearActiveMapPointTypeLeft1 = () => {
    activeMapPointType.value = ''
    console.log('🧹 Left1Component清除激活的地图撒点类型')
  }
})

// 初始化社区概览数据
const initCommunityData = () => {
  try {
    // 尝试从localStorage读取已存储的社区概览数据
    const storedData = localStorage.getItem('communityOverviewData')
    if (storedData) {
      const overviewData = JSON.parse(storedData)
      console.log('📊 从localStorage读取社区概览数据:', overviewData)
      
      updateCommunityDataFromOverview(overviewData)
    } else {
      console.log('⚠️ localStorage中没有找到社区概览数据，等待接口数据')
    }
  } catch (error) {
    console.error('❌ 初始化社区概览数据失败:', error)
  }
}

// 从概览数据更新社区数据
const updateCommunityDataFromOverview = (overviewData: any) => {
  console.log('🔄 开始更新社区数据，原始数据:', overviewData)
  
  // 映射数据到communityData格式
  const mappedData = {
    communityCount: overviewData.courtyardTotal || 0,
    populationCount: overviewData.populationTotal || 0,
    tagCount: overviewData.labelsTotal || 0,
    enterpriseCount: overviewData.enterpriseTotal || 0,
    relationCount: overviewData.residentsHouseTotal || 0,
    houseCount: overviewData.housesTotal || 0,
    // 保持原有的字段值（这些字段暂时没有对应的API数据）
    documentCount: 0,
    memberCount: 0,
    multiTagCount: 0
  }
  
  console.log('📊 映射后的数据:', mappedData)
  
  // 更新数据
  communityData.value = { ...mappedData }
  
  console.log('✅ 社区概览数据更新完成:', communityData.value)
}

// 组件卸载时清理事件监听器
let communityDataUpdateHandler: ((event: Event) => void) | null = null

onUnmounted(() => {
  if (communityDataUpdateHandler) {
    window.removeEventListener('updateCommunityData', communityDataUpdateHandler)
    console.log('Left1Component 事件监听器已清理')
  }
})

// ========== 页面数据初始化函数 ==========
const initLeft1PageData = (pageNumber: number) => {
  console.log(`初始化Left1页面 ${pageNumber} 的数据`)
  
  switch (pageNumber) {
    case 1: // 智能物联
      // 智能物联数据在onMounted中已初始化
      break
      
    case 2: // 崃建言
      updateOverviewData(overviewTimeRange.value)
      updateAdoptionData(adoptionTimeRange.value)
      updateProcessData(processTimeRange.value)
      break
      
    case 3: // 社区服务
      // 根据当前激活的tab加载对应数据
      if (serviceActiveTab.value === 'doctor') {
        getFamilyDoctorData()
      } else if (serviceActiveTab.value === 'tools') {
        updateToolsData(toolsTimeRange.value)
      }
      // 精治数仓数据已在onMounted中初始化
      break
      
    case 4: // 社区经济
      // 根据当前激活的tab加载对应数据
      if (economyActiveTab.value === 'navigation') {
        getNavigationData()
      } else if (economyActiveTab.value === 'tour') {
        getTourData()
      }
      break
  }
}

// 监听屏幕模式变化
watch(() => fullscreenStore.screenMode, (screenMode) => {
  console.log('Left1 屏幕模式变化:', screenMode)
  if (screenMode === 'wide' || screenMode === 'fullscreen') {
    // 进入宽屏模式或全屏模式，使用store状态控制页面
    const left1Page = fullscreenStore.getLeft1Page()
    if (left1Page !== null) {
      currentPage.value = left1Page
      console.log('Left1 进入', screenMode, '模式，页面切换到:', left1Page)
      // 初始化对应页面的数据
      initLeft1PageData(left1Page)
    }
  }
})

// 监听左侧页面变化
watch(() => fullscreenStore.leftPage, (newPage, oldPage) => {
  console.log('Left1 监听到左侧页面变化:', oldPage, '->', newPage, '当前屏幕模式:', fullscreenStore.screenMode)
  if (fullscreenStore.screenMode === 'wide' || fullscreenStore.screenMode === 'fullscreen') {
    const left1Page = fullscreenStore.getLeft1Page()
    if (left1Page !== null) {
      console.log('Left1 准备切换页面:', currentPage.value, '->', left1Page)
      currentPage.value = left1Page
      console.log('Left1 左侧页面变化，切换到页面:', left1Page)
      // 初始化对应页面的数据
      initLeft1PageData(left1Page)
    }
  }
})

// 获取家庭医生数据
const getFamilyDoctorData = async (customAreaCode?: string) => {
  try {
    console.log('🔄 调用家庭医生API')
    
    const areaCode = customAreaCode || getDistrictCode()
    const response = await getFamilyDoctorApi({ areaCode })
    
    console.log('📡 家庭医生API响应:', response)
    
    if (response && response.code === 200 && response.message && response.message.districtList) {
      const districtList = response.message.districtList
      
      // 注意：服务概况数据现在通过单独的API获取，不在这里设置
      
      // 更新家庭医生分布数据（前10个区域）
      doctorDistributionData.value = districtList.slice(0, 10).map(item => ({
        name: item.name,
        value: Math.floor(Math.random() * 100) + 20 // 临时模拟数据，实际应该是API返回的真实数据
      }))
      
      // 更新糖尿病患者签约数据
      diabetesSignupData.value = districtList.slice(0, 10).map(item => ({
        name: item.name,
        value: Math.floor(Math.random() * 3000) + 300 // 临时模拟数据
      }))
      
      // 更新签约率数据（使用useCount作为签约率）
      signupRateData.value = districtList.slice(0, 5).map(item => ({
        name: item.name,
        rate: item.useCount || 0
      }))
      
      console.log('✅ 家庭医生数据更新成功')
    } else {
      throw new Error('API返回数据格式异常')
    }
  } catch (error) {
    console.error('❌ 获取家庭医生数据失败:', error)
    // 保持原有数据，不做降级处理
  }
}

// 获取家庭医生服务概况数据
const getFamilyDoctorOverview = async (timeType: string, customAreaCode?: string) => {
  try {
    //console.log('🔄 调用家庭医生服务概况API，时间范围:', timeType)
    
    const areaCode = customAreaCode || getDistrictCode()
    const params: FamilyDoctorOverviewParams = {
      timeType,
      areaCode
    }
    
   // console.log('📡 家庭医生服务概况API参数:', params)
    
    const response = await getFamilyDoctorOverviewApi(params)
    
    //console.log('📡 家庭医生服务概况API响应:', response)
    
    if (response && response.code === 200 && response.data) {
      // 根据实际返回的数据结构更新doctorServiceData
      const apiData = response.data as any // 临时使用any类型避免类型错误
      
      doctorServiceData.value = {
        doctorCount: apiData.doctorCount || 0,
        residentCount: apiData.residentCount || 0,
        signupCount: apiData.contractCount || 0 // API返回的是contractCount，映射到signupCount
      }
      
     // console.log('✅ 家庭医生服务概况数据更新成功:', doctorServiceData.value)
    } else {
      throw new Error('API返回数据格式异常')
    }
  } catch (error) {
  //  console.error('❌ 获取家庭医生服务概况数据失败:', error)
    // 保持原有数据，不做降级处理
  }
}

// 处理家庭医生服务概况时间变更
const changeDoctorOverviewTime = async (timeType: string) => {
 // console.log('家庭医生服务概况时间变更:', timeType)
  doctorOverviewTimeRange.value = timeType
  await getFamilyDoctorOverview(timeType)
}

// 获取居民标签统计列表数据
const getResidentsTagStatistics = async (customCityCode?: string) => {
  try {
   // console.log('🔄 开始获取居民标签统计数据')
    
    // 从localStorage获取token
    const authorizeData = localStorage.getItem('authorizeData')
    let token = ''
    
    if (authorizeData) {
      try {
        const parsedData = JSON.parse(authorizeData)
        token = parsedData.token || ''
      } catch (error) {
       // console.warn('⚠️ 解析授权数据失败，尝试直接使用字符串')
        token = authorizeData.includes('token') ? authorizeData : ''
      }
    }
    
    if (!token) {
      //console.warn('⚠️ 未找到token，无法调用标签统计接口')
      return
    }
    
    const params: LabelStatisticsParams = {
      cityCode: customCityCode || '510183002014',
      token: token
    }
    
   // console.log('📡 调用标签统计接口，参数:', params)
    
    const response = await getLabelStatisticsApi(params)
    
   // console.log('📡 标签统计接口响应:', response)
    
    if (response && response.code === 1 && response.data) {
      try {
       // console.log('🔓 开始解密标签统计数据:', response.data)
        const decryptedData = decryptResponseData(response.data)
       // console.log('✅ 标签统计数据解密成功:', decryptedData)
        
        // 尝试解析为JSON
        let parsedData
        try {
          parsedData = JSON.parse(decryptedData)
         // console.log('📊 标签统计数据解析为JSON成功:', parsedData)
        } catch {
          parsedData = decryptedData
         // console.log('📝 标签统计数据使用字符串格式:', parsedData)
        }
        
        // 更新数据
        if (parsedData && Array.isArray(parsedData)) {
          // 解密后的数据直接是数组格式
          tagStatistics.value = parsedData.map((item: any) => ({
            name: item.labelName || '',
            count: item.labelCount || 0
          }))
          
         // console.log('✅ 标签统计数据更新成功:', tagStatistics.value)
        } else if (parsedData && typeof parsedData === 'object' && parsedData.tagList) {
          // 如果数据被包装在对象中
          tagStatistics.value = parsedData.tagList.map((item: any) => ({
            name: item.labelName || item.tagName || item.name || '',
            count: item.labelCount || item.count || 0
          }))
          
         // console.log('✅ 标签统计数据更新成功:', tagStatistics.value)
        } else {
         // console.warn('⚠️ 解密后的标签统计数据格式不正确:', parsedData)
          setFallbackTagData()
        }
        
      } catch (error) {
        //console.error('❌ 标签统计数据解密失败:', error)
        // 解密失败时，使用模拟数据作为降级
        setFallbackTagData()
      }
    } else {
     // console.warn('⚠️ 标签统计接口返回数据格式异常:', response)
      setFallbackTagData()
    }
    
  } catch (error) {
    //console.error('❌ 获取标签统计数据失败:', error)
    setFallbackTagData()
  }
}

// 设置降级标签数据
const setFallbackTagData = () => {
  tagStatistics.value = [
    { name: '老年人', count: 12580 },
    { name: '青年人', count: 8960 },
    { name: '儿童', count: 6540 },
    { name: '残疾人', count: 3420 },
    { name: '低保户', count: 2180 },
    { name: '党员', count: 4560 }
  ]
  multiTagCount.value = 15
}

// 获取多种标签人群数据
const getMultipleLabelsData = async (customCityCode?: string) => {
  try {
    console.log('🔄 开始获取多种标签人群数据')
    
    // 从localStorage获取token
    const authorizeData = localStorage.getItem('authorizeData')
    let token = ''
    
    if (authorizeData) {
      try {
        const parsedData = JSON.parse(authorizeData)
        token = parsedData.token || ''
      } catch (error) {
        console.warn('⚠️ 解析授权数据失败，尝试直接使用字符串')
        token = authorizeData.includes('token') ? authorizeData : ''
      }
    }
    
    if (!token) {
      console.warn('⚠️ 未找到token，无法调用多种标签人群接口')
      return
    }
    
    const params: MultipleLabelsParams = {
      cityCode: customCityCode || '510183002014',
      token: token
    }
    
    console.log('📡 调用多种标签人群接口，参数:', params)
    
    const response = await getMultipleLabelsApi(params)
    
    // ... existing response handling code ...
  } catch (error) {
    console.error('❌ 获取多种标签人群数据失败:', error)
    setFallbackMultiTagCount()
  }
}

// 获取文件和人员信息统计数据
const getFileAndResLogStatistics = async (customCityCode?: string) => {
  try {
    console.log('🔄 开始获取文件和人员信息统计数据')
    
    // 从localStorage获取token
    const authorizeData = localStorage.getItem('authorizeData')
    let token = ''
    
    if (authorizeData) {
      try {
        const parsedData = JSON.parse(authorizeData)
        token = parsedData.token || ''
      } catch (error) {
        console.warn('⚠️ 解析授权数据失败，尝试直接使用字符串')
        token = authorizeData.includes('token') ? authorizeData : ''
      }
    }
    
    if (!token) {
      console.warn('⚠️ 未找到token，无法调用文件和人员信息统计接口')
      return
    }
    
    const params: FileAndResLogStatisticsParams = {
      cityCode: customCityCode || '510183002014',
      token: token
    }
    
    console.log('📡 调用文件和人员信息统计接口，参数:', params)
    
    const response = await getFileAndResLogStatisticsApi(params)
    
    // ... existing response handling code ...
  } catch (error) {
    console.error('❌ 获取文件和人员信息统计数据失败:', error)
    setFallbackFileStats()
  }
}

// 获取社区文件资源列表数据
const getResResidentsLogList = async (customCityCode?: string) => {
  try {
    console.log('🔄 开始获取社区文件资源列表数据')
    
    // 从localStorage获取token
    const authorizeData = localStorage.getItem('authorizeData')
    let token = ''
    
    if (authorizeData) {
      try {
        const parsedData = JSON.parse(authorizeData)
        token = parsedData.token || ''
      } catch (error) {
        console.warn('⚠️ 解析授权数据失败，尝试直接使用字符串')
        token = authorizeData.includes('token') ? authorizeData : ''
      }
    }
    
    if (!token) {
      console.warn('⚠️ 未找到token，无法调用社区文件资源列表接口')
      return
    }
    
    const params: ResResidentsLogListParams = {
      cityCode: customCityCode || '510183002014',
      token: token,
      pageNumber: 1,
      pageSize: 20
    }
    
    console.log('📡 调用社区文件资源列表接口，参数:', params)
    
    const response = await getResResidentsLogListApi(params)
    
    // ... existing response handling code ...
  } catch (error) {
    console.error('❌ 获取社区文件资源列表数据失败:', error)
    setFallbackDataSourceList()
  }
}

const getAnticipateList = async (customCityCode?: string) => {
  try {
    console.log('🔄 开始获取数据使用预测列表数据')
    
    // 从localStorage获取token
    const authorizeData = localStorage.getItem('authorizeData')
    let token = ''
    
    if (authorizeData) {
      try {
        const parsedData = JSON.parse(authorizeData)
        token = parsedData.token || ''
      } catch (error) {
        console.warn('⚠️ 解析授权数据失败，尝试直接使用字符串')
        token = authorizeData.includes('token') ? authorizeData : ''
      }
    }
    
    if (!token) {
     // console.warn('⚠️ 未找到token，无法调用数据使用预测列表接口')
      return
    }
    
    const params: AnticipateListParams = {
      cityCode: customCityCode || '510183002014',
      token: token
    }
    
   // console.log('📡 调用数据使用预测列表接口，参数:', params)
    
    const response = await getAnticipateListApi(params)
    
    // ... existing response handling code ...
  } catch (error) {
    console.error('❌ 获取数据使用预测列表数据失败:', error)
    setFallbackAnticipateData()
  }
}

// 获取人口结构分析数据
const getAgeSummary = async (customCityCode?: string) => {
  try {
    console.log('🔄 开始获取人口结构分析数据')
    
    // 从localStorage获取token
    const authorizeData = localStorage.getItem('authorizeData')
    let token = ''
    
    if (authorizeData) {
      try {
        const parsedData = JSON.parse(authorizeData)
        token = parsedData.token || ''
      } catch (error) {
        console.warn('⚠️ 解析授权数据失败，尝试直接使用字符串')
        token = authorizeData.includes('token') ? authorizeData : ''
      }
    }
    
    if (!token) {
      console.warn('⚠️ 未找到token，无法调用人口结构分析接口')
      return
    }
    
    const params: AgeSummaryParams = {
      cityCode: customCityCode || '510183002014',
      token: token
    }
    
    console.log('📡 调用人口结构分析接口，参数:', params)
    
    const response = await getAgeSummaryApi(params)
    
    // ... existing response handling code ...
  } catch (error) {
    console.error('❌ 获取人口结构分析数据失败:', error)
    setFallbackAgeSummaryData()
  }
}

// 设置降级人口结构数据
const setFallbackAgeSummaryData = () => {
  ageSummaryData.value = [
    { name: '0-6岁', value: 375 },
    { name: '7-14岁', value: 172 },
    { name: '15-24岁', value: 305 },
    { name: '25-34岁', value: 325 },
    { name: '35-49岁', value: 1652 },
    { name: '50-59岁', value: 2823 },
    { name: '60-74岁', value: 3358 },
    { name: '75-79岁', value: 161 },
    { name: '80岁以上', value: 162 }
  ]
}

// 设置降级多重标签数据
const setFallbackMultiTagCount = () => {
  multiTagCount.value = 15
}

// 设置降级文件统计数据
const setFallbackFileStats = () => {
  fileAndResLogData.value = {
    fileTotal: 121212,
    resLogTotal: 23333
  }
}

// 设置降级数据源列表数据
const setFallbackDataSourceList = () => {
  dataSourceList.value = [
    { text: '公安户籍数据' },
    { text: '民政低保数据' },
    { text: '卫健医疗数据' },
    { text: '教育学籍数据' },
    { text: '住建房产数据' },
    { text: '人社就业数据' }
  ]
}

// 设置降级预测数据
const setFallbackAnticipateData = () => {
  anticipateDataList.value = [
    {
      title: '未来一个月将新增满80岁老人',
      count: 44
    }
  ]
}

// 创建全局更新函数，支持town_code参数
const updateAllInterfacesWithTownCode = (townCode: string) => {
  console.log('🔄 Left1Component开始使用town_code更新所有接口:', townCode)
  
  // 更新精治数仓相关接口（使用townCode作为cityCode）
  getFileAndResLogStatistics(townCode)
  getResidentsTagStatistics(townCode)
  getMultipleLabelsData(townCode)
  getResResidentsLogList(townCode)
  getAnticipateList(townCode)
  getAgeSummary(townCode)
  
  // 更新其他接口（使用townCode作为areaCode）
  getFamilyDoctorData(townCode)
  getFamilyDoctorOverview(doctorOverviewTimeRange.value, townCode)
  updateToolsData(toolsTimeRange.value, townCode)
  getNavigationData('2', townCode)
  getTourData(townCode)
  
  // 更新设备相关接口
  getDeviceWarningData(townCode)
  getDeviceRunStatistics(townCode)
  
  // 更新三色预警相关接口
  updateWarningData(timeRange.value, townCode)
  updateCurveData(timeRangeCurve.value, townCode)
  updateHorizontalData(timeRangeHorizontal.value, townCode)
  
  // 更新社区游线相关接口
  changeTourInfo('1', townCode)
  changeTourHots('1', townCode)
  changeTourList('1', townCode)

  // 根据当前激活的撒点类型，重新请求对应的地图数据
  if (activeMapPointType.value) {
    console.log('🗺️ 检测到激活的撒点类型:', activeMapPointType.value, '开始重新请求地图数据')

    // 先清除现有点位
    if ((window as any).clearResourceMapPoints) {
      (window as any).clearResourceMapPoints()
    }

    // 根据类型重新请求数据
    switch (activeMapPointType.value) {
      case 'family':
        console.log('🔄 重新请求家庭医生地图数据')
        handleFamilyServiceResourceMap(townCode)
        break
      case 'suggestion':
        console.log('🔄 重新请求崃建言地图数据')
        handleSuggestionResourceMap(townCode)
        break
      case 'warning':
        console.log('🔄 重新请求三色预警地图数据')
        handleWarningResourceMap()
        break
      case 'navigation':
        console.log('🔄 重新请求社区导览地图数据')
        handleNavigationResourceMap(townCode)
        break
      case 'tools':
        console.log('🔄 重新请求实用工具地图数据')
        handleToolsResourceMap(townCode)
        break
      default:
        console.log('📍 未知的撒点类型:', activeMapPointType.value)
    }
  } else {
    console.log('📍 当前没有激活的撒点类型，跳过地图数据更新')
  }

  // 注册toPage函数支持新的区域代码
  ;(window as any).toPage = (item: Record<string, any>) => toPage(item, townCode)

  console.log('✅ Left1Component所有接口已使用新的town_code重新请求')
}
</script>

<style lang="scss" scoped>
.left1-component {
  width: 100%;
  padding-bottom: 100px; /* 增加底部内边距确保最后内容可见 */
  height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.header-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  // padding: 0 20px;
  border-radius: 0;
  margin-bottom:20px;
  background: url('@/assets/common/title-bg.png') no-repeat center center;
  
 
  
  .header-back {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.208vw 0.312vw;
    border-radius: 0;
    transition: all 0.3s ease;
    background: #0c162d;
    height: 44px;
    width: 72px;
    justify-content: center;
    border: 0.026vw solid #24324b;
    
  
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
    
    .back-text {
      color: #1c79e2;
      font-size: 14px;
      font-weight: 500;
      
    
    }
  }
  
  .header-title {
    display: flex;
    align-items: center;
    gap: 0.208vw;
    
    @media (max-width: 2000px) {
      gap: 0.333vw;
    }
    
    .title-icon {
      width:19px;
      height:19px;
      border-radius: 0;
      margin-top: -0.130vw;
      
   
      
      img{
        width: 100%;
        height: 100%;
      }
    }
    
    .title-text {
      background: linear-gradient(0deg, #F7F9FC 0%, #9D9DA6 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-size: 20px;
      font-weight: 600;
      letter-spacing: 0.013vw;
      
    
    }
  }
}

// 趋势控件样式
.trend-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
  
  .warning-type-select {
    .warning-select {
      width: 140px;
      height: 33px;
      
      :deep(.el-input__wrapper) {
        background-color: #0a1629 !important;
        border: 1px solid #24324b !important;
        box-shadow: none !important;
        
        .el-input__inner {
          color: #ffffff !important;
          font-size: 12px;
          background-color: transparent !important;
          
          &::placeholder {
            color: rgba(255, 255, 255, 0.5) !important;
          }
        }
        
        .el-input__suffix {
          .el-input__suffix-inner {
            .el-select__caret {
              color: rgba(255, 255, 255, 0.6) !important;
            }
          }
        }
      }
      
      &:hover {
        :deep(.el-input__wrapper) {
          border-color: #1677FF !important;
        }
      }
      
      &.is-focus {
        :deep(.el-input__wrapper) {
          border-color: #1677FF !important;
          box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.2) !important;
        }
      }
    }
  }
}

// 全局下拉框样式覆盖
:deep(.el-select-dropdown) {
  background-color: #0a1629 !important;
  border: 1px solid #24324b !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.5) !important;
  
  .el-select-dropdown__item {
    color: #ffffff !important;
    font-size: 12px;
    background-color: transparent !important;
    
    &:hover {
      background-color: rgba(22, 119, 255, 0.2) !important;
      color: #ffffff !important;
    }
    
    &.is-selected {
      background-color: #1677FF !important;
      color: #ffffff !important;
    }
  }
  
  .el-select-dropdown__empty {
    color: rgba(255, 255, 255, 0.5) !important;
  }
}

// 特定组件的额外样式覆盖
.warning-type-select {
  :deep(.el-select--small) {
    .el-select__wrapper {
      background-color: #0a1629 !important;
      border: 1px solid #24324b !important;
      box-shadow: none !important;
      height: 33px !important;
      min-height: 33px !important;
      
      &.is-focus {
        border-color: #1677FF !important;
        box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.2) !important;
      }
      
      &:hover {
        border-color: #1677FF !important;
      }
      
      .el-select__selected-item {
        color: #ffffff !important;
      }
      
      .el-select__placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }
      
      .el-select__suffix {
        .el-select__caret {
          color: rgba(255, 255, 255, 0.6) !important;
        }
      }
    }
  }
}

.module-title {
  
}

.module-box {
  border: 0.026vw solid #20497F;
  border-radius: 0.104vw;
  padding: 0.390vw;
  margin-bottom: 0.260vw;
  background: rgba(0, 20, 40, 0.3);
  
  @media (max-width: 2000px) {
    border: 0.041vw solid #20497F;
    border-radius: 0.166vw;
    padding: 0.625vw;
    margin-bottom: 0.416vw;
  }
}

.module-box:last-child {
  margin-bottom: 0.520vw; /* 最后一个模块增加更多底部边距 */
  
  @media (max-width: 2000px) {
    margin-bottom: 0.833vw;
  }
}

.tools-placeholder {
  text-align: center;
  padding: 1.562vw 0.520vw;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.416vw;
  
  @media (max-width: 2000px) {
    padding: 2.5vw 0.833vw;
    font-size: 0.666vw;
  }
}

.title-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.390vw;
  
  @media (max-width: 2000px) {
    margin-bottom: 0.625vw;
  }
}

// 智能物联tab样式
.iot-tabs {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  
  .tab-item {
    padding: 8px 20px;
    background-image: url(/src/assets/common/selected2.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border: none;
    border-radius: 0;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      opacity: 0.8;
    }
    
    &.active {
      background-image: url(/src/assets/common/selected1.png);
    }
  }
}

// 设备页面tab样式
.device-tabs {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  
  .tab-item {
    padding: 8px 20px;
    background-image: url(/src/assets/common/selected2.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border: none;
    border-radius: 0;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      opacity: 0.8;
    }
    
    &.active {
      background-image: url(/src/assets/common/selected1.png);
    }
  }
}

// 物联设备相关样式
.device-overview {
  display: flex;
  // flex-direction: column;
  gap: 16px;
  
  .device-card {
    display: flex;
    align-items: center;
    padding: 16px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .device-icon {
      width: 32px;
      height: 32px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(23, 81, 159, 0.4);
      border-radius: 4px;
      
      img {
        width: 16px;
        height: 16px;
      }
    }
    
    .device-info {
      flex: 1;
      
      .device-title {
        font-size: 12px;
        color: #ffffff;
        margin-bottom: 4px;
      }
      
      .device-count {
        font-size: 18px;
        font-weight: bold;
        color: #166dc8;
        
        .unit {
          font-size: 12px;
          color: #ffffff;
          margin-left: 4px;
        }
      }
    }
  }
}

.device-distribution {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  
  .distribution-item {
    padding: 12px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    text-align: center;
    
    .item-name {
      font-size: 12px;
      color: #ffffff;
      margin-bottom: 4px;
    }
    
    .item-count {
      font-size: 16px;
      font-weight: bold;
      color: #166dc8;
    }
  }
}

.device-table {
  .table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    gap: 8px;
    padding: 12px 8px;
    background: rgba(23, 81, 159, 0.3);
    border-radius: 4px 4px 0 0;
    border: 1px solid #24324b;
    font-size: 12px;
    color: #ffffff;
    font-weight: bold;
  }
  
  .table-body {
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari and Opera */
    }
    
    .table-row {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
      gap: 8px;
      padding: 8px;
      border: 1px solid #24324b;
      border-top: none;
      background: rgba(0, 20, 40, 0.3);
      font-size: 12px;
      
      &:hover {
        background: rgba(23, 81, 159, 0.1);
      }
      
      .device-cell {
        display: flex;
        align-items: center;
        
        .device-cell-icon {
          width: 20px;
          height: 20px;
          margin-right: 8px;
          background: rgba(23, 81, 159, 0.4);
          border-radius: 2px;
          display: flex;
          align-items: center;
          justify-content: center;
          
          img {
            width: 100%;
            height: 100%;
          }
        }
        
        .device-info {
          display: flex;
          flex-direction: column;
          
          .device-name {
            color: #ffffff;
            font-size: 12px;
            margin-bottom: 2px;
          }
          
          .device-total {
            color: #166dc8;
            font-size: 11px;
            font-weight: bold;
          }
        }
      }
      
      .online-cell {
        display: flex;
        align-items: center;
        
        img {
          width: 12px;
          height: 12px;
          margin-right: 4px;
        }
        
        span {
          color: #166dc8;
        }
      }
      
      .error-count {
        color: #ffffff;
        
        &.has-error {
          color: #ff6b6b;
        }
      }
      
      .action-btn {
        background: #166dc8;
        color: #ffffff;
        border: none;
        padding: 4px 8px;
        border-radius: 2px;
        font-size: 12px;
        cursor: pointer;
        
        &:hover {
          background: #1890ff;
        }
      }
      
      .location-icon {
        width: 15px;
        height: 18px;
        cursor: pointer;
      }
    }
    
    .no-data {
      text-align: center;
      padding: 40px 20px;
      color: rgba(255, 255, 255, 0.6);
      font-size: 14px;
    }
  }
}

// 预警相关样式
.warning-overview {
  display: flex;
  align-items: center;
  justify-content: space-around;
  // flex-direction: column;
  gap: 10px;
  
  .warning-card {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .warning-icon {
      width: 32px;
      height: 32px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      img {
        width: 24px;
        height: 24px;
      }
    }
    
    .warning-info {
      flex: 1;
      
      .warning-count {
        font-size: 24px;
        font-weight: bold;
        color: #ff6b6b;
        margin-bottom: 4px;
      }
      
      .warning-title {
        font-size: 12px;
        color: #ffffff;
      }
    }
  }
}

.warning-table {
  margin: 16px 0;
  
  .warning-type-text,
  .warning-time-text {
    color: #fff;
    font-size: 12px;
  }

  .warning-area-text {
    font-size: 12px;
    color: #00d1f0;
  }

  .warning-status-text {
    font-size: 12px;
    
    &.status-pending {
      color: #0b6cd3;
    }
    
    &.status-completed {
      color: #02fbff;
    }
  }

  // 查看按钮样式
  :deep(.el-button--small) {
    background: #166dc8;
    color: #ffffff;
    border: none;
    padding: 4px 8px;
    border-radius: 2px;
    font-size: 12px;
    
    &:hover {
      background: #1890ff;
    }
  }
}

.pending-chart {
  margin: 16px 0;
  height: 200px;
}

.device-ranking-list,
.type-ranking-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin: 16px 0;

  .ranking-block {
    flex: 1;
    min-width: calc(50% - 6px);
    
    .ranking-content {
      padding: 8px 12px;
      background-color: #0c162d;
      border: 1px solid #24324b;
      border-radius: 4px;

      p {
        margin: 0;
        
        &:first-of-type {
          color: rgba(255, 255, 255, 0.7);
          font-size: 12px;
          margin-bottom: 4px;
        }

        &:last-of-type {
          color: rgba(255, 255, 255, 0.9);
          font-weight: 700;
          font-size: 14px;
        }
      }
    }
  }
}

.device-ranking-list .ranking-block {
  min-width: calc(33.33% - 8px);
}

// 确保表格在深色主题下正确显示
:deep(.el-table) {
  background-color: transparent !important;
  
  .el-table__header-wrapper {
    background-color: #0c162d !important;
  }
  
  .el-table__body-wrapper {
    background-color: transparent !important;
  }
  
  .el-table__row {
    background-color: transparent !important;
    
    &.el-table__row--striped {
      background-color: rgba(255, 255, 255, 0.02) !important;
    }
    
    &:hover {
      background-color: rgba(23, 81, 159, 0.3) !important;
    }
  }
  
  th.el-table__cell {
    background-color: #0c162d !important;
    border-bottom: 1px solid #24324b !important;
    color: rgba(255, 255, 255, 0.8) !important;
  }
  
  td.el-table__cell {
    background-color: transparent !important;
    border-bottom: 1px solid #24324b !important;
    color: #ffffff !important;
  }
  
  // 确保表格内部所有元素都是深色主题
  .el-table__empty-block {
    background-color: transparent !important;
  }
  
  .el-table__empty-text {
    color: rgba(255, 255, 255, 0.6) !important;
  }
  
  // 修复表格边框
  &::before {
    background-color: #24324b !important;
  }
  
  .el-table__border-left-patch {
    background-color: #0c162d !important;
  }
  
  // 确保表格主体背景
  .el-table__body {
    background-color: transparent !important;
  }
  
  // 确保表格头部背景
  .el-table__header {
    background-color: #0c162d !important;
  }
}

.chart-placeholder {
  .chart-content {
    .chart-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      
      .chart-label {
        width: 80px;
        font-size: 12px;
        color: #ffffff;
      }
      
      .chart-bar {
        flex: 1;
        height: 20px;
        background: rgba(23, 81, 159, 0.2);
        border-radius: 10px;
        margin: 0 12px;
        position: relative;
        
        .bar-fill {
          height: 100%;
          background: linear-gradient(90deg, #166dc8, #1890ff);
          border-radius: 10px;
          transition: width 0.3s ease;
        }
      }
      
      .chart-value {
        width: 40px;
        font-size: 12px;
        color: #166dc8;
        font-weight: bold;
        text-align: right;
      }
    }
  }
}

.risk-ranking {
  .rank-item {
    padding: 12px;
    margin-bottom: 8px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .rank-info {
      .rank-title {
        font-size: 12px;
        color: #ffffff;
        margin-bottom: 4px;
      }
      
      .rank-count {
        font-size: 16px;
        font-weight: bold;
        color: #166dc8;
      }
    }
  }
}

// 精准数仓样式
.community-overview {
  .overview-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 5px;
    
    .overview-card {
      display: flex;
      align-items: center;
      padding: 12px 5px;
      background: rgba(23, 81, 159, 0.2);
      border: 1px solid #24324b;
      border-radius: 10px;
      
      .card-icon {
        width:30px;
        height: 30px;
        margin-right: 6px;
        // background: rgba(22, 109, 200, 0.3);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        img{
          width: 30px;
          height: 30px;
        }
      }
      
      .card-info {
        flex: 1;
        
        .card-title {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 4px;
        }
        
        .card-count {
          font-size: 18px;
          font-weight: bold;
          color: #166dc8;
        }
      }
    }
  }
}

.data-statistics {
  display: flex;
  gap: 20px;
  margin-top: 32px;
  
  .stat-item {
    flex: 1;
    text-align: center;
    padding: 20px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .stat-number {
      font-size: 22px;
      font-weight: bold;
      color: #208DF8;
      margin-bottom: 15px;
    }
    
    .stat-label {
      font-size: 12px;
      color: #fff;
    }
  }
}
.tag-statistics-title{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .tag-title{
    font-size: 12px;
    color: #ffffff;
  }
  .tag-count{
    color: #02FBFF;
    margin: 0 5px;
  }
}
.tag-statistics {
  .tag-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .tag-title {
      font-size: 14px;
      color: #ffffff;
    }
    
    .tag-count {
      font-size: 14px;
      color: #ff6b6b;
      font-weight: bold;
    }
  }
  
  .tag-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px 10px;
    
    .tag-item {
      text-align: center;
      padding: 12px 14px;
      // background: rgba(23, 81, 159, 0.2);
      border: 1px solid #133056;
      border-radius: 10px;
      
      .tag-number {
        font-size: 20px;
        font-weight: bold;
        color: #fff;
        margin-bottom: 4px;
      }
      
      .tag-name {
        font-size: 12px;
        color: #0B6CD3;
      }
    }
  }
}

.data-source {
  margin-top: 32px;
  .source-item {
    display: flex;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .source-icon {
      width: 30px;
      height: 30px;
      margin-right: 12px;
      flex-shrink: 0;
      margin-top: 2px;
      
      img {
        width: 100%;
        height: 100%;
      }
    }
    
    .source-text {
      flex: 1;
      font-size: 12px;
      color: #fff;
      line-height: 1.4;
      display: flex;
      justify-content: space-between;
    }
  }
}

// 崃建言样式
.laijiyan-overview {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  
  .overview-card {
    display: flex;
    align-items: center;
    padding: 16px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .card-icon {
      width: 32px;
      height: 32px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(23, 81, 159, 0.4);
      border-radius: 4px;
      
      img {
        width: 20px;
        height: 20px;
      }
    }
    
    .card-info {
      flex: 1;
      
      .card-title {
        font-size: 12px;
        color: #ffffff;
        margin-bottom: 4px;
      }
      
      .card-count {
        font-size: 18px;
        font-weight: bold;
        color: #166dc8;
        
        .unit {
          font-size: 12px;
          color: #ffffff;
          margin-left: 4px;
        }
      }
    }
  }
}

.adoption-chart {
  .chart-placeholder {
    .chart-content {
      .chart-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        .chart-label {
          width: 80px;
          font-size: 12px;
          color: #ffffff;
        }
        
        .chart-bar {
          flex: 1;
          height: 20px;
          background: rgba(23, 81, 159, 0.2);
          border-radius: 10px;
          margin: 0 12px;
          position: relative;
          
          .bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #166dc8, #1890ff);
            border-radius: 10px;
            transition: width 0.3s ease;
          }
        }
        
        .chart-value {
          width: 40px;
          font-size: 12px;
          color: #166dc8;
          font-weight: bold;
          text-align: right;
        }
      }
    }
  }
}

.processing-status {
  .status-list {
    .status-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
      margin-bottom: 8px;
      background: rgba(23, 81, 159, 0.2);
      border: 1px solid #24324b;
      border-radius: 4px;
      
      .status-info {
        flex: 1;
        
        .status-title {
          font-size: 14px;
          color: #ffffff;
          margin-bottom: 4px;
        }
        
        .status-area {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 2px;
        }
        
        .status-time {
          font-size: 11px;
          color: rgba(255, 255, 255, 0.5);
        }
      }
      
      .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: bold;
        
        &.completed {
          background: rgba(76, 175, 80, 0.2);
          color: #4caf50;
          border: 1px solid #4caf50;
        }
        
        &.processing {
          background: rgba(255, 193, 7, 0.2);
          color: #ffc107;
          border: 1px solid #ffc107;
        }
        
        &.pending {
          background: rgba(244, 67, 54, 0.2);
          color: #f44336;
          border: 1px solid #f44336;
        }
      }
    }
  }
}

// 社区经济样式
.economy-tabs {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  
  .tab-item {
    padding: 8px 20px;
    background-image: url(/src/assets/common/selected2.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border: none;
    border-radius: 0;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      opacity: 0.8;
    }
    
    &.active {
      background-image: url(/src/assets/common/selected1.png);
    }
  }
}

// 社区导览样式
.date-box {
  justify-content: flex-end;
}

.data-box {
  margin: 24px 0 35px 0;
  padding: 0 24px 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  background: #0c162f;
  border-radius: 2px;
  border: 1px solid #293657;
  height: 80px;

  .left-box {
    display: flex;
    align-items: center;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 14px;
  }

  .right-box {
    font-weight: 900;
    color: transparent;
    font-family: Swei Fist Leg CJK SC;
    font-size: 20px;
    color: #ffffff;

    .text-color-blue {
      color: transparent;
      -webkit-background-clip: text;
      background-image: linear-gradient(0deg, #0b6cd3 20%, #fff 80%);
    }

    .unit {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      margin-left: 5px;
    }
  }
}

.resource-distribution {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  
  .distribution-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .item-name {
      font-size: 12px;
      color: #ffffff;
    }
    
    .item-count {
      font-size: 14px;
      font-weight: bold;
      color: #166dc8;
    }
  }
}

.usage-chart {
  .chart-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    
    .chart-label {
      width: 80px;
      font-size: 12px;
      color: #ffffff;
      flex-shrink: 0;
    }
    
    .chart-bar {
      flex: 1;
      height: 20px;
      background: rgba(23, 81, 159, 0.2);
      border-radius: 10px;
      margin: 0 12px;
      position: relative;
      display: flex;
      align-items: center;
      
      .bar-fill {
        height: 100%;
        background: linear-gradient(90deg, #166dc8, #1890ff);
        border-radius: 10px;
        transition: width 0.3s ease;
        min-width: 2px;
      }
      
      .bar-value {
        position: absolute;
        right: 8px;
        font-size: 11px;
        color: #ffffff;
        font-weight: bold;
        z-index: 1;
      }
    }
  }
}

.heat-ranking {
  .ranking-item {
    display: flex;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .ranking-index {
      width: 24px;
      height: 24px;
      background: #166dc8;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      color: #ffffff;
      margin-right: 12px;
      flex-shrink: 0;
    }
    
    .ranking-name {
      flex: 1;
      font-size: 13px;
      color: #ffffff;
      margin-right: 12px;
    }
    
    .ranking-values {
      display: flex;
      gap: 16px;
      
      .community-line,
      .life-circle {
        font-size: 11px;
        color: #166dc8;
      }
      
      .life-circle {
        color: #ffc107;
      }
    }
  }
}

// .module-title {
//   margin: 20px 0;
// }

// 预警消息列表样式
.warning-list {
  .warning-item {
    display: flex;
    flex-direction: column;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(23, 81, 159, 0.3);
      border-color: #166dc8;
    }
    
    .warning-type {
      font-size: 14px;
      font-weight: bold;
      color: #ffffff;
      margin-bottom: 4px;
    }
    
    .warning-area {
      font-size: 12px;
      color: #9D9DA6;
      margin-bottom: 4px;
    }
    
    .warning-time {
      font-size: 11px;
      color: #9D9DA6;
      margin-bottom: 6px;
    }
    
    .warning-status {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 12px;
      align-self: flex-start;
      
      &.pending {
        background: rgba(255, 159, 24, 0.2);
        color: #FF9F18;
        border: 1px solid #FF9F18;
      }
      
      &.processed {
        background: rgba(0, 181, 120, 0.2);
        color: #00B578;
        border: 1px solid #00B578;
      }
    }
  }
  
  .no-data {
    text-align: center;
    padding: 40px 20px;
    color: #9D9DA6;
    font-size: 14px;
  }
}

// 额外的表格深色主题强制样式
:deep(.warning-table) {
  .el-table,
  .el-table * {
    background-color: transparent !important;
  }
  
  .el-table__header-wrapper,
  .el-table__header-wrapper * {
    background-color: #0c162d !important;
  }
  
  .el-table__body-wrapper,
  .el-table__body-wrapper * {
    background-color: transparent !important;
  }
  
  .el-table__row,
  .el-table__row * {
    background-color: transparent !important;
  }
  
  .el-table__cell,
  .el-table__cell * {
    background-color: transparent !important;
  }
  
  th.el-table__cell,
  th.el-table__cell * {
    background-color: #0c162d !important;
  }
}

// 风险排行样式
.risk-ranking {
  .rank-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .rank-info {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .rank-title {
        font-size: 13px;
        color: #ffffff;
      }
      
      .rank-count {
        font-size: 14px;
        font-weight: bold;
        color: #166dc8;
      }
    }
  }
}

// 社区经济页面样式
.total-usage-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: rgba(23, 81, 159, 0.2);
  border: 1px solid #24324b;
  border-radius: 4px;
  
  .usage-icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    img {
      width: 24px;
      height: 24px;
    }
  }
  
  .usage-content {
    flex: 1;
    display:flex;
    align-items: center;
    
    .usage-title {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 4px;
    }
    
    .usage-number {
      font-size: 24px;
      font-weight: bold;
      color: #166dc8;
    }
    
    .usage-unit {
      font-size: 12px;
      color: #ffffff;
    }
  }
}

.no-data {
  text-align: center;
  padding: 40px 20px;
  color: #9D9DA6;
  font-size: 14px;
}
  
  .action-buttons {
    display: flex;
    gap: 8px;
    .action-btn {
      flex: 1;
      padding: 8px 12px;
      font-size: 12px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 4px;
      // background: transparent;
      // color: #9D9DA6;
      cursor: pointer;
      transition: all 0.3s ease;
       
    }
     .bg1{
        background: #107C9E;
        color: #fff;
      }
      .bg2{
        background: #FBA602;
        color: #fff;
      }
      .bg3{
        background: #1990FF;
        color: #fff;
      }
  }
</style> 