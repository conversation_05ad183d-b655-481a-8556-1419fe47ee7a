<template>
  <el-dialog
    :model-value="visible"
    :title="deviceType"
    width="1024px"
    @close="handleClose"
    :close-on-click-modal="false"
    class="device-dialog"
  >
    <div class="dialog-content">
      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form inline>
          <el-form-item label="设备名称">
            <el-input v-model="searchForm.deviceName" placeholder="请输入设备名称" />
          </el-form-item>
          <el-form-item label="设备状态">
            <el-select v-model="searchForm.deviceState" placeholder="请选择" clearable>
              <el-option label="在线" :value="1" />
              <el-option label="离线" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="设备编号">
            <el-input v-model="searchForm.deviceNumber" placeholder="请输入设备编号" />
          </el-form-item>
          <el-form-item label="开始日期">
            <el-date-picker
              v-model="searchForm.dateBegin"
              type="date"
              placeholder="选择日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="结束日期">
            <el-date-picker
              v-model="searchForm.dateEnd"
              type="date"
              placeholder="选择日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 设备列表表格 -->
      <div class="device-table">
        <el-table
          :data="deviceList"
          v-loading="loading"
          stripe
          height="400"
        >
          <el-table-column prop="DeviceName" label="设备名称" width="150" />
          <el-table-column prop="DeviceState" label="设备状态" width="100">
            <template #default="{ row }">
              <span :class="row.DeviceState === 1 ? 'status-online' : 'status-offline'">
                {{ row.DeviceState === 1 ? '在线' : '离线' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="DeviceSN" label="设备编号" width="180" />
          <el-table-column prop="ParkName" label="所在小区" width="150" />
          <el-table-column prop="DeviceAddress" label="设备地址" min-width="200" />
          <el-table-column prop="Position" label="坐标" width="120" />
          <el-table-column label="实时画面" width="100">
            <template #default="{ row }">
              <el-button 
                v-if="row.PDLVFP_Url" 
                type="primary" 
                size="small" 
                @click="handleViewVideo(row)"
              >
                查看
              </el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getDCDeviceListApi, getParkListApi } from '@/api/bigscreen'
import type { DCDeviceListParams, DCDeviceListResponse } from '@/api/bigscreen'

interface Props {
  visible: boolean
  deviceType: string
  deviceTypeId?: number
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 搜索表单
const searchForm = reactive({
  deviceName: '',
  deviceState: undefined as number | undefined,
  deviceNumber: '',
  dateBegin: '',
  dateEnd: ''
})

// 表格数据
const deviceList = ref<any[]>([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 小区列表
const parkList = ref<any[]>([])

// 获取小区列表
const getParkList = async () => {
  try {
    const result = await getParkListApi({ areaCode: '510183' })
    console.log('小区列表数据：', result)
    if (result && Array.isArray(result)) {
      parkList.value = result
    }
  } catch (error) {
    console.error('获取小区列表失败：', error)
  }
}

// 获取设备列表
const getDeviceList = async () => {
  loading.value = true
  try {
    const params: DCDeviceListParams = {
      AreaCode: '510183',
      Rows: pageSize.value,
      Page: currentPage.value,
      ...searchForm
    }
    
    if (props.deviceTypeId) {
      params.DeviceType = props.deviceTypeId
    }

    console.log('请求参数：', params)
    const result = await getDCDeviceListApi(params)
    console.log('设备列表数据：', result)
    
    if (result && result.rows) {
      deviceList.value = result.rows
      total.value = result.total || 0
    } else {
      deviceList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取设备列表失败：', error)
    ElMessage.error('获取设备列表失败')
    // 模拟数据作为备用
    deviceList.value = [
      {
        DeviceName: '火焰监测6',
        DeviceState: 1,
        DeviceSN: 'KMUD_TYXJ_HYJC06',
        ParkName: '太阳新居小区',
        DeviceAddress: '小区内部',
        Position: '104.123,30.456'
      },
      {
        DeviceName: '火焰监测5',
        DeviceState: 1,
        DeviceSN: 'KMUD_TYXJ_HYJC05',
        ParkName: '太阳新居小区',
        DeviceAddress: '小区内部',
        Position: '104.124,30.457'
      }
    ]
    total.value = 2
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  getDeviceList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    deviceName: '',
    deviceState: undefined,
    deviceNumber: '',
    dateBegin: '',
    dateEnd: ''
  })
  currentPage.value = 1
  getDeviceList()
}

// 分页变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  getDeviceList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getDeviceList()
}

// 查看视频
const handleViewVideo = (row: any) => {
  console.log('查看视频：', row)
  // 这里可以实现视频播放逻辑
  ElMessage.info('视频播放功能待实现')
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    getDeviceList()
    getParkList()
  }
})

onMounted(() => {
  if (props.visible) {
    getDeviceList()
    getParkList()
  }
})
</script>

<style scoped lang="scss">
.device-dialog {
  :deep(.el-dialog) {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border: 1px solid #4a90e2;
    border-radius: 8px;
  }
  
  :deep(.el-dialog__header) {
    background: rgba(74, 144, 226, 0.1);
    border-bottom: 1px solid #4a90e2;
    padding: 16px 20px;
    
    .el-dialog__title {
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
    }
    
    .el-dialog__close {
      color: #ffffff;
      &:hover {
        color: #4a90e2;
      }
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  height: 628px;
}

.search-form {
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(74, 144, 226, 0.3);
  
  :deep(.el-form-item__label) {
    color: #ffffff;
  }
  
  :deep(.el-input__wrapper) {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(74, 144, 226, 0.3);
    
    .el-input__inner {
      color: #ffffff;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }
  
  :deep(.el-select) {
    .el-select__wrapper {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(74, 144, 226, 0.3);
      
      .el-select__placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
      
      .el-select__selected-item {
        color: #ffffff;
      }
    }
  }
  
  :deep(.el-date-editor) {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(74, 144, 226, 0.3);
      
      .el-input__inner {
        color: #ffffff;
        
        &::placeholder {
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }
  }
}

.device-table {
  flex: 1;
  margin-bottom: 20px;
  
  :deep(.el-table) {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(74, 144, 226, 0.3);
    
    .el-table__header {
      background: rgba(74, 144, 226, 0.2);
      
      th {
        background: rgba(74, 144, 226, 0.2);
        color: #ffffff;
        border-bottom: 1px solid rgba(74, 144, 226, 0.3);
      }
    }
    
    .el-table__body {
      tr {
        background: rgba(255, 255, 255, 0.05);
        
        &:hover {
          background: rgba(74, 144, 226, 0.1);
        }
        
        &.el-table__row--striped {
          background: rgba(255, 255, 255, 0.1);
          
          &:hover {
            background: rgba(74, 144, 226, 0.1);
          }
        }
        
        td {
          color: #ffffff;
          border-bottom: 1px solid rgba(74, 144, 226, 0.2);
        }
      }
    }
  }
}

.status-online {
  color: #67c23a;
  font-weight: 600;
}

.status-offline {
  color: #f56c6c;
  font-weight: 600;
}

.pagination {
  display: flex;
  justify-content: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(74, 144, 226, 0.3);
  
  :deep(.el-pagination) {
    .el-pagination__total,
    .el-pagination__jump {
      color: #ffffff;
    }
    
    .el-select .el-select__wrapper {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(74, 144, 226, 0.3);
      
      .el-select__selected-item {
        color: #ffffff;
      }
    }
    
    .el-pager {
      li {
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
        border: 1px solid rgba(74, 144, 226, 0.3);
        
        &:hover {
          background: rgba(74, 144, 226, 0.3);
        }
        
        &.is-active {
          background: #4a90e2;
          color: #ffffff;
        }
      }
    }
    
    .btn-prev,
    .btn-next {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      border: 1px solid rgba(74, 144, 226, 0.3);
      
      &:hover {
        background: rgba(74, 144, 226, 0.3);
      }
    }
  }
}
</style>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getDCDeviceListApi, getParkListApi } from '@/api/bigscreen'
import type { DCDeviceListParams, DCDeviceListResponse } from '@/api/bigscreen'

interface Props {
  visible: boolean
  deviceType: string
  deviceTypeId?: number
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 搜索表单
const searchForm = reactive({
  deviceName: '',
  deviceState: undefined as number | undefined,
  deviceNumber: '',
  dateBegin: '',
  dateEnd: ''
})

// 表格数据
const deviceList = ref<any[]>([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 小区列表
const parkList = ref<any[]>([])

// 获取小区列表
const getParkList = async () => {
  try {
    const result = await getParkListApi({ areaCode: '510183' })
    console.log('小区列表数据：', result)
    if (result && Array.isArray(result)) {
      parkList.value = result
    }
  } catch (error) {
    console.error('获取小区列表失败：', error)
  }
}

// 获取设备列表
const getDeviceList = async () => {
  loading.value = true
  try {
    const params: DCDeviceListParams = {
      AreaCode: '510183',
      Rows: pageSize.value,
      Page: currentPage.value,
      ...searchForm
    }
    
    if (props.deviceTypeId) {
      params.DeviceType = props.deviceTypeId
    }

    console.log('请求参数：', params)
    const result = await getDCDeviceListApi(params)
    console.log('设备列表数据：', result)
    
    if (result && result.rows) {
      deviceList.value = result.rows
      total.value = result.total || 0
    } else {
      deviceList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取设备列表失败：', error)
    ElMessage.error('获取设备列表失败')
    // 模拟数据作为备用
    deviceList.value = [
      {
        DeviceName: '火焰监测6',
        DeviceState: 1,
        DeviceSN: 'KMUD_TYXJ_HYJC06',
        ParkName: '太阳新居小区',
        DeviceAddress: '小区内部',
        Position: '104.123,30.456'
      },
      {
        DeviceName: '火焰监测5',
        DeviceState: 1,
        DeviceSN: 'KMUD_TYXJ_HYJC05',
        ParkName: '太阳新居小区',
        DeviceAddress: '小区内部',
        Position: '104.124,30.457'
      }
    ]
    total.value = 2
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  getDeviceList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    deviceName: '',
    deviceState: undefined,
    deviceNumber: '',
    dateBegin: '',
    dateEnd: ''
  })
  currentPage.value = 1
  getDeviceList()
}

// 分页变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  getDeviceList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getDeviceList()
}

// 查看视频
const handleViewVideo = (row: any) => {
  console.log('查看视频：', row)
  // 这里可以实现视频播放逻辑
  ElMessage.info('视频播放功能待实现')
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    getDeviceList()
    getParkList()
  }
})

onMounted(() => {
  if (props.visible) {
    getDeviceList()
    getParkList()
  }
})
</script>

<style scoped lang="scss">
.device-dialog {
  :deep(.el-dialog) {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border: 1px solid #4a90e2;
    border-radius: 8px;
  }
  
  :deep(.el-dialog__header) {
    background: rgba(74, 144, 226, 0.1);
    border-bottom: 1px solid #4a90e2;
    padding: 16px 20px;
    
    .el-dialog__title {
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
    }
    
    .el-dialog__close {
      color: #ffffff;
      &:hover {
        color: #4a90e2;
      }
    }
  }
  
  :deep(.el-dialog__body) {
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  height: 628px;
}

.search-form {
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(74, 144, 226, 0.3);
  
  :deep(.el-form-item__label) {
    color: #ffffff;
  }
  
  :deep(.el-input__wrapper) {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(74, 144, 226, 0.3);
    
    .el-input__inner {
      color: #ffffff;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }
  
  :deep(.el-select) {
    .el-select__wrapper {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(74, 144, 226, 0.3);
      
      .el-select__placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
      
      .el-select__selected-item {
        color: #ffffff;
      }
    }
  }
  
  :deep(.el-date-editor) {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(74, 144, 226, 0.3);
      
      .el-input__inner {
        color: #ffffff;
        
        &::placeholder {
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }
  }
}

.device-table {
  flex: 1;
  margin-bottom: 20px;
  
  :deep(.el-table) {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(74, 144, 226, 0.3);
    
    .el-table__header {
      background: rgba(74, 144, 226, 0.2);
      
      th {
        background: rgba(74, 144, 226, 0.2);
        color: #ffffff;
        border-bottom: 1px solid rgba(74, 144, 226, 0.3);
      }
    }
    
    .el-table__body {
      tr {
        background: rgba(255, 255, 255, 0.05);
        
        &:hover {
          background: rgba(74, 144, 226, 0.1);
        }
        
        &.el-table__row--striped {
          background: rgba(255, 255, 255, 0.1);
          
          &:hover {
            background: rgba(74, 144, 226, 0.1);
          }
        }
        
        td {
          color: #ffffff;
          border-bottom: 1px solid rgba(74, 144, 226, 0.2);
        }
      }
    }
  }
}

.status-online {
  color: #67c23a;
  font-weight: 600;
}

.status-offline {
  color: #f56c6c;
  font-weight: 600;
}

.pagination {
  display: flex;
  justify-content: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(74, 144, 226, 0.3);
  
  :deep(.el-pagination) {
    .el-pagination__total,
    .el-pagination__jump {
      color: #ffffff;
    }
    
    .el-select .el-select__wrapper {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(74, 144, 226, 0.3);
      
      .el-select__selected-item {
        color: #ffffff;
      }
    }
    
    .el-pager {
      li {
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
        border: 1px solid rgba(74, 144, 226, 0.3);
        
        &:hover {
          background: rgba(74, 144, 226, 0.3);
        }
        
        &.is-active {
          background: #4a90e2;
          color: #ffffff;
        }
      }
    }
    
    .btn-prev,
    .btn-next {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      border: 1px solid rgba(74, 144, 226, 0.3);
      
      &:hover {
        background: rgba(74, 144, 226, 0.3);
      }
    }
  }
}
</style>