<template>
  <div class="left2-component">
    <!-- 标题栏 -->
    <div class="header-bar">
      <div class="header-back" @click="handleGoBack" :style="{ visibility: (fullscreenStore.screenMode === 'wide' || fullscreenStore.isFullscreen) ? 'hidden' : 'visible' }">
        <span class="back-text">上一页</span>
      </div>
      <div class="header-title">
        <div class="title-icon">
          <img src="@/assets/common/icon2.png" alt="title-icon">
        </div>
        <span class="title-text">{{ currentPageTitle }}</span>
      </div>
      <div class="header-back" @click="handleGoNext">
        <span class="back-text">下一页</span>
      </div>
    </div>
    
    <!-- 第二页：崃建言 -->
    <template v-if="currentPage === 2">
      <!-- 选项卡 -->
      <!-- <TabsComponent 
        v-model="activeTab" 
        :tabs="tabOptions"
        @change="handleTabChange"
        v-show="!fullscreenStore.isFullscreen"
      /> -->
      
      <!-- 社区导览内容 -->
    <template v-if="activeTab === 'guide'">
      <div class="module-box">
        <ModuleTitle title="崃建言概览" />
        <DateGroup :index="overviewTimeRange" :dateData="timeOptions" @change="changeOverview"/>
        <SuggestionOverview 
          :overviewData="overviewData" 
          :titleData="{receivedTitle:'已接收', feedbackTitle:'已反馈', receivedUnit:'条', feedbackUnit:'条'}" 
        />
         <div class="action-buttons" style="margin-top: 20px;">
           <button class="action-btn bg1" @click="handleSuggestionResourceMap">资源地图分布</button>
            <button class="action-btn bg2" @click="handleSuggestionHeatMap">辖区热力分布</button>
            <button class="action-btn bg3" @click="handleSuggestionDataDetail">数据详情</button>
          </div>
      </div>
      
      <div class="module-box">
        <ModuleTitle title="崃建言采纳情况" />
        <DateGroup :index="adoptionTimeRange" :dateData="adoptionTimeOptions" @change="changeAdoption"/>
        <EchartsAdoptionRate :chartData="adoptionData" :loading="false"/>
      </div>
      
      <div class="module-box">
        <ModuleTitle title="崃建言处理情况" />
        <DateGroup :index="processTimeRange" :dateData="processTimeOptions" @change="changeProcess"/>
        <EchartsProcessStatus :chartData="processData" :loading="false"/>
      </div>
    </template>
    

    </template>

    <!-- 第四页：社区经济 -->
    <template v-else-if="currentPage === 4">
      <!-- 社区经济选项卡 -->
      <div class="economy-tabs">
        <div 
          class="tab-item" 
          :class="{ active: economyActiveTab === 'navigation' }"
          @click="handleEconomyTabChange('navigation')"
        >
          社区导览
        </div>
        <div 
          class="tab-item" 
          :class="{ active: economyActiveTab === 'tour' }"
          @click="handleEconomyTabChange('tour')"
        >
          社区游线
        </div>
      </div>

      <!-- 社区导览内容 -->
      <template v-if="economyActiveTab === 'navigation'">
        <div class="module-box">
          <ModuleTitle title="使用总人次" />
          <DateGroup 
            :index="navigationTimeRange"
            :dateData="navigationTimeOptions"
            @change="changeNavigationTime"
          />
          <div class="total-usage-card">
            <div class="usage-icon">
              <img :src="communityX1" alt="使用总人次" />
            </div>
            <div class="usage-content">
              <div class="usage-title">使用总人次</div>
              <div class="usage-number">{{ navigationData.totalNumber.toLocaleString() }}</div>
              <div class="usage-unit">次</div>
            </div>
          </div>
          <!-- 社区导览操作按钮 -->
          <div class="action-buttons" style="margin-top: 20px;">
            <button class="action-btn bg1" @click="handleNavigationResourceMap">资源地图分布</button>
            <button class="action-btn bg2" @click="handleNavigationHeatMap">辖区热力分布</button>
            <button class="action-btn bg3" @click="handleNavigationDataDetail">数据详情</button>
          </div>
        </div>

        <div class="module-box">
          <ModuleTitle title="资源分布" />
          <EchartsResourcePie 
            :chartData="navigationData.resourcePieChartData" 
            :loading="false"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="使用数量" />
          <EchartsColumnar 
            :chartData="navigationData.chartData2" 
            :loading="false"
            :height="200"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="使用热度排行" />
          <EchartsColumnar 
            :chartData="navigationData.heatRankingChartData" 
            :loading="false"
            :height="200"
          />
        </div>
      </template>

      <!-- 社区游线内容 -->
      <template v-else-if="economyActiveTab === 'tour'">
        <div class="module-box">
          <ModuleTitle title="社区游线概览" />
          <DateGroup :index="tourTimeRange" :dateData="tourTimeOptions" @change="changeTourInfo"/>
          <ReuseOverview 
            :overviewData="{
              itemsCount: tourOverviewData.totalItems,
              clicksCount: tourOverviewData.totalClicks
            }" 
            :titleData="{
              itemsTitle: '游线总数',
              clicksTitle: '总浏览量', 
              itemsUnit: '条',
              clicksUnit: '次'
            }"
            :loading="tourIsLoadingInfo"
          />
            </div>

        <div class="module-box">
          <ModuleTitle title="社区游线热浏览概况" />
          <DateGroup :index="tourHotTimeRange" :dateData="tourTimeOptions" @change="changeTourHots"/>
          <EchartsHotReuse 
            :chartData="tourHotReuseData" 
            :loading="tourIsLoadingHotList"
            :dataKeys="{
              nameKey: 'tourLineName',
              valueKey: 'browseNumber'
            }"
          />
        </div>
        
        <div class="module-box">
          <ModuleTitle title="社区游线浏览及参与人数分布" />
          <DateGroup :index="tourListTimeRange" :dateData="tourTimeOptions" @change="changeTourList"/>
          <EchartsReuseDistribution 
            :chartData="tourDistributionData" 
            :loading="tourIsLoadingList"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="热门社区游线" />
          <DateGroup :index="tourHotListTimeRange" :dateData="tourTimeOptions" @change="changeTourHotList"/>
          <ReuseTable 
            :tableData="tourTableData"
            :columns="[
              { prop: 'districtName', label: '区域名称' },
              { prop: 'tourLineName', label: '游线名称' },
              { prop: 'browseNumber', label: '浏览次数' }
            ]"
            :loading="tourIsLoadingHotList"
          />
        </div>
      </template>
    </template>

    <!-- 社区导览数据详情弹窗 -->
    <CommunityTourDataDetailDialog
      v-model:visible="communityTourDataDetailVisible"
      :area-code="getDistrictCode()"
      :time-range="navigationTimeRange"
    />

    <!-- 崃建言数据详情弹窗 -->
    <SuggestionDataDetailDialog
      v-model:visible="suggestionDataDetailVisible"
      :area-code="getDistrictCode()"
      :time-range="overviewTimeRange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import ModuleTitle from './components/ModuleTitle.vue'
import DateGroup from './components/DateGroup.vue'
import SuggestionOverview from './components/SuggestionOverview.vue'
import EchartsAdoptionRate from './components/EchartsAdoptionRate.vue'
import EchartsProcessStatus from './components/EchartsProcessStatus.vue'
import TabsComponent from './components/TabsComponent.vue'
import TourOverview from './components/TourOverview.vue'
import EchartsHotBrowse from './components/EchartsHotBrowse.vue'
import EchartsTourDistribution from './components/EchartsTourDistribution.vue'
import ReuseOverview from './components/ReuseOverview.vue'
import EchartsHotReuse from './components/EchartsHotReuse.vue'
import EchartsReuseDistribution from './components/EchartsReuseDistribution.vue'
import ReuseTable from './components/ReuseTable.vue'
import EchartsColumnar from '@/views/viewsDemo/views/sidebar/compoments/EchartsColumnar.vue'
import EchartsResourcePie from './components/EchartsResourcePie.vue'
import CommunityTourDataDetailDialog from './components/CommunityTourDataDetailDialog.vue'
import SuggestionDataDetailDialog from './components/SuggestionDataDetailDialog.vue'
import { getSuggestionOverviewApi, getSuggestionAdoptApi, getSuggestionListApi, getTourInfoApi, getTourListApi, getTourHotsApi, getAreaCode } from '@/api/suggestion'
import { getCommunityTourApi, getSuggestionMapResourceApi, getCraftsmanMapResourceApi, getCommunityTourHeatMapApi } from '@/api/bigscreen'
import type { CommunityTourParams, SuggestionMapResourceParams, CraftsmanMapResourceParams } from '@/api/bigscreen'
import { getDistrictCode } from '@/utils/district'
import { useFullscreenStore } from '@/stores/fullscreen'

// 导入图片
import communityX1 from '@/assets/images/community/x1.png'

// 全屏状态管理
const fullscreenStore = useFullscreenStore()

// 页面切换状态
const currentPage = ref(2) // 2: 崃建言, 4: 社区经济
const currentPageTitle = computed(() => {
  switch (currentPage.value) {
    case 2: return '崃建言'
    case 4: return '社区经济'
    default: return '崃建言'
  }
})

// 跟踪当前激活的地图撒点类型
const activeMapPointType = ref('') // 'suggestion', 'navigation'

// 选项卡配置
const activeTab = ref('guide')
const tabOptions = [
  { label: '社区导览', value: 'guide' },
  { label: '社区游线', value: 'tour' }
]

// 时间选项
const timeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]

const adoptionTimeOptions = timeOptions.filter(i => ['2','3','4'].includes(i.value))
const processTimeOptions = timeOptions.filter(i => ['2','3','4'].includes(i.value))

// 时间范围选择
const overviewTimeRange = ref('0')
const adoptionTimeRange = ref('2')
const processTimeRange = ref('2')

// 模拟数据
const overviewData = ref({
  receivedCount: 156,
  feedbackCount: 142
})

const adoptionData = ref([
  { name: '采纳', value: 65, color: '#018FFF' },
  { name: '部分采纳', value: 23, color: '#E9B902' },
  { name: '留作参考', value: 12, color: '#5D7092' }
])

const processData = ref([
  { district: { name: '阳光社区' }, receivedCount: 25, feedbackCount: 22 },
  { district: { name: '和谐社区' }, receivedCount: 18, feedbackCount: 16 },
  { district: { name: '幸福社区' }, receivedCount: 32, feedbackCount: 28 },
  { district: { name: '安康社区' }, receivedCount: 28, feedbackCount: 25 },
  { district: { name: '美好社区' }, receivedCount: 21, feedbackCount: 19 },
  { district: { name: '温馨社区' }, receivedCount: 19, feedbackCount: 17 },
  { district: { name: '文明社区' }, receivedCount: 24, feedbackCount: 21 },
  { district: { name: '繁荣社区' }, receivedCount: 16, feedbackCount: 14 }
])

// 社区游线数据 - 与Left1Component保持一致
const tourTimeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]

const tourTimeRange = ref('1')
const tourHotTimeRange = ref('1')
const tourListTimeRange = ref('1')
const tourHotListTimeRange = ref('1')

const tourOverviewData = ref<any>({})
const tourTableData = ref<any[]>([])
const tourHotReuseData = ref<any[]>([])
const tourDistributionData = ref<any[]>([])

// 加载状态
const tourIsLoadingInfo = ref(false)
const tourIsLoadingList = ref(false)
const tourIsLoadingHotList = ref(false)

// 社区经济数据
const economyActiveTab = ref('navigation')

// 社区经济 - 社区导览数据
const navigationData = reactive<{
  totalNumber: number
  chartData1: {
    columnarDataBlue: number[]
    columnarDataYellow: number[]
    curveData: number[]
    labelData: string[]
  }
  chartData2: {
    columnarDataBlue: number[]
    columnarDataYellow: number[]
    curveData: number[]
    labelData: string[]
  }
  pieChartData: any[]
  pieChartLegendData: any[]
  pieChartSerie1Data: any[]
  pieChartSerie2Data: any[]
  resourcePieChartData: any[] // 新的饼图数据格式
  heatRankingChartData: {
    columnarDataBlue: number[]
    columnarDataYellow: number[]
    curveData: number[]
    labelData: string[]
  }
}>({
  totalNumber: 0,
  chartData1: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  },
  chartData2: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  },
  pieChartData: [],
  pieChartLegendData: [],
  pieChartSerie1Data: [],
  pieChartSerie2Data: [],
  resourcePieChartData: [], // 新的饼图数据格式
  heatRankingChartData: {
    columnarDataBlue: [],
    columnarDataYellow: [],
    curveData: [],
    labelData: []
  }
})

// 社区导览时间选择
const navigationTimeRange = ref('2') // 默认近三月
const navigationTimeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
  ]

// 社区导览数据详情弹窗相关
const communityTourDataDetailVisible = ref(false)

// 崃建言数据详情弹窗相关
const suggestionDataDetailVisible = ref(false)

const economyTourData = ref({
  totalItems: 85,
  totalClicks: 125847,
  hotList: [
    { tourLineName: '文化古街游', browseNumber: 2850 },
    { tourLineName: '美食体验游', browseNumber: 2156 },
    { tourLineName: '生态休闲游', browseNumber: 1923 },
    { tourLineName: '历史文化游', browseNumber: 1654 },
    { tourLineName: '民俗风情游', browseNumber: 1432 }
  ],
  distributionList: [
    { name: '城市中心区', tourCount: 8, browseCount: 3456 },
    { name: '历史文化区', tourCount: 6, browseCount: 2789 },
    { name: '自然生态区', tourCount: 7, browseCount: 2134 },
    { name: '商业休闲区', tourCount: 4, browseCount: 1876 },
    { name: '居民生活区', tourCount: 3, browseCount: 1234 }
  ]
})





// 时间范围变更处理
const changeOverview = async (index: string | number) => {
  overviewTimeRange.value = String(index)
  await updateOverviewData(String(index))
}

const changeAdoption = async (index: string | number) => {
  adoptionTimeRange.value = String(index)
  await updateAdoptionData(String(index))
}

const changeProcess = async (index: string | number) => {
  processTimeRange.value = String(index)
  await updateProcessData(String(index))
}

const handleTabChange = async (tabValue: string) => {
  activeTab.value = tabValue
  console.log('切换到选项卡:', tabValue)
  // 根据不同的选项卡加载不同的数据
  if (tabValue === 'tour') {
    await getTourDataNew()
  }
}

const handleGoBack = () => {
  console.log('返回上一页')
  // 这里可以添加返回逻辑
}

const handleGoNext = () => {
  if (fullscreenStore.screenMode === 'wide' || fullscreenStore.isFullscreen) {
    // 宽屏模式和全屏模式下切换左侧页面
    fullscreenStore.switchLeftPage()
    console.log('宽屏/全屏模式下切换左侧页面:', fullscreenStore.leftPage)
  } else {
    // 窄屏模式下的正常切换逻辑
    if (currentPage.value === 2) {
      currentPage.value = 4
      console.log('切换到社区经济页面')
    } else {
      currentPage.value = 2
      console.log('切换到崃建言页面')
    }
  }
}

const handleEconomyTabChange = async (tab: string) => {
  economyActiveTab.value = tab
  console.log('切换社区经济tab:', tab)
  
  // 根据不同的tab加载对应的数据
  if (tab === 'navigation') {
    await getNavigationData(navigationTimeRange.value)
  } else if (tab === 'tour') {
    await getTourDataNew()
  }
}

// 处理社区导览时间变更
const changeNavigationTime = async (timeType: string) => {
  console.log('社区导览时间变更:', timeType)
  navigationTimeRange.value = timeType
  await getNavigationData(timeType)
}

// 社区经济 - 社区导览数据获取
const getNavigationData = async (timeType: string = '2', customAreaCode?: string) => {
  try {
    console.log('🔄 调用社区导览API，时间范围:', timeType)
    
    // 清空现有数据
    navigationData.totalNumber = 0
    navigationData.chartData1 = {
      columnarDataBlue: [],
      columnarDataYellow: [],
      curveData: [],
      labelData: []
    }
    navigationData.chartData2 = {
      columnarDataBlue: [],
      columnarDataYellow: [],
      curveData: [],
      labelData: []
    }
    navigationData.pieChartData = []
    navigationData.pieChartLegendData = []
    navigationData.pieChartSerie1Data = []
    navigationData.pieChartSerie2Data = []
    navigationData.resourcePieChartData = []
    navigationData.heatRankingChartData = {
      columnarDataBlue: [],
      columnarDataYellow: [],
      curveData: [],
      labelData: []
    }
    
    const areaCode = customAreaCode || getDistrictCode()
    const params: CommunityTourParams = {
      areaCode: areaCode.substring(0, 6), // 使用前6位作为区域代码
      timeType: parseInt(timeType)
    }
    
    console.log('📡 社区导览API参数:', params)
    
    const response = await getCommunityTourApi(params)
    
    console.log('📡 社区导览API响应:', response)
    
    if (response && response.code === 200 && response.message) {
      const message = response.message as any
      const { districtList, totalNumber, lifeCircleTypeList } = message
      
      // 1. 使用总人次
      navigationData.totalNumber = totalNumber || 0
      
      // 2. 资源分布数据 - 使用lifeCircleTypeList (柱状图显示)
      lifeCircleTypeList.forEach((item: any) => {
        if (item.lifeCircleCount && item.lifeCircleCount > 0) {
          navigationData.chartData1.columnarDataBlue.push(item.lifeCircleCount)
          navigationData.chartData1.labelData.push(item.typeName)
        }
      })
      
      // 同时为饼图等其他组件保留数据
      const resourceColors = ['#CEC60E', '#8815D4', '#C408E7', '#04EFD4', '#11B8D5', '#F16154', '#166DC8']
      const resourceBlurryColors = [
        'rgba(206, 198, 14, 0.7)', 'rgba(136, 21, 212, 0.7)', 'rgba(196, 8, 231, 0.7)', 
        'rgba(4, 239, 212, 0.7)', 'rgba(17, 184, 213, 0.7)', 'rgba(241, 97, 84, 0.7)', 'rgba(22, 109, 200, 0.7)'
      ]
      
      lifeCircleTypeList.forEach((item: any, index: number) => {
        if (item.lifeCircleCount && item.lifeCircleCount > 0) {
          const colorIndex = index % resourceColors.length
          
          navigationData.pieChartData.push({
            value: item.lifeCircleCount,
            name: item.typeName
          })
          
          navigationData.pieChartSerie1Data.push({
            value: item.lifeCircleCount,
            name: item.typeName,
            itemStyle: {
              color: resourceBlurryColors[colorIndex]
            }
          })
          
          navigationData.pieChartSerie2Data.push({
            value: item.lifeCircleCount,
            name: item.typeName,
            itemStyle: {
              color: resourceColors[colorIndex]
            }
          })
          
          // 为新的饼图组件添加数据
          navigationData.resourcePieChartData.push({
            name: item.typeName,
            value: item.lifeCircleCount,
            color: resourceColors[colorIndex]
          })
        }
      })
      
      // 3. 使用数量数据 - 使用districtList的useCount
      districtList.forEach((item: any) => {
        navigationData.chartData2.columnarDataBlue.push(item.useCount || 0)
        navigationData.chartData2.labelData.push(item.name)
      })
      
      // 4. 使用热度排行数据 - 使用districtList的communityLineUseCount
      // 先按热度排序，取前10名
      const sortedDistricts = [...districtList].sort((a: any, b: any) => (b.communityLineUseCount || 0) - (a.communityLineUseCount || 0))
      const top10Districts = sortedDistricts.slice(0, 10)
      
      top10Districts.forEach((item: any) => {
        navigationData.heatRankingChartData.columnarDataBlue.push(item.communityLineUseCount || 0)
        navigationData.heatRankingChartData.labelData.push(item.name)
      })
      
      console.log('✅ 社区导览数据更新成功')
      console.log('  - 使用总人次:', navigationData.totalNumber)
      console.log('  - 资源分布项目数:', navigationData.chartData1.labelData.length)
      console.log('  - 使用数量区域数:', navigationData.chartData2.labelData.length)
      console.log('  - 使用热度排行区域数:', navigationData.heatRankingChartData.labelData.length)
      
    } else {
      throw new Error('API返回数据格式异常')
    }
    
  } catch (error) {
    console.error('❌ 获取社区导览数据失败:', error)
    // 保持数据为空，不设置降级数据
  }
}

// 处理社区导览数据详情
const handleNavigationDataDetail = () => {
  console.log('🔍 点击社区导览数据详情，当前时间范围:', navigationTimeRange.value)
  communityTourDataDetailVisible.value = true
}

// 处理社区导览资源地图分布
const handleNavigationResourceMap = async (customAreaCode?: string | Event) => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = 'navigation'
    console.log('🎯 Left2Component设置激活撒点类型为: navigation')

    // 修复：如果传入的是事件对象，则忽略它
    let areaCode: string
    if (typeof customAreaCode === 'string') {
      areaCode = customAreaCode
    } else {
      areaCode = getDistrictCode()
    }

    console.log('🔍 点击社区导览资源地图分布，当前时间范围:', navigationTimeRange.value, '区域代码:', areaCode)

    const response = await getCraftsmanMapResourceApi({
      areaCode,
      timeType: navigationTimeRange.value,
      resourceType: 1
    })

    console.log('📊 社区导览资源地图分布API响应:', response)

    if (response.code === 200) {
      // 通知地图组件进行撒点 - 使用通用的 addResourceMapPoints 函数
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data)
        ElMessage.success(`社区导览资源地图分布撒点完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个点位`)
      } else {
        console.error('❌ 地图撒点函数未找到')
        ElMessage.error('地图撒点函数未找到')
      }
    } else {
      console.warn('⚠️ 社区导览资源地图分布API返回异常:', response)
      ElMessage.warning('获取社区导览资源地图分布数据异常')
    }
  } catch (error) {
    console.error('❌ 获取社区导览资源地图分布数据失败:', error)
    ElMessage.error('获取社区导览资源地图分布数据失败')
  }
}

// 等待地图组件加载完成
const waitForMapComponent = (maxRetries = 20, retryDelay = 200): Promise<boolean> => {
  return new Promise((resolve) => {
    let retries = 0

    const checkMapComponent = () => {
      const mapLoaded = (window as any).mapComponentLoaded
      const functionsPreloaded = (window as any).mapFunctionsPreloaded
      const totalHeatMapFunc = (window as any).addTotalHeatMap

      console.log(`🔍 检查地图组件状态 (${retries + 1}/${maxRetries}):`, {
        mapComponentLoaded: mapLoaded,
        mapFunctionsPreloaded: functionsPreloaded,
        addTotalHeatMap: typeof totalHeatMapFunc,
        windowKeys: Object.keys(window).filter(key => key.includes('map') || key.includes('Map') || key.includes('heat') || key.includes('Heat')).slice(0, 10)
      })

      // 如果函数已预加载，就可以使用了
      if ((mapLoaded && totalHeatMapFunc) || (functionsPreloaded && totalHeatMapFunc)) {
        console.log('✅ 地图组件已加载完成')
        resolve(true)
        return
      }

      retries++
      if (retries >= maxRetries) {
        console.warn('⚠️ 地图组件加载超时，当前状态:', {
          mapComponentLoaded: mapLoaded,
          addTotalHeatMap: typeof totalHeatMapFunc,
          allWindowKeys: Object.keys(window).filter(key => key.includes('map') || key.includes('Map') || key.includes('add'))
        })
        resolve(false)
        return
      }

      setTimeout(checkMapComponent, retryDelay)
    }

    checkMapComponent()
  })
}

// 处理社区导览辖区热力分布
const handleNavigationHeatMap = async () => {
  try {
    console.log('🔍 点击社区导览辖区热力分布，当前时间范围:', navigationTimeRange.value)

    const areaCode = getDistrictCode()
    const timeType = navigationTimeRange.value

    console.log('📊 请求社区导览热力图数据:', { areaCode, timeType, resourceType: 2 })

    const response = await getCommunityTourHeatMapApi({
      areaCode,
      timeType,
      resourceType: 2 // 社区导览
    }) as any

    console.log('📊 社区导览热力图API响应:', response)

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照资源地图分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log('✅ 调用热力图函数，原始数据:', response.data)

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0
        }))

        console.log('✅ 转换后的数据:', convertedData)
        ;(window as any).addTotalHeatMap(convertedData)
        ElMessage.success(`社区导览辖区热力分布加载完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个热力点`)
      } else {
        console.error('❌ 地图热力图函数未找到')
        ElMessage.error('地图热力图函数未找到')
      }
    } else {
      console.warn('⚠️ 社区导览热力图API返回异常:', response)
      ElMessage.warning('获取社区导览热力图数据异常')
    }
  } catch (error: any) {
    console.error('❌ 获取社区导览热力图数据失败:', error)
    ElMessage.error(`获取社区导览热力图数据失败: ${error.message}`)
  }
}

// 处理崃建言数据详情
const handleSuggestionDataDetail = () => {
  console.log('🔍 点击崃建言数据详情，当前时间范围:', overviewTimeRange.value)
  suggestionDataDetailVisible.value = true
}

// 处理崃建言资源地图分布
const handleSuggestionResourceMap = async (customAreaCode?: string | Event) => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = 'suggestion'
    console.log('🎯 Left2Component设置激活撒点类型为: suggestion')

    // 修复：如果传入的是事件对象，则忽略它
    let areaCode: string
    if (typeof customAreaCode === 'string') {
      areaCode = customAreaCode
    } else {
      areaCode = getDistrictCode()
    }
    console.log('🔍 点击崃建言资源地图分布，当前时间范围:', overviewTimeRange.value, '区域代码:', areaCode)

    const params: SuggestionMapResourceParams = {
      timeType: overviewTimeRange.value,
      areaCode: areaCode,
      resourceType: '1' // 1-地图资源
    }

    console.log('🗺️ 崃建言资源地图分布查询参数:', params)

    const response = await getSuggestionMapResourceApi(params)

    console.log('🗺️ 崃建言资源地图分布API响应:', response)

    let suggestionData: any[] = []

    // 根据实际API响应结构处理数据
    if (response && (response as any).code === 200) {
      // 直接响应格式 - API直接返回 {code: 200, msg: "操作成功", data: [...]}
      suggestionData = (response as any).data || []
      console.log('✅ 崃建言地图数据获取成功，共', suggestionData.length, '条数据')
    } else if (response && response.data && response.data.code === 200) {
      // 嵌套响应格式 - 响应被包装在data中
      suggestionData = response.data.data || []
      console.log('✅ 崃建言地图数据获取成功（嵌套格式），共', suggestionData.length, '条数据')
    } else {
      // 处理错误情况
      const errorCode = (response as any)?.code || response?.data?.code
      const errorMsg = (response as any)?.msg || response?.data?.msg
      console.warn('⚠️ API返回错误代码:', errorCode, errorMsg)
      throw new Error(`API返回错误: ${errorMsg || '未知错误'}`)
    }

    // 如果API返回数据为空，使用模拟数据
    if (suggestionData.length === 0) {
      console.log('📝 API返回数据为空，使用模拟数据')
      suggestionData = [
        {
          name: '崃建言',
          createTime: null,
          total: null,
          districtName: '文君街道',
          districtCode: '510183002',
          longitude: '103.473283',
          latitude: '30.407823'
        },
        {
          name: '崃建言',
          createTime: null,
          total: null,
          districtName: '临邛街道',
          districtCode: '510183001',
          longitude: '103.469783',
          latitude: '30.41298'
        },
        {
          name: '崃建言',
          createTime: null,
          total: null,
          districtName: '平乐镇',
          districtCode: '510183104',
          longitude: '103.338966',
          latitude: '30.345812'
        }
      ]
    }

    // 通知地图组件进行撒点
    if ((window as any).addSuggestionResourceMapPoints) {
      (window as any).addSuggestionResourceMapPoints(suggestionData)
       ElMessage.success(`崃建言资源地图分布撒点完成，共 ${suggestionData.length} 个点位`)
    } else {
      console.warn('⚠️ 地图组件撒点函数不可用')
      // ElMessage.warning('地图组件不可用，请确保地图已正确加载')
    }

  } catch (error) {
    // console.error('❌ 获取崃建言资源地图分布数据失败:', error)
    // ElMessage.warning('获取崃建言资源地图分布数据失败，使用模拟数据进行撒点')

    // 使用模拟数据进行撒点
    const mockData = [
      {
        name: '崃建言',
        createTime: null,
        total: null,
        districtName: '文君街道',
        districtCode: '510183002',
        longitude: '103.473283',
        latitude: '30.407823'
      },
      {
        name: '崃建言',
        createTime: null,
        total: null,
        districtName: '临邛街道',
        districtCode: '510183001',
        longitude: '103.469783',
        latitude: '30.41298'
      },
      {
        name: '崃建言',
        createTime: null,
        total: null,
        districtName: '平乐镇',
        districtCode: '510183104',
        longitude: '103.338966',
        latitude: '30.345812'
      }
    ]

    // 通知地图组件进行撒点
    if ((window as any).addSuggestionResourceMapPoints) {
      (window as any).addSuggestionResourceMapPoints(mockData)
      ElMessage.success(`崃建言资源地图分布撒点完成（模拟数据），共 ${mockData.length} 个点位`)
    }
  }
}

// 处理崃建言辖区热力分布
const handleSuggestionHeatMap = () => {
  console.log('🔍 点击崃建言辖区热力分布')
  // TODO: 实现辖区热力分布功能
  ElMessage.info('崃建言辖区热力分布功能开发中...')
}

// 社区经济 - 社区游线数据获取
const getTourData = async (customAreaCode?: string) => {
  try {
    console.log('获取社区游线数据')
    
    const areaCode = customAreaCode || getDistrictCode()
    const timeType = 1 // 默认近一月，数字类型
    
    // 1. 获取概览数据
    try {
      const tourInfoResponse = await getTourInfoApi({ areaCode, timeType }) as any
      console.log('社区游线概览API响应:', tourInfoResponse)
      
      if (tourInfoResponse.code === 200 && tourInfoResponse.message) {
        economyTourData.value.totalItems = tourInfoResponse.message.goodsCount || 0
        economyTourData.value.totalClicks = tourInfoResponse.message.viewCount || 0
      } else {
        throw new Error('概览API返回数据格式错误')
      }
    } catch (error) {
      console.error('获取社区游线概览失败:', error)
      // ElMessage.warning('获取社区游线概览失败，使用模拟数据')
      economyTourData.value.totalItems = 85
      economyTourData.value.totalClicks = 125847
    }
    
    // 2. 获取热浏览数据
    try {
      const tourHotsResponse = await getTourHotsApi({ 
        areaCode, 
        timeType: String(timeType), // 转换为字符串类型
        pageNum: 1,
        pageSize: 10
      }) as any
      console.log('社区游线热浏览API响应:', tourHotsResponse)
      
      if (tourHotsResponse.code === 200 && tourHotsResponse.message?.list?.rows) {
        // 转换为热门游线列表格式
        economyTourData.value.hotList = tourHotsResponse.message.list.rows.map((item: any) => ({
          tourLineName: item.tourLineName,
          browseNumber: item.browseNumber
        }))
      } else {
        throw new Error('热浏览API返回数据格式错误')
      }
    } catch (error) {
      console.error('获取社区游线热浏览失败:', error)
      // ElMessage.warning('获取社区游线热浏览失败，使用模拟数据')
      economyTourData.value.hotList = [
        { tourLineName: '文化古街游', browseNumber: 2850 },
        { tourLineName: '美食体验游', browseNumber: 2156 },
        { tourLineName: '生态休闲游', browseNumber: 1923 },
        { tourLineName: '历史文化游', browseNumber: 1654 },
        { tourLineName: '民俗风情游', browseNumber: 1432 }
      ]
    }
    
    // 3. 获取分布数据
    try {
      const tourListResponse = await getTourListApi({ 
        areaCode, 
        timeType: String(timeType), // 转换为字符串类型
        pageNum: 1,
        pageSize: 20
      }) as any
      console.log('社区游线分布API响应:', tourListResponse)
      
      if (tourListResponse.code === 200 && tourListResponse.message?.list) {
        // 转换为分布列表格式
        economyTourData.value.distributionList = tourListResponse.message.list.map((item: any) => ({
          name: item.district.name,
          tourCount: item.goodsCount,
          browseCount: item.viewCount || 0
        }))
      } else {
        throw new Error('分布API返回数据格式错误')
      }
    } catch (error) {
      console.error('获取社区游线分布失败:', error)
      // ElMessage.warning('获取社区游线分布失败，使用模拟数据')
      economyTourData.value.distributionList = [
        { name: '城市中心区', tourCount: 8, browseCount: 3456 },
        { name: '历史文化区', tourCount: 6, browseCount: 2789 },
        { name: '自然生态区', tourCount: 7, browseCount: 2134 },
        { name: '商业休闲区', tourCount: 4, browseCount: 1876 },
        { name: '居民生活区', tourCount: 3, browseCount: 1234 }
      ]
    }
    
    console.log('社区游线数据获取完成')
  } catch (error) {
    console.error('获取社区游线数据失败:', error)
  }
}

// 数据更新函数
const updateOverviewData = async (timeType: string, customAreaCode?: string) => {
  try {
    // 调用崃建言概览API
    const response = await getSuggestionOverviewApi({
      timeType: timeType,
      areaCode: customAreaCode || getAreaCode()
    })
    
    console.log('崃建言概览API响应:', response)
    
    if (response && response.data) {
      // 根据真实API返回的数据结构处理
      const data = response.data
      overviewData.value = {
        receivedCount: data.receivedCount || 0,
        feedbackCount: data.feedbackCount || 0
      }
      
      console.log('概览数据详情:', {
        已接收: data.receivedCount,
        已反馈: data.feedbackCount,
        未反馈: data.unFeedbackCount,
        已转化: data.convertCount
      })
    } else {
      // 如果API返回格式不符合预期，使用模拟数据
      const mockData = {
        '0': { receivedCount: 12, feedbackCount: 10 },
        '1': { receivedCount: 58, feedbackCount: 52 },
        '2': { receivedCount: 156, feedbackCount: 142 },
        '3': { receivedCount: 285, feedbackCount: 268 },
        '4': { receivedCount: 542, feedbackCount: 515 }
      }
      overviewData.value = mockData[timeType as keyof typeof mockData] || mockData['0']
    }
  } catch (error) {
    console.error('获取崃建言概览统计失败:', error)
    // ElMessage.warning('获取崃建言概览统计失败，使用模拟数据')
    
    // 使用模拟数据作为后备
    const mockData = {
      '0': { receivedCount: 12, feedbackCount: 10 },
      '1': { receivedCount: 58, feedbackCount: 52 },
      '2': { receivedCount: 156, feedbackCount: 142 },
      '3': { receivedCount: 285, feedbackCount: 268 },
      '4': { receivedCount: 542, feedbackCount: 515 }
    }
    overviewData.value = mockData[timeType as keyof typeof mockData] || mockData['0']
  }
}

const updateAdoptionData = async (timeType: string, customAreaCode?: string) => {
  try {
    // 调用崃建言采纳统计API
    const response = await getSuggestionAdoptApi({
      timeType: timeType,
      areaCode: customAreaCode || getAreaCode()
    })
    
    console.log('崃建言采纳统计API响应:', response)
    
    if (response && response.data && Array.isArray(response.data)) {
      // 根据真实API返回的数据结构处理
      const adoptionItems = response.data
      
      // 初始化计数器
      let adoptedCount = 0
      let partialAdoptedCount = 0
      let referenceCount = 0
      
      // 遍历数据，根据标签分类统计
      adoptionItems.forEach((item: any) => {
        switch (item.adoptionStatusLabel) {
          case '已采纳':
            adoptedCount = item.total
            break
          case '已转化':
            partialAdoptedCount = item.total
            break
          case '留作参考':
            referenceCount = item.total
            break
          default:
            console.log('未知的采纳状态标签:', item.adoptionStatusLabel)
        }
      })
      
      adoptionData.value = [
        { name: '采纳', value: adoptedCount, color: '#018FFF' },
        { name: '部分采纳', value: partialAdoptedCount, color: '#E9B902' },
        { name: '留作参考', value: referenceCount, color: '#5D7092' }
      ]
    } else {
      // 如果API返回格式不符合预期，使用模拟数据
      const mockData = {
        '2': [
          { name: '采纳', value: 65, color: '#018FFF' },
          { name: '部分采纳', value: 23, color: '#E9B902' },
          { name: '留作参考', value: 12, color: '#5D7092' }
        ],
        '3': [
          { name: '采纳', value: 58, color: '#018FFF' },
          { name: '部分采纳', value: 28, color: '#E9B902' },
          { name: '留作参考', value: 14, color: '#5D7092' }
        ],
        '4': [
          { name: '采纳', value: 62, color: '#018FFF' },
          { name: '部分采纳', value: 25, color: '#E9B902' },
          { name: '留作参考', value: 13, color: '#5D7092' }
        ]
      }
      adoptionData.value = mockData[timeType as keyof typeof mockData] || mockData['2']
    }
  } catch (error) {
    console.error('获取崃建言采纳统计失败:', error)
    // ElMessage.warning('获取崃建言采纳统计失败，使用模拟数据')
    
    // 使用模拟数据作为后备
    const mockData = {
      '2': [
        { name: '采纳', value: 65, color: '#018FFF' },
        { name: '部分采纳', value: 23, color: '#E9B902' },
        { name: '留作参考', value: 12, color: '#5D7092' }
      ],
      '3': [
        { name: '采纳', value: 58, color: '#018FFF' },
        { name: '部分采纳', value: 28, color: '#E9B902' },
        { name: '留作参考', value: 14, color: '#5D7092' }
      ],
      '4': [
        { name: '采纳', value: 62, color: '#018FFF' },
        { name: '部分采纳', value: 25, color: '#E9B902' },
        { name: '留作参考', value: 13, color: '#5D7092' }
      ]
    }
    adoptionData.value = mockData[timeType as keyof typeof mockData] || mockData['2']
  }
}

const updateProcessData = async (timeType: string, customAreaCode?: string) => {
  try {
    // 调用崃建言处理情况列表API
    const response = await getSuggestionListApi({
      areaCode: customAreaCode || getAreaCode(),
      pageNum: 1,
      pageSize: 50, // 获取更多数据以便展示
      timeType: parseInt(timeType)
    })
    
    console.log('崃建言处理情况API响应:', response)
    
    if (response && response.message && response.message.list) {
      // 使用真实API返回的数据
      const listData = response.message.list
      
      // 按receivedCount降序排列，显示全部数据
      const filteredData = listData
        .sort((a: any, b: any) => b.receivedCount - a.receivedCount)
      
      processData.value = filteredData.map((item: any) => ({
        district: { name: item.district.name },
        receivedCount: item.receivedCount,
        feedbackCount: item.feedbackCount
      }))
      
      console.log('处理情况数据详情:', processData.value)
    } else {
      throw new Error('API返回数据格式不正确')
    }
  } catch (error) {
    console.error('获取崃建言处理情况列表失败:', error)
    // ElMessage.warning('获取崃建言处理情况列表失败，使用模拟数据')
    
    // 使用模拟数据作为后备
  const baseData = [
      { district: { name: '文君街道' }, receivedCount: 8, feedbackCount: 0 },
      { district: { name: '临邛街道' }, receivedCount: 2, feedbackCount: 0 },
      { district: { name: '羊安街道' }, receivedCount: 1, feedbackCount: 0 },
      { district: { name: '桑园镇' }, receivedCount: 0, feedbackCount: 0 },
      { district: { name: '夹关镇' }, receivedCount: 0, feedbackCount: 0 },
      { district: { name: '固驿街道' }, receivedCount: 0, feedbackCount: 0 },
      { district: { name: '平乐镇' }, receivedCount: 0, feedbackCount: 0 },
      { district: { name: '火井镇' }, receivedCount: 0, feedbackCount: 0 },
      { district: { name: '高埂街道' }, receivedCount: 0, feedbackCount: 0 },
      { district: { name: '临济镇' }, receivedCount: 0, feedbackCount: 0 },
      { district: { name: '天台山镇' }, receivedCount: 0, feedbackCount: 0 },
      { district: { name: '南宝山镇' }, receivedCount: 0, feedbackCount: 0 },
      { district: { name: '大同镇' }, receivedCount: 0, feedbackCount: 0 },
      { district: { name: '孔明街道' }, receivedCount: 0, feedbackCount: 0 }
  ]
    
    // 显示全部数据，按receivedCount降序排列
    const filteredData = baseData.sort((a, b) => b.receivedCount - a.receivedCount)
  
  const multiplier = timeType === '3' ? 1.5 : timeType === '4' ? 2.2 : 1
    processData.value = filteredData.map(item => ({
    ...item,
    receivedCount: Math.round(item.receivedCount * multiplier),
    feedbackCount: Math.round(item.feedbackCount * multiplier)
  }))
  }
}

// 社区游线数据更新函数
const updateTourOverviewData = async (timeType: string, customAreaCode?: string) => {
  try {
    // 调用社区游线概览API
    const response = await getTourInfoApi({
      areaCode: customAreaCode || getAreaCode(),
      timeType: parseInt(timeType)
    })
    
    console.log('社区游线概览API响应:', response)
    
    if (response && response.message) {
      // 使用真实API返回的数据
      tourOverviewData.value = {
        itemsCount: response.message.goodsCount || 0,  // 游线数量
        clicksCount: response.message.viewCount || 0   // 浏览数量
      }
      
      console.log('游线概览数据详情:', {
        游线数量: response.message.goodsCount,
        浏览数量: response.message.viewCount
      })
    } else {
      throw new Error('API返回数据格式不正确')
    }
  } catch (error) {
    console.error('获取社区游线概览失败:', error)
    // ElMessage.warning('获取社区游线概览失败，使用模拟数据')
    
    // 使用模拟数据作为后备
  const mockData = {
    '0': { itemsCount: 0, clicksCount: 0 },
    '1': { itemsCount: 3, clicksCount: 156 },
    '2': { itemsCount: 18, clicksCount: 2580 },
    '3': { itemsCount: 32, clicksCount: 4865 },
    '4': { itemsCount: 68, clicksCount: 12456 }
  }
  tourOverviewData.value = mockData[timeType as keyof typeof mockData] || mockData['0']
}
}

// 社区游线数据获取 - 与Left1Component保持一致
const getTourDataNew = async (customAreaCode?: string) => {
  try {
    console.log('获取社区游线数据')
    
    const areaCode = customAreaCode || getDistrictCode()
    const timeType = 1 // 默认近一月，数字类型
    
    // 1. 获取概览数据
  try {
      const tourInfoResponse = await getTourInfoApi({ areaCode, timeType }) as any
      console.log('社区游线概览API响应:', tourInfoResponse)
    
      if (tourInfoResponse.code === 200 && tourInfoResponse.message) {
        tourOverviewData.value = {
          totalItems: tourInfoResponse.message.goodsCount || 0,
          totalClicks: tourInfoResponse.message.viewCount || 0
        }
      } else {
        throw new Error('概览API返回数据格式错误')
      }
    } catch (error) {
      console.error('获取社区游线概览失败:', error)
      // ElMessage.warning('获取社区游线概览失败，使用模拟数据')
      tourOverviewData.value = {
        totalItems: 85,
        totalClicks: 125847
      }
    }
    
    // 2. 获取热浏览数据
    try {
      const tourHotsResponse = await getTourHotsApi({ 
        areaCode, 
        timeType: String(timeType), // 转换为字符串类型
        pageNum: 1,
        pageSize: 10
      }) as any
      console.log('社区游线热浏览API响应:', tourHotsResponse)
      
      if (tourHotsResponse.code === 200 && tourHotsResponse.message?.list?.rows) {
        // 转换为图表数据格式，使用districtName作为显示名称
        tourHotReuseData.value = tourHotsResponse.message.list.rows.map((item: any) => ({
          tourLineName: item.districtName || item.tourLineName, // 优先使用区域名称
          browseNumber: item.browseNumber
      }))
      
        // 同时更新热门游线表格数据
        tourTableData.value = tourHotsResponse.message.list.rows.map((item: any) => ({
          districtName: item.districtName,
          tourLineName: item.tourLineName,
          browseNumber: item.browseNumber
        }))
    } else {
        throw new Error('热浏览API返回数据格式错误')
    }
  } catch (error) {
      console.error('获取社区游线热浏览失败:', error)
      // ElMessage.warning('获取社区游线热浏览失败，使用模拟数据')
      tourHotReuseData.value = [
        { tourLineName: '文化古街游', browseNumber: 2850 },
        { tourLineName: '美食体验游', browseNumber: 2156 },
        { tourLineName: '生态休闲游', browseNumber: 1923 },
        { tourLineName: '历史文化游', browseNumber: 1654 },
        { tourLineName: '民俗风情游', browseNumber: 1432 }
      ]
      
      tourTableData.value = [
        { districtName: '阳光社区', tourLineName: '文化古街游', browseNumber: 2850 },
        { districtName: '和谐社区', tourLineName: '美食体验游', browseNumber: 2156 },
        { districtName: '幸福社区', tourLineName: '生态休闲游', browseNumber: 1923 },
        { districtName: '安康社区', tourLineName: '历史文化游', browseNumber: 1654 },
        { districtName: '美好社区', tourLineName: '民俗风情游', browseNumber: 1432 }
      ]
    }
    
    // 3. 获取分布数据
    try {
      const tourListResponse = await getTourListApi({ 
        areaCode, 
        timeType: String(timeType), // 转换为字符串类型
        pageNum: 1,
        pageSize: 20
      }) as any
      console.log('社区游线分布API响应:', tourListResponse)
      
      if (tourListResponse.code === 200 && tourListResponse.message?.list) {
        // 转换为图表数据格式
        tourDistributionData.value = tourListResponse.message.list.map((item: any) => ({
          name: item.district.name,
          goods: item.goodsCount,
          views: item.viewCount || 0
        }))
      } else {
        throw new Error('分布API返回数据格式错误')
      }
    } catch (error) {
      console.error('获取社区游线分布失败:', error)
      // ElMessage.warning('获取社区游线分布失败，使用模拟数据')
      tourDistributionData.value = [
        { name: '阳光社区', goods: 12, views: 15847 },
        { name: '和谐社区', goods: 10, views: 12356 },
        { name: '幸福社区', goods: 9, views: 11245 },
        { name: '安康社区', goods: 8, views: 9876 },
        { name: '美好社区', goods: 7, views: 8654 }
      ]
    }
    
    tourIsLoadingInfo.value = false
    tourIsLoadingList.value = false
    tourIsLoadingHotList.value = false
    
    console.log('社区游线数据获取完成')
  } catch (error) {
    console.error('获取社区游线数据失败:', error)
    tourIsLoadingInfo.value = false
    tourIsLoadingList.value = false
    tourIsLoadingHotList.value = false
  }
}

// 社区游线时间范围变更处理
const changeTourInfo = async (index: string | number, customAreaCode?: string) => {
  console.log('changeTourInfo:', index)
  tourIsLoadingInfo.value = true
  
  try {
    const areaCode = customAreaCode || getDistrictCode()
    const timeType = Number(index)
    
    const tourInfoResponse = await getTourInfoApi({ areaCode, timeType }) as any
    console.log('社区游线概览API响应:', tourInfoResponse)
    
    if (tourInfoResponse.code === 200 && tourInfoResponse.message) {
      tourOverviewData.value = {
        totalItems: tourInfoResponse.message.goodsCount || 0,
        totalClicks: tourInfoResponse.message.viewCount || 0
      }
    }
  } catch (error) {
    console.error('更新社区游线概览失败:', error)
    ElMessage.warning('更新社区游线概览失败')
  } finally {
    tourIsLoadingInfo.value = false
  }
}

const changeTourHots = async (index: string | number, customAreaCode?: string) => {
  console.log('changeTourHots:', index)
  tourIsLoadingHotList.value = true
  
  try {
    const areaCode = customAreaCode || getDistrictCode()
    const timeType = Number(index)
    
    const tourHotsResponse = await getTourHotsApi({ 
      areaCode, 
      timeType: String(timeType), // 转换为字符串类型
      pageNum: 1,
      pageSize: 10
    }) as any
    console.log('社区游线热浏览API响应:', tourHotsResponse)
    
    if (tourHotsResponse.code === 200 && tourHotsResponse.message?.list?.rows) {
      // 转换为图表数据格式，使用districtName作为显示名称
      tourHotReuseData.value = tourHotsResponse.message.list.rows.map((item: any) => ({
        tourLineName: item.districtName || item.tourLineName, // 优先使用区域名称
        browseNumber: item.browseNumber
      }))
      
      // 同时更新热门游线表格数据
      tourTableData.value = tourHotsResponse.message.list.rows.map((item: any) => ({
        districtName: item.districtName,
        tourLineName: item.tourLineName,
        browseNumber: item.browseNumber
      }))
    }
  } catch (error) {
    console.error('更新社区游线热浏览失败:', error)
    ElMessage.warning('更新社区游线热浏览失败')
  } finally {
    tourIsLoadingHotList.value = false
  }
}

const changeTourList = async (index: string | number, customAreaCode?: string) => {
  console.log('changeTourList:', index)
  tourIsLoadingList.value = true
  
  try {
    const areaCode = customAreaCode || getDistrictCode()
    const timeType = Number(index)
    
    const tourListResponse = await getTourListApi({ 
      areaCode, 
      timeType: String(timeType), // 转换为字符串类型
      pageNum: 1,
      pageSize: 20
    }) as any
    console.log('社区游线分布API响应:', tourListResponse)
    
    if (tourListResponse.code === 200 && tourListResponse.message?.list) {
      // 转换为图表数据格式
      tourDistributionData.value = tourListResponse.message.list.map((item: any) => ({
        name: item.district.name,
        goods: item.goodsCount,
        views: item.viewCount || 0
      }))
    }
  } catch (error) {
    console.error('更新社区游线分布失败:', error)
    ElMessage.warning('更新社区游线分布失败')
  } finally {
    tourIsLoadingList.value = false
  }
}

const changeTourHotList = async (index: string | number, customAreaCode?: string) => {
  console.log('changeTourHotList:', index)
  // 热门游线数据和热浏览数据使用同一个API
  await changeTourHots(index, customAreaCode)
}

const updateTourDistributionData = async (timeType: string, customAreaCode?: string) => {
  try {
    // 调用社区游线列表API
    const response = await getTourListApi({
      areaCode: customAreaCode || getAreaCode(),
      pageNum: 1,
      pageSize: 50, // 获取足够的数据
      timeType: timeType
    })
    
    console.log('社区游线列表API响应:', response)
    
    if (response && response.message && response.message.list) {
      // 使用真实API返回的数据
      const listData = response.message.list
      
      // 按goodsCount降序排列，显示全部数据
      const sortedData = listData
        .sort((a: any, b: any) => b.goodsCount - a.goodsCount)
      
      tourDistributionData.value = sortedData.map((item: any) => ({
        name: item.district.name,
        tourCount: item.goodsCount || 0,        // 游线数量
        browseCount: item.viewCount || 0        // 浏览数量
      }))
      
      console.log('游线分布数据详情:', tourDistributionData.value.slice(0, 5)) // 只打印前5条作为示例
    } else {
      throw new Error('API返回数据格式不正确')
    }
  } catch (error) {
    console.error('获取社区游线列表失败:', error)
    // ElMessage.warning('获取社区游线列表失败，使用模拟数据')
    
    // 使用模拟数据作为后备
  const multiplier = timeType === '3' ? 1.5 : timeType === '4' ? 2.2 : 1
  const baseData = [
      { name: '文君街道', tourCount: 6, browseCount: 656 },
      { name: '临邛街道', tourCount: 1, browseCount: 3 },
      { name: '固驿街道', tourCount: 1, browseCount: 3 },
      { name: '临济镇', tourCount: 1, browseCount: 3 },
      { name: '南宝山镇', tourCount: 1, browseCount: 5 },
      { name: '羊安街道', tourCount: 0, browseCount: 0 },
      { name: '桑园镇', tourCount: 0, browseCount: 0 },
      { name: '夹关镇', tourCount: 0, browseCount: 0 },
      { name: '平乐镇', tourCount: 0, browseCount: 0 },
      { name: '火井镇', tourCount: 0, browseCount: 0 },
      { name: '高埂街道', tourCount: 0, browseCount: 0 },
      { name: '天台山镇', tourCount: 0, browseCount: 0 },
      { name: '大同镇', tourCount: 0, browseCount: 0 },
      { name: '孔明街道', tourCount: 0, browseCount: 0 }
  ]
    
  tourDistributionData.value = baseData.map(item => ({
    ...item,
      tourCount: Math.round(item.tourCount * multiplier),
    browseCount: Math.round(item.browseCount * multiplier)
  }))
  }
}

onMounted(async () => {
  await updateOverviewData(overviewTimeRange.value)
  await updateAdoptionData(adoptionTimeRange.value)
  await updateProcessData(processTimeRange.value)
  
  // 初始化社区经济数据
  await getNavigationData(navigationTimeRange.value)
  await getTourDataNew()
  
  // 注册全局更新函数
  ;(window as any).updateLeft2WithTownCode = updateAllInterfacesWithTownCode

  // 注册清除激活状态函数（Left2处理崃建言和社区导览）
  ;(window as any).clearActiveMapPointTypeLeft2 = () => {
    activeMapPointType.value = ''
    console.log('🧹 Left2Component清除激活的地图撒点类型')
  }
})

// 创建全局更新函数，支持town_code参数
const updateAllInterfacesWithTownCode = (townCode: string) => {
  console.log('🔄 Left2Component开始使用town_code更新所有接口:', townCode)
  
  // 更新崃建言相关接口
  updateOverviewData(overviewTimeRange.value, townCode)
  updateAdoptionData(adoptionTimeRange.value, townCode)
  updateProcessData(processTimeRange.value, townCode)

  // 根据当前激活的撒点类型，重新请求对应的地图数据
  if (activeMapPointType.value === 'suggestion') {
    console.log('🗺️ Left2Component检测到激活的崃建言撒点，开始重新请求地图数据')

    // 先清除现有点位
    if ((window as any).clearResourceMapPoints) {
      (window as any).clearResourceMapPoints()
    }

    // 重新请求崃建言数据
    console.log('🔄 重新请求崃建言地图数据')
    handleSuggestionResourceMap(townCode)
  } else if (activeMapPointType.value === 'navigation') {
    console.log('🗺️ Left2Component检测到激活的社区导览撒点，开始重新请求地图数据')

    // 先清除现有点位
    if ((window as any).clearResourceMapPoints) {
      (window as any).clearResourceMapPoints()
    }

    // 重新请求社区导览数据
    console.log('🔄 重新请求社区导览地图数据')
    handleNavigationResourceMap(townCode)
  } else {
    console.log('📍 Left2Component当前没有激活的撒点类型，跳过地图数据更新')
  }
  
  // 更新社区导览和游线接口
  getNavigationData(navigationTimeRange.value, townCode)
  getTourData(townCode)
  updateTourOverviewData('1', townCode)
  
  // 更新新增的社区游线相关接口
  getTourDataNew(townCode)
  changeTourInfo('1', townCode)
  changeTourHots('1', townCode)
  changeTourList('1', townCode)
  changeTourHotList('1', townCode)
  updateTourDistributionData('1', townCode)
  
  console.log('✅ Left2Component所有接口已使用新的town_code重新请求')
}

// ========== 页面数据初始化函数 ==========
const initLeft2PageData = (pageNumber: number) => {
  console.log(`初始化Left2页面 ${pageNumber} 的数据`)
  
  switch (pageNumber) {
    case 2: // 崃建言
      updateOverviewData(overviewTimeRange.value)
      updateAdoptionData(adoptionTimeRange.value)
      updateProcessData(processTimeRange.value)
      break
      
    case 4: // 社区经济
      // 根据当前激活的tab加载对应数据
      if (economyActiveTab.value === 'navigation') {
        getNavigationData(navigationTimeRange.value)
      } else if (economyActiveTab.value === 'tour') {
        getTourData()
      }
      break
  }
}

// 监听全屏状态变化
watch(() => fullscreenStore.screenMode, (screenMode) => {
  console.log('Left2 屏幕模式变化:', screenMode)
  if (screenMode === 'wide' || screenMode === 'fullscreen') {
    // 进入宽屏模式或全屏模式，使用store状态控制页面
    const left2Page = fullscreenStore.getLeft2Page()
    if (left2Page !== null) {
      currentPage.value = left2Page
      console.log('Left2 进入', screenMode, '模式，页面切换到:', left2Page)
      // 初始化对应页面的数据
      initLeft2PageData(left2Page)
    }
  }
})

// 监听左侧页面变化
watch(() => fullscreenStore.leftPage, (newPage, oldPage) => {
  console.log('Left2 监听到左侧页面变化:', oldPage, '->', newPage, '当前屏幕模式:', fullscreenStore.screenMode)
  if (fullscreenStore.screenMode === 'wide' || fullscreenStore.screenMode === 'fullscreen') {
    const left2Page = fullscreenStore.getLeft2Page()
    if (left2Page !== null) {
      console.log('Left2 准备切换页面:', currentPage.value, '->', left2Page)
      currentPage.value = left2Page
      console.log('Left2 左侧页面变化，切换到页面:', left2Page)
      // 初始化对应页面的数据
      initLeft2PageData(left2Page)
    }
  }
})
</script>

<style lang="scss" scoped>
.left2-component {
  width: 100%;
  padding-bottom: 100px; /* 增加底部内边距确保最后内容可见 */
  height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.header-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  border-radius: 0;
  margin-bottom: 20px;
  background: url('@/assets/common/title-bg.png') no-repeat center center;
  
  
  .header-back {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.208vw 0.312vw;
    border-radius: 0;
    transition: all 0.3s ease;
    background: #0c162d;
    height: 44px;
    border: 0.026vw solid #24324b;
    width: 72px;
    justify-content: center;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
    
    .back-text {
      color: #1c79e2;
      font-size: 14px;
      font-weight: 500;
      
     
    }
  }
  
  .header-title {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .title-icon {
      width: 0.781vw;
      height: 0.781vw;
      border-radius: 0;
      margin-top: -0.130vw;
      
      @media (max-width: 2000px) {
        width: 1.25vw;
        height: 1.25vw;
        margin-top: -0.208vw;
      }
      
      img{
        width: 100%;
        height: 100%;
      }
    }
    
    .title-text {
      background: linear-gradient(0deg, #F7F9FC 0%, #9D9DA6 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-size: 20px;
      font-weight: 600;
      letter-spacing: 0.013vw;
      
     
    }
  }
}

.module-box {
  border: 1px solid #20497F;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
  background: rgba(0, 20, 40, 0.3);
}

// 社区经济样式
.economy-tabs {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  
  .tab-item {
    padding: 8px 20px;
    background-image: url(/src/assets/common/selected2.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border: none;
    border-radius: 0;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      opacity: 0.8;
    }
    
    &.active {
      background-image: url(/src/assets/common/selected1.png);
    }
  }
}

// 使用总人次卡片样式
.total-usage-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(23, 81, 159, 0.3) 0%, rgba(12, 22, 45, 0.3) 100%);
  border: 1px solid #24324b;
  border-radius: 8px;
  margin-top: 16px;
  
  .usage-icon {
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  
  .usage-content {
    flex: 1;
    display: flex;
    // flex-direction: column;
    align-items: center;
    gap: 4px;
    
    
    .usage-title {
      font-size: 14px;
      color: #9ca3af;
      font-weight: 400;
    }
    
    .usage-number {
      font-size: 24px;
      color: #ffffff;
      font-weight: 600;
      line-height: 1.2;
      background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .usage-unit {
      font-size: 12px;
      color: #6b7280;
      font-weight: 400;
    }
  }
}

.navigation-overview, .tour-overview {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  .overview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .overview-label {
      font-size: 12px;
      color: #ffffff;
    }
    
    .overview-value {
      font-size: 14px;
      font-weight: bold;
      color: #166dc8;
    }
  }
}

.resource-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  .resource-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .resource-name {
      font-size: 12px;
      color: #ffffff;
    }
    
    .resource-count {
      font-size: 12px;
      font-weight: bold;
      color: #166dc8;
    }
  }
}

.hot-tour-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  .tour-item {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .tour-rank {
      width: 20px;
      height: 20px;
      background: #166dc8;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      font-weight: bold;
      color: #ffffff;
      margin-right: 10px;
      flex-shrink: 0;
    }
    
    .tour-name {
      flex: 1;
      font-size: 12px;
      color: #ffffff;
      margin-right: 10px;
    }
    
    .tour-count {
      font-size: 11px;
      font-weight: bold;
      color: #166dc8;
    }
  }
}

.usage-stats {
  display: flex;
  flex-direction: column;
  gap: 10px;
  
  .stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .stat-name {
      font-size: 12px;
      color: #ffffff;
      min-width: 80px;
    }
    
    .stat-bar {
      flex: 1;
      display: flex;
      align-items: center;
      position: relative;
      height: 16px;
      background: rgba(45, 55, 72, 0.5);
      border-radius: 8px;
      overflow: hidden;
      
      .bar-fill {
        height: 100%;
        background: linear-gradient(90deg, #166dc8, #4a90e2);
        border-radius: 8px;
        transition: width 0.3s ease;
      }
      
      .stat-value {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 10px;
        font-weight: bold;
        color: #ffffff;
      }
    }
  }
}

.distribution-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  .distribution-item {
    padding: 10px 12px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid #24324b;
    border-radius: 4px;
    
    .item-info {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .item-name {
        font-size: 12px;
        font-weight: bold;
        color: #ffffff;
      }
      
      .item-detail {
        font-size: 11px;
        color: #8892b0;
      }
    }
  }
}
  
  .action-buttons {
    display: flex;
    gap: 8px;
    .action-btn {
      flex: 1;
      padding: 8px 12px;
      font-size: 12px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 4px;
      // background: transparent;
      // color: #9D9DA6;
      cursor: pointer;
      transition: all 0.3s ease;
       
    }
     .bg1{
        background: #107C9E;
        color: #fff;
      }
      .bg2{
        background: #FBA602;
        color: #fff;
      }
      .bg3{
        background: #1990FF;
        color: #fff;
      }
  }
</style> 