<template>
  <Teleport to="body">
    <div v-if="visible" class="notice-detail-modal" @click="handleMaskClick">
      <div class="notice-detail-dialog" @click.stop>
        <!-- 弹窗头部 -->
        <div class="dialog-header">
          <div class="dialog-title">{{ title }}</div>
          <div class="close-btn" @click="handleClose">×</div>
        </div>

        <!-- 弹窗内容 -->
        <div class="dialog-content">
          <!-- 搜索表单 -->
          <div class="search-form">
            <el-form ref="ruleFormRef" :inline="true" :model="searchForm">
              <el-form-item prop="title">
                <el-input 
                  v-model="searchForm.title" 
                  placeholder="标题关键字" 
                  clearable 
                  @clear="handleTitleClear"
                />
              </el-form-item>
        
    
              <el-form-item>
                <el-button class="submit-btn" type="primary" :icon="Search" @click="handleSearch">
                  查询
                </el-button>
                <el-button class="reset-btn" :icon="Refresh" @click="handleReset">
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 数据列表表格 -->
          <div class="table-box">
            <el-table
              :max-height="'calc(100vh - 280px)'"
              :data="dataList"
              v-loading="loading"
              element-loading-text="加载中..."
              element-loading-background="rgba(0, 0, 0, 0.6)"
              class="notice-table"
            >
              <el-table-column label="标题" prop="name" align="center" min-width="200">
                <template #default="{ row }">
                  <span class="title-text">{{ row.name || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="类型" prop="typeName" align="center" width="100">
                <template #default="{ row }">
                  <span class="type-tag" :class="getTypeClass(row.typeName)">{{ row.typeName || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="发布区域" prop="districtName" align="center" width="120">
                <template #default="{ row }">
                  {{ row.districtName || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="发布时间" prop="createTime" align="center" width="160">
                <template #default="{ row }">
                  {{ row.createTime || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="辖区代码" prop="districtCode" align="center" width="120">
                <template #default="{ row }">
                  {{ row.districtCode || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="经度" prop="longitude" align="center" width="100">
                <template #default="{ row }">
                  {{ row.longitude || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="纬度" prop="latitude" align="center" width="100">
                <template #default="{ row }">
                  {{ row.latitude || '-' }}
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="page-box">
              <el-config-provider :locale="zhCn">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 30, 50]"
                  :small="false"
                  :disabled="false"
                  background
                  layout="sizes, prev, pager, next, jumper, total"
                  :total="total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </el-config-provider>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { getNoticeMapResourcePageApi } from '@/api/bigscreen'
import type { NoticeMapResourcePageParams, NoticeMapResourcePageItem } from '@/api/bigscreen'
import zhCn from 'element-plus/es/locale/lang/zh-cn'


interface Props {
  visible: boolean
  title: string
  timeType: string
  areaCode: string
  resourceType: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const ruleFormRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  title: '',
  type: '',
  area: '',
  status: ''
})

// 数据列表
const dataList = ref<NoticeMapResourcePageItem[]>([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    getDataList()
  }
})

// 获取数据列表
const getDataList = async () => {
  try {
    loading.value = true
    
    const params: NoticeMapResourcePageParams = {
      timeType: props.timeType,
      areaCode: props.areaCode,
      resourceType: props.resourceType,
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      orderByColumn: '',
      isAsc: 'asc'
    }
    
    console.log('🔍 数据详情查询参数:', params)

    const response = await getNoticeMapResourcePageApi(params)
    
    console.log('🔍 数据详情API响应:', response)

    if (response && response.code === 200) {
      // 检查是否有嵌套的data字段
      const responseData = response.data || response
      dataList.value = responseData.rows || []
      total.value = responseData.total || 0
      console.log('✅ 数据详情更新成功，共', dataList.value.length, '条数据')
    } else {
      console.warn('⚠️ 数据详情API返回数据格式异常:', response)
      throw new Error('API返回数据格式异常')
    }
  } catch (error) {
    console.error('获取数据详情失败:', error)
    // ElMessage.warning('获取数据详情失败，使用模拟数据')
    
    // 使用模拟数据
    dataList.value = [
      {
        name: '关于开展社区环境整治活动的通知',
        typeName: '宣传',
        createTime: '2024-01-15 10:30:00',
        total: null,
        districtName: '临邛街道',
        districtCode: '510183001',
        longitude: '103.469783',
        latitude: '30.41298'
      },
      {
        name: '2024年春节期间服务安排公告',
        typeName: '党建',
        createTime: '2024-01-10 14:20:00',
        total: null,
        districtName: '文君街道',
        districtCode: '510183002',
        longitude: '103.473283',
        latitude: '30.407823'
      },
      {
        name: '居民医保政策解读',
        typeName: '政策',
        createTime: '2024-01-08 09:15:00',
        total: null,
        districtName: '大同镇',
        districtCode: '510183212',
        longitude: '103.299951',
        latitude: '30.452429'
      }
    ]
    total.value = 3
  } finally {
    loading.value = false
  }
}

// 处理标题清空
const handleTitleClear = () => {
  console.log('🔍 标题已清空，自动刷新列表')
  if (props.visible) {
    currentPage.value = 1
    getDataList()
  }
}

// 查询
const handleSearch = () => {
  console.log('🔍 执行查询操作')
  currentPage.value = 1
  getDataList()
}

// 重置
const handleReset = () => {
  console.log('🔄 重置查询条件')
  if (ruleFormRef.value) {
    ruleFormRef.value.resetFields()
  }
  Object.assign(searchForm, {
    title: '',
    type: '',
    area: '',
    status: ''
  })
  currentPage.value = 1
  getDataList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  console.log(`每页显示 ${val} 条`)
  pageSize.value = val
  currentPage.value = 1
  getDataList()
}

const handleCurrentChange = (val: number) => {
  console.log(`当前页: ${val}`)
  currentPage.value = val
  getDataList()
}

// 标题点击处理
const handleTitleClick = (row: NoticeMapResourcePageItem) => {
  console.log('点击标题:', row.name)
  ElMessage.info(`查看详情: ${row.name}`)
  // 这里可以打开详情页面或弹窗
}

// 获取类型样式
const getTypeClass = (type: string) => {
  const typeMap: Record<string, string> = {
    '宣传': 'type-promotion',
    '党建': 'type-party',
    '政策': 'type-policy',
    '公告': 'type-announcement',
    '通知': 'type-notice',
    '活动': 'type-activity'
  }
  return typeMap[type] || 'type-default'
}

// 获取状态样式
const getStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    '已发布': 'status-published',
    '待发布': 'status-pending',
    '已撤销': 'status-cancelled'
  }
  return statusMap[status] || 'status-default'
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 点击遮罩关闭
const handleMaskClick = () => {
  emit('update:visible', false)
}

// 组件挂载时的初始化
onMounted(() => {
  if (props.visible) {
    getDataList()
  }
})
</script>

<style lang="scss" scoped>
.notice-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
  
  /* 隐藏滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

.notice-detail-dialog {
  width: 1200px;
  // height: 700px;
  max-width: 95vw;
  max-height: 95vh;
  background: #060E1F;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(74, 144, 226, 0.3);
}

.dialog-header {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #060E1F;
  
  .dialog-title {
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
  }
  
  .close-btn {
    width: 28px;
    height: 28px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.dialog-content {
  flex: 1;
  padding: 20px;
  background: #060E1F;
  overflow-y: auto;
  
  scrollbar-width: none;
  -ms-overflow-style: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

.search-form {
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(74, 144, 226, 0.2);
  
  .el-form-item {
    margin-bottom: 0;
    margin-right: 16px;
  }
  
  .submit-btn {
    background: #4A90E2;
    border: none;
    color: #ffffff;
    
    &:hover {
      background: #357abd;
    }
  }
  
  .reset-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.table-box {
  background: #060E1F;

  .notice-table {
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    background: #060E1F !important;
    
    :deep(.el-table) {
      background: #060E1F !important;
      color: #ffffff !important;

      .el-table__header-wrapper {
        background: #060E1F !important;

        .el-table__header {
          background: #060E1F !important;

          th {
            background: #060E1F !important;
            color: #ffffff !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;

            .cell {
              color: #ffffff !important;
            }
          }
        }
      }

      .el-table__body-wrapper {
        background: #060E1F !important;

        .el-table__body {
          background: #060E1F !important;

          tr {
            background: #060E1F !important;

            td {
              background: #060E1F !important;
              color: #ffffff !important;
              border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;

              .cell {
                color: #ffffff !important;
              }
            }

            &:hover td {
              background: rgba(74, 144, 226, 0.15) !important;

              .cell {
                color: #ffffff !important;
              }
            }
          }
        }
      }

      .el-table__empty-block {
        background: #060E1F !important;

        .el-table__empty-text {
          color: rgba(255, 255, 255, 0.6) !important;
        }
      }

      .el-table__fixed,
      .el-table__fixed-right {
        background: #060E1F !important;
      }
    }
  }
}

.title-text {
  color: #4A90E2;
  cursor: pointer;
  
  &:hover {
    color: #357abd;
    text-decoration: underline;
  }
}

.type-tag {
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid transparent;

  &.type-promotion {
    background: rgba(255, 193, 7, 0.25);
    color: #ffdb4d;
    border-color: rgba(255, 193, 7, 0.4);
  }

  &.type-party {
    background: rgba(220, 53, 69, 0.25);
    color: #ff6b7a;
    border-color: rgba(220, 53, 69, 0.4);
  }

  &.type-policy {
    background: rgba(40, 167, 69, 0.25);
    color: #5cb85c;
    border-color: rgba(40, 167, 69, 0.4);
  }

  &.type-announcement {
    background: rgba(0, 123, 255, 0.25);
    color: #4da6ff;
    border-color: rgba(0, 123, 255, 0.4);
  }

  &.type-notice {
    background: rgba(108, 117, 125, 0.25);
    color: #adb5bd;
    border-color: rgba(108, 117, 125, 0.4);
  }

  &.type-activity {
    background: rgba(220, 53, 69, 0.25);
    color: #ff6b7a;
    border-color: rgba(220, 53, 69, 0.4);
  }

  &.type-default {
    background: rgba(108, 117, 125, 0.25);
    color: #adb5bd;
    border-color: rgba(108, 117, 125, 0.4);
  }
}

.view-count {
  color: #4A90E2;
  font-weight: bold;
}

.status-text {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  
  &.status-published {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
  }
  
  &.status-pending {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
  }
  
  &.status-cancelled {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
  }
  
  &.status-default {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
  }
}

.page-box {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(74, 144, 226, 0.3);
  margin-top: 16px;
  
  :deep(.el-pagination) {
    .el-pagination__total,
    .el-pagination__jump {
      color: #ffffff;
    }
    
    .el-select .el-select__wrapper {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(74, 144, 226, 0.3);
      
      .el-select__selected-item {
        color: #ffffff;
      }
    }
    
    .el-pager {
      li {
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
        border: 1px solid rgba(74, 144, 226, 0.3);
        
        &:hover {
          background: rgba(74, 144, 226, 0.3);
        }
        
        &.is-active {
          background: #4a90e2;
          color: #ffffff;
        }
      }
    }
    
    .btn-prev,
    .btn-next {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      border: 1px solid rgba(74, 144, 226, 0.3);
      
      &:hover {
        background: rgba(74, 144, 226, 0.3);
      }
    }
  }
}

// 输入框样式
.notice-detail-dialog {
  .el-input {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      box-shadow: none !important;
      
      &:hover {
        border-color: rgba(255, 255, 255, 0.4) !important;
      }
      
      &.is-focus {
        border-color: #4A90E2 !important;
        box-shadow: 0 0 0 1px rgba(74, 144, 226, 0.2) !important;
      }
    }
    
    .el-input__inner {
      background: transparent !important;
      border: none !important;
      color: #ffffff !important;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }
    }
  }
  
  .el-select {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      box-shadow: none !important;
      
      &:hover {
        border-color: rgba(255, 255, 255, 0.4) !important;
      }
      
      &.is-focus {
        border-color: #4A90E2 !important;
        box-shadow: 0 0 0 1px rgba(74, 144, 226, 0.2) !important;
      }
    }
    
    .el-input__inner {
      background: transparent !important;
      border: none !important;
      color: #ffffff !important;
    }
    
    .el-select__caret {
      color: rgba(255, 255, 255, 0.7) !important;
    }
    
    .el-select__placeholder {
      color: rgba(255, 255, 255, 0.5) !important;
    }
  }
}

// 深色模式全局覆盖
.notice-detail-dialog {
  // 覆盖Element Plus的默认样式
  .el-table {
    --el-table-bg-color: #060E1F !important;
    --el-table-tr-bg-color: #060E1F !important;
    --el-table-header-bg-color: #060E1F !important;
    --el-table-row-hover-bg-color: rgba(74, 144, 226, 0.15) !important;
    --el-table-text-color: #ffffff !important;
    --el-table-header-text-color: #ffffff !important;
    --el-table-border-color: rgba(255, 255, 255, 0.1) !important;
  }

  // 分页组件深色样式
  .el-pagination {
    --el-pagination-bg-color: #060E1F !important;
    --el-pagination-text-color: #ffffff !important;
    --el-pagination-border-color: rgba(255, 255, 255, 0.1) !important;
    --el-pagination-hover-color: #4a90e2 !important;
  }

  // 表单组件深色样式
  .el-form-item__label {
    color: #ffffff !important;
  }

  // 按钮深色样式
  .el-button {
    --el-button-text-color: #ffffff !important;
    --el-button-bg-color: rgba(255, 255, 255, 0.1) !important;
    --el-button-border-color: rgba(255, 255, 255, 0.2) !important;
    --el-button-hover-text-color: #ffffff !important;
    --el-button-hover-bg-color: rgba(255, 255, 255, 0.2) !important;
    --el-button-hover-border-color: rgba(255, 255, 255, 0.4) !important;
  }
}
</style>

// 下拉选项样式
<style lang="scss">
.el-select-dropdown {
  background: #060E1F !important;
  border: 1px solid rgba(74, 144, 226, 0.3) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.8) !important;
  z-index: 999999 !important;

  .el-select-dropdown__item {
    background: #060E1F !important;
    color: #ffffff !important;
    padding: 8px 16px !important;

    &:hover {
      background: rgba(74, 144, 226, 0.2) !important;
      color: #ffffff !important;
    }

    &.selected {
      background: #4a90e2 !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }
  }
}
</style> 