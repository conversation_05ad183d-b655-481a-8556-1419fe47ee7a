<template>
  <div class="community-book-overview">
    <div class="overview-cards">
      <div class="overview-card">
        <div class="card-icon">
          <img src="@/assets/common/person.png" alt="社区图书数" onerror="this.style.display='none'" />
          <div class="icon-fallback">📚</div>
        </div>
        <div class="card-content">
          <div class="card-title">社区图书数</div>
          <div class="card-number">{{ overviewData.totalCount }}</div>
        </div>
      </div>
      <div class="overview-card">
        <div class="card-icon">
          <img src="@/assets/common/views.png" alt="图书借阅次数" onerror="this.style.display='none'" />
          <div class="icon-fallback">📖</div>
        </div>
        <div class="card-content">
          <div class="card-title">图书借阅次数</div>
          <div class="card-number">{{ overviewData.totalViews }}次</div>
        </div>
      </div>
    </div>

    <div class="action-buttons">
      <button class="action-btn bg1" @click="handleResourceMap">资源地图分布</button>
      <button class="action-btn bg2" @click="handleHeatMap">辖区热力分布</button>
      <button class="action-btn bg3" @click="handleDataDetail">数据详情</button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  overviewData: {
    totalCount: number;
    totalViews: number;
  };
  loading?: boolean;
}

interface Emits {
  resourceMap: [];
  heatMap: [];
  dataDetail: [];
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const handleResourceMap = () => {
  emit("resourceMap");
};

const handleHeatMap = () => {
  emit("heatMap");
};

const handleDataDetail = () => {
  emit("dataDetail");
};
</script>

<style lang="scss" scoped>
.bg1 {
  background: #107c9e;
  color: #fff;
}
.bg2 {
  background: #fba602;
  color: #fff;
}
.bg3 {
  background: #1990ff;
  color: #fff;
}
.community-book-overview {
  .overview-cards {
    display: flex;
    gap: 15px;
    margin-bottom: 16px;
  }

  .overview-card {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 16px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid rgba(23, 81, 159, 0.3);
    border-radius: 8px;

    .card-icon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .icon-fallback {
        font-size: 24px;
        display: none;
      }

      img[style*="display: none"] + .icon-fallback {
        display: block;
      }
    }

    .card-content {
      flex: 1;

      .card-title {
        font-size: 12px;
        color: #9d9da6;
        margin-bottom: 4px;
      }

      .card-number {
        font-size: 24px;
        font-weight: bold;
        color: #00d4ff;
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;

    .action-btn {
      flex: 1;
      padding: 8px 12px;
      font-size: 12px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        opacity: 0.8;
        transform: translateY(-1px);
      }
    }
  }
}
</style>