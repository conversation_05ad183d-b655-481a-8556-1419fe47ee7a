<template>
  <div ref="chartRef" style="width: 100%; height: 250px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  borderColor: {
    type: String,
    default: '#2AEFFC'
  }
})

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

// 监听props变化
watch(() => props.chartData, () => {
  if (chart) {
    initChart()
  }
}, { deep: true })

function initChart() {
  if (!chartRef.value) return
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderWidth: 1,
      borderColor: props.borderColor,
      textStyle: {
        color: '#CFE3FC',
        fontSize: 12
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    legend: {
      data: ['红色预警', '黄色预警', '蓝色预警'],
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      itemWidth: 12,
      itemHeight: 8,
      top: 10
    },
    grid: {
      left: '5%',
      right: '5%',
      top: '15%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.chartData.label || [],
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 10,
        interval: 0,
        rotate: 0
      },
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    series: [
      {
        name: '红色预警',
        type: 'line',
        data: props.chartData.red || [],
        itemStyle: {
          color: '#FF3141'
        },
        lineStyle: {
          width: 2,
          color: '#FF3141'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 49, 65, 0.3)' },
            { offset: 1, color: 'rgba(255, 49, 65, 0.1)' }
          ])
        },
        symbol: 'circle',
        symbolSize: 4
      },
      {
        name: '黄色预警',
        type: 'line',
        data: props.chartData.yellow || [],
        itemStyle: {
          color: '#FF9F18'
        },
        lineStyle: {
          width: 2,
          color: '#FF9F18'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 159, 24, 0.3)' },
            { offset: 1, color: 'rgba(255, 159, 24, 0.1)' }
          ])
        },
        symbol: 'circle',
        symbolSize: 4
      },
      {
        name: '蓝色预警',
        type: 'line',
        data: props.chartData.blue || [],
        itemStyle: {
          color: '#1677FF'
        },
        lineStyle: {
          width: 2,
          color: '#1677FF'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(22, 119, 255, 0.3)' },
            { offset: 1, color: 'rgba(22, 119, 255, 0.1)' }
          ])
        },
        symbol: 'circle',
        symbolSize: 4
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式处理
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }
  
  window.addEventListener('resize', resizeHandler)
}
</script> 