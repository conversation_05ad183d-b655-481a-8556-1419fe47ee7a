<template>
  <div class="signup-rate-container">
    <div 
      v-for="(item, index) in rateData" 
      :key="index" 
      class="rate-item"
    >
      <span class="area-name">{{ item.name }}</span>
      <span class="rate-value">{{ item.rate }}%</span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface RateItem {
  name: string
  rate: number
}

defineProps<{
  rateData: RateItem[]
}>()
</script>

<style scoped>
.signup-rate-container {
  margin-top: 20px;
  padding-bottom: 20px; /* 增加底部内边距 */
}

.rate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.rate-item:last-child {
  margin-bottom: 0; /* 最后一个项目不需要底部边距 */
}

.rate-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.5);
  transform: translateX(3px);
}

.area-name {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.rate-value {
  font-size: 16px;
  font-weight: bold;
  color: #00FFFF;
  min-width: 60px;
  text-align: right;
}
</style> 