<template>
  <div class="chart-container" ref="chartRef" style="width: 100%; height: 250px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const loadingSettings = {
  text: '加载中...',
  color: '#2AEFFC',
  textColor: '#fff',
  maskColor: 'rgba(0, 0, 0, 0.5)'
}

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
let intervalId: number | null = null
const displayCount = 6

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

watch(() => props.chartData, () => {
  if (chart) {
    initChart()
  }
}, { deep: true })

watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading(loadingSettings)
  } else {
    chart?.hideLoading()
    initChart()
  }
})

function initChart() {
  if (!chartRef.value) return
  
  if (intervalId) {
    clearInterval(intervalId)
  }
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  
  const chartData = Array.isArray(props.chartData) ? props.chartData : []
  const allLabels = chartData.map((item: any) => item.district?.name || '未知区域')
  const allWishData = chartData.map((item: any) => item.wishParticipants || 0)
  const allServiceData = chartData.map((item: any) => item.serviceParticipants || 0)
  
  let currentLabels = allLabels.slice(0, displayCount)
  let currentWishData = allWishData.slice(0, displayCount)
  let currentServiceData = allServiceData.slice(0, displayCount)
  
  const updateChart = () => {
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        backgroundColor: 'rgba(0,0,0,0.7)',
        borderWidth: 1,
        borderColor: '#2AEFFC',
        textStyle: {
          color: '#CFE3FC',
          fontSize: 12
        }
      },
      legend: {
        data: ['微心愿参与人数', '微服务参与人数'],
        textStyle: {
          color: '#fff',
          fontSize: 12
        },
        icon: 'rect',
        itemWidth: 12,
        itemHeight: 8,
        top: 10
      },
      grid: {
        left: '8%',
        right: '8%',
        top: '20%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 10
        }
      },
      yAxis: {
        type: 'category',
        data: currentLabels,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 10
        },
        axisTick: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        }
      },
      series: [
        {
          name: '微心愿参与人数',
          type: 'bar',
          data: currentWishData,
          itemStyle: {
            borderRadius: [0, 4, 4, 0],
            color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
              { offset: 0, color: '#FF9F18' },
              { offset: 1, color: 'rgba(255, 159, 24, 0.3)' }
            ])
          },
          barWidth: '35%',
          label: {
            show: true,
            position: 'right',
            color: '#fff',
            fontSize: 10,
            formatter: function(params: any) {
              return params.value > 0 ? params.value : '';
            }
          }
        },
        {
          name: '微服务参与人数',
          type: 'bar',
          data: currentServiceData,
          itemStyle: {
            borderRadius: [0, 4, 4, 0],
            color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
              { offset: 0, color: '#1677FF' },
              { offset: 1, color: 'rgba(22, 119, 255, 0.3)' }
            ])
          },
          barWidth: '35%',
          label: {
            show: true,
            position: 'right',
            color: '#fff',
            fontSize: 10,
            formatter: function(params: any) {
              return params.value > 0 ? params.value : '';
            }
          }
        }
      ]
    }
    
    chart?.setOption(option)
  }
  
  updateChart()
  
  // 如果数据超过显示数量，启动轮播
  if (allLabels.length > displayCount) {
    let currentIndex = 0
    intervalId = window.setInterval(() => {
      currentIndex = (currentIndex + 1) % allLabels.length
      currentLabels = []
      currentWishData = []
      currentServiceData = []
      
      for (let i = 0; i < displayCount; i++) {
        const index = (currentIndex + i) % allLabels.length
        currentLabels.push(allLabels[index])
        currentWishData.push(allWishData[index])
        currentServiceData.push(allServiceData[index])
      }
      
      updateChart()
    }, 3000)
  }
  
  // 响应式处理
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }
  
  window.addEventListener('resize', resizeHandler)
}

onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
  window.removeEventListener('resize', function() {
    chart?.resize()
  })
})
</script> 