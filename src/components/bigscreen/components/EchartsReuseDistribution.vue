<template>
  <div ref="chartRef" style="width: 100%; height: 250px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  seriesNames: {
    type: Object,
    default: () => ({
      goods: '物品数量',
      views: '浏览量'
    })
  }
})

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

watch(() => props.chartData, () => {
  if (chart) {
    initChart()
  }
}, { deep: true })

function initChart() {
  if (!chartRef.value) return
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  
  const chartData = Array.isArray(props.chartData) ? props.chartData : []
  const categories = chartData.map((item: any) => item.name || item.districtName || '')
  const goodsCounts = chartData.map((item: any) => item.goodsCount || item.count || 0)
  const viewCounts = chartData.map((item: any) => item.viewCount || item.views || item.browseCount || 0)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderWidth: 1,
      borderColor: '#2AEFFC',
      textStyle: {
        color: '#CFE3FC',
        fontSize: 12
      }
    },
    legend: {
      data: [props.seriesNames.goods, props.seriesNames.views],
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12
      },
      top: '5%'
    },
    grid: {
      left: '8%',
      right: '8%',
      top: '20%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: categories,
        axisPointer: {
          type: 'shadow'
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 10,
          interval: 0,
          rotate: 25
        },
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
        nameTextStyle: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 10
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 10
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      {
        type: 'value',
        name: '浏览量',
        nameTextStyle: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 10
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 10
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: props.seriesNames.goods,
        type: 'bar',
        yAxisIndex: 0,
        data: goodsCounts,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#FF9F18' },
            { offset: 1, color: 'rgba(255, 159, 24, 0.3)' }
          ])
        },
        barWidth: '30%',
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 10
        }
      },
      {
        name: props.seriesNames.views,
        type: 'line',
        yAxisIndex: 1,
        data: viewCounts,
        lineStyle: {
          color: '#00FFFF',
          width: 2
        },
        itemStyle: {
          color: '#00FFFF'
        },
        symbol: 'circle',
        symbolSize: 6,
        label: {
          show: true,
          position: 'top',
          color: '#00FFFF',
          fontSize: 10
        }
      }
    ]
  }
  
  chart?.setOption(option)
  
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }
  
  window.addEventListener('resize', resizeHandler)
}

onBeforeUnmount(() => {
  window.removeEventListener('resize', function() {
    chart?.resize()
  })
})
</script> 