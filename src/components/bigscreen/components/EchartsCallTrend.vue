<template>
  <div ref="chartRef" style="width: 100%; height: 250px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  timeType: {
    type: String,
    default: 'today'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

const loadingSettings = {
  text: '加载中...',
  color: '#2AEFFC',
  textColor: '#fff',
  maskColor: 'rgba(0, 0, 0, 0.5)'
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

watch([() => props.chartData, () => props.timeType], () => {
  if (chart) {
    initChart()
  }
}, { deep: true })

watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading(loadingSettings)
  } else {
    chart?.hideLoading()
    initChart()
  }
})

function initChart() {
  if (!chartRef.value) return
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  
  // 生成24小时时间轴
  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'))
  
  const todayData = props.chartData.today || []
  const yesterdayData = props.chartData.yesterday || []
  
  const series = []
  
  // 今日数据
  if (props.timeType === 'today' || props.timeType === 'both') {
    series.push({
      name: '今日来电',
      type: 'line',
      data: todayData,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        color: '#5B8FF9',
        width: 3
      },
      itemStyle: {
        color: '#5B8FF9',
        borderColor: '#fff',
        borderWidth: 2
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(91, 143, 249, 0.3)' },
          { offset: 1, color: 'rgba(91, 143, 249, 0.1)' }
        ])
      }
    })
  }
  
  // 昨日数据
  if (props.timeType === 'yesterday' || props.timeType === 'both') {
    series.push({
      name: '昨日来电',
      type: 'line',
      data: yesterdayData,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        color: '#5AD8A6',
        width: 3
      },
      itemStyle: {
        color: '#5AD8A6',
        borderColor: '#fff',
        borderWidth: 2
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(90, 216, 166, 0.3)' },
          { offset: 1, color: 'rgba(90, 216, 166, 0.1)' }
        ])
      }
    })
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderWidth: 1,
      borderColor: '#2AEFFC',
      textStyle: {
        color: '#CFE3FC',
        fontSize: 12
      },
      formatter: function(params: any) {
        let html = `${params[0].axisValue}:00<br/>`
        params.forEach((param: any) => {
          html += `<span style="color: ${param.color}">${param.seriesName}: ${param.value}</span><br/>`
        })
        return html
      }
    },
    legend: {
      data: series.map(s => s.name),
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      icon: 'rect',
      itemWidth: 12,
      itemHeight: 8,
      top: 10
    },
    grid: {
      left: '8%',
      right: '5%',
      top: '20%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: hours,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10,
        interval: 1, // 每2个小时显示一次
        formatter: function(value: string) {
          const hour = parseInt(value)
          return hour % 2 === 0 ? hour.toString() : ''
        }
      },
      axisTick: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    series: series
  }
  
  chart?.setOption(option)
  
  // 响应式处理
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }
  
  window.addEventListener('resize', resizeHandler)
}

onBeforeUnmount(() => {
  window.removeEventListener('resize', function() {
    chart?.resize()
  })
})
</script> 