<template>
  <div class="tools-usage-cards">
    <div 
      v-for="(item, index) in toolsData" 
      :key="index" 
      class="usage-card"
      :class="item.color"
    >
      <div class="card-icon">
        <img :src="item.img" :alt="item.title" />
      </div>
      <div class="card-content">
        <div class="card-title">{{ item.title }}</div>
        <div class="card-value">
          <span class="count">{{ item.count || 0 }}</span>
          <span class="unit">{{ item.unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ToolItem {
  img: string
  title: string
  count?: number
  unit: string
  color: string
}

defineProps<{
  toolsData: ToolItem[]
}>()
</script>

<style scoped>
.tools-usage-cards {
  display: flex;
  justify-content: space-between;
  gap: 0.260vw;
  margin-top: 0.390vw;
  
  @media (max-width: 2000px) {
    gap: 0.416vw;
    margin-top: 0.625vw;
  }
}

.usage-card {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 0.208vw;
  background: rgba(255, 255, 255, 0.05);
  border: 0.026vw solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  
  
}

.usage-card:hover {
  transform: translateY(-0.078vw);
  box-shadow: 0 0.156vw 0.520vw rgba(0, 212, 255, 0.3);
  
  @media (max-width: 2000px) {
    transform: translateY(-0.125vw);
    box-shadow: 0 0.25vw 0.833vw rgba(0, 212, 255, 0.3);
  }
}

.usage-card.blue {
  border-color: rgba(0, 212, 255, 0.4);
}

.usage-card.green {
  border-color: rgba(0, 255, 150, 0.4);
}

.usage-card.orange {
  border-color: rgba(255, 159, 24, 0.4);
}

.card-icon {
  margin-right: 10px;
  flex-shrink: 0;
  
 
}

.card-icon img {
  width: 25px;
  height: 25px;
  
 
}

.card-content {
  flex: 1;
  min-width: 0;
}

.card-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  /* margin-bottom: 0.130vw; */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  
  
}

.card-value {
  display: flex;
  align-items: baseline;
}

.count {
  font-size: 14px;
  font-weight: bold;
  color: #00FFFF;
  
 
}

.usage-card.green .count {
  color: #00FF96;
}

.usage-card.orange .count {
  color: #FF9F18;
}

.unit {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-left: 0.078vw;
  
 
}
</style> 