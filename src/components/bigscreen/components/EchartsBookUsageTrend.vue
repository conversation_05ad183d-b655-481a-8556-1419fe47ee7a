<template>
  <div class="echarts-book-usage-trend">
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">数据加载中...</div>
    </div>
    <div v-else ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

interface ChartDataItem {
  month: string
  bookCount: number
  viewCount: number
}

interface Props {
  chartData: ChartDataItem[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) {
    console.log('图表容器不存在')
    return
  }

  if (!chartInstance) {
    chartInstance = echarts.init(chartRef.value)
  }

  // 如果没有数据，使用默认数据
  const defaultData = [
    { month: '1月', bookCount: 180, viewCount: 100 },
    { month: '2月', bookCount: 140, viewCount: 120 },
    { month: '3月', bookCount: 230, viewCount: 100 },
    { month: '4月', bookCount: 120, viewCount: 130 },
    { month: '5月', bookCount: 220, viewCount: 200 },
    { month: '6月', bookCount: 200, viewCount: 220 },
    { month: '7月', bookCount: 157, viewCount: 216 },
    { month: '8月', bookCount: 144, viewCount: 216 }
  ]

  const chartData = props.chartData.length > 0 ? props.chartData : defaultData
  console.log('📊 EchartsBookUsageTrend - 接收到的数据:', props.chartData)
  console.log('📊 EchartsBookUsageTrend - 使用的数据:', chartData)

  const months = chartData.map(item => item.month)
  const bookCounts = chartData.map(item => item.bookCount)
  const viewCounts = chartData.map(item => item.viewCount)

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00D4FF',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      },
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: [
        {
          name: '图书数',
          icon: 'rect',
          itemStyle: {
            color: '#00D4FF'
          }
        },
        {
          name: '浏览量',
          icon: 'circle',
          itemStyle: {
            color: '#FFA500'
          }
        }
      ],
      textStyle: {
        color: '#fff',
        fontSize: 13
      },
      top: 10,
      itemWidth: 12,
      itemHeight: 8
    },
    grid: {
      left: '8%',
      right: '8%',
      top: '20%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: months,
        axisPointer: {
          type: 'shadow'
        },
        axisLabel: {
          color: '#9D9DA6',
          fontSize: 11,
          interval: 0, // 显示所有标签
          rotate: 0,   // 不旋转
          margin: 8
        },
        axisLine: {
          lineStyle: {
            color: '#394B5C'
          }
        },
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: '#394B5C'
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        min: 0,
        max: 250,
        interval: 50,
        position: 'left',
        axisLabel: {
          formatter: '{value}',
          color: '#9D9DA6',
          fontSize: 12
        },
        axisLine: {
          lineStyle: {
            color: '#394B5C'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#394B5C',
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        name: '',
        min: 0,
        max: 250,
        interval: 50,
        position: 'right',
        axisLabel: {
          formatter: '{value}',
          color: '#9D9DA6',
          fontSize: 12
        },
        axisLine: {
          lineStyle: {
            color: '#394B5C'
          }
        },
        splitLine: {
          show: false // 右侧Y轴不显示分割线，避免重复
        }
      }
    ],
    series: [
      {
        name: '图书数',
        type: 'bar',
        yAxisIndex: 0,
        data: bookCounts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00D4FF' },
            { offset: 1, color: '#0099CC' }
          ])
        },
        barWidth: '40%',
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 11,
          fontWeight: 'bold'
        }
      },
      {
        name: '浏览量',
        type: 'line',
        yAxisIndex: 1, // 使用右侧Y轴
        data: viewCounts,
        lineStyle: {
          color: '#FFA500',
          width: 3
        },
        itemStyle: {
          color: '#FFA500',
          borderColor: '#fff',
          borderWidth: 2
        },
        symbol: 'circle',
        symbolSize: 8,
        label: {
          show: true,
          position: 'top',
          color: '#FFA500',
          fontSize: 11,
          fontWeight: 'bold',
          offset: [0, -10]
        },
        emphasis: {
          scale: true,
          scaleSize: 10
        }
      }
    ]
  }

  chartInstance.setOption(option, true)

  // 响应式调整
  const handleResize = () => {
    chartInstance?.resize()
  }

  window.addEventListener('resize', handleResize)
}

onMounted(() => {
  setTimeout(() => {
    initChart()
  }, 100)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    window.removeEventListener('resize', () => {})
  }
})

watch(() => props.chartData, () => {
  initChart()
}, { deep: true })
</script>

<style lang="scss" scoped>
.echarts-book-usage-trend {
  width: 100%;
  height: 250px;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;

    .loading-spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(0, 212, 255, 0.3);
      border-top: 3px solid #00D4FF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      margin-top: 10px;
      color: #9D9DA6;
      font-size: 12px;
    }
  }

  .chart {
    width: 100%;
    height: 100%;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
