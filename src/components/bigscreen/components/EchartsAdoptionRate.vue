<template>
  <div ref="chartRef" style="width: 100%; height: 250px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

watch(() => props.chartData, () => {
  if (chart) {
    initChart()
  }
}, { deep: true })

watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading({
      text: '加载中...',
      color: '#2AEFFC',
      textColor: '#fff',
      maskColor: 'rgba(0, 0, 0, 0.5)'
    })
  } else {
    chart?.hideLoading()
    initChart()
  }
})

const initChart = () => {
  if (!chartRef.value) return
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  
  const option = {
    legend: {
      orient: 'horizontal',
      top: 15,
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      data: props.chartData.map((item: any) => item.name),
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 20
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderColor: '#2AEFFC',
      textStyle: {
        color: '#CFE3FC',
        fontSize: 12
      },
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: '采纳情况',
      type: 'pie',
      radius: ['35%', '65%'],
      center: ['50%', '60%'],
      itemStyle: {
        borderRadius: 8,
        borderColor: '#021121',
        borderWidth: 2
      },
      data: props.chartData.map((item: any) => ({
        value: item.value,
        name: item.name,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: `${item.color}00` },
            { offset: 1, color: item.color }
          ])
        }
      })),
      label: {
        color: '#fff',
        fontSize: 10
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  
  chart.setOption(option)
  
  // 响应式处理
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }
  
  window.addEventListener('resize', resizeHandler)
}
</script> 