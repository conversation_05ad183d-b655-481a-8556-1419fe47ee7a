<template>
  <div ref="chartRef" style="width: 100%; height: 280px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

interface TreemapDataItem {
  content: string
  count: number
}

const props = withDefaults(defineProps<{
  chartData: TreemapDataItem[]
  loading?: boolean
}>(), {
  loading: false
})

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

const loadingSettings = {
  text: '加载中...',
  color: '#5B8FF9',
  textColor: '#fff',
  maskColor: 'rgba(0, 0, 0, 0.5)'
}

// 颜色配置
const colors = ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E86452', '#6DC8EC', '#945FB9', '#FF9845']

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

watch(() => props.chartData, () => {
  if (chart) {
    initChart()
  }
}, { deep: true })

watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading(loadingSettings)
  } else {
    chart?.hideLoading()
    initChart()
  }
})

function initChart() {
  if (!chartRef.value) return
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  
  if (props.loading) {
    chart.showLoading(loadingSettings)
    return
  }
  
  const chartData = Array.isArray(props.chartData) ? props.chartData : []
  
  // 计算总数用于计算百分比
  const total = chartData.reduce((sum, item) => sum + item.count, 0)
  
  // 检查是否为空数据或暂无数据的情况
  const isEmptyData = total === 0 || chartData.length === 0 || 
    (chartData.length === 1 && chartData[0].content === '暂无数据')
  
  // 转换数据格式为ECharts treemap需要的格式
  const treemapData = chartData.map((item, index) => {
    let percentage = '0.0'
    if (!isEmptyData && total > 0) {
      percentage = ((item.count / total) * 100).toFixed(1)
    }
    
    return {
      name: item.content,
      value: item.count,
      percentage: percentage,
      itemStyle: {
        color: colors[index % colors.length]
      }
    }
  })
  
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderWidth: 1,
      borderColor: '#5B8FF9',
      textStyle: {
        color: '#CFE3FC',
        fontSize: 12
      },
      formatter: function(params: any) {
        if (isEmptyData) {
          return `${params.name}`
        }
        return `${params.name}<br/>次数: ${params.value}<br/>占比: ${params.data.percentage}%`
      }
    },
    series: [
      {
        name: 'AI智能客服热门问题',
        type: 'treemap',
        width: '100%',
        height: '100%',
        roam: false,
        nodeClick: false,
        breadcrumb: {
          show: false
        },
        label: {
          show: true,
          formatter: function(params: any) {
            if (isEmptyData) {
              return `{title|${params.name}}`
            }
            return `{title|${params.name}}\n{count|${params.value}次}\n{percent|${params.data.percentage}%}`
          },
          rich: {
            title: {
              fontSize: 14,
              fontWeight: 'bold',
              color: '#fff',
              lineHeight: 20
            },
            count: {
              fontSize: 12,
              color: '#fff',
              lineHeight: 18
            },
            percent: {
              fontSize: 11,
              color: 'rgba(255,255,255,0.8)',
              lineHeight: 16
            }
          }
        },
        itemStyle: {
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          gapWidth: 1
        },
        emphasis: {
          itemStyle: {
            borderColor: 'rgba(255, 255, 255, 0.3)',
            borderWidth: 2
          },
          label: {
            fontSize: 16
          }
        },
        data: treemapData
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式处理
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }
  
  window.addEventListener('resize', resizeHandler)
}

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
  }
  window.removeEventListener('resize', function() {
    chart?.resize()
  })
})
</script>

<style scoped>
/* 组件样式 */
</style> 