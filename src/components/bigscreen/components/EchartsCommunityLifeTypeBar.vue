<template>
  <div class="echarts-community-life-type-bar">
    <v-chart
      class="chart"
      :option="chartOption"
      :loading="loading"
      autoresize
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

use([
  CanvasRenderer,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

interface Props {
  chartData: Array<{
    name: string
    value: number
  }>
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 添加调试信息
console.log('🔍 EchartsCommunityLifeTypeBar - 接收到的数据:', props.chartData)

const chartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: '#00D4FF',
    borderWidth: 1,
    textStyle: {
      color: '#fff'
    },
    formatter: (params: any) => {
      const dataIndex = params[0].dataIndex
      // 由于数据被反转了，需要计算原始索引
      const originalIndex = props.chartData.length - 1 - dataIndex
      const originalName = props.chartData[originalIndex]?.name || ''
      const value = params[0].value
      return `<div style="font-weight: bold; margin-bottom: 5px;">${originalName}</div>
              <div style="margin: 2px 0;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params[0].color}; margin-right: 5px;"></span>
                信息数: ${value}
              </div>`
    }
  },
  legend: {
    data: ['信息数'],
    textStyle: {
      color: '#9D9DA6',
      fontSize: 12
    },
    top: '2%',
    left: 'center',
    itemWidth: 12,
    itemHeight: 8
  },


  grid: {
    left: '5%',
    right: '8%',
    bottom: '10%',
    top: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    axisLine: {
      show: true,
      lineStyle: {
        color: '#4A5568'
      }
    },
    axisLabel: {
      color: '#9D9DA6',
      fontSize: 11,
      margin: 8
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: '#4A5568'
      }
    },
    splitLine: {
      lineStyle: {
        color: '#2D3748',
        type: 'dashed'
      }
    },
    max: 150,
    interval: 30
  },
  yAxis: {
    type: 'category',
    data: props.chartData.map(item => {
      // 处理过长的类型名称
      const name = item.name
      if (name.length > 8) {
        return name.substring(0, 8) + '...'
      }
      return name
    }).reverse(), // 反转顺序，从上到下显示
    axisLine: {
      lineStyle: {
        color: '#4A5568'
      }
    },
    axisLabel: {
      color: '#9D9DA6',
      fontSize: 11,
      interval: 0,
      rotate: 0,
      overflow: 'truncate',
      width: 80
    },
    axisTick: {
      show: false
    }
  },
  series: [
    {
      name: '信息数',
      type: 'bar',
      data: props.chartData.map(item => ({
        value: item.value,
        itemStyle: {
          color: '#00D4FF'
        }
      })).reverse(), // 反转数据顺序以匹配Y轴
      barWidth: '60%',
      label: {
        show: true,
        position: 'right',
        color: '#fff',
        fontSize: 12,
        formatter: '{c}'
      }
    }
  ]
}))
</script>

<style lang="scss" scoped>
.echarts-community-life-type-bar {
  width: 100%;
  height: 320px;

  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
