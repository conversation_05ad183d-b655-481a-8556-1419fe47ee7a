<template>
  <div class="warning-data">
    <div class="warning-item red">
      <div class="icon">
        <img src="@/assets/common/light.png" alt="">
        <div class="name">红色预警</div>
      </div>
     
      <div class="count">{{ warningData.red }}条</div>
    </div>
    <div class="warning-item yellow">
      <div class="icon">
        <img src="@/assets/common/light.png" alt="">
        <div class="name">黄色预警</div>
      </div>
      
      <div class="count">{{ warningData.yellow }}条</div>
    </div>
    <div class="warning-item blue">
      <div class="icon">
        <img src="@/assets/common/light.png" alt="">
        <div class="name">蓝色预警</div>
      </div>
      
      <div class="count">{{ warningData.blue }}条</div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  warningData: {
    type: Object,
    required: true
  }
})
</script>

<style lang="scss" scoped>
.warning-data {
  display: flex;
  justify-content: space-between;
  // padding: 0 one(10);
  margin: one(20) 0;
  
  @media (max-width: 2000px) {
    padding: 0 two(10);
    margin: two(20) 0;
  }

  .warning-item {
    text-align: center;
    padding: one(20) one(15);
    border-radius: one(8);
    width: 30%;
    margin: 0 one(5);
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    border: one(1) solid transparent;
    transition: all 0.3s ease;
    
    @media (max-width: 2000px) {
      padding: two(20) two(15);
      border-radius: two(8);
      margin: 0 two(5);
      border: two(1) solid transparent;
    }

    .icon {
      margin-bottom: one(8);
      display: flex;
      align-items: center;
      justify-content: center;
      
      @media (max-width: 2000px) {
        margin-bottom: two(8);
      }
      
      img {
        width: 20px;
        height: 20px;
        
       
      }
    }

    .name {
      font-size: 14px;
      // margin-bottom: 10px;
      color: #fff;
      font-weight: 500;
      
     
    }

    .count {
      font-size: 13px;
      font-weight: bold;
      color: #fff;
      
      
    }

    &.red {
      background: linear-gradient(135deg, rgba(255, 49, 65, 0.8), rgba(255, 49, 65, 0.6));
      border-color: #FF3141;
      box-shadow: 0 one(4) one(15) rgba(255, 49, 65, 0.3);
      
      @media (max-width: 2000px) {
        box-shadow: 0 two(4) two(15) rgba(255, 49, 65, 0.3);
      }
    }

    &.yellow {
      background: linear-gradient(135deg, rgba(255, 159, 24, 0.8), rgba(255, 159, 24, 0.6));
      border-color: #FF9F18;
      box-shadow: 0 one(4) one(15) rgba(255, 159, 24, 0.3);
      
      @media (max-width: 2000px) {
        box-shadow: 0 two(4) two(15) rgba(255, 159, 24, 0.3);
      }
    }

    &.blue {
      background: linear-gradient(135deg, rgba(22, 119, 255, 0.8), rgba(22, 119, 255, 0.6));
      border-color: #1677FF;
      box-shadow: 0 one(4) one(15) rgba(22, 119, 255, 0.3);
      
      @media (max-width: 2000px) {
        box-shadow: 0 two(4) two(15) rgba(22, 119, 255, 0.3);
      }
    }

    &:hover {
      transform: translateY(one(-2));
      box-shadow: 0 one(8) one(25) rgba(255, 255, 255, 0.1);
      
      @media (max-width: 2000px) {
        transform: translateY(two(-2));
        box-shadow: 0 two(8) two(25) rgba(255, 255, 255, 0.1);
      }
    }
  }
}
</style> 