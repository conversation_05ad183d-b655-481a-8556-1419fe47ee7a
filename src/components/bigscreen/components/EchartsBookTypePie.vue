<template>
  <div class="echarts-book-type-pie">
    <v-chart
      class="chart"
      :option="chartOption"
      :loading="loading"
      autoresize
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

use([
  CanvasRenderer,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent
])

interface Props {
  chartData: Array<{
    name: string
    value: number
  }>
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const chartOption = computed(() => {
  // console.log('📊 EchartsBookTypePie - 接收到的数据:', props.chartData)
  // 计算总数和百分比
  const total = props.chartData.reduce((sum, item) => sum + item.value, 0)

  // 定义颜色映射，完全按照图片中的颜色
  const colorMap: { [key: string]: string } = {
    '文学': '#FF6B6B',     // 红色
    '文化': '#4ECDC4',     // 青色
    '科学': '#45B7D1',     // 蓝色
    '教育': '#96CEB4',     // 绿色
    '历史': '#F1C40F',     // 黄色
    '地理': '#FF9F43',     // 橙色
    '艺术': '#E74C3C',     // 深红色
    '社会科学': '#3498DB', // 深蓝色
    '工业': '#9B59B6',     // 紫色
    '自然科学': '#1ABC9C', // 青绿色
    '经济': '#F39C12',     // 金色
    // 添加更多可能的图书类型
    '哲学': '#E67E22',     // 橙红色
    '宗教': '#8E44AD',     // 深紫色
    '语言': '#16A085',     // 深青色
    '医学': '#C0392B',     // 深红色
    '数理': '#2980B9',     // 深蓝色
    '生物': '#27AE60',     // 深绿色
    '天文': '#34495E',     // 深灰色
    '计算机': '#7F8C8D',   // 灰色
    '法律': '#D35400',     // 深橙色
    '政治': '#8B4513',     // 棕色
    '军事': '#2F4F4F',     // 深灰绿色
    '体育': '#FF1493',     // 深粉色
    '音乐': '#9370DB',     // 中紫色
    '美术': '#FF4500',     // 橙红色
    '摄影': '#32CD32',     // 酸橙绿
    '建筑': '#DAA520',     // 金黄色
    '农业': '#228B22',     // 森林绿
    '交通': '#4682B4',     // 钢蓝色
    '环境': '#008B8B',     // 深青色
    '其他': '#95A5A6'      // 默认灰色
  }

  const dataWithPercentage = props.chartData.map((item, index) => {
    const percentage = total > 0 ? ((item.value / total) * 100).toFixed(0) : 0

    // 如果没有匹配的颜色，使用默认颜色数组
    const defaultColors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#F1C40F',
      '#FF9F43', '#E74C3C', '#3498DB', '#9B59B6', '#1ABC9C',
      '#F39C12', '#E67E22', '#8E44AD', '#16A085', '#C0392B'
    ];

    const color = colorMap[item.name] || defaultColors[index % defaultColors.length] || '#95A5A6';

    return {
      name: item.name,
      value: item.value,
      percentage: `${percentage}%`,
      itemStyle: {
        color: color
      }
    }
  })

  return {
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        return `<div style="font-weight: bold; margin-bottom: 5px;">${params.seriesName}</div>
                <div style="margin: 2px 0;">
                  <span style="display: inline-block; width: 10px; height: 10px; background-color: ${params.color}; margin-right: 5px;"></span>
                  ${params.name}: ${params.value} (${params.percent}%)
                </div>`
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00D4FF',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      orient: 'horizontal',
      top: '0',
      left: 'center',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      itemWidth: 12,
      itemHeight: 8,
      itemGap: 12,
      data: dataWithPercentage.map(item => ({
        name: item.name,
        icon: 'rect'
      }))
    },
    series: [
      {
        name: '图书类型分布',
        type: 'pie',
        radius: ['30%', '55%'], // 缩小环形图
        center: ['50%', '60%'], // 位置稍微下移
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          color: '#fff',
          fontSize: 11,
          fontWeight: 'bold',
          formatter: function(params: any) {
            const name = params.name
            const percentage = params.data.percentage
            // 如果名称太长，进行截断
            const displayName = name.length > 4 ? name.substring(0, 4) + '...' : name
            return `${displayName}\n${percentage}`
          },
          lineHeight: 14,
          overflow: 'truncate'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: true,
          lineStyle: {
            color: '#fff',
            width: 1
          },
          length: 15,
          length2: 10
        },
        data: dataWithPercentage
      }
    ]
  }
})
</script>

<style lang="scss" scoped>
.echarts-book-type-pie {
  width: 100%;
  height: 320px;
  padding: 0 10px; // 左右增加内边距，确保标签显示完整

  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
