<template>
  <div class="information-overview">
    <div class="overview-cards">
      <div class="overview-card">
        <div class="card-icon">
          <img src="@/assets/common/icon4.png" alt="信息公告总数" />
        </div>
        <div class="card-content">
          <div class="card-title">信息公告总数</div>
          <div class="card-number">{{ overviewData.totalCount }}</div>
        </div>
      </div>
    </div>
    
    <div class="action-buttons">
      <button class="action-btn bg1" @click="handleResourceMap">资源地图分布</button>
      <button class="action-btn bg2" @click="handleHeatMap">辖区热力分布</button>
      <button class="action-btn bg3" @click="handleDataDetail">数据详情</button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  overviewData: {
    totalCount: number
  }
}

interface Emits {
  resourceMap: []
  heatMap: []
  dataDetail: []
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const handleResourceMap = () => {
  emit('resourceMap')
}

const handleHeatMap = () => {
  emit('heatMap')
}

const handleDataDetail = () => {
  emit('dataDetail')
}
</script>

<style lang="scss" scoped>
.bg1{
  background: #107C9E;
  color: #fff;
}
.bg2{
  background: #FBA602;
  color: #fff;
}
.bg3{
  background: #1990FF;
  color: #fff;
}
.information-overview {
  .overview-cards {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
  }
  
  .overview-card {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 16px;
    background: rgba(23, 81, 159, 0.2);
    border: 1px solid rgba(23, 81, 159, 0.3);
    border-radius: 8px;
    
    .card-icon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      
      img {
        width: 100%;
        height: 100%;
      }
    }
    
    .card-content {
      flex: 1;
      
      .card-title {
        font-size: 12px;
        color: #9D9DA6;
        margin-bottom: 4px;
      }
      
      .card-number {
        font-size: 24px;
        font-weight: bold;
        color: #00D4FF;
      }
    }
  }
  
  .action-buttons {
    display: flex;
    gap: 8px;
    
    .action-btn {
      flex: 1;
      padding: 8px 12px;
      font-size: 12px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 4px;
      // background: transparent;
      // color: #9D9DA6;
      cursor: pointer;
      transition: all 0.3s ease;
      
      // &:hover {
      //   border-color: #00D4FF;
      //   color: #00D4FF;
      // }
      
      // &.active {
      //   background: #00D4FF;
      //   border-color: #00D4FF;
      //   color: #fff;
      // }
    }
  }
}
</style> 