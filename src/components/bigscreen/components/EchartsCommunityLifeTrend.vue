<template>
  <div class="echarts-community-life-trend">
    <v-chart
      class="chart"
      :option="chartOption"
      :loading="loading"
      autoresize
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { TreemapChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

use([
  CanvasRenderer,
  TreemapChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent
])

interface Props {
  chartData: Array<{
    name: string
    value: number
    percentage: string
  }>
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 添加调试信息
console.log('🔍 EchartsCommunityLifeTrend - 接收到的数据:', props.chartData)

const chartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: '#00D4FF',
    borderWidth: 0,
    textStyle: {
      color: '#fff'
    }
  },
  series: [
    {
      name: '热门话题分布',
      type: 'treemap',
      width: '100%',
      height: '100%',
      roam: false,
      nodeClick: false,
      breadcrumb: {
        show: false
      },
      label: {
        show: true,
        position: 'inside',
        color: '#fff',
        fontSize: 12,
        fontWeight: 'bold',
        formatter: function(params: any) {
          return params.name + '\n' + params.data.percentage
        }
      },
      itemStyle: {
        borderColor: '#1a1a1a',
        borderWidth: 0,
        gapWidth: 0
      },
      levels: [
        {
          itemStyle: {
            borderColor: '#1a1a1a',
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ],
      data: props.chartData.map((item, index) => {
        const colors = [
          '#8B5CF6', // 紫色 - 游戏
          '#3B82F6', // 蓝色 - 舞蹈
          '#10B981', // 绿色 - 跑步
          '#F59E0B', // 橙色 - 宠物
          '#EF4444', // 红色 - 邻里服务
          '#6366F1', // 靛蓝 - 育儿互助
          '#8B5CF6', // 紫色变体 - 其他话题
        ]
        return {
          name: item.name,
          value: item.value,
          percentage: item.percentage,
          itemStyle: {
            color: colors[index % colors.length]
          }
        }
      })
    }
  ]
}))
</script>

<style lang="scss" scoped>
.echarts-community-life-trend {
  width: 100%;
  height: 240px;

  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
