<template>
  <div class="service-overview">
    <div class="service-item">
      <div class="service-icon">
        <img src="@/assets/doctor/x1.png" alt="家庭医生" />
      </div>
      <div class="service-content">
        <div class="service-label">家庭医生数</div>
        <div class="service-value">{{ serviceData.doctorCount || 0 }} <span class="unit">人</span></div>
      </div>
    </div>
    
    <div class="service-item">
      <div class="service-icon">
        <img src="@/assets/doctor/x2.png" alt="居民人数" />
      </div>
      <div class="service-content">
        <div class="service-label">居民人数</div>
        <div class="service-value">{{ serviceData.residentCount || 0 }} <span class="unit">人</span></div>
      </div>
    </div>
    
    <div class="service-item">
      <div class="service-icon">
        <img src="@/assets/doctor/x3.png" alt="签约人数" />
      </div>
      <div class="service-content">
        <div class="service-label">签约人数</div>
        <div class="service-value orange">{{ serviceData.signupCount || 0 }} <span class="unit">人</span></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  serviceData: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
.service-overview {
  display: flex;
  justify-content: space-between;
  gap: 0.260vw;
  /* margin-top: 0.520vw; */
  
  @media (max-width: 2000px) {
    gap: 0.416vw;
    /* margin-top: 0.833vw; */
  }
}

.service-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 0.026vw solid rgba(0, 212, 255, 0.3);
  border-radius: 0.208vw;
  background: rgba(0, 212, 255, 0.1);
  transition: all 0.3s ease;
  
  
}

.service-item:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: translateX(0.078vw);
  
  @media (max-width: 2000px) {
    transform: translateX(0.125vw);
  }
}

.service-icon {
  margin-right: 0.390vw;
  
  @media (max-width: 2000px) {
    margin-right: 0.625vw;
  }
}

.service-icon img {
  width: 0.833vw;
  height: 0.833vw;
  
  @media (max-width: 2000px) {
    width: 1.333vw;
    height: 1.333vw;
  }
}

.service-content {
  flex: 1;
}

.service-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.130vw;
  
  
}

.service-value {
  font-size: 13px;
  font-weight: bold;
  color: #00FFFF;
  
  
  
  &.orange {
    color: #FF9F18;
  }
}

.unit {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 0.052vw;
  
  
}
</style> 