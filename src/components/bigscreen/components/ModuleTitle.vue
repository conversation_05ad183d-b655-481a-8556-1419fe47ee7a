<script setup lang="ts">
defineProps<{
  title: string
}>()
</script>

<template>
  <div class="title-2">
    <img src="@/assets/common/corner.png" alt="" class="corner-icon">
    <span>{{ title }}</span>
  </div>
</template>

<style lang="scss" scoped>
.title-2 {
  display: flex;
  align-items: center;
  margin:10px 0;

  .corner-icon {
    width: 25px;
    height: 25px;
    margin-right: 0.208vw;
    

  }

  span {
    font-size: 16px;
    font-weight: 600;
    color: #FFFFFF;
    
  
  }
}
</style> 