<template>
  <div class="information-table">
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">数据加载中...</div>
    </div>
    <div v-else class="table-container">
      <div class="table-header">
        <div class="header-col area">所属区域</div>
        <div class="header-col title">信息公告标题</div>
        <div class="header-col time">发布时间</div>
      </div>
      <div class="table-body">
        <div 
          v-if="tableData.length === 0" 
          class="no-data"
        >
          暂无数据
        </div>
        <div 
          v-else
          v-for="(item, index) in tableData" 
          :key="index" 
          class="table-row"
        >
          <div class="table-col area">{{ item.area }}</div>
          <div class="table-col title" :title="item.title">{{ item.title }}</div>
          <div class="table-col time">{{ item.publishTime }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface TableDataItem {
  area: string
  title: string
  publishTime: string
}

interface Props {
  tableData: TableDataItem[]
  loading?: boolean
}

withDefaults(defineProps<Props>(), {
  loading: false
})
</script>

<style lang="scss" scoped>
.information-table {
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    
    .loading-spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(0, 212, 255, 0.3);
      border-top: 3px solid #00D4FF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    .loading-text {
      margin-top: 10px;
      color: #9D9DA6;
      font-size: 12px;
    }
  }

  .table-container {
    border: 1px solid #394B5C;
    border-radius: 4px;
    overflow: hidden;
    
    .table-header {
      display: grid;
      grid-template-columns: 1fr 2fr 1fr;
      background: rgba(23, 81, 159, 0.3);
      border-bottom: 1px solid #394B5C;
      
      .header-col {
        padding: 12px 8px;
        font-size: 12px;
        font-weight: bold;
        color: #fff;
        text-align: center;
        border-right: 1px solid #394B5C;
        
        &:last-child {
          border-right: none;
        }
      }
    }
    
    .table-body {
      max-height: 240px;
      overflow-y: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;
      
      &::-webkit-scrollbar {
        display: none;
      }
      
      .no-data {
        padding: 40px;
        text-align: center;
        color: #9D9DA6;
        font-size: 14px;
      }
      
      .table-row {
        display: grid;
        grid-template-columns: 1fr 2fr 1fr;
        border-bottom: 1px solid #394B5C;
        transition: background-color 0.3s ease;
        
        &:hover {
          background: rgba(23, 81, 159, 0.1);
        }
        
        &:last-child {
          border-bottom: none;
        }
        
        .table-col {
          padding: 10px 8px;
          font-size: 11px;
          color: #fff;
          border-right: 1px solid #394B5C;
          display: flex;
          align-items: center;
          
          &:last-child {
            border-right: none;
          }
          
          &.area {
            justify-content: center;
            color: #9D9DA6;
          }
          
          &.title {
            color: #fff;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            
            &:hover {
              color: #00D4FF;
            }
          }
          
          &.time {
            justify-content: center;
            color: #FFA500;
          }
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 