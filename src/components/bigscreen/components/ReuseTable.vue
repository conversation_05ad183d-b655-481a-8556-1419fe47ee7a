<template>
  <div class="reuse-table" v-loading="loading">
    <el-table
      :data="tableData"
      style="width: 100%"
      :show-header="true"
      :max-height="computedMaxHeight"
      :empty-text="''"
    >
      <template v-for="column in columns" :key="column.prop">
        <el-table-column
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          align="center"
        >
          <template #default="scope">
            <span :class="getColumnClass(column.prop)">
              {{ scope.row[column.prop] }}
            </span>
          </template>
        </el-table-column>
      </template>
    </el-table>
    
    <div class="table-empty" v-if="!loading && tableData.length === 0">
      <span>暂无数据</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
interface Column {
  prop: string
  label: string
  width?: string | number
}

const props = defineProps({
  tableData: {
    type: Array as () => any[],
    required: true
  },
  columns: {
    type: Array as () => Column[],
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  maxHeight: {
    type: [String, Number],
    default: 'auto'
  }
})

function getColumnClass(prop: string) { 
  if (prop === 'browseNumber' || prop === 'count' || prop === 'views') {
    return 'number-cell'
  }
  return 'text-cell'
}

// 计算表格的最大高度
const computedMaxHeight = computed(() => {
  if (props.maxHeight === 'auto') {
    return undefined
  }
  return props.maxHeight
})
</script>

<style scoped>
.reuse-table {
  margin-top: 15px;
  background: transparent !important;
}

.table-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 3.906vw;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.364vw;
  
  @media (max-width: 2000px) {
    height: 6.25vw;
    font-size: 0.583vw;
  }
}

:deep(.el-table) {
  background: transparent !important;
  color: rgba(255, 255, 255, 0.9);
  border: none !important;
}

:deep(.el-table__inner-wrapper) {
  background: transparent !important;
}

:deep(.el-table__body-wrapper) {
  background: transparent !important;
}

:deep(.el-table__header-wrapper) {
  background: transparent !important;
}

:deep(.el-table .el-table__body) {
  background: transparent !important;
}

:deep(.el-table .el-table__header) {
  background: transparent !important;
}

:deep(.el-table th) {
  background: rgba(0, 212, 255, 0.1) !important;
  border: 0.026vw solid rgba(0, 212, 255, 0.2) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  /* font-size: 0.312vw !important; */
  padding: 0.208vw 0 !important;
  

}

:deep(.el-table td) {
  background: transparent !important;
  border: 0.026vw solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.8) !important;
  /* font-size: 0.286vw !important; */
  padding: 0.156vw 0 !important;
  
  @media (max-width: 2000px) {
    border: 0.041vw solid rgba(255, 255, 255, 0.1) !important;
    /* font-size: 0.458vw !important; */
    padding: 0.25vw 0 !important;
  }
}

:deep(.el-table tbody tr) {
  background: transparent !important;
}

:deep(.el-table thead tr) {
  background: transparent !important;
}

:deep(.el-table::before) {
  display: none;
}

:deep(.el-table tr:hover > td) {
  background: rgba(0, 212, 255, 0.1) !important;
}

:deep(.el-loading-mask) {
  background: rgba(0, 0, 0, 0.5) !important;
}

.text-cell {
  color: rgba(255, 255, 255, 0.8);
}

.number-cell {
  color: #00FFFF;
  font-weight: 500;
}
</style> 