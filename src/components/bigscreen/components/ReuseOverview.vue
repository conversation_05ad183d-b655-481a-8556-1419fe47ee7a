<template>
  <div class="reuse-overview">
    <div class="overview-item orange-border">
      <div class="overview-icon">
        <img src="@/assets/common/light.png" alt="物品数量" />
      </div>
      <div class="overview-content">
        <div class="overview-label">{{ titleData.itemsTitle }}</div>
        <div class="overview-value">{{ overviewData.itemsCount || 0 }} <span class="unit">{{ titleData.itemsUnit || '个' }}</span></div>
      </div>
    </div>
    
    <div class="overview-item cyan-border">
      <div class="overview-icon">
        <img src="@/assets/common/light.png" alt="浏览量" />
      </div>
      <div class="overview-content">
        <div class="overview-label">{{ titleData.clicksTitle }}</div>
        <div class="overview-value cyan">{{ overviewData.clicksCount || 0 }} <span class="unit">{{ titleData.clicksUnit || '次' }}</span></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  overviewData: {
    type: Object,
    required: true
  },
  titleData: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
.reuse-overview {
  display: flex;
  justify-content: space-between;
  gap: 0.390vw;
  margin-top: 0.390vw;
  
  @media (max-width: 2000px) {
    gap: 0.625vw;
    margin-top: 0.625vw;
  }
}

.overview-item {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 0.520vw 0.390vw;
  border-radius: 0.208vw;
  background: rgba(255, 255, 255, 0.05);
  border: 0.026vw solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  width: 140px;
  /* height: 68px; */
}

.overview-item:hover {
  transform: translateY(-0.078vw);
  box-shadow: 0 0.156vw 0.520vw rgba(0, 212, 255, 0.3);
  
  @media (max-width: 2000px) {
    transform: translateY(-0.125vw);
    box-shadow: 0 0.25vw 0.833vw rgba(0, 212, 255, 0.3);
  }
}

.overview-item.orange-border {
  border-color: rgba(255, 159, 24, 0.4);
}

.overview-item.cyan-border {
  border-color: rgba(0, 212, 255, 0.4);
}

.overview-icon {
  margin-right: 0.390vw;
  flex-shrink: 0;
  
  @media (max-width: 2000px) {
    margin-right: 0.625vw;
  }
}

.overview-icon img {
  width: 1.041vw;
  height: 1.041vw;
  
  @media (max-width: 2000px) {
    width: 1.666vw;
    height: 1.666vw;
  }
}

.overview-content {
  flex: 1;
  min-width: 0;
}

.overview-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10px;
  
  
}

.overview-value {
  font-size: 13px;
  font-weight: bold;
  color: #FF9F18;
  
 
  
  &.cyan {
    color: #00FFFF;
  }
}

.unit {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 0.130vw;
  
}
</style> 