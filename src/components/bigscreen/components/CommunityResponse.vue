<template>
  <div class="response-container">
    <div class="response-item">
      <div class="response-icon">
        <img src="@/assets/common/light.png" alt="来电" />
      </div>
      <div class="response-content">
        <div class="response-label">来电数</div>
        <div class="response-value">{{ responseData.callCount || 0 }}</div>
      </div>
    </div>
    
    <div class="response-item">
      <div class="response-icon">
        <img src="@/assets/common/light.png" alt="服务人数" />
      </div>
      <div class="response-content">
        <div class="response-label">服务人数</div>
        <div class="response-value">{{ responseData.serviceCount || 0 }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  responseData: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
.response-container {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.response-item {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 20px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  background: rgba(0, 212, 255, 0.1);
  transition: all 0.3s ease;
}

.response-item:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
}

.response-icon {
  margin-right: 15px;
}

.response-icon img {
  width: 32px;
  height: 32px;
}

.response-content {
  flex: 1;
}

.response-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 5px;
}

.response-value {
  font-size: 24px;
  font-weight: bold;
  color: #00FFFF;
}
</style> 