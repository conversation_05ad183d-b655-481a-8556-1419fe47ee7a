<template>
  <Teleport to="body">
    <div v-if="visible" class="device-list-modal" @click="handleMaskClick">
      <div class="device-list-dialog" @click.stop>
      <!-- 弹窗头部 -->
      <div class="dialog-header">
        <div class="dialog-title">{{ deviceTypeName }}</div>
        <div class="close-btn" @click="handleClose">×</div>
      </div>

      <!-- 弹窗内容 -->
      <div class="dialog-content">
        <!-- 搜索表单 -->
        <div class="search-form">
          <el-form ref="ruleFormRef" :inline="true" :model="form">
            <el-form-item prop="deviceId">
              <el-input 
                v-model="form.deviceId" 
                placeholder="设备SN" 
                clearable 
                @clear="handleDeviceIdClear"
              />
            </el-form-item>
            <el-form-item prop="parkId">
              <el-select 
                v-model="form.parkId" 
                placeholder="所在小区" 
                clearable
                style="width: 140px;"
                @change="handleParkChange"
              >
                <el-option 
                  v-for="park in parkList" 
                  :key="park.ParkId" 
                  :label="park.Name" 
                  :value="park.ParkId"
                />
              </el-select>
            </el-form-item>
            <!-- <el-form-item prop="dateRangeMin">
              <el-date-picker 
                v-model="form.dateRangeMin" 
                type="date" 
                placeholder="开始日期"
                format="YYYY-M-D"
                value-format="YYYY-M-D"
                :teleported="false"
                :popper-class="'date-picker-popper'"
                clearable
              />
            </el-form-item>
            <el-form-item prop="dateRangeMax">
              <el-date-picker 
                v-model="form.dateRangeMax" 
                type="date" 
                placeholder="结束日期"
                format="YYYY-M-D"
                value-format="YYYY-M-D"
                :teleported="false"
                :popper-class="'date-picker-popper'"
                clearable
              />
            </el-form-item> -->
            <el-form-item>
              <el-button class="submit-btn" type="primary" :icon="Search" @click="onSubmit()">
                查询
              </el-button>
              <el-button class="reset-btn" :icon="Refresh" @click="resetForm(ruleFormRef)">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 设备列表表格 -->
        <div class="table-box">
          <el-table
            :max-height="'calc(100vh - 280px)'"
            :data="deviceList"
            v-loading="loading"
            element-loading-text="加载中..."
            element-loading-background="rgba(0, 0, 0, 0.6)"
            class="device-table"
          >
            <el-table-column label="设备名称" prop="DeviceName" align="center" min-width="200">
              <template #default="{ row }">
                {{ row.DeviceName || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="所属区域" prop="AreaName" align="center" width="120">
              <template #default="{ row }">
                {{ row.AreaName || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="设备编号" prop="DeviceSN" align="center" width="180">
              <template #default="{ row }">
                {{ row.DeviceSN || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="位置坐标" prop="Position" align="center" width="150">
              <template #default="{ row }">
                {{ row.Position || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="设备状态" align="center" width="90">
              <template #default="scope">
                <div>
                  <span v-if="scope.row.DeviceState === 1" class="online">在线</span>
                  <span v-else class="offline">离线</span>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="page-box">
            <el-config-provider :locale="zhCn">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 30, 50]"
                :small="false"
                :disabled="false"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalPage"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </el-config-provider>
          </div>
        </div>
      </div>
    </div>

    <!-- 地图弹窗 -->
    <el-dialog 
      title="位置坐标" 
      v-model="dialogVisible" 
      width="600px"
      :close-on-click-modal="false"
      class="map-dialog"
    >
      <div id="map" style="height: 400px;"></div>
    </el-dialog>


    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, onUnmounted } from 'vue'
import { ElMessage, ElConfigProvider } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { getDCDeviceListApi, getParkListApi, getDeviceTypeStatisticsApi } from '@/api/bigscreen'
import type { DCDeviceListParams, GetDeviceTypeStatisticsParams } from '@/api/bigscreen'
import AMapLoader from '@amap/amap-jsapi-loader'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

interface Props {
  visible: boolean
  deviceTypeName: string
  deviceType: number
  areaCode: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const ruleFormRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  deviceId: '',
  parkId: null as number | null,
  dateRangeMin: '',
  dateRangeMax: ''
})

// 推送状态选项已直接在模板中定义

// 小区列表
const parkList = ref<Array<{ ParkId: number; Name: string }>>([])

// 设备列表
const deviceList = ref<Array<any>>([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalPage = ref(0)
const loading = ref(false)

// 弹窗状态
const dialogVisible = ref(false)

// 地图实例
let map: any = null

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    getParkList()
    getDCDeviceList()
  }
})

// 监听小区选择变化，自动刷新列表
watch(() => form.parkId, (newVal, oldVal) => {
  // 只有在弹窗显示状态下且值确实发生变化时才刷新
  if (props.visible && newVal !== oldVal) {
    console.log('🏘️ 小区选择发生变化:', oldVal, '->', newVal)
    currentPage.value = 1 // 重置到第一页
    getDCDeviceList()
  }
})

// 处理小区选择变化
const handleParkChange = (value: number | null) => {
  console.log('🏘️ 小区选择器变化事件:', value)
  if (value) {
    const selectedPark = parkList.value.find(park => park.ParkId === value)
    console.log('🏘️ 选择的小区:', selectedPark)
  } else {
    console.log('🏘️ 清空小区选择')
  }
}

// 处理设备SN清空
const handleDeviceIdClear = () => {
  console.log('🔍 设备SN已清空，自动刷新列表')
  if (props.visible) {
    currentPage.value = 1 // 重置到第一页
    getDCDeviceList()
  }
}

// 获取小区列表
const getParkList = async () => {
  try {
    console.log('🏘️ 开始获取小区列表，区域代码:', props.areaCode)
    const response = await getParkListApi({ areaCode: props.areaCode }) as any
    console.log('🏘️ 小区列表API响应:', response)
    
    if (response && response.state === 1 && Array.isArray(response.message)) {
      parkList.value = response.message.Rows
      console.log('✅ 小区列表加载成功，共', parkList.value.length, '个小区:', parkList.value)
    } else {
      console.warn('⚠️ 小区列表API返回数据格式异常:', response)
      throw new Error('API返回数据格式异常')
    }
  } catch (error) {
    console.error('❌ 获取小区列表失败:', error)
    // 使用模拟数据
    parkList.value = [
      { ParkId: 3390, Name: '东岳新居' },
      { ParkId: 3391, Name: '东岳臻苑' },
      { ParkId: 3395, Name: '临江社区小区' },
      { ParkId: 3388, Name: '博盛康郡' },
      { ParkId: 3301, Name: '太阳新居小区' }
    ]
    console.log('🔄 使用模拟小区数据，共', parkList.value.length, '个小区:', parkList.value)
  }
}

// 获取设备列表
const getDCDeviceList = async () => {
  try {
    loading.value = true
    
    const params: GetDeviceTypeStatisticsParams = {
      areaCode: props.areaCode,
      deviceType: props.deviceType
    }
    
    console.log('🔍 设备类型统计查询参数:', params)

    const response = await getDeviceTypeStatisticsApi(params)
    
    console.log('🔍 设备类型统计API响应:', response)
    
    if (response && response.state === 1 && response.message) {
      // 根据实际返回的数据结构处理
      deviceList.value = response.message.List || []
      totalPage.value = response.message.Count || 0
      console.log('✅ 设备类型统计更新成功，共', deviceList.value.length, '条数据')
    } else {
      console.warn('⚠️ 设备类型统计API返回数据格式异常:', response)
      throw new Error('API返回数据格式异常')
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
    // ElMessage.warning('获取设备列表失败，使用模拟数据')
    
    // 使用模拟数据
    deviceList.value = [
      {
        Id: 1,
        DeviceSN: 'DT001',
        DeviceName: '电梯监控1',
        Position: '116.397428,39.90923',
        AreaName: '博盛康郡',
        DeviceState: 1
      },
      {
        Id: 2,
        DeviceSN: 'DT002',
        DeviceName: '电梯监控2',
        Position: '116.407428,39.91923',
        AreaName: '阳光小区',
        DeviceState: 2
      },
      {
        Id: 3,
        DeviceSN: 'YW001',
        DeviceName: '烟雾传感器1',
        Position: '116.387428,39.89923',
        AreaName: '和谐家园',
        DeviceState: 1
      }
    ]
    totalPage.value = deviceList.value.length
  } finally {
    loading.value = false
  }
}

// 查询
const onSubmit = () => {
  currentPage.value = 1
  getDCDeviceList()
}

// 重置表单
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  currentPage.value = 1
  getDCDeviceList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  console.log(`${val} items per page`)
  pageSize.value = val
  currentPage.value = 1
  loading.value = true
  getDCDeviceList()
}

const handleCurrentChange = (val: number) => {
  console.log(`current page: ${val}`)
  currentPage.value = val
  loading.value = true
  getDCDeviceList()
}

// 显示地图
const showMap = (position: string) => {
  dialogVisible.value = true
  nextTick(() => {
    initMap(position)
  })
}

// 初始化地图
const initMap = (position: string) => {
  // 高德地图key，实际使用时需要替换为真实的key
  const mapKey = '您的高德地图key'
  
  AMapLoader.load({
    key: mapKey,
    version: '2.0',
    plugins: [
      'AMap.DistrictSearch',
      'AMap.AutoComplete',
      'AMap.PlaceSearch',
      'AMap.InfoWindow',
      'AMap.MouseTool',
      'AMap.PolyEditor'
    ]
  }).then((AMap: any) => {
    map = new AMap.Map('map', {
      viewMode: '3D',
      zoom: 15,
      center: [116.397428, 39.90923]
    })

    const pos = position.split(',')
    
    if (pos.length >= 2) {
      const marker = new AMap.Marker({
        icon: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png',
        position: new AMap.LngLat(parseFloat(pos[0]), parseFloat(pos[1])),
        offset: new AMap.Pixel(-13, -30)
      })
      marker.setMap(map)
      map.setFitView(marker)
    }
  }).catch((error: any) => {
    console.error('地图加载失败:', error)
    ElMessage.error('地图加载失败，请检查网络连接或地图API密钥')
  })
}



// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 点击遮罩关闭
const handleMaskClick = () => {
  emit('update:visible', false)
}

// 组件销毁时清理地图
onUnmounted(() => {
  if (map) {
    map.destroy()
  }
})
</script>

<style lang="scss" scoped>
.device-list-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
  
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, and Opera */
  }
}

.device-list-dialog {
  width: 1200px;
  max-width: 95vw;
  max-height: 95vh;
  background: #060E1F;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(74, 144, 226, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  background: rgba(23, 81, 159, 0.1);

  .dialog-title {
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
  }

  .close-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #ffffff;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    background: transparent;
    border: none;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: #ff6b6b;
    }
  }
}

.dialog-content {
  flex: 1;
  padding: 20px;
  background: #060E1F;
  overflow-y: auto;
  
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, and Opera */
  }
}

.search-form {
  padding: 16px 24px;
  border-bottom: 1px solid rgba(74, 144, 226, 0.2);
  background: rgba(23, 81, 159, 0.05);
  margin: 0;
  margin-bottom: 0;
  
  :deep(.el-form-item) {
    margin-bottom: 10px;
    margin-right: 15px;
  }
  
  :deep(.el-input) {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      box-shadow: none !important;
      
      &:hover {
        border-color: rgba(255, 255, 255, 0.4) !important;
      }
      
      &.is-focus {
        border-color: #4A90E2 !important;
      }
    }
    
    .el-input__inner {
      background: transparent !important;
      border: none !important;
      color: #ffffff !important;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }
    }
    
    .el-input__suffix,
    .el-input__prefix {
      .el-input__suffix-inner,
      .el-input__prefix-inner {
        color: rgba(255, 255, 255, 0.7) !important;
      }
    }
  }
  
  :deep(.el-select) {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      box-shadow: none !important;
      
      &:hover {
        border-color: rgba(255, 255, 255, 0.4) !important;
      }
      
      &.is-focus {
        border-color: #4A90E2 !important;
      }
    }
    
    .el-input__inner {
      background: transparent !important;
      border: none !important;
      color: #ffffff !important;
    }
    
    .el-select__caret {
      color: rgba(255, 255, 255, 0.7) !important;
    }
    
    .el-select__placeholder {
      color: rgba(255, 255, 255, 0.5) !important;
    }
  }
  
  :deep(.el-date-editor) {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      box-shadow: none !important;
    }
    
    .el-input__inner {
      background: transparent !important;
      border: none !important;
      color: #ffffff !important;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }
    }
    
    .el-input__prefix,
    .el-input__suffix {
      color: rgba(255, 255, 255, 0.7) !important;
    }
    
    .el-input__prefix-inner,
    .el-input__suffix-inner {
      color: rgba(255, 255, 255, 0.7) !important;
    }
  }
  
  .submit-btn {
    background: #4A90E2;
    border-color: #4A90E2;
    
    &:hover {
      background: #357ABD;
      border-color: #357ABD;
    }
  }
  
  .reset-btn {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
    }
  }
}

.table-box {
  /* 隐藏表格容器滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, and Opera */
  }
  
  .table-box {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    /* 隐藏表格滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, and Opera */
    }
  }

  .device-table {
    flex: 1;
    overflow: hidden;
    
    :deep(.el-table) {
      background: #060E1F !important;
      border: 1px solid rgba(255, 255, 255, 0.1);
      
      /* 隐藏Element表格内部滚动条 */
      .el-table__body-wrapper {
        scrollbar-width: none !important; /* Firefox */
        -ms-overflow-style: none !important; /* IE and Edge */
        
        &::-webkit-scrollbar {
          display: none !important; /* Chrome, Safari, and Opera */
        }
      }
      
      .el-table__header-wrapper {
        scrollbar-width: none !important; /* Firefox */
        -ms-overflow-style: none !important; /* IE and Edge */
        
        &::-webkit-scrollbar {
          display: none !important; /* Chrome, Safari, and Opera */
        }
      }
      
      .el-table__header {
        background: #060E1F !important;
        
        th {
          background: #060E1F !important;
          color: #ffffff !important;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
          border-right: 1px solid rgba(255, 255, 255, 0.05) !important;
        }
      }
      
      .el-table__body {
        background: #060E1F !important;
        
        tr {
          background: #060E1F !important;
          
          td {
            background: #060E1F !important;
            color: #ffffff !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
            border-right: 1px solid rgba(255, 255, 255, 0.05) !important;
          }
          
          &:hover {
            td {
              background: rgba(74, 144, 226, 0.1) !important;
            }
          }
        }
      }
      
      .el-table__empty-block {
        background: #060E1F !important;
        color: #ffffff !important;
      }
      
      .el-table__empty-text {
        color: rgba(255, 255, 255, 0.6) !important;
      }
    }
    
    .link-btn {
      color: #4A90E2;
      cursor: pointer;
      
      &:hover {
        color: #357ABD;
        text-decoration: underline;
      }
    }
    

    
    .online {
      color: #4CAF50;
      font-weight: 500;
    }
    
    .offline {
      color: #F44336;
      font-weight: 500;
    }
  }
  
  .page-box {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    
    :deep(.el-pagination) {
      background: transparent !important;
      
      .el-pager li {
        background: rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        
        &.active {
          background: #4A90E2 !important;
          border-color: #4A90E2 !important;
          color: #ffffff !important;
        }
        
        &:hover {
          background: rgba(255, 255, 255, 0.2) !important;
        }
      }
      
      .btn-prev, .btn-next {
        background: rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        
        &:hover {
          background: rgba(255, 255, 255, 0.2) !important;
        }
        
        &:disabled {
          background: rgba(255, 255, 255, 0.05) !important;
          color: rgba(255, 255, 255, 0.3) !important;
          border-color: rgba(255, 255, 255, 0.1) !important;
        }
      }
      
      .el-select {
        .el-input__wrapper {
          background: rgba(255, 255, 255, 0.1) !important;
          border: 1px solid rgba(255, 255, 255, 0.2) !important;
          box-shadow: none !important;
        }
        
        .el-input__inner {
          background: transparent !important;
          border: none !important;
          color: #ffffff !important;
        }
        
        .el-select__caret {
          color: #ffffff !important;
        }
      }
      
      .el-pagination__total {
        color: #ffffff !important;
      }
      
      .el-pagination__jump {
        color: #ffffff !important;
      }
    }
  }
}

/* 全局强制覆盖样式 - 确保输入框和选择框都是深色 */
.device-list-modal .el-input__wrapper,
.device-list-modal .el-textarea__inner,
.device-list-modal .el-select .el-input__wrapper {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: none !important;
}

.device-list-modal .el-input__inner,
.device-list-modal .el-textarea__inner {
  background: transparent !important;
  color: #ffffff !important;
}

.device-list-modal .el-input__inner::placeholder,
.device-list-modal .el-textarea__inner::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

.device-list-modal .el-select__caret {
  color: rgba(255, 255, 255, 0.7) !important;
}

.device-list-modal .el-date-editor .el-input__wrapper {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* 超级强制覆盖 el-select 样式 */
.device-list-modal .el-select {
  .el-input {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      box-shadow: none !important;
    }
    
    .el-input__inner {
      background: transparent !important;
      color: #ffffff !important;
    }
  }
}
:deep(.el-select__wrapper) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: none !important;
}
/* 直接覆盖所有可能的选择器 */
.el-select .el-input__wrapper,
.el-select__wrapper {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: none !important;
}

.el-select .el-input__inner {
  background: transparent !important;
  color: #ffffff !important;
}

.el-select__caret {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 专门针对 el-select__wrapper */
.el-select__wrapper {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: none !important;
}

/* 针对弹窗内的所有select */
.device-list-dialog .el-select,
.device-list-modal .el-select {
  .el-input__wrapper {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: none !important;
  }
  
  .el-input__inner {
    background: transparent !important;
    color: #ffffff !important;
  }
  
  .el-select__caret {
    color: rgba(255, 255, 255, 0.7) !important;
  }
  
  .el-select__placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
  }
}

/* 弹窗内的 el-select__wrapper */
.device-list-dialog .el-select__wrapper,
.device-list-modal .el-select__wrapper {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: none !important;
}

/* 下拉选项弹出框样式 - 使用更强的选择器 */
.el-select-dropdown,
.el-popper[data-popper-placement] {
  background: #1a1a2e !important;
  border: 1px solid #409eff !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.8) !important;
  z-index: 999999 !important;
}

.el-select-dropdown .el-select-dropdown__item,
.el-popper .el-select-dropdown__item {
  background: #1a1a2e !important;
  color: #ffffff !important;
  font-size: 14px !important;
  line-height: 34px !important;
  padding: 8px 20px !important;
  min-height: 34px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.el-select-dropdown .el-select-dropdown__item:hover,
.el-popper .el-select-dropdown__item:hover {
  background: #409eff !important;
  color: #ffffff !important;
}

.el-select-dropdown .el-select-dropdown__item.selected,
.el-popper .el-select-dropdown__item.selected {
  background: #409eff !important;
  color: #ffffff !important;
  font-weight: bold !important;
}

.el-select-dropdown .el-select-dropdown__item.disabled,
.el-popper .el-select-dropdown__item.disabled {
  color: rgba(255, 255, 255, 0.3) !important;
  background: transparent !important;
}

/* 确保下拉框显示在最上层 */
.el-popper {
  z-index: 999999 !important;
}

.el-popper.is-pure {
  background: #1a1a2e !important;
  border: 1px solid #409eff !important;
}

/* 自定义下拉框样式 - 强制显示 */
.custom-select-dropdown {
  background: #1a1a2e !important;
  border: 2px solid #409eff !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
  z-index: 999999 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.custom-select-dropdown .el-select-dropdown__list {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  max-height: none !important;
  overflow: visible !important;
}

.custom-select-dropdown .el-select-dropdown__item {
  background: #1a1a2e !important;
  color: #ffffff !important;
  padding: 12px 20px !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  min-height: 40px !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.custom-select-dropdown .el-select-dropdown__item:hover {
  background: #409eff !important;
  color: #ffffff !important;
}

.custom-select-dropdown .el-select-dropdown__item.selected {
  background: #409eff !important;
  color: #ffffff !important;
  font-weight: bold !important;
}

/* 确保下拉框正确定位 */
.custom-select-dropdown {
  transform: none !important;
}

/* 下拉选项样式 - 确保z-index足够高 */
.el-select-dropdown {
  z-index: 999999 !important;
  background: #1a1a2e !important;
  border: 1px solid #409eff !important;
}

.el-select-dropdown .el-select-dropdown__item {
  background: #1a1a2e !important;
  color: #ffffff !important;
  padding: 8px 16px !important;
}

.el-select-dropdown .el-select-dropdown__item:hover {
  background: #409eff !important;
  color: #ffffff !important;
}

.el-select-dropdown .el-select-dropdown__item.selected {
  background: #409eff !important;
  color: #ffffff !important;
}

.el-popper {
  z-index: 999999 !important;
}

/* 日期选择器弹出框样式 */
.el-picker-panel {
  background: #060E1F !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
}

.el-picker-panel .el-date-picker__header {
  background: transparent !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.el-picker-panel .el-date-picker__header-label {
  color: #ffffff !important;
}

.el-picker-panel .el-date-table td {
  color: #ffffff !important;
}

.el-picker-panel .el-date-table td.available:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.el-picker-panel .el-date-table td.current:not(.disabled) {
  background: rgba(64, 158, 255, 0.3) !important;
  color: #409eff !important;
}

// 深色模式全局覆盖
.device-list-dialog {
  // 覆盖Element Plus的默认样式
  .el-table {
    --el-table-bg-color: #060E1F !important;
    --el-table-tr-bg-color: #060E1F !important;
    --el-table-header-bg-color: #060E1F !important;
    --el-table-row-hover-bg-color: rgba(74, 144, 226, 0.15) !important;
    --el-table-text-color: #ffffff !important;
    --el-table-header-text-color: #ffffff !important;
    --el-table-border-color: rgba(255, 255, 255, 0.1) !important;
  }

  // 分页组件深色样式
  .el-pagination {
    --el-pagination-bg-color: #060E1F !important;
    --el-pagination-text-color: #ffffff !important;
    --el-pagination-border-color: rgba(255, 255, 255, 0.1) !important;
    --el-pagination-hover-color: #4a90e2 !important;
  }

  // 表单组件深色样式
  .el-form-item__label {
    color: #ffffff !important;
  }

  // 输入框样式
  .el-input {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      box-shadow: none !important;

      &:hover {
        border-color: rgba(255, 255, 255, 0.4) !important;
      }

      &.is-focus {
        border-color: #4A90E2 !important;
        box-shadow: 0 0 0 1px rgba(74, 144, 226, 0.2) !important;
      }
    }

    .el-input__inner {
      background: transparent !important;
      border: none !important;
      color: #ffffff !important;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }
    }
  }

  // 选择器样式
  .el-select {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      box-shadow: none !important;

      &:hover {
        border-color: rgba(255, 255, 255, 0.4) !important;
      }

      &.is-focus {
        border-color: #4A90E2 !important;
        box-shadow: 0 0 0 1px rgba(74, 144, 226, 0.2) !important;
      }
    }

    .el-input__inner {
      background: transparent !important;
      border: none !important;
      color: #ffffff !important;
    }

    .el-select__caret {
      color: rgba(255, 255, 255, 0.7) !important;
    }

    .el-select__placeholder {
      color: rgba(255, 255, 255, 0.5) !important;
    }
  }
}

/* 全局隐藏DeviceListDialog弹窗内的所有滚动条 */
.device-list-modal {
  * {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none !important; /* Chrome, Safari, and Opera */
    }
  }
}
</style>

// 下拉选项样式
<style lang="scss">
.el-select-dropdown {
  background: #060E1F !important;
  border: 1px solid rgba(74, 144, 226, 0.3) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.8) !important;
  z-index: 999999 !important;

  .el-select-dropdown__item {
    background: #060E1F !important;
    color: #ffffff !important;
    padding: 8px 16px !important;

    &:hover {
      background: rgba(74, 144, 226, 0.2) !important;
      color: #ffffff !important;
    }

    &.selected {
      background: #4a90e2 !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }
  }
}

// 强制覆盖所有可能的白色背景
:deep(.el-popper) {
  background: #060E1F !important;
  border: 1px solid rgba(74, 144, 226, 0.3) !important;

  .el-select-dropdown__item {
    background: #060E1F !important;
    color: #ffffff !important;

    &:hover {
      background: rgba(74, 144, 226, 0.3) !important;
    }

    &.is-selected {
      background: #4A90E2 !important;
      color: #ffffff !important;
    }
  }
}
</style>

<style lang="scss">
// 全局样式，用于弹窗组件
.map-dialog {
  .el-dialog {
    background: #060E1F;
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 999999 !important;
    
    .el-dialog__header {
      background: #060E1F;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      
      .el-dialog__title {
        color: #ffffff;
      }
      
      .el-dialog__headerbtn {
        .el-dialog__close {
          color: #ffffff;
        }
      }
    }
    
    .el-dialog__body {
      background: #060E1F;
      padding: 20px;
    }
  }
}

// 确保Element Plus的弹窗组件始终显示在最顶层
.el-dialog__wrapper {
  z-index: 999999 !important;
}

// 全局强制弹窗层级
:global(.device-list-modal) {
  z-index: 999999 !important;
  position: fixed !important;
  inset: 0 !important;
}

// 确保所有弹窗相关元素都在最顶层
:global(.el-overlay),
:global(.el-overlay-dialog),
:global(.el-dialog__wrapper),
:global(.el-dialog),
:global(.el-popper),
:global(.el-select-dropdown),
:global(.el-tooltip__popper) {
  z-index: 999999 !important;
}



// 强制覆盖所有输入框样式
.device-list-dialog {
  .el-input {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      box-shadow: none !important;
      
      &:hover {
        border-color: rgba(255, 255, 255, 0.4) !important;
      }
      
      &.is-focus {
        border-color: #4A90E2 !important;
        box-shadow: 0 0 0 1px rgba(74, 144, 226, 0.2) !important;
      }
    }
    
    .el-input__inner {
      background: transparent !important;
      border: none !important;
      color: #ffffff !important;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }
    }
  }
  
  .el-select {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      box-shadow: none !important;
      
      &:hover {
        border-color: rgba(255, 255, 255, 0.4) !important;
      }
      
      &.is-focus {
        border-color: #4A90E2 !important;
        box-shadow: 0 0 0 1px rgba(74, 144, 226, 0.2) !important;
      }
    }
    
    .el-input__inner {
      background: transparent !important;
      border: none !important;
      color: #ffffff !important;
    }
    
    .el-select__caret {
      color: rgba(255, 255, 255, 0.7) !important;
    }
    
    .el-select__placeholder {
      color: rgba(255, 255, 255, 0.5) !important;
    }
  }
  
  .el-date-editor {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      box-shadow: none !important;
      
      &:hover {
        border-color: rgba(255, 255, 255, 0.4) !important;
      }
      
      &.is-focus {
        border-color: #4A90E2 !important;
        box-shadow: 0 0 0 1px rgba(74, 144, 226, 0.2) !important;
      }
    }
    
    .el-input__inner {
      background: transparent !important;
      border: none !important;
      color: #ffffff !important;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }
    }
    
    .el-input__prefix,
    .el-input__suffix {
      color: rgba(255, 255, 255, 0.7) !important;
      
      .el-input__prefix-inner,
      .el-input__suffix-inner {
        color: rgba(255, 255, 255, 0.7) !important;
      }
    }
  }
}

// Element Plus 下拉框样式
.el-select-dropdown,
.el-popper {
  background: #060E1F !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  
  .el-select-dropdown__item {
    color: #ffffff !important;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1) !important;
    }
    
    &.selected {
      background: #4A90E2 !important;
      color: #ffffff !important;
    }
  }
  
  .el-scrollbar__view {
    background: #060E1F !important;
  }
}

// Element Plus 日期选择器样式
.el-date-picker {
  background: #060E1F !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  
  .el-date-picker__header {
    color: #ffffff !important;
  }
  
  .el-date-table {
    td {
      color: #ffffff !important;
      
      &.available:hover {
        background: rgba(255, 255, 255, 0.1) !important;
      }
      
      &.current {
        background: #4A90E2 !important;
        color: #ffffff !important;
      }
    }
  }
}

// Element Plus 加载遮罩样式
.el-loading-mask {
  background-color: rgba(6, 14, 31, 0.8) !important;
  
  .el-loading-spinner {
    .el-loading-text {
      color: #ffffff !important;
    }
    
    .circular {
      color: #4A90E2 !important;
    }
  }
}

// Element Plus 表格加载状态
.el-table__empty-block {
  background: #060E1F !important;
  
  .el-table__empty-text {
    color: rgba(255, 255, 255, 0.6) !important;
  }
}

// 全局输入框样式覆盖（确保在任何情况下都生效）
.el-input__wrapper {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: none !important;
  
  &:hover {
    border-color: rgba(255, 255, 255, 0.4) !important;
  }
  
  &.is-focus {
    border-color: #4A90E2 !important;
    box-shadow: 0 0 0 1px rgba(74, 144, 226, 0.2) !important;
  }
}

.el-input__inner {
  background: transparent !important;
  border: none !important;
  color: #ffffff !important;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
  }
}

.el-select__caret {
  color: rgba(255, 255, 255, 0.7) !important;
}

.el-select__placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

// 强制覆盖Element Plus表格样式
.device-list-dialog {
  .el-table {
    background-color: #060E1F !important;
    color: #ffffff !important;
    
    .el-table__header-wrapper {
      background-color: #060E1F !important;
      
      .el-table__header {
        background-color: #060E1F !important;
        
        th {
          background-color: #060E1F !important;
          color: #ffffff !important;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        }
      }
    }
    
    .el-table__body-wrapper {
      background-color: #060E1F !important;
      
      .el-table__body {
        background-color: #060E1F !important;
        
        tr {
          background-color: #060E1F !important;
          
          td {
            background-color: #060E1F !important;
            color: #ffffff !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
          }
          
          &:hover td {
            background-color: rgba(74, 144, 226, 0.1) !important;
          }
        }
      }
    }
    
    .el-table__fixed {
      background-color: #060E1F !important;
    }
    
    .el-table__fixed-header-wrapper {
      background-color: #060E1F !important;
    }
    
    .el-table__fixed-body-wrapper {
      background-color: #060E1F !important;
    }
  }
}

// Element Plus 日期选择器弹出面板样式
.date-picker-popper,
.el-picker-panel {
  background: #060E1F !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
  
  .el-picker-panel__body {
    background: #060E1F !important;
  }
  
  .el-picker-panel__content {
    background: #060E1F !important;
  }
  
  .el-date-picker__header {
    background: #060E1F !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    
    .el-picker-panel__icon-btn {
      color: #ffffff !important;
      
      &:hover {
        color: #4A90E2 !important;
      }
    }
    
    .el-date-picker__header-label {
      color: #ffffff !important;
      
      &:hover {
        color: #4A90E2 !important;
      }
    }
  }
  
  .el-date-table {
    background: #060E1F !important;
    
    th {
      color: rgba(255, 255, 255, 0.7) !important;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    }
    
    td {
      color: #ffffff !important;
      border: none !important;
      
      &.available:hover {
        background: rgba(74, 144, 226, 0.2) !important;
        color: #ffffff !important;
      }
      
      &.current {
        background: #4A90E2 !important;
        color: #ffffff !important;
      }
      
      &.today {
        color: #4A90E2 !important;
        font-weight: bold !important;
      }
      
      .cell {
        color: inherit !important;
      }
    }
  }
  
  .el-picker-panel__footer {
    background: #060E1F !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    
    .el-button {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      color: #ffffff !important;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2) !important;
      }
      
      &.el-button--primary {
        background: #4A90E2 !important;
        border-color: #4A90E2 !important;
        
        &:hover {
          background: #357ABD !important;
          border-color: #357ABD !important;
        }
      }
    }
  }
}
</style> 