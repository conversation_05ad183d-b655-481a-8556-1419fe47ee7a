<template>
  <div class="echarts-information-type-pie">
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">数据加载中...</div>
    </div>
    <div v-else class="chart-container">
      <div ref="chartRef" class="chart"></div>
      <div class="legend-container">
        <div class="legend-item" v-for="item in chartData" :key="item.name">
          <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
          <div class="legend-label">{{ item.name }}</div>
          <div class="legend-value">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

interface ChartDataItem {
  name: string
  value: number
  color: string
}

interface Props {
  chartData: ChartDataItem[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) {
    console.log('饼图容器不存在')
    return
  }

  console.log('饼图容器尺寸:', chartRef.value.offsetWidth, 'x', chartRef.value.offsetHeight)
  console.log('饼图数据:', props.chartData)

  if (!chartInstance) {
    chartInstance = echarts.init(chartRef.value)
    console.log('饼图实例已创建:', chartInstance)
  }

  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00D4FF',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      },
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '信息公告类型',
        type: 'pie',
        radius: ['30%', '60%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#001529',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data: props.chartData.length > 0 ? props.chartData.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        })) : []
      }
    ]
  }

  console.log('饼图配置:', option)
  chartInstance.setOption(option, true) // 第二个参数为true，强制清空后重新设置
  console.log('饼图配置已设置')

  // 响应式调整
  const handleResize = () => {
    chartInstance?.resize()
  }
  
  window.addEventListener('resize', handleResize)
}

onMounted(() => {
  console.log('饼图组件挂载，数据:', props.chartData)
  // 延时初始化，确保DOM渲染完成
  setTimeout(() => {
    initChart()
  }, 100)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    window.removeEventListener('resize', () => {})
  }
})

watch(() => props.chartData, (newData) => {
  console.log('饼图数据变化:', newData)
  initChart()
}, { deep: true })

// 移除loading状态的watch，避免重复初始化
// watch(() => props.loading, (newVal) => {
//   console.log('饼图loading状态变化:', newVal)
//   if (!newVal) {
//     initChart()
//   }
// })
</script>

<style lang="scss" scoped>
.echarts-information-type-pie {
  height: 200px;
  
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    
    .loading-spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(0, 212, 255, 0.3);
      border-top: 3px solid #00D4FF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    .loading-text {
      margin-top: 10px;
      color: #9D9DA6;
      font-size: 12px;
    }
  }
  
  .chart-container {
    display: flex;
    height: 100%;
    
    .chart {
      flex: 1;
      height: 100%;
    }
    
    .legend-container {
      width: 120px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 8px;
      padding-left: 10px;
      
      .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 11px;
        
        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
        }
        
        .legend-label {
          flex: 1;
          color: #fff;
        }
        
        .legend-value {
          color: #00D4FF;
          font-weight: bold;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 