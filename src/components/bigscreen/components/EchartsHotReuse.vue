<template>
  <div ref="chartRef" style="width: 100%; height: 200px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  dataKeys: {
    type: Object,
    default: () => ({
      nameKey: 'name',
      valueKey: 'value'
    })
  }
})

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

watch(() => props.chartData, () => {
  if (chart) {
    initChart()
  }
}, { deep: true })

watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading({
      text: '加载中...',
      color: '#2AEFFC',
      textColor: '#fff',
      maskColor: 'rgba(0, 0, 0, 0.5)'
    })
  } else {
    chart?.hideLoading()
    initChart()
  }
})

const initChart = () => {
  if (!chartRef.value) return
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  
  // 转换数据格式为柱状图
  const chartData = Array.isArray(props.chartData) ? props.chartData : []
  const categories = chartData.map((item: any) => item[props.dataKeys.nameKey] || item.name || '')
  const values = chartData.map((item: any) => item[props.dataKeys.valueKey] || item.value || 0)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderColor: '#2AEFFC',
      textStyle: {
        color: '#CFE3FC',
        fontSize: 12
      }
    },
    grid: {
      left: '8%',
      right: '8%',
      top: '10%',
      bottom: '35%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10,
        interval: 0,
        rotate: 25
      },
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '热度',
      nameTextStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    series: [
      {
        type: 'bar',
        data: values,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: function(params: any) {
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FCEA2B', '#FF9F43', '#EE5A24', '#0097E6', '#8C7AE6', '#00D2D3']
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: colors[params.dataIndex % colors.length] },
              { offset: 1, color: colors[params.dataIndex % colors.length] + '66' }
            ])
          }
        },
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 10
        }
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式处理
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }
  
  window.addEventListener('resize', resizeHandler)
}
</script> 