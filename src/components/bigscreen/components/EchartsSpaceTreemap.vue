<template>
  <div ref="chartRef" style="width: 100%; height: 350px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Array,
    required: true
  },
  borderColor: {
    type: String,
    default: '#2AEFFC'
  },
  titleData: {
    type: Object,
    required: false,
    default: () => ({
      chartName: '热浏览',
      valueName: '浏览量'
    })
  },
  dataKeys: {
    type: Object,
    default: () => ({
      nameKey: 'name',
      valueKey: 'value'
    })
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const loadingSettings = {
  text: '加载中...',
  color: '#2AEFFC',
  textColor: '#fff',
  maskColor: 'rgba(0, 0, 0, 0.5)'
}

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

onMounted(() => {
  nextTick(() => {
    chart = echarts.init(chartRef.value!)
    if (props.loading) {
      chart.showLoading(loadingSettings)
    } else {
      initChart()
    }
  })
})

// 监听loading状态变化
watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading(loadingSettings)
  } else {
    chart?.hideLoading()
    initChart()
  }
})

// 监听数据变化
watch(() => props.chartData, () => {
  if (chart) {
    initChart()
  }
}, { deep: true })

function initChart() {
  if (!chart) return
  
  const chartData = Array.isArray(props.chartData) ? props.chartData : []
  
  // 计算总数
  const total = chartData.reduce((sum: number, item: any) => sum + (item?.[props.dataKeys.valueKey] || 0), 0)
  
  // 转换数据格式为矩形树图需要的格式
  const data = chartData.map((item: any, index: number) => {
    const value = item?.[props.dataKeys.valueKey] || 0
    const percentage = total > 0 ? parseFloat(((value / total) * 100).toFixed(2)) : 0
    
    return {
      name: item?.[props.dataKeys.nameKey] || '未知',
      value: percentage,
      [props.dataKeys.valueKey]: value,
      itemStyle: {
        color: getColor(index),
        borderColor: '#021121',
        borderWidth: 1
      }
    }
  })

  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderWidth: 1,
      borderColor: props.borderColor,
      textStyle: {
        color: '#CFE3FC',
        fontSize: 12
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      formatter: function(info: any) {
        return [
          `${info.name}: ${info.value}%`,
          `${props.titleData.valueName}: ${info.data?.[props.dataKeys.valueKey] || 0}`
        ].join('<br/>')
      }
    },
    series: [{
      name: props.titleData.chartName,
      type: 'treemap',
      visibleMin: 0,
      visualMin: 0,
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      data: data,
      breadcrumb: {
        show: false
      },
      roam: false,
      nodeClick: false,
      gapWidth: 2,
      label: {
        show: true,
        formatter: '{b}\n{c}%',
        align: 'center',
        verticalAlign: 'middle',
        lineHeight: 18,
        fontSize: 12,
        color: '#ffffff',
        fontWeight: 'bold'
      },
      upperLabel: {
        show: false,
        height: 30
      },
      itemStyle: {
        borderColor: '#021121',
        borderWidth: 1
      }
    }]
  }
  
  chart.setOption(option)
  
  // 响应式处理
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }
  
  window.addEventListener('resize', resizeHandler)
}

// 获取颜色
function getColor(index: number) {
  const colors = [
    '#4A90E2', // 蓝色
    '#7ED321', // 绿色
    '#F5A623', // 橙色
    '#D0021B', // 红色
    '#9013FE', // 紫色
    '#50E3C2', // 青色
    '#B8E986', // 浅绿
    '#4A4A4A', // 灰色
    '#F8E71C', // 黄色
    '#BD10E0'  // 品红
  ]
  return colors[index % colors.length]
}

onBeforeUnmount(() => {
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }
  window.removeEventListener('resize', resizeHandler)
  if (chart) {
    chart.dispose()
  }
})
</script> 