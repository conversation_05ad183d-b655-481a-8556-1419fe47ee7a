<template>
  <div ref="chartRef" style="width: 100%; height: 280px"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from "vue";
import * as echarts from "echarts";

interface ChartDataItem {
  name: string;
  posts: number;
  views: number;
}

const props = withDefaults(
  defineProps<{
    chartData: ChartDataItem[];
    loading?: boolean;
  }>(),
  {
    loading: false,
  }
);

// 添加调试信息
console.log('🔍 EchartsCommunityLifeUsage - 接收到的数据:', props.chartData)

const chartRef = ref<HTMLElement>();
let chart: echarts.ECharts | null = null;
let intervalId: number | null = null;
const displayCount = 10; // 每次显示10个数据项

const loadingSettings = {
  text: "加载中...",
  color: "#00D4FF",
  textColor: "#fff",
  maskColor: "rgba(0, 0, 0, 0.5)",
};

onMounted(() => {
  nextTick(() => {
    initChart();
  });
});

watch(
  () => props.chartData,
  () => {
    if (chart) {
      initChart();
    }
  },
  { deep: true }
);

watch(
  () => props.loading,
  (val) => {
    if (val) {
      chart?.showLoading(loadingSettings);
    } else {
      chart?.hideLoading();
      initChart();
    }
  }
);

function initChart() {
  if (!chartRef.value) return;

  if (intervalId) {
    clearInterval(intervalId);
  }

  if (chart) {
    chart.dispose();
  }

  chart = echarts.init(chartRef.value);

  const chartData = Array.isArray(props.chartData) ? props.chartData : [];
  console.log('📊 EchartsCommunityLifeUsage - 接收到的数据:', props.chartData);

  const allNames = chartData.map((item: ChartDataItem) => {
    const name = item.name;
    if (name.length > 4) {
      return name.substring(0, 4) + "...";
    }
    return name;
  });
  const allPosts = chartData.map((item: ChartDataItem) => Number(item.posts) || 0);
  const allViews = chartData.map((item: ChartDataItem) => Number(item.views) || 0);

  console.log('📊 处理后的数据:', { allNames, allPosts, allViews });

  let currentNames = allNames.slice(0, displayCount);
  let currentPosts = allPosts.slice(0, displayCount);
  let currentViews = allViews.slice(0, displayCount);

  const updateChart = () => {
    console.log('📊 EchartsCommunityLifeUsage - 接收到的数据:', chartData);
    console.log('📊 EchartsCommunityLifeUsage - currentPosts:', currentPosts);
    console.log('📊 EchartsCommunityLifeUsage - currentViews:', currentViews);
    const option = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        borderColor: "#00D4FF",
        borderWidth: 1,
        textStyle: {
          color: "#fff",
        },
        formatter: (params: any) => {
          const dataIndex = params[0].dataIndex;
          const originalName = chartData[getCurrentOriginalIndex(dataIndex)]?.name || "";
          let result = `<div style="font-weight: bold; margin-bottom: 5px;">${originalName}</div>`;
          params.forEach((param: any) => {
            result += `<div style="margin: 2px 0;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; margin-right: 5px;"></span>
              ${param.seriesName}: ${param.value}
            </div>`;
          });
          return result;
        },
      },

      legend: {
        data: ["图书数", "浏览量"],
        textStyle: {
          color: "#9D9DA6",
          fontSize: 14,
          fontWeight: "bold",
        },
        top: "2%",
        right: "5%",
        itemWidth: 12,
        itemHeight: 8,
      },

      grid: {
        left: "8%",
        right: "5%",
        bottom: "0",
        top: "15%",
        containLabel: true,
      },

      xAxis: {
        type: "category",
        data: currentNames,
        axisLine: {
          show: true,
          lineStyle: {
            color: "#4A5568",
          },
        },
        axisLabel: {
          color: "#9D9DA6",
          fontSize: 14,
          interval: 0,
          rotate: -45,
          margin: 15,
          overflow: "truncate",
          width: 80,
          fontWeight: "bold",
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: "#4A5568",
          },
        },
      },

      yAxis: {
        type: "value",
        axisLine: {
          show: true,
          lineStyle: {
            color: "#4A5568",
          },
        },
        axisLabel: {
          color: "#9D9DA6",
          fontSize: 14,
          fontWeight: "bold",
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: "#4A5568",
          },
        },
        splitLine: {
          lineStyle: {
            color: "#2D3748",
            type: "dashed",
          },
        },
        max: 160,
      },

      series: [
        {
          name: "图书数",
          type: "bar",
          data: currentPosts,
          itemStyle: {
            color: "#50C878", // 绿色
          },
          barWidth: "25%",
          label: {
            show: true,
            position: "top",
            color: "#fff",
            fontSize: 14,
            fontWeight: "bold",
          },
        },
        {
          name: "浏览量",
          type: "bar",
          data: currentViews,
          itemStyle: {
            color: "#00D4FF",
          },
          barWidth: "25%",
          label: {
            show: true,
            position: "top",
            color: "#fff",
            fontSize: 14,
            fontWeight: "bold",
          },
        },
      ],
    };
    chart?.setOption(option);
  };

  // 获取当前显示项对应的原始数据索引
  let currentStartIndex = 0;
  const getCurrentOriginalIndex = (displayIndex: number) => {
    return (currentStartIndex + displayIndex) % chartData.length;
  };

  updateChart();

  // 如果数据超过显示数量，启动轮播
  if (allNames.length > displayCount) {
    intervalId = window.setInterval(() => {
      currentStartIndex = (currentStartIndex + 1) % allNames.length;
      currentNames = [];
      currentPosts = [];
      currentViews = [];

      for (let i = 0; i < displayCount; i++) {
        const index = (currentStartIndex + i) % allNames.length;
        currentNames.push(allNames[index]);
        currentPosts.push(allPosts[index]);
        currentViews.push(allViews[index]);
      }

      updateChart();
    }, 3000); // 每3秒切换一次
  }

  // 响应式处理
  const resizeHandler = () => {
    if (chart) {
      chart.resize();
    }
  };

  window.addEventListener("resize", resizeHandler);
}

// 监听数据变化，重新渲染图表
watch(() => props.chartData, () => {
  if (chart && props.chartData && props.chartData.length > 0) {
    console.log('📊 EchartsCommunityLifeUsage - 数据变化，重新渲染图表');
    nextTick(() => {
      // 清除之前的定时器
      if (intervalId) {
        clearInterval(intervalId);
        intervalId = null;
      }
      // 重新初始化图表
      initChart();
    });
  }
}, { deep: true, immediate: false });

onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId);
  }
  window.removeEventListener("resize", function () {
    chart?.resize();
  });
});
</script>

<style lang="scss" scoped>
// 样式已经通过内联方式定义在template中
</style>
