<template>
  <div class="activity-overview">
    <div class="overview-item purple-border">
      <div class="overview-icon">
        <img src="@/assets/common/light.png" alt="活动数量" />
      </div>
      <div class="overview-content">
        <div class="overview-label">活动总数</div>
        <div class="overview-value">{{ overviewData.activityCount || 0 }} <span class="unit">个</span></div>
      </div>
    </div>
    
    <div class="overview-item orange-border">
      <div class="overview-icon">
        <img src="@/assets/common/light.png" alt="进行中活动" />
      </div>
      <div class="overview-content">
        <div class="overview-label">进行中活动</div>
        <div class="overview-value orange">{{ overviewData.ongoingCount || 0 }} <span class="unit">个</span></div>
      </div>
    </div>
    
    <div class="overview-item cyan-border">
      <div class="overview-icon">
        <img src="@/assets/common/light.png" alt="已结束活动" />
      </div>
      <div class="overview-content">
        <div class="overview-label">已结束活动</div>
        <div class="overview-value cyan">{{ overviewData.completedCount || 0 }} <span class="unit">个</span></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  overviewData: {
    type: Object,
    required: true
  }
})

// 移除完成率计算，现在直接显示已结束活动数
</script>

<style scoped>
.activity-overview {
  display: flex;
  justify-content: space-between;
  gap: 0.312vw;
  margin-top: 0.390vw;
  
  @media (max-width: 2000px) {
    gap: 0.5vw;
    margin-top: 0.625vw;
  }
}

.overview-item {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 0.390vw 0.312vw;
  border-radius: 0.208vw;
  background: rgba(255, 255, 255, 0.05);
  border: 0.026vw solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  width: 140px;
  /* height: 68px; */
  

}

.overview-item:hover {
  transform: translateY(-0.078vw);
  box-shadow: 0 0.156vw 0.520vw rgba(0, 212, 255, 0.3);
  

}

.overview-item.purple-border {
  border-color: rgba(138, 43, 226, 0.4);
}

.overview-item.orange-border {
  border-color: rgba(255, 159, 24, 0.4);
}

.overview-item.cyan-border {
  border-color: rgba(0, 212, 255, 0.4);
}

.overview-icon {
  margin-right: 0.312vw;
  flex-shrink: 0;
  
  @media (max-width: 2000px) {
    margin-right: 0.5vw;
  }
}

.overview-icon img {
  width: 0.911vw;
  height: 0.911vw;
  
  @media (max-width: 2000px) {
    width: 1.458vw;
    height: 1.458vw;
  }
}

.overview-content {
  flex: 1;
  min-width: 0;
}

.overview-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  
  
}

.overview-value {
  font-size: 13px;
  font-weight: bold;
  color: #8A2BE2;
  
 
  
  &.orange {
    color: #FF9F18;
  }
  
  &.cyan {
    color: #00FFFF;
  }
}

.unit {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 0.104vw;
  
  
}
</style> 