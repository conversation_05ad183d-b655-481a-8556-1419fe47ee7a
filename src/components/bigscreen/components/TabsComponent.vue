<template>
  <div class="tabs-container">
    <div 
      v-for="(tab, index) in tabs" 
      :key="tab.value"
      :class="['tab-item', { active: modelValue === tab.value }]"
      @click="handleTabClick(tab.value)"
    >
      <span class="tab-text">{{ tab.label }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  modelValue: string
  tabs?: Array<{label: string, value: string}>
}>()

const emit = defineEmits(['update:modelValue', 'change'])

const tabs = props.tabs || [
  { label: '三色预警', value: 'precision' },
  { label: '家庭医生', value: 'doctor' },
  { label: '实用工具', value: 'tools' }
]

function handleTabClick(value: string) {
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<style lang="scss" scoped>
.tabs-container {
  display: flex;
  margin-bottom: one(20);
  gap: one(2);
  
  @media (max-width: 2000px) {
    margin-bottom: two(20);
    gap: two(2);
  }
}

.tab-item {
  position: relative;
  flex: 1;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-image: url('@/assets/common/selected2.png'); /* 未选中状态背景 */
  background-size: 100% 100%;
  background-repeat: no-repeat;
  transition: all 0.3s ease;
  

  
  &:hover {
    transform: translateY(one(-1));
    
    @media (max-width: 2000px) {
      transform: translateY(two(-1));
    }
  }
  
  &.active {
    background-image: url('@/assets/common/selected1.png'); /* 选中状态背景 */
    transform: translateY(one(-2));
    filter: brightness(1.2);
    z-index: 2;
    
    @media (max-width: 2000px) {
      transform: translateY(two(-2));
    }
    
    .tab-text {
      color: #ffffff;
      font-weight: 600;
      text-shadow: 0 0 one(8) rgba(255, 255, 255, 0.6);
      
      @media (max-width: 2000px) {
        text-shadow: 0 0 two(8) rgba(255, 255, 255, 0.6);
      }
    }
  }
}

.tab-text {
  font-size:14px;
  color: #ffffff;
  font-weight: 500;
  transition: all 0.3s ease;
  text-align: center;
  padding: 0 0.260vw;
  
 
}

/* 针对不同状态的背景调整 */
.tab-item:not(.active) {
  opacity: 0.8;
  
  .tab-text {
    color: #ffffff;
  }
  
  &:hover .tab-text {
    color: #ffffff;
  }
}
</style> 