<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  index: string | number
  dateData: Array<{
    label: string
    value: string | number
  }>
}>()

const currentVal = ref<string | number>(props.index)

/** 双向绑定 */
const emit = defineEmits(['update:modelValue', 'change'])

/** 切换日期选项 */
function _change(val: string | number) {
  currentVal.value = val
  emit('change', val)
}
</script>

<template>
  <div class="time-filter">
    <div
      v-for="(item, index) in props.dateData"
      :key="item.value"
      :class="['time-item', { active: item.value === currentVal }]"
      @click="_change(item.value)"
    >
      {{ item.label }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.time-filter {
  display: flex;
  justify-content: space-around;
  margin: 15px 0;
  flex-wrap: wrap;
  

  .time-item {
    width: 64px;
    height: 33px;
    color: #FFFFFF;
    font-weight: 400;
    font-size: 14px;
    line-height:33px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.104vw;
    cursor: pointer;
    text-align: center;
    margin: 0 0.078vw;
    border: 0.026vw solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    


    &:hover {
      color: rgba(255, 255, 255, 0.8);
      border-color: rgba(255, 255, 255, 0.4);
    }

    &.active {
      color: #fff;
      background: linear-gradient(135deg, #1677FF, #0D4A9F);
      border-color: #1677FF;
    }
  }
}
</style> 