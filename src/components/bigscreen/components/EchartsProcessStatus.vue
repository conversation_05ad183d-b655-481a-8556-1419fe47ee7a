<template>
  <div ref="chartRef" style="width: 100%; height: 250px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

interface ChartDataItem {
  district?: {
    name: string
  }
  receivedCount?: number
  feedbackCount?: number
}

interface SeriesNames {
  received: string
  feedback: string
}

const props = withDefaults(defineProps<{
  chartData: ChartDataItem[]
  borderColor?: string
  loading?: boolean
  seriesNames?: SeriesNames
}>(), {
  borderColor: '#2AEFFC',
  loading: false,
  seriesNames: () => ({
    received: '已接收',
    feedback: '已反馈'
  })
})

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
let intervalId: number | null = null
const displayCount = 6

const loadingSettings = {
  text: '加载中...',
  color: '#2AEFFC',
  textColor: '#fff',
  maskColor: 'rgba(0, 0, 0, 0.5)'
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

watch(() => props.chartData, () => {
  if (chart) {
    initChart()
  }
}, { deep: true })

watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading(loadingSettings)
  } else {
    chart?.hideLoading()
    initChart()
  }
})

function initChart() {
  if (!chartRef.value) return
  
  if (intervalId) {
    clearInterval(intervalId)
  }
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  
  const chartData = Array.isArray(props.chartData) ? props.chartData : []
  const allLabels = chartData.map((item: ChartDataItem) => item.district?.name || '未知区域')
  const allReceivedCounts = chartData.map((item: ChartDataItem) => item.receivedCount || 0)
  const allFeedbackCounts = chartData.map((item: ChartDataItem) => item.feedbackCount || 0)
  
  let currentLabels = allLabels.slice(0, displayCount)
  let currentReceivedCounts = allReceivedCounts.slice(0, displayCount)
  let currentFeedbackCounts = allFeedbackCounts.slice(0, displayCount)
  
  const updateChart = () => {
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.7)',
        borderWidth: 1,
        borderColor: props.borderColor,
        textStyle: {
          color: '#CFE3FC',
          fontSize: 12
        }
      },
      legend: {
        data: [props.seriesNames.received, props.seriesNames.feedback],
        textStyle: {
          color: '#fff',
          fontSize: 12
        },
        icon: 'rect',
        itemWidth: 12,
        itemHeight: 8,
        top: 10
      },
      grid: {
        left: '8%',
        right: '5%',
        top: '20%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: currentLabels,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          interval: 0,
          rotate: 20,
          fontSize: 10
        },
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 10
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      series: [
        {
          name: props.seriesNames.received,
          type: 'bar',
          data: currentReceivedCounts,
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#FF9F18' },
              { offset: 1, color: 'rgba(255, 159, 24, 0.3)' }
            ])
          },
          barWidth: '35%'
        },
        {
          name: props.seriesNames.feedback, 
          type: 'bar',
          data: currentFeedbackCounts,
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#00B578' },
              { offset: 1, color: 'rgba(0, 181, 120, 0.3)' }
            ])
          },
          barWidth: '35%'
        }
      ]
    }
    chart?.setOption(option)
  }
  
  updateChart()
  
  // 如果数据超过显示数量，启动轮播
  if (allLabels.length > displayCount) {
    let currentIndex = 0
    intervalId = window.setInterval(() => {
      currentIndex = (currentIndex + 1) % allLabels.length
      currentLabels = []
      currentReceivedCounts = []
      currentFeedbackCounts = []
      
      for (let i = 0; i < displayCount; i++) {
        const index = (currentIndex + i) % allLabels.length
        currentLabels.push(allLabels[index])
        currentReceivedCounts.push(allReceivedCounts[index])
        currentFeedbackCounts.push(allFeedbackCounts[index])
      }
      
      updateChart()
    }, 3000)
  }
  
  // 响应式处理
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }
  
  window.addEventListener('resize', resizeHandler)
}

onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
  window.removeEventListener('resize', function() {
    chart?.resize()
  })
})
</script> 