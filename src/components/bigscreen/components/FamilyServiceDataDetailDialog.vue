<template>
  <Teleport to="body">
    <div v-if="visible" class="family-service-detail-modal" @click="handleMaskClick">
      <el-config-provider :locale="zhCn">
        <div class="family-service-detail-dialog" @click.stop>
        <!-- 弹窗头部 -->
        <div class="dialog-header">
          <div class="dialog-title">
            家庭医生服务概况数据详情
            <!-- <span class="time-range-info">（{{ getTimeRangeText() }}）</span> -->
          </div>
          <div class="close-btn" @click="handleClose">×</div>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
          <el-form
            ref="ruleFormRef"
            :model="searchForm"
            :inline="true"
            label-width="80px"
            class="search-form-content"
          >
            <el-form-item label="关键字" prop="key">
              <el-input
                v-model="searchForm.key"
                placeholder="请输入医生姓名或服务名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
              <el-button :icon="Refresh" @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 数据列表表格 -->
        <div class="table-box">
          <el-table
            :max-height="'calc(100vh - 280px)'"
            :data="dataList"
            v-loading="loading"
            element-loading-text="加载中..."
            element-loading-background="rgba(0, 0, 0, 0.6)"
            class="family-service-table"
          >
            <el-table-column label="医生姓名" prop="doctorName" align="center" width="120">
              <template #default="{ row }">
                <span class="doctor-name">{{ row.doctorName || '-' }}</span>
              </template>
            </el-table-column>

            <el-table-column label="服务名称" prop="name" align="center" width="120">
              <template #default="{ row }">
                <span class="service-name">{{ row.name || '-' }}</span>
              </template>
            </el-table-column>

            <el-table-column label="区域名称" prop="districtName" align="center" min-width="150">
              <template #default="{ row }">
                <span class="district-name">{{ row.districtName || '-' }}</span>
              </template>
            </el-table-column>

            <el-table-column label="区域代码" prop="districtCode" align="center" width="140">
              <template #default="{ row }">
                <span class="district-code">{{ row.districtCode || '-' }}</span>
              </template>
            </el-table-column>

            <el-table-column label="服务总数" prop="total" align="center" width="100">
              <template #default="{ row }">
                <span class="count-text">{{ row.total || 0 }}</span>
              </template>
            </el-table-column>

            <el-table-column label="经度" prop="longitude" align="center" width="120">
              <template #default="{ row }">
                <span class="coordinate-text">{{ row.longitude || '-' }}</span>
              </template>
            </el-table-column>

            <el-table-column label="纬度" prop="latitude" align="center" width="120">
              <template #default="{ row }">
                <span class="coordinate-text">{{ row.latitude || '-' }}</span>
              </template>
            </el-table-column>

            <el-table-column label="创建时间" prop="createTime" align="center" width="160">
              <template #default="{ row }">
                <span class="time-text">{{ formatTime(row.createTime) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-box">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            class="family-service-pagination"
            :prev-text="'上一页'"
            :next-text="'下一页'"
            :page-size-text="'条/页'"
            :total-text="'共 {total} 条'"
            :jumper-text="'前往'"
            :page-classifier="'页'"
          />
        </div>
        </div>
      </el-config-provider>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { getFamilyServiceDataDetailApi } from '@/api/bigscreen'
import type { FamilyServiceDataDetailParams, FamilyServiceDataDetailItem } from '@/api/bigscreen'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

// 组件Props
interface Props {
  visible: boolean
  areaCode?: string
  timeRange?: string  // 时间范围参数
}

// 组件Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const ruleFormRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  key: ''
})

// 数据列表
const dataList = ref<FamilyServiceDataDetailItem[]>([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 获取时间范围文本
const getTimeRangeText = () => {
  const timeRangeMap: Record<string, string> = {
    '0': '当天',
    '1': '一周',
    '2': '近三月',
    '3': '近六月',
    '4': '近一年'
  }
  return timeRangeMap[props.timeRange || '2'] || '近三月'
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    getDataList()
  }
})

// 监听时间范围变化
watch(() => props.timeRange, () => {
  if (props.visible) {
    getDataList()
  }
})

// 获取数据列表
const getDataList = async () => {
  try {
    loading.value = true
    
    const params: FamilyServiceDataDetailParams = {
      timeType: props.timeRange || '2',
      areaCode: props.areaCode || '510183',
      key: searchForm.key || undefined,
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      orderByColumn: '',
      isAsc: 'asc'
    }
    
    console.log('🔍 家庭医生服务概况数据详情查询参数:', params)

    const response = await getFamilyServiceDataDetailApi(params)
    
    console.log('🔍 家庭医生服务概况数据详情API响应:', response)
    
    // 根据实际API响应结构处理数据
    if (response) {
      // 检查是否有data字段
      if (response.data) {
        const apiData = response.data
        console.log('🔍 API数据结构:', apiData)
        
        if (apiData.code === 200) {
          dataList.value = apiData.rows || []
          total.value = apiData.total || 0
          console.log('✅ 家庭医生服务概况数据详情更新成功，共', dataList.value.length, '条数据')
        } else {
          console.warn('⚠️ API返回错误代码:', apiData.code, apiData.msg)
          throw new Error(`API返回错误: ${apiData.msg || '未知错误'}`)
        }
      } else if ((response as any).code !== undefined) {
        // 直接响应格式（没有包装在data中）
        const directResponse = response as any
        console.log('🔍 直接响应格式，code:', directResponse.code)
        if (directResponse.code === 200) {
          dataList.value = directResponse.rows || []
          total.value = directResponse.total || 0
          console.log('✅ 家庭医生服务概况数据详情更新成功（直接格式），共', dataList.value.length, '条数据')
        } else {
          console.warn('⚠️ API返回错误代码（直接格式）:', directResponse.code, directResponse.msg)
          throw new Error(`API返回错误: ${directResponse.msg || '未知错误'}`)
        }
      } else {
        console.warn('⚠️ 无法识别的响应格式:', response)
        throw new Error('无法识别的响应格式')
      }
    } else {
      console.warn('⚠️ 家庭医生服务概况数据详情API返回数据为空')
      throw new Error('API返回数据为空')
    }
  } catch (error) {
    console.error('获取家庭医生服务概况数据详情失败:', error)
    ElMessage.warning('获取数据失败，使用模拟数据')
    
    // 使用模拟数据
    dataList.value = [
      {
        name: '家庭医生',
        createTime: '2025-07-15 19:18:03',
        total: 2,
        districtName: '文君街道',
        districtCode: '510183002',
        longitude: '103.473283',
        latitude: '30.407823',
        doctorName: '吕艳'
      },
      {
        name: '家庭医生',
        createTime: '2025-07-16 08:39:59',
        total: 2,
        districtName: '桑园镇',
        districtCode: '510183103',
        longitude: '103.460793',
        latitude: '30.47483',
        doctorName: '郑霞'
      },
      {
        name: '家庭医生',
        createTime: '2025-07-10 00:56:29',
        total: 3,
        districtName: '桑园镇',
        districtCode: '510183103',
        longitude: '103.460793',
        latitude: '30.47483',
        doctorName: '钱淑珍'
      },
      {
        name: '家庭医生',
        createTime: '2025-06-29 06:52:46',
        total: 2,
        districtName: '临邛街道',
        districtCode: '510183001',
        longitude: '103.469783',
        latitude: '30.41298',
        doctorName: '贺杰'
      },
      {
        name: '家庭医生',
        createTime: '2025-06-28 20:18:44',
        total: 1,
        districtName: '南宝山镇',
        districtCode: '510183121',
        longitude: '103.258092',
        latitude: '30.402464',
        doctorName: '阎毅'
      }
    ]
    total.value = 8
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  getDataList()
}

// 重置
const handleReset = () => {
  ruleFormRef.value?.resetFields()
  searchForm.key = ''
  currentPage.value = 1
  getDataList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  getDataList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getDataList()
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return '-'
  return time.replace('T', ' ').substring(0, 19)
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 点击遮罩关闭
const handleMaskClick = () => {
  emit('update:visible', false)
}

// 组件挂载时的初始化
onMounted(() => {
  if (props.visible) {
    getDataList()
  }
})
</script>

<style lang="scss" scoped>
.family-service-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;

  /* 隐藏滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.family-service-detail-dialog {
  width: 1400px;
  max-width: 95vw;
  max-height: 95vh;
  background: #060E1F;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(74, 144, 226, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  background: rgba(23, 81, 159, 0.1);
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;

  .time-range-info {
    font-size: 14px;
    font-weight: normal;
    color: #4A90E2;
    margin-left: 8px;
  }
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #ffffff;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ff6b6b;
  }
}

.search-form {
  padding: 16px 24px;
  border-bottom: 1px solid rgba(74, 144, 226, 0.2);
  background: rgba(23, 81, 159, 0.05);
}

.search-form-content {
  :deep(.el-form-item) {
    margin-bottom: 0;

    .el-form-item__label {
      color: #ffffff !important;
      font-size: 14px;
    }
  }

  :deep(.el-input) {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;

      .el-input__inner {
        color: #ffffff !important;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5) !important;
        }
      }
    }
  }

  :deep(.el-button) {
    &.el-button--primary {
      background: #4A90E2 !important;
      border-color: #4A90E2 !important;

      &:hover {
        background: #357abd !important;
        border-color: #357abd !important;
      }
    }

    &:not(.el-button--primary) {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      color: #ffffff !important;

      &:hover {
        background: rgba(255, 255, 255, 0.2) !important;
        border-color: rgba(255, 255, 255, 0.3) !important;
      }
    }
  }
}

.table-box {
  flex: 1;
  padding: 0 24px;
  overflow: hidden;

  :deep(.family-service-table) {
    background: #060E1F !important;

    .el-table__header-wrapper {
      background: #060E1F !important;

      .el-table__header {
        background: #060E1F !important;

        th {
          background: #060E1F !important;
          border-bottom: 1px solid rgba(74, 144, 226, 0.3);
          color: #ffffff !important;
          font-weight: bold;
          font-size: 13px;
        }
      }
    }

    .el-table__body-wrapper {
      background: #060E1F !important;

      .el-table__body {
        background: #060E1F !important;

        tr {
          background: #060E1F !important;

          &:hover {
            background: rgba(74, 144, 226, 0.15) !important;
          }

          td {
            background: #060E1F !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: #ffffff;
            font-size: 12px;
          }
        }
      }
    }
  }
}

.district-name {
  color: #4A90E2;
  font-weight: 500;
}

.district-code {
  color: #fff;
  font-size: 14px;
  // font-family: 'Courier New', monospace;
}

.doctor-name {
  color: #90EE90;
  font-weight: 500;
}

.service-name {
  color: #FFD700;
  font-weight: 500;
}

.coordinate-text {
  color: #90EE90;
  font-size: 14px;
  // font-family: 'Courier New', monospace;
}

.count-text {
  color: #4A90E2;
  font-weight: bold;
}

.time-text {
  color: #4A90E2;
}

.pagination-box {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  background: #060E1F !important;
  padding: 10px 0;

  .family-service-pagination {
    background: #060E1F !important;

    :deep(.el-pagination) {
      color: #ffffff !important;
      background: #060E1F !important;

      // 强制覆盖所有可能的白色背景
      * {
        background: #060E1F !important;
        color: #ffffff !important;
      }

      .el-pagination__total,
      .el-pagination__jump,
      .el-pagination__classifier {
        color: #ffffff !important;
        background: #060E1F !important;
      }

      .el-select {
        .el-input {
          .el-input__wrapper {
            background: #060E1F !important;
            border: 1px solid rgba(74, 144, 226, 0.3) !important;
            box-shadow: none !important;

            &:hover {
              border-color: rgba(74, 144, 226, 0.5) !important;
            }

            .el-input__inner {
              color: #ffffff !important;
              background: #060E1F !important;
            }

            .el-input__suffix {
              .el-input__suffix-inner {
                .el-select__caret {
                  color: #ffffff !important;
                }
              }
            }
          }
        }
      }

      .el-pagination__editor {
        .el-input {
          .el-input__wrapper {
            background: #060E1F !important;
            border: 1px solid rgba(74, 144, 226, 0.3) !important;

            .el-input__inner {
              color: #ffffff !important;
              background: #060E1F !important;
            }
          }
        }
      }

      .btn-prev,
      .btn-next {
        background: #060E1F !important;
        color: #ffffff !important;
        border: 1px solid rgba(74, 144, 226, 0.3) !important;

        &:hover {
          background: rgba(74, 144, 226, 0.3) !important;
          border-color: #4A90E2 !important;
          color: #ffffff !important;
        }

        &:disabled {
          background: rgba(255, 255, 255, 0.05) !important;
          color: rgba(255, 255, 255, 0.3) !important;
          border-color: rgba(255, 255, 255, 0.1) !important;
        }
      }

      .el-pager {
        background: #060E1F !important;

        li {
          background: #060E1F !important;
          color: #ffffff !important;
          border: 1px solid rgba(74, 144, 226, 0.3) !important;
          margin: 0 2px;

          &:hover {
            background: rgba(74, 144, 226, 0.3) !important;
            border-color: #4A90E2 !important;
            color: #ffffff !important;
          }

          &.is-active {
            background: #4A90E2 !important;
            border-color: #4A90E2 !important;
            color: #ffffff !important;
          }

          &.more {
            background: #060E1F !important;
            color: #ffffff !important;
          }
        }
      }
    }
  }
}

// 深色模式全局覆盖
.family-service-detail-dialog {
  // 覆盖Element Plus的默认样式
  .el-table {
    --el-table-bg-color: #060E1F !important;
    --el-table-tr-bg-color: #060E1F !important;
    --el-table-header-bg-color: #060E1F !important;
    --el-table-row-hover-bg-color: rgba(74, 144, 226, 0.15) !important;
    --el-table-text-color: #ffffff !important;
    --el-table-header-text-color: #ffffff !important;
    --el-table-border-color: rgba(255, 255, 255, 0.1) !important;
  }

  // 分页组件深色样式
  .el-pagination {
    --el-pagination-bg-color: #060E1F !important;
    --el-pagination-text-color: #ffffff !important;
    --el-pagination-border-color: rgba(255, 255, 255, 0.1) !important;
    --el-pagination-hover-color: #4a90e2 !important;
    background: #060E1F !important;

    .el-pagination__total,
    .el-pagination__jump,
    .el-pagination__sizes,
    .el-pagination__editor {
      background: #060E1F !important;
      color: #ffffff !important;
    }

    .btn-prev,
    .btn-next,
    .el-pager li {
      background: #060E1F !important;
      color: #ffffff !important;
      border: 1px solid rgba(74, 144, 226, 0.3) !important;
    }
  }

  // 表单组件深色样式
  .el-form-item__label {
    color: #ffffff !important;
  }

  .el-input__wrapper {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;

    .el-input__inner {
      color: #ffffff !important;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }
    }
  }

  .el-select .el-input .el-select__caret {
    color: #ffffff !important;
  }
}

// 全局强制深色样式覆盖
:deep(.el-pagination) {
  background: #060E1F !important;

  * {
    background: #060E1F !important;
    color: #ffffff !important;
  }

  .el-input__wrapper {
    background: #060E1F !important;
    border: 1px solid rgba(74, 144, 226, 0.3) !important;
  }

  .el-input__inner {
    background: #060E1F !important;
    color: #ffffff !important;
  }

  .btn-prev,
  .btn-next,
  .el-pager li {
    background: #060E1F !important;
    color: #ffffff !important;
    border: 1px solid rgba(74, 144, 226, 0.3) !important;
  }
}

// 强制覆盖所有可能的白色背景
:deep(.el-popper) {
  background: #060E1F !important;
  border: 1px solid rgba(74, 144, 226, 0.3) !important;

  .el-select-dropdown__item {
    background: #060E1F !important;
    color: #ffffff !important;

    &:hover {
      background: rgba(74, 144, 226, 0.3) !important;
    }

    &.is-selected {
      background: #4A90E2 !important;
      color: #ffffff !important;
    }
  }
}
</style>
