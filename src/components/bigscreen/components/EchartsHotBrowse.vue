<template>
  <div class="hot-browse-container">
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p class="loading-text">加载中...</p>
    </div>
    <div v-else ref="chartRef" style="width: 100%; height: 250px;"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

onMounted(() => {
  nextTick(() => {
    if (!props.loading) {
      initChart()
    }
  })
})

watch(() => props.chartData, () => {
  if (chart && !props.loading) {
    initChart()
  }
}, { deep: true })

watch(() => props.loading, (val) => {
  if (!val && chartRef.value) {
    nextTick(() => {
      initChart()
    })
  }
})

function initChart() {
  if (!chartRef.value || props.loading) return
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  
  // 使用传入的chartData数据，如果没有数据则使用默认数据
  const hotBrowseData = Array.isArray(props.chartData) && props.chartData.length > 0 
    ? props.chartData 
    : [
    { name: '文化游线', value: 88 },
    { name: '历史游线', value: 76 },
    { name: '美食游线', value: 65 },
    { name: '生态游线', value: 54 },
    { name: '休闲游线', value: 43 },
    { name: '研学游线', value: 38 },
    { name: '红色游线', value: 32 },
    { name: '民俗游线', value: 28 }
  ]
  
  const categories = hotBrowseData.map((item: any) => item.name || item.tourLineName || '')
  const values = hotBrowseData.map((item: any) => item.value || item.browseNumber || 0)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderWidth: 1,
      borderColor: '#2AEFFC',
      textStyle: {
        color: '#CFE3FC',
        fontSize: 12
      },
      formatter: function(params: any) {
        const param = params[0]
        return `${param.name}<br/>热度: ${param.value}`
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      top: '10%',
      bottom: '35%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10,
        interval: 0,
        rotate: 45
      },
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '热度',
      nameTextStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    series: [
      {
        type: 'bar',
        data: values,
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: function(params: any) {
            const colors = ['#00FFFF', '#00FF96', '#FF9F18', '#DEB565', '#5D7092', '#E91E63', '#9C27B0', '#673AB7']
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: colors[params.dataIndex % colors.length] },
              { offset: 1, color: colors[params.dataIndex % colors.length] + '66' }
            ])
          }
        },
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 10
        }
      }
    ]
  }
  
  chart?.setOption(option)
  
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }
  
  window.addEventListener('resize', resizeHandler)
}

onBeforeUnmount(() => {
  window.removeEventListener('resize', function() {
    chart?.resize()
  })
})
</script>

<style scoped>
.hot-browse-container {
  width: 100%;
  height: 6.510vw;
  display: flex;
  align-items: center;
  justify-content: center;
  
  @media (max-width: 2000px) {
    height: 10.416vw;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading-spinner {
  width: 1.041vw;
  height: 1.041vw;
  border: 0.078vw solid rgba(0, 255, 255, 0.2);
  border-top: 0.078vw solid #00FFFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 0.390vw;
  
  @media (max-width: 2000px) {
    width: 1.666vw;
    height: 1.666vw;
    border: 0.125vw solid rgba(0, 255, 255, 0.2);
    border-top: 0.125vw solid #00FFFF;
    margin-bottom: 0.625vw;
  }
}

.loading-text {
  color: #00FFFF;
  font-size: 0.364vw;
  margin: 0;
  
  @media (max-width: 2000px) {
    font-size: 0.583vw;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 