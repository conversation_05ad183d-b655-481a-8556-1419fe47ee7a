<template>
  <div class="echarts-information-trend">
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">数据加载中...</div>
    </div>
    <div v-else ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

interface ChartDataItem {
  month: string
  publishCount: number
  viewCount: number
}

interface Props {
  chartData: ChartDataItem[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) {
    console.log('图表容器不存在')
    return
  }

  console.log('图表容器尺寸:', chartRef.value.offsetWidth, 'x', chartRef.value.offsetHeight)

  if (!chartInstance) {
    chartInstance = echarts.init(chartRef.value)
    console.log('图表实例已创建:', chartInstance)
  }

  console.log('趋势图数据:', props.chartData)
  
  const months = props.chartData.length > 0 ? props.chartData.map(item => item.month) : []
  const publishCounts = props.chartData.length > 0 ? props.chartData.map(item => item.publishCount) : []
  const viewCounts = props.chartData.length > 0 ? props.chartData.map(item => item.viewCount) : []
  
  console.log('趋势图映射数据:', { months, publishCounts, viewCounts })

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00D4FF',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      },
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: ['发布总数', '浏览量'],
      textStyle: {
        color: '#fff'
      },
      top: 10
    },
    xAxis: [
      {
        type: 'category',
        data: months,
        axisPointer: {
          type: 'shadow'
        },
        axisLabel: {
          color: '#9D9DA6',
          fontSize: 11
        },
        axisLine: {
          lineStyle: {
            color: '#394B5C'
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
        min: 0,
        axisLabel: {
          formatter: '{value}',
          color: '#9D9DA6',
          fontSize: 11
        },
        axisLine: {
          lineStyle: {
            color: '#394B5C'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#394B5C',
            type: 'dashed'
          }
        }
      }
    ],
    series: [
      {
        name: '发布总数',
        type: 'bar',
        yAxisIndex: 0,
        data: publishCounts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#4A90E2' },
            { offset: 1, color: '#1E3C72' }
          ])
        },
        barWidth: '30%'
      },
      {
        name: '浏览量',
        type: 'line',
        yAxisIndex: 0,
        data: viewCounts,
        lineStyle: {
          color: '#FFA500',
          width: 3
        },
        itemStyle: {
          color: '#FFA500'
        },
        symbol: 'circle',
        symbolSize: 6,
        emphasis: {
          scale: true,
          scaleSize: 8
        }
      }
    ]
  }

  chartInstance.setOption(option, true) // 第二个参数为true，强制清空后重新设置
  console.log('图表配置已设置:', option)

  // 响应式调整
  const handleResize = () => {
    chartInstance?.resize()
  }
  
  window.addEventListener('resize', handleResize)
}

onMounted(() => {
  console.log('趋势图组件挂载，数据:', props.chartData)
  // 延时初始化，确保DOM渲染完成
  setTimeout(() => {
    initChart()
  }, 100)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    window.removeEventListener('resize', () => {})
  }
})

watch(() => props.chartData, (newData) => {
  console.log('趋势图数据变化:', newData)
  initChart()
}, { deep: true })

// 移除loading状态的watch，避免重复初始化
// watch(() => props.loading, (newVal) => {
//   if (!newVal) {
//     initChart()
//   }
// })
</script>

<style lang="scss" scoped>
.echarts-information-trend {
  height: 250px;
  
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    
    .loading-spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(0, 212, 255, 0.3);
      border-top: 3px solid #00D4FF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    .loading-text {
      margin-top: 10px;
      color: #9D9DA6;
      font-size: 12px;
    }
  }
  
  .chart {
    width: 100%;
    height: 100%;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 