<template>
  <div v-if="visible" class="warning-detail-modal" @click="handleMaskClick">
    <div class="warning-detail-dialog" @click.stop>
      <!-- 弹窗头部 -->
      <div class="dialog-header">
        <div class="header-left">
          <div class="play-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M8 5v14l11-7z" fill="#00BFFF"/>
            </svg>
          </div>
          <div class="dialog-title">{{ eventDetail?.type || eventDetail?.TotalEventName || '预警详情' }}</div>
        </div>
        <div class="close-btn" @click="handleClose">×</div>
      </div>

      <!-- 弹窗内容 -->
      <div class="dialog-content">
        <div class="content-main">
          <!-- 左侧预警信息 -->
          <div class="warning-info">
            <div class="info-section">
              <h3 class="section-title">
                <span class="dot"></span>
                预警信息
              </h3>
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">预警事件：</span>
                  <span class="info-value">{{ eventDetail?.type || eventDetail?.TotalEventName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">预警时间：</span>
                  <span class="info-value">{{ eventDetail?.time || eventDetail?.EventDate || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">预警类型：</span>
                  <span class="info-value">{{ eventDetail?.type || eventDetail?.TotalEventName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">预警设备：</span>
                  <span class="info-value">{{ eventDetail?.device || '电梯监控2' }}</span>
                </div>
              </div>
              
              <div class="analysis-section">
                <div class="info-item full-width">
                  <span class="info-label">分析说明：</span>
                  <div class="analysis-content">
                    {{ getAnalysisDescription() }}
                  </div>
                </div>
              </div>
              
              <div class="location-section">
                <div class="info-item">
                  <span class="info-label">所属辖区：</span>
                  <span class="info-value">{{ eventDetail?.area || eventDetail?.ParkName || '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧事件过程 -->
          <div class="event-process">
            <div class="process-section">
              <h3 class="section-title">
                <span class="dot"></span>
                事件过程 / 预警位置
              </h3>
              <div class="process-content">
                <div class="process-item">
                  <div class="process-number">1</div>
                  <div class="process-text">事件标记已读</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部相关照片 -->
        <div class="photos-section">
          <h3 class="section-title">
            <span class="dot"></span>
            相关照片
          </h3>
          <div class="photos-grid">
            <div class="photo-item">
              <img 
                :src="getEventPhoto()" 
                alt="预警相关照片" 
                @error="handleImageError"
                @click="previewImage"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  visible: boolean
  eventDetail: {
    id?: number
    DCEId?: number
    type?: string
    TotalEventName?: string
    area?: string
    ParkName?: string
    time?: string
    EventDate?: string
    status?: string
    CompleteState?: string
    device?: string
    Description?: string
  }
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 获取分析说明
const getAnalysisDescription = () => {
  const eventType = props.eventDetail?.type || props.eventDetail?.TotalEventName || ''
  const time = props.eventDetail?.time || props.eventDetail?.EventDate || ''
  const device = props.eventDetail?.device || '电梯监控2'
  
  if (eventType.includes('非机动车进入电梯')) {
    return `设备【${device}(YAJD_BSKJ_DTJK02)】于 ${time} 触发预警【${eventType}】，事件描述信息：监测到有非机动车进入电梯`
  } else if (eventType.includes('高空抛物')) {
    return `设备【${device}】于 ${time} 触发预警【${eventType}】，事件描述信息：监测到高空抛物行为`
  } else {
    return `设备【${device}】于 ${time} 触发预警【${eventType}】，事件描述信息：${props.eventDetail?.Description || '监测到异常情况'}`
  }
}

// 获取事件照片
const getEventPhoto = () => {
  // 这里可以根据事件类型返回不同的默认图片
  // 或者从API获取真实的事件照片
  return '/src/assets/images/warning-photo-placeholder.jpg'
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 设置默认占位图
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjMjQzMjRiIi8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjNjY3Nzg4IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7ml6Dlm77niYc8L3RleHQ+Cjwvc3ZnPgo='
}

// 预览图片
const previewImage = () => {
  // 这里可以实现图片预览功能
  console.log('预览图片')
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 点击遮罩关闭
const handleMaskClick = () => {
  emit('update:visible', false)
}
</script>

<style lang="scss" scoped>
.warning-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.warning-detail-dialog {
  width: 1000px;
  max-width: 95vw;
  max-height: 90vh;
  background: #0a1426;
  border: 1px solid #1e3a5f;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dialog-header {
  padding: 16px 24px;
  border-bottom: 1px solid #1e3a5f;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #0a1426;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .play-icon {
      width: 32px;
      height: 32px;
      background: rgba(0, 191, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .dialog-title {
      font-size: 18px;
      font-weight: bold;
      color: #ffffff;
    }
  }
  
  .close-btn {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #1e3a5f;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: #00bfff;
    }
  }
}

.dialog-content {
  padding: 24px;
  background: #0a1426;
  overflow-y: auto;
  flex: 1;
}

.content-main {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.warning-info {
  flex: 1;
  
  .info-section {
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
      margin-bottom: 16px;
      
      .dot {
        width: 8px;
        height: 8px;
        background: #00bfff;
        border-radius: 50%;
      }
    }
    
    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 24px;
    }
    
    .info-item {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      &.full-width {
        grid-column: 1 / -1;
      }
      
      .info-label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
      }
      
      .info-value {
        color: #ffffff;
        font-size: 14px;
        font-weight: 500;
      }
    }
    
    .analysis-section {
      margin-bottom: 24px;
      
      .analysis-content {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(0, 191, 255, 0.2);
        border-radius: 4px;
        padding: 12px;
        color: #ffffff;
        font-size: 14px;
        line-height: 1.5;
        margin-top: 8px;
      }
    }
    
    .location-section {
      .info-item {
        flex-direction: row;
        gap: 8px;
        align-items: center;
      }
    }
  }
}

.event-process {
  width: 300px;
  
  .process-section {
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
      margin-bottom: 16px;
      
      .dot {
        width: 8px;
        height: 8px;
        background: #00bfff;
        border-radius: 50%;
      }
    }
    
    .process-content {
      .process-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 0;
        
        .process-number {
          width: 24px;
          height: 24px;
          background: #00bfff;
          color: #ffffff;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: bold;
          flex-shrink: 0;
        }
        
        .process-text {
          color: #ffffff;
          font-size: 14px;
        }
      }
    }
  }
}

.photos-section {
  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 16px;
    
    .dot {
      width: 8px;
      height: 8px;
      background: #00bfff;
      border-radius: 50%;
    }
  }
  
  .photos-grid {
    display: flex;
    gap: 12px;
    
    .photo-item {
      width: 120px;
      height: 90px;
      border: 1px solid #1e3a5f;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        border-color: #00bfff;
        transform: scale(1.05);
      }
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}
</style> 