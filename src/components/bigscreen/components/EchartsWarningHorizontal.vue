<template>
  <div ref="chartRef" style="width: 100%; height: 280px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  borderColor: {
    type: String,
    default: '#2AEFFC'
  }
})

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
let intervalId: number | null = null
const displayCount = 8 // 每次显示8个数据项

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

// 监听props变化
watch(() => props.chartData, () => {
  if (chart) {
    initChart()
  }
}, { deep: true })

function initChart() {
  if (!chartRef.value) return

  if (intervalId) {
    clearInterval(intervalId)
  }

  if (chart) {
    chart.dispose()
  }

  chart = echarts.init(chartRef.value)

  const chartData = props.chartData || {}
  const originalLabels = chartData.label || []
  const originalRedData = chartData.red || []
  const originalYellowData = chartData.yellow || []
  const originalBlueData = chartData.blue || []

  // 过滤掉所有数据都为0的项目
  const filteredData = originalLabels
    .map((label: string, index: number) => ({
      label,
      red: originalRedData[index] || 0,
      yellow: originalYellowData[index] || 0,
      blue: originalBlueData[index] || 0
    }))
    .filter(item => item.red > 0 || item.yellow > 0 || item.blue > 0)

  // 提取过滤后的数据
  const allLabels = filteredData.map(item => item.label)
  const allRedData = filteredData.map(item => item.red)
  const allYellowData = filteredData.map(item => item.yellow)
  const allBlueData = filteredData.map(item => item.blue)

  // 处理标签名称，超过4个字符的截断
  const processedLabels = allLabels.map((label: string) => {
    if (label.length > 4) {
      return label.substring(0, 4) + "..."
    }
    return label
  })

  // 如果过滤后没有数据，显示空状态
  if (allLabels.length === 0) {
    const option = {
      title: {
        text: '暂无预警数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          color: '#9D9DA6',
          fontSize: 16
        }
      }
    }
    chart?.setOption(option)
    return
  }

  let currentLabels = processedLabels.slice(0, displayCount)
  let currentRedData = allRedData.slice(0, displayCount)
  let currentYellowData = allYellowData.slice(0, displayCount)
  let currentBlueData = allBlueData.slice(0, displayCount)

  const updateChart = () => {
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: props.borderColor,
        borderWidth: 1,
        textStyle: {
          color: '#fff',
          fontSize: 12
        },
        formatter: (params: any) => {
          const dataIndex = params[0].dataIndex
          const originalName = allLabels[getCurrentOriginalIndex(dataIndex)] || ''
          let result = `<div style="font-weight: bold; margin-bottom: 5px;">${originalName}</div>`
          params.forEach((param: any) => {
            result += `<div style="margin: 2px 0;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; margin-right: 5px;"></span>
              ${param.seriesName}: ${param.value}
            </div>`
          })
          return result
        }
      },
      legend: {
        data: ['红色预警', '黄色预警', '蓝色预警'],
        textStyle: {
          color: '#9D9DA6',
          fontSize: 14,
          fontWeight: 'bold'
        },
        top: '2%',
        right: '5%',
        itemWidth: 12,
        itemHeight: 8
      },
      grid: {
        left: '8%',
        right: '5%',
        bottom: '0',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: currentLabels,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#4A5568'
          }
        },
        axisLabel: {
          color: '#9D9DA6',
          fontSize: 12,
          interval: 0,
          rotate: -45,
          margin: 15,
          overflow: 'truncate',
          width: 60,
          fontWeight: 'bold'
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: '#4A5568'
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#4A5568'
          }
        },
        axisLabel: {
          color: '#9D9DA6',
          fontSize: 12,
          fontWeight: 'bold'
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: '#4A5568'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#2D3748',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '红色预警',
          type: 'bar',
          data: currentRedData,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#FF3141' },
              { offset: 1, color: 'rgba(255, 49, 65, 0.6)' }
            ])
          },
          barWidth: '20%',
          label: {
            show: true,
            position: 'top',
            color: '#fff',
            fontSize: 12,
            fontWeight: 'bold'
          }
        },
        {
          name: '黄色预警',
          type: 'bar',
          data: currentYellowData,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#FF9F18' },
              { offset: 1, color: 'rgba(255, 159, 24, 0.6)' }
            ])
          },
          barWidth: '20%',
          label: {
            show: true,
            position: 'top',
            color: '#fff',
            fontSize: 12,
            fontWeight: 'bold'
          }
        },
        {
          name: '蓝色预警',
          type: 'bar',
          data: currentBlueData,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#1677FF' },
              { offset: 1, color: 'rgba(22, 119, 255, 0.6)' }
            ])
          },
          barWidth: '20%',
          label: {
            show: true,
            position: 'top',
            color: '#fff',
            fontSize: 12,
            fontWeight: 'bold'
          }
        }
      ]
    }
    chart?.setOption(option)
  }

  // 获取当前显示项对应的原始数据索引
  let currentStartIndex = 0
  const getCurrentOriginalIndex = (displayIndex: number) => {
    return (currentStartIndex + displayIndex) % allLabels.length
  }

  updateChart()

  // 如果数据超过显示数量，启动轮播
  if (allLabels.length > displayCount) {
    intervalId = window.setInterval(() => {
      currentStartIndex = (currentStartIndex + 1) % allLabels.length
      currentLabels = []
      currentRedData = []
      currentYellowData = []
      currentBlueData = []

      for (let i = 0; i < displayCount; i++) {
        const index = (currentStartIndex + i) % allLabels.length
        currentLabels.push(processedLabels[index])
        currentRedData.push(allRedData[index])
        currentYellowData.push(allYellowData[index])
        currentBlueData.push(allBlueData[index])
      }

      updateChart()
    }, 3000) // 每3秒切换一次
  }

  // 响应式处理
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }

  window.addEventListener('resize', resizeHandler)
}

onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
  window.removeEventListener('resize', function () {
    chart?.resize()
  })
})
</script>
