<template>
  <div ref="chartRef" style="width: 100%; height: 200px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  borderColor: {
    type: String,
    default: '#2AEFFC'
  }
})

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

// 监听props变化
watch(() => props.chartData, () => {
  if (chart) {
    initChart()
  }
}, { deep: true })

function initChart() {
  if (!chartRef.value) return
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderWidth: 1,
      borderColor: props.borderColor,
      textStyle: {
        color: '#CFE3FC',
        fontSize: 12
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    legend: {
      data: ['红色预警', '黄色预警', '蓝色预警'],
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      icon: 'rect',
      itemWidth: 12,
      itemHeight: 8,
      top: 10
    },
    grid: {
      left: '8%',
      right: '5%',
      top: '15%',
      bottom: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: props.chartData.label || [],
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: 10
      },
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      }
    },
    series: [
      {
        name: '红色预警',
        type: 'bar',
        data: props.chartData.red || [],
        itemStyle: {
          borderRadius: [0, 4, 4, 0],
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#FF3141' },
            { offset: 1, color: 'rgba(255, 49, 65, 0.6)' }
          ])
        },
        barWidth: '15%',
        label: {
          show: true,
          position: 'right',
          color: '#fff',
          fontSize: 10,
          formatter: '{c}'
        }
      },
      {
        name: '黄色预警',
        type: 'bar',
        data: props.chartData.yellow || [],
        itemStyle: {
          borderRadius: [0, 4, 4, 0],
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#FF9F18' },
            { offset: 1, color: 'rgba(255, 159, 24, 0.6)' }
          ])
        },
        barWidth: '15%',
        label: {
          show: true,
          position: 'right',
          color: '#fff',
          fontSize: 10,
          formatter: '{c}'
        }
      },
      {
        name: '蓝色预警',
        type: 'bar',
        data: props.chartData.blue || [],
        itemStyle: {
          borderRadius: [0, 4, 4, 0],
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#1677FF' },
            { offset: 1, color: 'rgba(22, 119, 255, 0.6)' }
          ])
        },
        barWidth: '15%',
        label: {
          show: true,
          position: 'right',
          color: '#fff',
          fontSize: 10,
          formatter: '{c}'
        }
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式处理
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }
  
  window.addEventListener('resize', resizeHandler)
}
</script> 