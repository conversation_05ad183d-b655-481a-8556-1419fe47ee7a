<template>
  <div ref="chartRef" style="width: 100%; height: 250px;"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  chartData: {
    type: Array,
    required: true
  },
  borderColor: {
    type: String,
    default: '#2AEFFC'
  },
  loading: {
    type: Boolean,
    default: false
  },
  seriesNames: {
    type: Object,
    default: () => ({
      publish: '发布数',
      complete: '完成数'
    })
  }
})

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
let intervalId: number | null = null
const displayCount = 6

const loadingSettings = {
  text: '加载中...',
  color: '#2AEFFC',
  textColor: '#fff',
  maskColor: 'rgba(0, 0, 0, 0.5)'
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

watch(() => props.chartData, () => {
  if (chart) {
    initChart()
  }
}, { deep: true })

watch(() => props.loading, (val) => {
  if (val) {
    chart?.showLoading(loadingSettings)
  } else {
    chart?.hideLoading()
    initChart()
  }
})

function initChart() {
  if (!chartRef.value) return
  
  if (intervalId) {
    clearInterval(intervalId)
  }
  
  if (chart) {
    chart.dispose()
  }
  
  chart = echarts.init(chartRef.value)
  
  const chartData = Array.isArray(props.chartData) ? props.chartData : []
  const allLabels = chartData.map((item: any) => item.district?.name || '未知区域')
  const allPublishCounts = chartData.map((item: any) => item.total || 0)
  const allCompleteCounts = chartData.map((item: any) => item.ended || 0)
  
  let currentLabels = allLabels.slice(0, displayCount)
  let currentPublishCounts = allPublishCounts.slice(0, displayCount)
  let currentCompleteCounts = allCompleteCounts.slice(0, displayCount)
  
  const updateChart = () => {
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.7)',
        borderWidth: 1,
        borderColor: props.borderColor,
        textStyle: {
          color: '#CFE3FC',
          fontSize: 12
        }
      },
      legend: {
        data: [props.seriesNames.publish, props.seriesNames.complete],
        textStyle: {
          color: '#fff',
          fontSize: 12
        },
        icon: 'rect',
        itemWidth: 12,
        itemHeight: 8,
        top: 10
      },
      grid: {
        left: '8%',
        right: '5%',
        top: '20%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: currentLabels,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          interval: 0,
          rotate: 20,
          fontSize: 10
        },
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 10
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      series: [
        {
          name: props.seriesNames.publish,
          type: 'bar',
          data: currentPublishCounts,
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#FF9F18' },
              { offset: 1, color: 'rgba(255, 159, 24, 0.3)' }
            ])
          },
          barWidth: '35%'
        },
        {
          name: props.seriesNames.complete, 
          type: 'bar',
          data: currentCompleteCounts,
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#00B578' },
              { offset: 1, color: 'rgba(0, 181, 120, 0.3)' }
            ])
          },
          barWidth: '35%'
        }
      ]
    }
    chart?.setOption(option)
  }
  
  updateChart()
  
  // 如果数据超过显示数量，启动轮播
  if (allLabels.length > displayCount) {
    let currentIndex = 0
    intervalId = window.setInterval(() => {
      currentIndex = (currentIndex + 1) % allLabels.length
      currentLabels = []
      currentPublishCounts = []
      currentCompleteCounts = []
      
      for (let i = 0; i < displayCount; i++) {
        const index = (currentIndex + i) % allLabels.length
        currentLabels.push(allLabels[index])
        currentPublishCounts.push(allPublishCounts[index])
        currentCompleteCounts.push(allCompleteCounts[index])
      }
      
      updateChart()
    }, 3000)
  }
  
  // 响应式处理
  const resizeHandler = () => {
    if (chart) {
      chart.resize()
    }
  }
  
  window.addEventListener('resize', resizeHandler)
}

onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
  window.removeEventListener('resize', function() {
    chart?.resize()
  })
})
</script> 