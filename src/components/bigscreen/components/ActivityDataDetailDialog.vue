<template>
  <Teleport to="body">
    <div v-if="visible" class="activity-detail-modal" @click="handleMaskClick">
      <div class="activity-detail-dialog" @click.stop>
        <!-- 弹窗头部 -->
        <div class="dialog-header">
          <div class="dialog-title">社区活动数据详情</div>
          <div class="close-btn" @click="handleClose">×</div>
        </div>

        <!-- 弹窗内容 -->
        <div class="dialog-content">
          <!-- 搜索表单 -->
          <div class="search-form">
            <el-form ref="ruleFormRef" :inline="true" :model="searchForm">
              <el-form-item prop="key">
                <el-input
                  v-model="searchForm.key"
                  placeholder="活动标题关键字"
                  clearable
                  @clear="handleTitleClear"
                />
              </el-form-item>

              <el-form-item>
                <el-button class="submit-btn" type="primary" :icon="Search" @click="handleSearch">
                  查询
                </el-button>
                <el-button class="reset-btn" :icon="Refresh" @click="handleReset">
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 数据列表表格 -->
          <div class="table-box">
            <el-table
              :max-height="'calc(100vh - 280px)'"
              :data="dataList"
              v-loading="loading"
              element-loading-text="加载中..."
              element-loading-background="rgba(0, 0, 0, 0.6)"
              class="activity-table"
            >
              <el-table-column label="活动标题" prop="title" align="center" min-width="200">
                <template #default="{ row }">
                  <span class="title-text">{{ row.title || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="活动副标题" prop="subtitle" align="center" min-width="180">
                <template #default="{ row }">
                  {{ row.subtitle || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="活动状态" prop="label" align="center" width="100">
                <template #default="{ row }">
                  <span class="status-tag" :class="getStatusClass(row.label)">{{ getStatusText(row.label) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="所属辖区" prop="districtName" align="center" width="120">
                <template #default="{ row }">
                  {{ row.districtName || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="活动地点" prop="location" align="center" min-width="180">
                <template #default="{ row }">
                  {{ row.location || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="联系人" prop="contactName" align="center" width="80">
                <template #default="{ row }">
                  {{ row.contactName || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="联系电话" prop="contactPhone" align="center" width="120">
                <template #default="{ row }">
                  {{ row.contactPhone || '-' }}
                </template>
              </el-table-column>
              <!-- <el-table-column label="浏览次数" prop="browseNumber" align="center" width="100">
                <template #default="{ row }">
                  <span class="view-count">{{ row.browseNumber || 0 }}</span>
                </template>
              </el-table-column> -->
              <el-table-column label="创建时间" prop="createTime" align="center" width="160">
                <template #default="{ row }">
                  {{ row.createTime || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="辖区代码" prop="districtCode" align="center" width="120">
                <template #default="{ row }">
                  {{ row.districtCode || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="经度" prop="longitude" align="center" width="100">
                <template #default="{ row }">
                  {{ row.longitude || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="纬度" prop="latitude" align="center" width="100">
                <template #default="{ row }">
                  {{ row.latitude || '-' }}
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="page-box">
              <el-config-provider :locale="zhCn">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 30, 50]"
                  :small="false"
                  :disabled="false"
                  background
                  layout="sizes, prev, pager, next, jumper, total"
                  :total="total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </el-config-provider>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { ElMessage, ElConfigProvider } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { getActivityDataDetailApi } from '@/api/bigscreen'
import type { ActivityDataDetailParams } from '@/api/bigscreen'

// Props
interface Props {
  visible: boolean
  areaCode: string
  timeRange: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  areaCode: '510183',
  timeRange: '2'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const loading = ref(false)
const dataList = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 搜索表单
const searchForm = ref({
  key: ''
})

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 点击遮罩关闭
const handleMaskClick = (e: Event) => {
  if (e.target === e.currentTarget) {
    handleClose()
  }
}

// 获取状态样式类
const getStatusClass = (label: string | number) => {
  switch (label) {
    case '1':
    case 1:
      return 'status-ongoing'
    case '2':
    case 2:
      return 'status-ended'
    case '0':
    case 0:
      return 'status-pending'
    default:
      return 'status-default'
  }
}

// 获取状态文本
const getStatusText = (label: string | number) => {
  switch (label) {
    case '1':
    case 1:
      return '进行中'
    case '2':
    case 2:
      return '已结束'
    case '0':
    case 0:
      return '未开始'
    default:
      return '未知'
  }
}

// 清除标题搜索
const handleTitleClear = () => {
  searchForm.value.key = ''
  handleSearch()
}

// 重置搜索
const handleReset = () => {
  searchForm.value.key = ''
  currentPage.value = 1
  getDataList()
}

// 获取数据列表
const getDataList = async () => {
  try {
    loading.value = true
    
    const params: ActivityDataDetailParams = {
      timeType: props.timeRange || '2',
      areaCode: props.areaCode || '510183',
      key: searchForm.value.key || undefined,
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      orderByColumn: '',
      isAsc: 'asc'
    }
    
    console.log('🔍 社区活动数据详情查询参数:', params)

    const response = await getActivityDataDetailApi(params)
    
    console.log('🔍 社区活动数据详情API响应:', response)

    if (response && response.code === 0) {
      // 根据实际API返回结构处理数据
      dataList.value = response.rows || []
      total.value = response.total || 0
      console.log('✅ 社区活动数据详情更新成功，共', dataList.value.length, '条数据')
    } else {
      console.warn('⚠️ 社区活动数据详情API返回数据格式异常:', response)
      throw new Error('API返回数据格式异常')
    }
  } catch (error) {
    console.error('获取社区活动数据详情失败:', error)
    ElMessage.warning('获取数据失败，使用模拟数据')
    
    // 使用模拟数据，符合实际API返回结构
    dataList.value = [
      {
        createTime: "2025-05-26 14:56:19",
        total: null,
        districtName: "顺河社区",
        districtCode: "510183002014",
        longitude: "103.453104",
        latitude: "30.399958",
        activityId: "1926895156096167937",
        label: "2",
        title: "\"传承非遗文化，共建和谐社区\"",
        subtitle: "\"传承非遗文化，共建和谐社区\"",
        location: "文君街道滨江路下段12号",
        contactName: "王",
        contactPhone: "15420169840",
        browseNumber: 1
      },
      {
        createTime: "2025-05-09 14:21:55",
        total: null,
        districtName: "顺河社区",
        districtCode: "510183002014",
        longitude: "103.453104",
        latitude: "30.399958",
        activityId: "1920725903701716993",
        label: "1",
        title: "首届户外亲子健步走活动",
        subtitle: "首届户外亲子健步走活动",
        location: "文君街道钱江凤凰城(东区)钱江凤凰城东区",
        contactName: "王",
        contactPhone: "13902184510",
        browseNumber: 1
      },
      {
        createTime: "2025-05-09 16:58:40",
        total: null,
        districtName: "顺河社区",
        districtCode: "510183002014",
        longitude: "103.453104",
        latitude: "30.399958",
        activityId: "1920765353223794689",
        label: "1",
        title: "时光里的她",
        subtitle: "顺河社区致敬每一位不被定义的女性力量",
        location: "文君街道滨江路下段钱江凤凰城",
        contactName: "李",
        contactPhone: "15698401025",
        browseNumber: 0
      }
    ]
    total.value = 3
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  getDataList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getDataList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  getDataList()
}

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 重置搜索条件和分页
    searchForm.value.key = ''
    currentPage.value = 1
    pageSize.value = 20
    // 获取数据
    getDataList()
  }
})

// 组件挂载时的初始化
onMounted(() => {
  if (props.visible) {
    getDataList()
  }
})
</script>

<style lang="scss" scoped>
.activity-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;

  /* 隐藏滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.activity-detail-dialog {
  width: 1200px;
  max-width: 95vw;
  max-height: 95vh;
  background: #060E1F;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(74, 144, 226, 0.3);
}

.dialog-header {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #060E1F;

  .dialog-title {
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
  }

  .close-btn {
    width: 28px;
    height: 28px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.dialog-content {
  flex: 1;
  padding: 20px;
  background: #060E1F;
  overflow-y: auto;

  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.search-form {
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(74, 144, 226, 0.2);

  .el-form-item {
    margin-bottom: 0;
    margin-right: 16px;
  }

  .submit-btn {
    background: #4A90E2;
    border: none;
    color: #ffffff;

    &:hover {
      background: #357abd;
    }
  }

  .reset-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.table-box {
  background: #060E1F;

  .activity-table {
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    background: #060E1F !important;

    :deep(.el-table) {
      background: #060E1F !important;
      color: #ffffff !important;

      .el-table__header-wrapper {
        background: #060E1F !important;

        .el-table__header {
          background: #060E1F !important;

          th {
            background: #060E1F !important;
            color: #ffffff !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;

            .cell {
              color: #ffffff !important;
            }
          }
        }
      }

      .el-table__body-wrapper {
        background: #060E1F !important;

        .el-table__body {
          background: #060E1F !important;

          tr {
            background: #060E1F !important;

            td {
              background: #060E1F !important;
              color: #ffffff !important;
              border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;

              .cell {
                color: #ffffff !important;
              }
            }

            &:hover td {
              background: rgba(74, 144, 226, 0.15) !important;

              .cell {
                color: #ffffff !important;
              }
            }
          }
        }
      }

      .el-table__empty-block {
        background: #060E1F !important;

        .el-table__empty-text {
          color: rgba(255, 255, 255, 0.6) !important;
        }
      }

      .el-table__fixed,
      .el-table__fixed-right {
        background: #060E1F !important;
      }
    }
  }
}

.title-text {
  color: #4A90E2;
  cursor: pointer;

  &:hover {
    color: #357abd;
    text-decoration: underline;
  }
}

.status-tag {
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid transparent;

  &.status-ongoing {
    background: rgba(40, 167, 69, 0.25);
    color: #5cb85c;
    border-color: rgba(40, 167, 69, 0.4);
  }

  &.status-ended {
    background: rgba(108, 117, 125, 0.25);
    color: #adb5bd;
    border-color: rgba(108, 117, 125, 0.4);
  }

  &.status-pending {
    background: rgba(255, 193, 7, 0.25);
    color: #ffdb4d;
    border-color: rgba(255, 193, 7, 0.4);
  }

  &.status-default {
    background: rgba(108, 117, 125, 0.25);
    color: #adb5bd;
    border-color: rgba(108, 117, 125, 0.4);
  }
}

.view-count {
  color: #4A90E2;
  font-weight: bold;
}

.page-box {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(74, 144, 226, 0.3);
  margin-top: 16px;

  :deep(.el-pagination) {
    .el-pagination__total,
    .el-pagination__jump {
      color: #ffffff;
    }

    .el-select .el-select__wrapper {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(74, 144, 226, 0.3);

      .el-select__selected-item {
        color: #ffffff;
      }
    }

    .el-pager {
      li {
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
        border: 1px solid rgba(74, 144, 226, 0.3);

        &:hover {
          background: rgba(74, 144, 226, 0.3);
        }

        &.is-active {
          background: #4a90e2;
          color: #ffffff;
        }
      }
    }

    .btn-prev,
    .btn-next {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      border: 1px solid rgba(74, 144, 226, 0.3);

      &:hover {
        background: rgba(74, 144, 226, 0.3);
      }
    }
  }
}

// 输入框样式
.activity-detail-dialog {
  .el-input {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      box-shadow: none !important;

      &:hover {
        border-color: rgba(255, 255, 255, 0.4) !important;
      }

      &.is-focus {
        border-color: #4A90E2 !important;
        box-shadow: 0 0 0 1px rgba(74, 144, 226, 0.2) !important;
      }
    }

    .el-input__inner {
      background: transparent !important;
      border: none !important;
      color: #ffffff !important;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }
    }
  }

  .el-select {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      box-shadow: none !important;

      &:hover {
        border-color: rgba(255, 255, 255, 0.4) !important;
      }

      &.is-focus {
        border-color: #4A90E2 !important;
        box-shadow: 0 0 0 1px rgba(74, 144, 226, 0.2) !important;
      }
    }

    .el-input__inner {
      background: transparent !important;
      border: none !important;
      color: #ffffff !important;
    }

    .el-select__caret {
      color: rgba(255, 255, 255, 0.7) !important;
    }

    .el-select__placeholder {
      color: rgba(255, 255, 255, 0.5) !important;
    }
  }
}

// 深色模式全局覆盖
.activity-detail-dialog {
  // 覆盖Element Plus的默认样式
  .el-table {
    --el-table-bg-color: #060E1F !important;
    --el-table-tr-bg-color: #060E1F !important;
    --el-table-header-bg-color: #060E1F !important;
    --el-table-row-hover-bg-color: rgba(74, 144, 226, 0.15) !important;
    --el-table-text-color: #ffffff !important;
    --el-table-header-text-color: #ffffff !important;
    --el-table-border-color: rgba(255, 255, 255, 0.1) !important;
  }

  // 分页组件深色样式
  .el-pagination {
    --el-pagination-bg-color: #060E1F !important;
    --el-pagination-text-color: #ffffff !important;
    --el-pagination-border-color: rgba(255, 255, 255, 0.1) !important;
    --el-pagination-hover-color: #4a90e2 !important;
  }

  // 表单组件深色样式
  .el-form-item__label {
    color: #ffffff !important;
  }

  // 按钮深色样式
  .el-button {
    --el-button-text-color: #ffffff !important;
    --el-button-bg-color: rgba(255, 255, 255, 0.1) !important;
    --el-button-border-color: rgba(255, 255, 255, 0.2) !important;
    --el-button-hover-text-color: #ffffff !important;
    --el-button-hover-bg-color: rgba(255, 255, 255, 0.2) !important;
    --el-button-hover-border-color: rgba(255, 255, 255, 0.4) !important;
  }
}
</style>

// 下拉选项样式
<style lang="scss">
.el-select-dropdown {
  background: #060E1F !important;
  border: 1px solid rgba(74, 144, 226, 0.3) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.8) !important;
  z-index: 999999 !important;

  .el-select-dropdown__item {
    background: #060E1F !important;
    color: #ffffff !important;
    padding: 8px 16px !important;

    &:hover {
      background: rgba(74, 144, 226, 0.2) !important;
      color: #ffffff !important;
    }

    &.selected {
      background: #4a90e2 !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }
  }
}
</style>
