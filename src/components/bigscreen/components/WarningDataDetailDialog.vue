<template>
  <Teleport to="body">
    <div v-if="visible" class="warning-data-modal" @click="handleMaskClick">
      <div class="warning-data-dialog" @click.stop>
        <!-- 弹窗头部 -->
        <div class="dialog-header">
          <div class="dialog-title">
            三色预警数据详情
            <span class="time-range-info">（{{ getTimeRangeText() }}）</span>
          </div>
          <div class="close-btn" @click="handleClose">×</div>
        </div>

        <!-- 弹窗内容 -->
        <div class="dialog-content">
          <!-- 搜索表单 -->
          <div class="search-form">
            <el-form ref="ruleFormRef" :inline="true" :model="searchForm">
              <el-form-item prop="areaCode">
                <el-input 
                  v-model="searchForm.areaCode" 
                  placeholder="区域代码" 
                  clearable 
                />
              </el-form-item>
              
              <!-- <el-form-item prop="rybType">
                <el-select
                  v-model="searchForm.rybType"
                  placeholder="预警类型"
                  clearable
                >
                  <el-option label="红色预警" value="1" />
                  <el-option label="黄色预警" value="2" />
                  <el-option label="蓝色预警" value="3" />
                </el-select>
              </el-form-item>

              <el-form-item prop="warningType">
                <el-select
                  v-model="searchForm.warningType"
                  placeholder="预警事件"
                  clearable
                >
                  <el-option label="电梯监控" value="543" />
                  <el-option label="高空抛物" value="531" />
                </el-select>
              </el-form-item>

              <el-form-item prop="disposeState">
                <el-select
                  v-model="searchForm.disposeState"
                  placeholder="处置状态"
                  clearable
                >
                  <el-option label="未处置" value="1" />
                  <el-option label="已处置" value="2" />
                  <el-option label="处置中" value="3" />
                </el-select>
              </el-form-item>
         -->
              <el-form-item>
                <el-button class="submit-btn" type="primary" :icon="Search" @click="handleSearch">
                  查询
                </el-button>
                <el-button class="reset-btn" :icon="Refresh" @click="handleReset">
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 数据列表表格 -->
          <div class="table-box">
            <el-table
              :max-height="'calc(100vh - 280px)'"
              :data="dataList"
              v-loading="loading"
              element-loading-text="加载中..."
              element-loading-background="rgba(0, 0, 0, 0.6)"
              class="warning-table"
            >
              <el-table-column label="预警类型" prop="RYBType" align="center" width="100">
                <template #default="{ row }">
                  <span class="type-tag" :class="getWarningTypeClass(row.RYBType)">
                    {{ getWarningTypeName(row.RYBType) }}
                  </span>
                </template>
              </el-table-column>

              <el-table-column label="预警事件" prop="WarningTypeName" align="center" min-width="150">
                <template #default="{ row }">
                  <span class="event-text">{{ row.WarningTypeName || '-' }}</span>
                </template>
              </el-table-column>

              <el-table-column label="所属区域" prop="AreaName" align="center" width="200">
                <template #default="{ row }">
                  <span>{{ row.AreaName || '-' }}</span>
                </template>
              </el-table-column>

              <el-table-column label="所属小区" prop="ParkName" align="center" width="120">
                <template #default="{ row }">
                  <span>{{ row.ParkName || '-' }}</span>
                </template>
              </el-table-column>

              <el-table-column label="处置状态" prop="DisposeState" align="center" width="100">
                <template #default="{ row }">
                  <span class="status-tag" :class="getStatusClass(row.DisposeState)">
                    {{ getStatusName(row.DisposeState) }}
                  </span>
                </template>
              </el-table-column>

              <el-table-column label="预警时间" prop="EventTime" align="center" width="160">
                <template #default="{ row }">
                  <span class="time-text">{{ formatTime(row.EventTime) }}</span>
                </template>
              </el-table-column>
              
              <!-- <el-table-column label="操作" align="center" width="100">
                <template #default="{ row }">
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="handleViewDetail(row)"
                    class="detail-btn"
                  >
                    查看详情
                  </el-button>
                </template>
              </el-table-column> -->
            </el-table>
          </div>

          <!-- 分页 -->
           
          <div class="page-box">
            <el-config-provider :locale="zhCn">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                :small="false"
                :disabled="false"
                background
                layout="sizes, prev, pager, next, jumper, total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                class="warning-pagination"
              />
            </el-config-provider>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

// 预警数据项接口
interface WarningDataItem {
  Id?: number
  RowNumber?: number
  WarningType?: number
  WarningTypeName?: string
  RYBType?: number
  AreaName?: string
  ParkName?: string
  DisposeState?: number
  EventTime?: string
  AssignPerson?: string
  ParkId?: number
}

// 组件Props
interface Props {
  visible: boolean
  areaCode?: string
  timeRange?: string  // 时间范围参数
}

// 组件Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'view-detail', data: WarningDataItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const ruleFormRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  areaCode: '',
  rybType: '',
  warningType: '',
  disposeState: ''
})

// 数据列表
const dataList = ref<WarningDataItem[]>([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 时间范围计算函数
const getDateRange = (rangeType: string) => {
  const now = new Date()
  const start = new Date(now)

  switch(rangeType) {
    case '0': // 当天
      start.setHours(0, 0, 0, 0)
      return {
        start: formatDateForAPI(start),
        end: formatDateForAPI(now)
      }
    case '1': // 一周
      start.setDate(start.getDate() - 7)
      return {
        start: formatDateForAPI(start),
        end: formatDateForAPI(now)
      }
    case '2': // 近三月
      start.setMonth(start.getMonth() - 3)
      return {
        start: formatDateForAPI(start),
        end: formatDateForAPI(now)
      }
    case '3': // 近六月
      start.setMonth(start.getMonth() - 6)
      return {
        start: formatDateForAPI(start),
        end: formatDateForAPI(now)
      }
    case '4': // 近一年
      start.setFullYear(start.getFullYear() - 1)
      return {
        start: formatDateForAPI(start),
        end: formatDateForAPI(now)
      }
    default:
      // 默认近三月
      start.setMonth(start.getMonth() - 3)
      return {
        start: formatDateForAPI(start),
        end: formatDateForAPI(now)
      }
  }
}

// 格式化日期为API需要的格式
const formatDateForAPI = (date: Date) => {
  return date.toISOString().split('T')[0]
}

// 获取时间范围文本
const getTimeRangeText = () => {
  const timeRangeMap: Record<string, string> = {
    '0': '当天',
    '1': '一周',
    '2': '近三月',
    '3': '近六月',
    '4': '近一年'
  }
  return timeRangeMap[props.timeRange || '2'] || '近三月'
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 初始化搜索表单
    searchForm.areaCode = props.areaCode || '510183'
    getDataList()
  }
})

// 监听时间范围变化
watch(() => props.timeRange, () => {
  if (props.visible) {
    getDataList()
  }
})

// 获取数据列表
const getDataList = async () => {
  try {
    loading.value = true

    // 根据时间范围计算日期
    const { start, end } = getDateRange(props.timeRange || '2')

    console.log('🕐 使用时间范围:', props.timeRange, '对应时间段:', getTimeRangeText(), '日期范围:', start, '至', end)

    const params = {
      Rows: pageSize.value,
      Page: currentPage.value,
      AreaCode: searchForm.areaCode || props.areaCode || '510183',
      DateRangeMin: start,
      DateRangeMax: end,
      ParkId: null,
      DisposeState: searchForm.disposeState ? parseInt(searchForm.disposeState) : null,
      WarningType: searchForm.warningType ? parseInt(searchForm.warningType) : null,
      RYBType: searchForm.rybType ? parseInt(searchForm.rybType) : null
    }
    
    console.log('🔍 三色预警数据详情查询参数:', params)

    // 调用API
    const response = await fetch('https://qlzhsq.qlzhsq.cn:8131/api/DCEventRYB/RYBEventList', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params)
    })
    
    const result = await response.json()
    console.log('🔍 三色预警数据详情API响应:', result)

    if (result && result.state === 1 && result.message) {
      dataList.value = result.message.Rows || []
      total.value = result.message.Total || 0
      console.log('✅ 三色预警数据详情更新成功，共', dataList.value.length, '条数据')
    } else {
      console.warn('⚠️ 三色预警数据详情API返回数据格式异常:', result)
      throw new Error('API返回数据格式异常')
    }
  } catch (error) {
    console.error('获取三色预警数据详情失败:', error)
    ElMessage.warning('获取数据失败，请稍后重试')
    
    // 使用模拟数据
    dataList.value = [
      {
        Id: 1,
        RowNumber: 1,
        RYBType: 1,
        WarningType: 531,
        WarningTypeName: '高空抛物',
        AreaName: '邛崃市-临邛街道-前进社区',
        ParkName: '东岳臻苑',
        DisposeState: 2,
        EventTime: '2025-04-15 10:30:00',
        AssignPerson: '',
        ParkId: 3391
      },
      {
        Id: 2,
        RowNumber: 2,
        RYBType: 2,
        WarningType: 543,
        WarningTypeName: '电梯监控',
        AreaName: '邛崃市-羊安街道-羊安社区',
        ParkName: '博盛康郡',
        DisposeState: 1,
        EventTime: '2025-04-14 14:20:00',
        AssignPerson: '',
        ParkId: 3388
      }
    ]
    total.value = 2
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  getDataList()
}

// 重置
const handleReset = () => {
  ruleFormRef.value?.resetFields()
  searchForm.areaCode = props.areaCode || '510183'
  searchForm.rybType = ''
  searchForm.warningType = ''
  searchForm.disposeState = ''
  currentPage.value = 1
  getDataList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  getDataList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getDataList()
}

// 查看详情
const handleViewDetail = (row: WarningDataItem) => {
  emit('view-detail', row)
}

// 获取预警类型名称
const getWarningTypeName = (type: number | null) => {
  const typeMap: Record<number, string> = {
    1: '红色预警',
    2: '黄色预警',
    3: '蓝色预警'
  }
  return typeMap[type || 0] || '-'
}

// 获取预警类型样式
const getWarningTypeClass = (type: number | null) => {
  const typeMap: Record<number, string> = {
    1: 'type-red',
    2: 'type-yellow',
    3: 'type-blue'
  }
  return typeMap[type || 0] || 'type-default'
}

// 获取状态名称
const getStatusName = (state: number | null) => {
  const stateMap: Record<number, string> = {
    1: '未处置',
    2: '已处置',
    3: '处置中'
  }
  return stateMap[state || 0] || '-'
}

// 获取状态样式
const getStatusClass = (state: number | null) => {
  const stateMap: Record<number, string> = {
    1: 'status-pending',
    2: 'status-disposed',
    3: 'status-processing'
  }
  return stateMap[state || 0] || 'status-default'
}

// 获取区域名称
const getAreaName = (areaCode: string) => {
  // 这里可以根据区域代码返回区域名称
  const areaMap: Record<string, string> = {
    '510183001': '临邛街道',
    '510183002': '文君街道',
    '510183212': '大同镇'
  }
  return areaMap[areaCode] || ''
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return '-'
  return time.replace('T', ' ').substring(0, 19)
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 点击遮罩关闭
const handleMaskClick = () => {
  emit('update:visible', false)
}

// 组件挂载时的初始化
onMounted(() => {
  if (props.visible) {
    getDataList()
  }
})
</script>

<style lang="scss" scoped>
.warning-data-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
  
  /* 隐藏滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

.warning-data-dialog {
  width: 1200px;
  max-width: 95vw;
  max-height: 95vh;
  background: #060E1F;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(74, 144, 226, 0.3);
}

.dialog-header {
  padding: 16px 24px;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #060E1F;

  .dialog-title {
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;

    .time-range-info {
      font-size: 14px;
      font-weight: normal;
      color: #4A90E2;
      margin-left: 8px;
    }
  }

  .close-btn {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(74, 144, 226, 0.3);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background: rgba(74, 144, 226, 0.2);
      border-color: #4A90E2;
    }
  }
}

.dialog-content {
  padding: 24px;
  background: #060E1F;
  overflow-y: auto;
  flex: 1;
}

.search-form {
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(74, 144, 226, 0.2);

  .el-form-item {
    margin-bottom: 0;
    margin-right: 16px;
  }

  .submit-btn {
    background: #4A90E2;
    border: none;
    color: #ffffff;

    &:hover {
      background: #357abd;
    }
  }

  .reset-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.table-box {
  background: #060E1F;

  .warning-table {
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    background: #060E1F !important;

    :deep(.el-table) {
      background: #060E1F !important;
      color: #ffffff;
    }

    :deep(.el-table__header-wrapper) {
      background: #060E1F !important;

      .el-table__header {
        background: #060E1F !important;

        th {
          background: #060E1F !important;
          border-bottom: 1px solid rgba(74, 144, 226, 0.3);
          color: #ffffff !important;
          font-weight: bold;
          font-size: 13px;
        }
      }
    }

    :deep(.el-table__body-wrapper) {
      background: #060E1F !important;

      .el-table__body {
        background: #060E1F !important;

        tr {
          background: #060E1F !important;

          &:hover {
            background: rgba(23, 81, 159, 0.2) !important;
          }

          &:nth-child(even) {
            background: rgba(255, 255, 255, 0.02) !important;

            &:hover {
              background: rgba(23, 81, 159, 0.2) !important;
            }
          }

          td {
            background: transparent !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: #ffffff !important;
            font-size: 12px;
          }
        }
      }
    }

    :deep(.el-table__empty-block) {
      background: #060E1F !important;
      color: #ffffff;
    }

    :deep(.el-table__empty-text) {
      color: #ffffff !important;
    }
  }

  .type-tag {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;

    &.type-red {
      background: rgba(255, 49, 65, 0.2);
      color: #FF3141;
      border: 1px solid #FF3141;
    }

    &.type-yellow {
      background: rgba(255, 159, 24, 0.2);
      color: #FF9F18;
      border: 1px solid #FF9F18;
    }

    &.type-blue {
      background: rgba(22, 119, 255, 0.2);
      color: #1677FF;
      border: 1px solid #1677FF;
    }

    &.type-default {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
  }

  .status-tag {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;

    &.status-disposed {
      background: rgba(0, 255, 0, 0.2);
      color: #00ff00;
      border: 1px solid #00ff00;
    }

    &.status-pending {
      background: rgba(255, 165, 0, 0.2);
      color: #FFA500;
      border: 1px solid #FFA500;
    }

    &.status-processing {
      background: rgba(0, 191, 255, 0.2);
      color: #00BFFF;
      border: 1px solid #00BFFF;
    }

    &.status-default {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
  }

  .event-text {
    color: #ffffff;
  }

  .time-text {
    color: #4A90E2;
  }

  .detail-btn {
    background: #4A90E2;
    border: none;
    color: #ffffff;
    font-size: 12px;

    &:hover {
      background: #357abd;
    }
  }
}
.page-box {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(74, 144, 226, 0.3);
  margin-top: 16px;
  
  :deep(.el-pagination) {
    .el-pagination__total,
    .el-pagination__jump {
      color: #ffffff;
    }
    
    .el-select .el-select__wrapper {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(74, 144, 226, 0.3);
      
      .el-select__selected-item {
        color: #ffffff;
      }
    }
    
    .el-pager {
      li {
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
        border: 1px solid rgba(74, 144, 226, 0.3);
        
        &:hover {
          background: rgba(74, 144, 226, 0.3);
        }
        
        &.is-active {
          background: #4a90e2;
          color: #ffffff;
        }
      }
    }
    
    .btn-prev,
    .btn-next {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      border: 1px solid rgba(74, 144, 226, 0.3);
      
      &:hover {
        background: rgba(74, 144, 226, 0.3);
      }
    }
  }
}



// 深色模式全局覆盖
.warning-detail-dialog {
  // 覆盖Element Plus的默认样式
  .el-table {
    --el-table-bg-color: #060E1F !important;
    --el-table-tr-bg-color: #060E1F !important;
    --el-table-header-bg-color: #060E1F !important;
    --el-table-row-hover-bg-color: rgba(74, 144, 226, 0.15) !important;
    --el-table-text-color: #ffffff !important;
    --el-table-header-text-color: #ffffff !important;
    --el-table-border-color: rgba(255, 255, 255, 0.1) !important;
  }

  // 分页组件深色样式
  .el-pagination {
    --el-pagination-bg-color: #060E1F !important;
    --el-pagination-text-color: #ffffff !important;
    --el-pagination-border-color: rgba(255, 255, 255, 0.1) !important;
    --el-pagination-hover-color: #4a90e2 !important;
    background: #060E1F !important;

    .el-pagination__total,
    .el-pagination__jump,
    .el-pagination__sizes,
    .el-pagination__editor {
      background: #060E1F !important;
      color: #ffffff !important;
    }

    .btn-prev,
    .btn-next,
    .el-pager li {
      background: #060E1F !important;
      color: #ffffff !important;
      border: 1px solid rgba(74, 144, 226, 0.3) !important;
    }
  }

  // 表单组件深色样式
  .el-form-item__label {
    color: #ffffff !important;
  }

  .el-input__wrapper {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;

    .el-input__inner {
      color: #ffffff !important;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }
    }
  }

  .el-select .el-input .el-select__caret {
    color: #ffffff !important;
  }
}

// 强制覆盖所有可能的白色背景
.warning-detail-modal {
  * {
    &:not(.el-icon):not(.el-loading-spinner):not(.el-loading-text) {
      background-color: #060E1F !important;
    }
  }

  .el-table,
  .el-table__header,
  .el-table__body,
  .el-table__header-wrapper,
  .el-table__body-wrapper,
  .el-pagination,
  .el-pager,
  .el-pager li,
  .btn-prev,
  .btn-next {
    background: #060E1F !important;
    background-color: #060E1F !important;
  }
}
</style>
