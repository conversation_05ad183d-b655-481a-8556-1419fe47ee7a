<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  modelValue: string
  tabs?: Array<{label: string, value: string}>
}>()

const emit = defineEmits(['update:modelValue', 'change'])

const tabs = ref(props.tabs || [
  { label: 'labe1', value: '0' },
  { label: 'labe2', value: '1' }
])

function handleChange(val: string) {
  emit('update:modelValue', val)
  emit('change', val)
}
</script>

<template>
  <div class="split-tab">
    <template v-for="(tab, index) in tabs" :key="tab.value">
      <span
        :class="['tab-item', { active: modelValue === tab.value }]"
        @click="handleChange(tab.value)"
      >
        {{ tab.label }}
      </span>
      <span v-if="index < tabs.length - 1" class="tab-divider">/</span>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.split-tab {
  display: flex;
  align-items: center;
  margin-right: 0.520vw;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  
 

  .tab-item {
    cursor: pointer;
    padding: 0 0.130vw;
    transition: color 0.3s ease;
    
    @media (max-width: 2000px) {
      padding: 0 0.208vw;
    }

    &:hover {
      color: rgba(255, 255, 255, 0.9);
    }

    &.active {
      color: #00FFFF;
      font-weight: 500;
    }
  }

  .tab-divider {
    padding: 0 0.130vw;
    color: rgba(255, 255, 255, 0.5);
    
    @media (max-width: 2000px) {
      padding: 0 0.208vw;
    }
  }
}
</style> 