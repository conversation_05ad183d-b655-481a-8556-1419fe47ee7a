<template>
  <div class="reuse-overview">
    <div class="data-item" style="border: 1px solid #FF9F18">
      <div class="icon">
        <img src="@/assets/common/received.png" alt="received">
      </div>
      <div class="text">
        <div class="name">{{ titleData.receivedTitle}}</div>
        <div class="value">{{ overviewData.receivedCount || 0 }}{{ titleData.receivedUnit }}</div>
      </div>
    </div>
    <div class="data-item" style="border: 1px solid #00B578">
      <div class="icon">
        <img src="@/assets/common/feedback.png" alt="feedback">
      </div>
      <div class="text">
        <div class="name">{{ titleData.feedbackTitle}}</div>
        <div class="value">{{ overviewData.feedbackCount || 0 }}{{ titleData.feedbackUnit }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  overviewData: {
    type: Object,
    required: true,
    default: () => ({
      receivedCount: 0,
      feedbackCount: 0
    })
  },
  titleData: {
    type: Object,
    required: false,
    default: () => ({
      receivedTitle: '已接收',
      feedbackTitle: '已反馈',
      receivedUnit: '条',
      feedbackUnit: '条'
    })
  }
})
</script>

<style lang="scss" scoped>
.reuse-overview {
  display: flex;
  justify-content: space-between;
  gap: one(20);
  
  @media (max-width: 2000px) {
    gap: two(20);
  }
  
  .data-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.05); 
    border-radius: one(8);
    padding: 15px;
    width: 140px;
    height: 68px;
    transition: all 0.3s ease;
    
 
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(one(-2));
      
      @media (max-width: 2000px) {
        transform: translateY(two(-2));
      }
    }
    
    .icon {
      width: one(40);
      height: one(40);
      margin-right: one(15);
      
      @media (max-width: 2000px) {
        width: two(40);
        height: two(40);
        margin-right: two(15);
      }
      
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    
    .text {
      .name {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0.130vw;
        font-weight: 500;
        
     
      }
      
      .value {
        font-size: 12px;
        font-weight: bold;
        color: #fff;
        
      
      }
    }
  }
}
</style> 