<template>
  <div class="right1-component">
    <!-- 标题栏 -->
    <div class="header-bar">
      <div class="header-back" @click="handleGoBack" :style="{ visibility: 'visible' }">
        <span class="back-text">上一页</span>
      </div>
      <div class="header-title">
        <div class="title-icon">
          <img src="@/assets/common/icon3.png" alt="title-icon" />
        </div>
        <span class="title-text">{{ currentPageTitle }}</span>
      </div>
      <div
        class="header-back"
        @click="handleGoNext"
        :style="{
          visibility:
            fullscreenStore.screenMode === 'wide' || fullscreenStore.isFullscreen
              ? 'hidden'
              : 'visible',
        }"
      >
        <span class="back-text">下一页</span>
      </div>
    </div>

    <!-- 第一页：协商铃 -->
    <template v-if="currentPage === 1">
      <div class="module-box">
        <ModuleTitle title="社区响应概况" />
        <DateGroup :index="bellTimeRange" :dateData="timeOptions" @change="changeBellOverview" />
        <CommunityResponse :responseData="bellOverviewData" />
        <div class="action-buttons" style="margin-top: 20px">
          <button class="action-btn bg1" @click="handleCommunityResponseResourceMap">
            资源地图分布
          </button>
          <button class="action-btn bg2" @click="handleCommunityResponseHeatMap">
            辖区热力分布
          </button>
          <button class="action-btn bg3" @click="handleCommunityResponseDataDetail">
            数据详情
          </button>
        </div>
      </div>

      <div class="module-box">
        <ModuleTitle title="社区响应处理情况" />
        <DateGroup
          :index="bellProcessTimeRange"
          :dateData="processTimeOptions"
          @change="changeBellProcess"
        />
        <EchartsProcessStatus
          :chartData="bellProcessData"
          :seriesNames="{
            received: '响铃数量',
            feedback: '响应数量',
          }"
          :loading="bellProcessLoading"
        />
      </div>

      <div class="module-box">
        <ModuleTitle title="社区来电时段概况" />
        <EchartsCallTrend :chartData="bellCurveData" :loading="bellCurveLoading" timeType="both" />
      </div>

      <div class="module-box">
        <ModuleTitle title="AI智能客服热门问题" />
        <DateGroup
          :index="bellHotTimeRange"
          :dateData="processTimeOptions"
          @change="changeBellHot"
        />
        <EchartsTreemap :chartData="bellHotData" :loading="bellHotLoading" />
      </div>
    </template>

    <!-- 第二页：连心桥 -->
    <template v-else-if="currentPage === 2">
      <div class="module-box">
        <ModuleTitle title="微互助概况" />
        <DateGroup
          :index="mutualTimeRange"
          :dateData="timeOptions"
          @change="changeMutualOverview"
        />
        <MutualAidOverview :overviewData="mutualOverviewData" :loading="mutualOverviewLoading" />
      </div>

      <div class="module-box">
        <ModuleTitle title="微互助达成情况分布" />
        <div style="display: flex; align-items: center">
          <SplitTab
            v-model="mutualTabValue"
            :tabs="[
              { label: '微心愿', value: '0' },
              { label: '微服务', value: '1' },
            ]"
            @change="handleMutualTabChange"
          />
          <DateGroup
            :index="mutualDistTimeRange"
            :dateData="processTimeOptions"
            @change="changeMutualDist"
          />
        </div>
        <EchartsCompletionDistribution :chartData="mutualDistData" :loading="mutualDistLoading" />
      </div>

      <div class="module-box">
        <ModuleTitle title="微互助服务人数" />
        <DateGroup
          :index="mutualServiceTimeRange"
          :dateData="processTimeOptions"
          @change="changeMutualService"
        />
        <EchartsParticipantHorizontal
          :chartData="mutualServiceData"
          :loading="mutualServiceLoading"
        />
      </div>

      <div class="module-box">
        <ModuleTitle title="微心愿/微服务热力图" />
        <div style="display: flex; align-items: center">
          <SplitTab
            v-model="mutualHotTabValue"
            :tabs="[
              { label: '微心愿', value: '0' },
              { label: '微服务', value: '1' },
            ]"
            @change="handleMutualHotTabChange"
          />
          <DateGroup
            :index="mutualHotTimeRange"
            :dateData="processTimeOptions"
            @change="changeMutualHot"
          />
        </div>
        <EchartsTreemap :chartData="mutualHotData" :loading="mutualHotLoading" />
      </div>
    </template>

    <!-- 第三页：社区共享 -->
    <template v-else-if="currentPage === 3">
      <!-- 社区共享选项卡 -->
      <div class="sharing-tabs">
        <div
          class="tab-item"
          :class="{ active: sharingActiveTab === 'goods' }"
          @click="handleSharingTabChange('goods')"
        >
          社区好物
        </div>
        <div
          class="tab-item"
          :class="{ active: sharingActiveTab === 'shared' }"
          @click="handleSharingTabChange('shared')"
        >
          社区物品
        </div>
        <div
          class="tab-item"
          :class="{ active: sharingActiveTab === 'market' }"
          @click="handleSharingTabChange('market')"
        >
          跳蚤市场
        </div>
      </div>

      <!-- 社区好物内容 -->
      <template v-if="sharingActiveTab === 'goods'">
        <div class="module-box">
          <ModuleTitle title="社区好物概览" />
          <DateGroup
            :index="goodsTimeRange"
            :dateData="timeOptions"
            @change="changeGoodsOverview"
          />
          <ReuseOverview
            :overviewData="goodsOverviewData"
            :titleData="{ itemsTitle: '社区好物物品', clicksTitle: '总浏览量' }"
          />
          <div class="action-buttons" style="margin-top: 20px">
            <button class="action-btn bg1" @click="handleGoodsResourceMap">资源地图分布</button>
            <button class="action-btn bg2" @click="handleGoodsHeatMap">辖区热力分布</button>
            <button class="action-btn bg3" @click="handleGoodsDataDetail">数据详情</button>
          </div>
        </div>

        <div class="module-box">
          <ModuleTitle title="社区好物热浏览概况" />
          <DateGroup
            :index="goodsHotTimeRange"
            :dateData="processTimeOptions"
            @change="changeGoodsHot"
          />
          <EchartsTreemap :chartData="goodsHotData" :loading="goodsHotLoading" />
        </div>

        <div class="module-box">
          <ModuleTitle title="社区好物浏览及使用人数分布" />
          <DateGroup
            :index="goodsDistTimeRange"
            :dateData="processTimeOptions"
            @change="changeGoodsDist"
          />
          <EchartsReuseDistribution
            :chartData="goodsDistData"
            :loading="goodsDistLoading"
            :seriesNames="{
              goods: '社区好物数量',
              views: '浏览量',
            }"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="社区好物热门贴" />
          <DateGroup
            :index="goodsHotTableTimeRange"
            :dateData="processTimeOptions"
            @change="changeGoodsHotTable"
          />
          <ReuseTable
            :tableData="goodsHotTableData"
            :columns="goodsHotTableColumns"
            :loading="goodsHotTableLoading"
          />
        </div>
      </template>

      <!-- 社区物品内容 -->
      <template v-else-if="sharingActiveTab === 'shared'">
        <div class="module-box">
          <ModuleTitle title="共享物品概览" />
          <DateGroup
            :index="sharedTimeRange"
            :dateData="timeOptions"
            @change="changeSharedOverview"
          />
          <ReuseOverview
            :overviewData="sharedOverviewData"
            :titleData="{ itemsTitle: '共享物品', clicksTitle: '总浏览量' }"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="共享热浏览概况" />
          <DateGroup
            :index="sharedHotTimeRange"
            :dateData="processTimeOptions"
            @change="changeSharedHot"
          />
          <EchartsTreemap :chartData="sharedHotData" :loading="sharedHotLoading" />
        </div>

        <div class="module-box">
          <ModuleTitle title="共享物品浏览及使用人数分布" />
          <DateGroup
            :index="sharedDistTimeRange"
            :dateData="processTimeOptions"
            @change="changeSharedDist"
          />
          <EchartsReuseDistribution
            :chartData="sharedDistData"
            :loading="sharedDistLoading"
            :seriesNames="{
              goods: '共享物品数量',
              views: '浏览量',
            }"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="共享物品热门贴" />
          <DateGroup
            :index="sharedHotTableTimeRange"
            :dateData="processTimeOptions"
            @change="changeSharedHotTable"
          />
          <ReuseTable
            :tableData="sharedHotTableData"
            :columns="sharedHotTableColumns"
            :loading="sharedHotTableLoading"
          />
        </div>
      </template>

      <!-- 跳蚤市场内容 -->
      <template v-else-if="sharingActiveTab === 'market'">
        <div class="module-box">
          <ModuleTitle title="二手闲置概览" />
          <DateGroup
            :index="marketTimeRange"
            :dateData="timeOptions"
            @change="changeMarketOverview"
          />
          <ReuseOverview
            :overviewData="marketOverviewData"
            :titleData="{ itemsTitle: '二手闲置物品', clicksTitle: '总浏览量' }"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="闲置热浏览概况" />
          <DateGroup
            :index="marketHotTimeRange"
            :dateData="processTimeOptions"
            @change="changeMarketHot"
          />
          <EchartsTreemap :chartData="marketHotData" :loading="marketHotLoading" />
        </div>

        <div class="module-box">
          <ModuleTitle title="闲置物品浏览及使用人数分布" />
          <DateGroup
            :index="marketDistTimeRange"
            :dateData="processTimeOptions"
            @change="changeMarketDist"
          />
          <EchartsReuseDistribution
            :chartData="marketDistData"
            :loading="marketDistLoading"
            :seriesNames="{
              goods: '闲置物品数量',
              views: '浏览量',
            }"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="二手闲置热门贴" />
          <DateGroup
            :index="marketHotTableTimeRange"
            :dateData="processTimeOptions"
            @change="changeMarketHotTable"
          />
          <ReuseTable
            :tableData="marketHotTableData"
            :columns="marketHotTableColumns"
            :loading="marketHotTableLoading"
          />
        </div>
      </template>
    </template>

    <!-- 第四页：社区圈子 -->
    <template v-else-if="currentPage === 4">
      <!-- 社区圈子选项卡 -->
      <div class="circle-tabs">
        <div
          class="tab-item"
          :class="{ active: circleActiveTab === 'activity' }"
          @click="handleCircleTabChange('activity')"
        >
          社区活动
        </div>
        <div
          class="tab-item"
          :class="{ active: circleActiveTab === 'space' }"
          @click="handleCircleTabChange('space')"
        >
          社区空间
        </div>
        <div
          class="tab-item"
          :class="{ active: circleActiveTab === 'craftsman' }"
          @click="handleCircleTabChange('craftsman')"
        >
          社区匠人
        </div>
      </div>

      <!-- 社区活动内容 -->
      <template v-if="circleActiveTab === 'activity'">
        <div class="module-box">
          <ModuleTitle title="社区活动概览" />
          <DateGroup
            :index="activityTimeRange"
            :dateData="timeOptions"
            @change="changeActivityOverview"
          />
          <ActivityOverview :overviewData="activityOverviewData" />
          <div class="action-buttons" style="margin-top: 20px">
            <button class="action-btn bg1" @click="handleActivityResourceMap">资源地图分布</button>
            <button class="action-btn bg2" @click="handleActivityHeatMap">辖区热力分布</button>
            <button class="action-btn bg3" @click="handleActivityDataDetail">数据详情</button>
          </div>
        </div>

        <div class="module-box">
          <ModuleTitle title="社区活动热浏览概况" />
          <DateGroup
            :index="activityHotTimeRange"
            :dateData="processTimeOptions"
            @change="changeActivityHot"
          />
          <EchartsTreemap :chartData="activityHotData" :loading="activityHotLoading" />
        </div>

        <div class="module-box">
          <ModuleTitle title="社区活动浏览及参与人数分布" />
          <DateGroup
            :index="activityDistTimeRange"
            :dateData="processTimeOptions"
            @change="changeActivityDist"
          />
          <EchartsActivityDistribution
            :chartData="activityDistData"
            :loading="activityDistLoading"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="热门社区活动" />
          <DateGroup
            :index="activityHotTableTimeRange"
            :dateData="timeOptions"
            @change="changeActivityHotTable"
          />
          <ReuseTable
            :tableData="activityHotTableData"
            :columns="[
              { prop: 'districtName', label: '所属辖区' },
              { prop: 'title', label: '社区活动标题' },
              { prop: 'userCount', label: '参与人数' },
            ]"
            :loading="activityHotTableLoading"
          />
        </div>
      </template>

      <!-- 社区空间内容 -->
      <template v-else-if="circleActiveTab === 'space'">
        <div class="module-box">
          <ModuleTitle title="社区空间概览" />
          <DateGroup
            :index="spaceTimeRange"
            :dateData="timeOptions"
            @change="changeSpaceOverview"
          />
          <ReuseOverview
            :overviewData="spaceOverviewData"
            :titleData="{ itemsTitle: '社区空间', clicksTitle: '总浏览量' }"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="社区空间热浏览概况" />
          <DateGroup
            :index="spaceHotTimeRange"
            :dateData="processTimeOptions"
            @change="changeSpaceHot"
          />
          <EchartsTreemap :chartData="spaceHotData" :loading="spaceHotLoading" />
        </div>

        <div class="module-box">
          <ModuleTitle title="社区空间浏览及使用人数分布" />
          <DateGroup
            :index="spaceDistTimeRange"
            :dateData="processTimeOptions"
            @change="changeSpaceDist"
          />
          <EchartsReuseDistribution
            :chartData="spaceDistData"
            :loading="spaceDistLoading"
            :seriesNames="{
              goods: '社区空间数量',
              views: '浏览量',
            }"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="热门社区空间" />
          <DateGroup
            :index="spaceHotTableTimeRange"
            :dateData="timeOptions"
            @change="changeSpaceHotTable"
          />
          <ReuseTable
            :tableData="spaceHotTableData"
            :columns="spaceHotTableColumns"
            :loading="spaceHotTableLoading"
          />
        </div>
      </template>

      <!-- 社区匠人内容 -->
      <template v-else-if="circleActiveTab === 'craftsman'">
        <div class="module-box">
          <ModuleTitle title="社区匠人概览" />
          <DateGroup
            :index="craftsmanTimeRange"
            :dateData="timeOptions"
            @change="changeCraftsmanOverview"
          />
          <ReuseOverview
            :overviewData="craftsmanOverviewData"
            :titleData="{ itemsTitle: '社区匠人', clicksTitle: '总浏览量' }"
          />
          <div class="action-buttons" style="margin-top: 20px">
            <button class="action-btn bg1" @click="handleCraftsmanResourceMap">资源地图分布</button>
            <button class="action-btn bg2" @click="handleCraftsmanHeatMap">辖区热力分布</button>
            <button class="action-btn bg3" @click="handleCraftsmanDataDetail">数据详情</button>
          </div>
        </div>

        <div class="module-box">
          <ModuleTitle title="社区匠人热浏览概况" />
          <DateGroup
            :index="craftsmanHotTimeRange"
            :dateData="processTimeOptions"
            @change="changeCraftsmanHot"
          />
          <EchartsTreemap :chartData="craftsmanHotData" :loading="craftsmanHotLoading" />
        </div>

        <div class="module-box">
          <ModuleTitle title="社区匠人浏览及关注人数分布" />
          <DateGroup
            :index="craftsmanDistTimeRange"
            :dateData="processTimeOptions"
            @change="changeCraftsmanDist"
          />
          <EchartsReuseDistribution
            :chartData="craftsmanDistData"
            :loading="craftsmanDistLoading"
            :seriesNames="{
              goods: '社区匠人数量',
              views: '浏览量',
            }"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="热门社区匠人" />
          <DateGroup
            :index="craftsmanHotTableTimeRange"
            :dateData="processTimeOptions"
            @change="changeCraftsmanHotTable"
          />
          <ReuseTable
            :tableData="craftsmanHotTableData"
            :columns="craftsmanHotTableColumns"
            :loading="craftsmanHotTableLoading"
          />
        </div>
      </template>
    </template>

    <!-- 第五页：信息公告 -->
    <template v-else-if="currentPage === 5">
      <div class="module-box">
        <ModuleTitle title="信息公告概况" />
        <DateGroup
          :index="informationTimeRange"
          :dateData="timeOptions"
          @change="changeInformationOverview"
        />
        <InformationOverview
          :overviewData="informationOverviewData"
          @resourceMap="handleResourceMap"
          @heatMap="handleHeatMap"
          @dataDetail="handleDataDetail"
        />
      </div>

      <div class="module-box">
        <ModuleTitle title="信息公告类型分布" />
        <EchartsInformationTypePie
          :chartData="informationTypeData"
          :loading="informationTypeLoading"
        />
      </div>

      <div class="module-box">
        <ModuleTitle title="信息公告浏览量趋势" />
        <EchartsInformationTrend
          :chartData="informationTrendData"
          :loading="informationTrendLoading"
        />
      </div>

      <div class="module-box">
        <ModuleTitle title="最新信息公告动态" />
        <InformationTable :tableData="informationTableData" :loading="informationTableLoading" />
      </div>
    </template>

    <!-- 第六页：社区生活 -->
    <template v-else-if="currentPage === 6">
      <div class="module-box">
        <ModuleTitle title="社区生活概况" />
        <DateGroup :index="lifeTimeRange" :dateData="timeOptions" @change="changeLifeOverview" />
        <CommunityLifeOverview
          :overviewData="lifeOverviewData"
          :loading="lifeOverviewLoading"
          @resourceMap="handleLifeResourceMap"
          @heatMap="handleLifeHeatMap"
          @dataDetail="handleLifeDataDetail"
        />
      </div>
      <div class="module-box">
        <ModuleTitle title="社区圈子类型分布" />
        <EchartsCommunityLifeTypeBar :chartData="lifeTypeData" :loading="lifeTypeLoading" />
      </div>

      <div class="module-box">
        <ModuleTitle title="社区圈子信息数分布" />
        <EchartsCommunityLifeUsage :chartData="lifeUsageData" :loading="lifeUsageLoading" />
      </div>

      <div class="module-box">
        <ModuleTitle title="社区圈子热门话题分布" />
        <EchartsCommunityLifeTrend :chartData="lifeTopicData" :loading="lifeTopicLoading" />
      </div>

      <!-- 社区图书模块 -->
      <div class="module-box">
        <ModuleTitle title="社区图书概况" />
        <CommunityBookOverview 
          :overview-data="bookOverviewData" 
          :loading="bookOverviewLoading"
          @resourceMap="handleBookResourceMap"
          @heatMap="handleBookHeatMap"
          @dataDetail="handleBookDataDetail"
        />
      </div>

      <div class="module-box">
        <ModuleTitle title="社区图书类型分布" />
        <EchartsBookTypePie :chartData="bookTypeData" :loading="bookTypeLoading" />
      </div>

      <div class="module-box">
        <ModuleTitle title="社区图书使用分布" />
        <EchartsCommunityLifeUsage :chartData="bookUsageData" :loading="bookUsageLoading" />
      </div>

      <div class="module-box">
        <ModuleTitle title="社区图书使用趋势图" />
        <EchartsBookUsageTrend :chartData="bookTrendData" :loading="bookTrendLoading" />
      </div>
    </template>
  </div>

  <!-- 数据详情弹窗 -->
  <NoticeDataDetailDialog
    v-model:visible="noticeDetailVisible"
    :title="noticeDetailTitle"
    :time-type="informationTimeRange"
    :area-code="getDistrictCode()"
    :resource-type="noticeResourceType"
  />

  <!-- 社区活动数据详情弹窗 -->
  <ActivityDataDetailDialog
    v-model:visible="activityDetailVisible"
    :area-code="getDistrictCode()"
    :time-range="activityTimeRange"
  />

  <!-- 社区好物数据详情弹窗 -->
  <StoreDataDetailDialog
    v-model:visible="storeDetailVisible"
    :area-code="getDistrictCode()"
    :time-range="goodsTimeRange"
  />

  <!-- 社区响应概况数据详情弹窗 -->
  <CommunityResponseDataDetailDialog
    v-model:visible="communityResponseDetailVisible"
    :area-code="getDistrictCode()"
    :time-range="bellTimeRange"
  />

  <!-- 社区匠人数据详情弹窗 -->
  <CraftsmanDataDetailDialog
    v-model:visible="craftsmanDetailVisible"
    :area-code="getDistrictCode()"
    :time-range="craftsmanTimeRange"
  />

  <!-- 社区圈子数据详情弹窗 -->
  <CommunityGroupDataDetailDialog
    v-model:visible="communityGroupDetailVisible"
    :area-code="getDistrictCode()"
    :time-range="lifeTimeRange"
  />

  <!-- 社区图书数据详情弹窗 -->
  <CommunityBookDataDetailDialog
    v-model:visible="bookDetailVisible"
    :area-code="getDistrictCode()"
    :time-range="lifeTimeRange"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, onBeforeUnmount } from "vue";
import { ElMessage } from "element-plus";
import ModuleTitle from "./components/ModuleTitle.vue";
import DateGroup from "./components/DateGroup.vue";
import ReuseOverview from "./components/ReuseOverview.vue";
import ActivityOverview from "./components/ActivityOverview.vue";
import EchartsHotReuse from "./components/EchartsHotReuse.vue";
import EchartsReuseDistribution from "./components/EchartsReuseDistribution.vue";
import EchartsActivityDistribution from "./components/EchartsActivityDistribution.vue";
import CommunityResponse from "./components/CommunityResponse.vue";
import EchartsProcessStatus from "./components/EchartsProcessStatus.vue";
import EchartsCallTrend from "./components/EchartsCallTrend.vue";
import EchartsTreemap from "./components/EchartsTreemap.vue";
import MutualAidOverview from "./components/MutualAidOverview.vue";
import EchartsCompletionDistribution from "./components/EchartsCompletionDistribution.vue";
import EchartsParticipantHorizontal from "./components/EchartsParticipantHorizontal.vue";
import SplitTab from "./components/SplitTab.vue";
import ReuseTable from "./components/ReuseTable.vue";
import InformationOverview from "./components/InformationOverview.vue";
import EchartsInformationTypePie from "./components/EchartsInformationTypePie.vue";
import EchartsInformationTrend from "./components/EchartsInformationTrend.vue";
import InformationTable from "./components/InformationTable.vue";
import ActivityDataDetailDialog from "./components/ActivityDataDetailDialog.vue";
import StoreDataDetailDialog from "./components/StoreDataDetailDialog.vue";
import { useFullscreenStore } from "@/stores/fullscreen";
import {
  getBellInfoApi,
  getBellListApi,
  getAiHotsApi,
  getBellCallLineApi,
  getMutualAidInfoApi,
  getMutualAidListApi,
  getMutualAidServiceListApi,
  getMutualAidHotsApi,
  getStoreInfoApi,
  getStoreHotsApi,
  getStoreListApi,
  getMarketInfoApi,
  getMarketHotsApi,
  getMarketListApi,
  getActivityInfoApi,
  getActivityHotsApi,
  getActivityListApi,
  getSpaceInfoApi,
  getSpaceHotList,
  getSpaceListApi,
  getCraftsmanInfoApi,
  getCraftsmanHotListApi,
  getCraftsmanDistributionApi,
} from "@/api/suggestion";
import {
  getNoticeStatisticsApi,
  getNoticeMapResourceApi,
  getCallMapResourceApi,
  getCraftsmanMapResourceApi,
  getStoreMapResourceApi,
  getActivityMapResourceApi,
} from "@/api/bigscreen";
import type {
  BellInfoParams,
  BellListParams,
  AiHotsParams,
  BellCallLineParams,
  MutualAidInfoParams,
  MutualAidListParams,
  MutualAidServiceListParams,
  MutualAidHotsParams,
  StoreInfoParams,
  StoreHotsParams,
  StoreHotItem,
  StoreListParams,
  StoreListItem,
  MarketInfoParams,
  MarketHotsParams,
  MarketListParams,
  ActivityInfoParams,
  ActivityHotsParams,
  ActivityListParams,
  SpaceInfoParams,
  SpaceHotParams,
  SpaceListParams,
  CraftsmanInfoParams,
  CraftsmanHotParams,
  CraftsmanDistributionParams,
} from "@/api/suggestion";
import type {
  NoticeStatisticsParams,
  NoticeMapResourceParams,
  CallMapResourceParams,
  CraftsmanMapResourceParams,
  StoreMapResourceParams,
  ActivityMapResourceParams,
} from "@/api/bigscreen";
import { getDistrictCode } from "@/utils/district";
// 导入社区生活相关的API函数和类型
import { getCommunityLifeInfoApi, getCommunityBookInfoApi, getCommunityGroupMapResourceApi, getCommunityBookMapResourceApi } from "@/api/community";
import type { CommunityLifeInfoParams, CommunityBookInfoParams, CommunityGroupMapResourceParams, CommunityBookMapResourceParams } from "@/api/community";
import NoticeDataDetailDialog from "./components/NoticeDataDetailDialog.vue";
import CommunityResponseDataDetailDialog from "./components/CommunityResponseDataDetailDialog.vue";
import CraftsmanDataDetailDialog from "./components/CraftsmanDataDetailDialog.vue";
import CommunityGroupDataDetailDialog from "./components/CommunityGroupDataDetailDialog.vue";
import CommunityBookDataDetailDialog from "./components/CommunityBookDataDetailDialog.vue";
import CommunityLifeOverview from "./components/CommunityLifeOverview.vue";
import CommunityBookOverview from "./components/CommunityBookOverview.vue";
import EchartsCommunityLifeTypeBar from "./components/EchartsCommunityLifeTypeBar.vue";
import EchartsCommunityLifeUsage from "./components/EchartsCommunityLifeUsage.vue";
import EchartsCommunityLifeTrend from "./components/EchartsCommunityLifeTrend.vue";
import EchartsBookTypePie from "./components/EchartsBookTypePie.vue";
import EchartsBookUsageTrend from "./components/EchartsBookUsageTrend.vue";

// 全屏状态管理
const fullscreenStore = useFullscreenStore();

// 页面切换状态
const currentPage = ref(1); // 1: 协商铃, 2: 连心桥, 3: 社区共享, 4: 社区圈子, 5: 信息公告, 6: 社区生活
const currentPageTitle = computed(() => {
  switch (currentPage.value) {
    case 1:
      return "找社区";
    case 2:
      return "连心桥";
    case 3:
      return "社区共享";
    case 4:
      return "社区圈子";
    case 5:
      return "信息公告";
    case 6:
      return "社区生活";
    default:
      return "找社区";
  }
});

// 跟踪当前激活的地图撒点类型
const activeMapPointType = ref(""); // 'notice', 'call', 'craftsman', 'store', 'activity'

// 时间选项
const timeOptions = [
  { label: "当天", value: "0" },
  { label: "一周", value: "1" },
  { label: "近三月", value: "2" },
  { label: "近六月", value: "3" },
  { label: "近一年", value: "4" },
];

const processTimeOptions = [
  { label: "近三月", value: "2" },
  { label: "近六月", value: "3" },
  { label: "近一年", value: "4" },
];

// ========== 协商铃相关数据 ==========
const bellTimeRange = ref("0");
const bellProcessTimeRange = ref("2");
const bellHotTimeRange = ref("2");

const bellOverviewData = ref({
  callCount: 156,
  serviceCount: 98,
});

const bellProcessData = ref([
  { district: { name: "文君街道" }, receivedCount: 45, feedbackCount: 32 },
  { district: { name: "临邛街道" }, receivedCount: 32, feedbackCount: 25 },
  { district: { name: "羊安街道" }, receivedCount: 67, feedbackCount: 45 },
]);

const bellCurveData = ref({
  today: Array(24).fill(0),
  yesterday: Array(24).fill(0),
});

const bellHotData = ref([
  { content: "如何申请社区服务？", count: 156 },
  { content: "垃圾分类指南", count: 142 },
  { content: "停车位申请", count: 128 },
  { content: "物业费查询", count: 115 },
  { content: "维修报修流程", count: 98 },
]);

const bellProcessLoading = ref(false);
const bellCurveLoading = ref(false);
const bellHotLoading = ref(false);

// ========== 连心桥相关数据 ==========
const mutualTimeRange = ref("0");
const mutualDistTimeRange = ref("2");
const mutualServiceTimeRange = ref("2");
const mutualTabValue = ref("0");

const mutualOverviewData = ref({
  demandTotal: 156,
  demandEnded: 98,
  serviceTotal: 234,
  serviceEnded: 187,
});

const mutualDistData = ref([
  { district: { name: "社区A" }, publishCount: 45, completeCount: 32 },
  { district: { name: "社区B" }, publishCount: 32, completeCount: 25 },
  { district: { name: "社区C" }, publishCount: 67, completeCount: 45 },
]);

const mutualServiceData = ref([
  { district: { name: "社区A" }, wishData: 120, serviceData: 180 },
  { district: { name: "社区B" }, wishData: 85, serviceData: 120 },
  { district: { name: "社区C" }, wishData: 150, serviceData: 200 },
  { district: { name: "社区D" }, wishData: 60, serviceData: 90 },
  { district: { name: "社区E" }, wishData: 110, serviceData: 160 },
]);

const mutualDistLoading = ref(false);
const mutualServiceLoading = ref(false);
const mutualOverviewLoading = ref(false);

// 微互助热力图
const mutualHotTimeRange = ref("2");
const mutualHotTabValue = ref("0");

const mutualHotData = ref([
  { content: "我想要一盒水彩笔", count: 3 },
  { content: "免费为老人理发", count: 1 },
  { content: "上门做菜", count: 1 },
  { content: "提供房屋漏水修缮", count: 0 },
  { content: "测试", count: 0 },
]);

const mutualHotLoading = ref(false);

// ========== 社区共享相关数据 ==========
const sharingActiveTab = ref("goods");

// 社区好物
const goodsTimeRange = ref("0");
const goodsHotTimeRange = ref("2");
const goodsDistTimeRange = ref("2");

const goodsOverviewData = ref({
  itemsCount: 156,
  clicksCount: 8567,
});

const goodsHotData = ref([
  { content: "社区特产蜂蜜", count: 234 },
  { content: "手工编织围巾", count: 198 },
  { content: "自制果酱", count: 167 },
  { content: "有机蔬菜", count: 145 },
  { content: "手工皂", count: 123 },
]);

const goodsDistData = ref([
  { district: { name: "文君街道" }, goods: 45, views: 1234 },
  { district: { name: "临邛街道" }, goods: 32, views: 987 },
  { district: { name: "羊安街道" }, goods: 28, views: 756 },
]);

const goodsHotLoading = ref(false);
const goodsDistLoading = ref(false);

// 社区好物热门贴
const goodsHotTableTimeRange = ref("2");
const goodsHotTableData = ref([
  { district: "前进社区", name: "瓷胎竹编", browseNumber: 4 },
  { district: "文君井社区", name: "文君茶", browseNumber: 3 },
  { district: "渔唱社区", name: "周鸭子", browseNumber: 2 },
  { district: "顺河社区", name: "邛崃黑猪", browseNumber: 2 },
  { district: "洪川社区", name: "邛酒", browseNumber: 1 },
]);
const goodsHotTableLoading = ref(false);
const goodsHotTableColumns = [
  { prop: "district", label: "所属辖区", width: 120 },
  { prop: "name", label: "社区好物标题", width: 150 },
  { prop: "browseNumber", label: "浏览量", width: 80 },
];

// 社区物品
const sharedTimeRange = ref("0");
const sharedHotTimeRange = ref("2");
const sharedDistTimeRange = ref("2");

const sharedOverviewData = ref({
  itemsCount: 89,
  clicksCount: 4567,
});

const sharedHotData = ref([
  { content: "共享图书", count: 178 },
  { content: "儿童玩具", count: 156 },
  { content: "运动器材", count: 134 },
  { content: "厨房用具", count: 112 },
  { content: "园艺工具", count: 98 },
]);

const sharedDistData = ref([
  { district: { name: "文君街道" }, goods: 28, views: 789 },
  { district: { name: "临邛街道" }, goods: 25, views: 654 },
  { district: { name: "羊安街道" }, goods: 22, views: 543 },
]);

const sharedHotLoading = ref(false);
const sharedDistLoading = ref(false);

// 共享物品热门贴
const sharedHotTableTimeRange = ref("2");
const sharedHotTableData = ref([
  { district: "邛崃市", name: "共享雨伞", browseNumber: 50 },
  { district: "顺河社区", name: "共享婴儿推车", browseNumber: 7 },
]);
const sharedHotTableColumns = [
  { prop: "district", label: "所属辖区", width: 120 },
  { prop: "name", label: "共享物品标题", width: 180 },
  { prop: "browseNumber", label: "浏览量", width: 100 },
];
const sharedHotTableLoading = ref(false);

// 跳蚤市场
const marketTimeRange = ref("0");
const marketHotTimeRange = ref("2");
const marketDistTimeRange = ref("2");

const marketOverviewData = ref({
  itemsCount: 234,
  clicksCount: 12456,
});

const marketHotData = ref([
  { content: "二手家具", count: 345 },
  { content: "儿童用品", count: 289 },
  { content: "电子产品", count: 234 },
  { content: "服装鞋帽", count: 198 },
  { content: "书籍文具", count: 167 },
]);

const marketDistData = ref([
  { district: { name: "文君街道" }, goods: 78, views: 2345 },
  { district: { name: "临邛街道" }, goods: 65, views: 1987 },
  { district: { name: "羊安街道" }, goods: 52, views: 1654 },
]);

const marketHotLoading = ref(false);
const marketDistLoading = ref(false);

// 二手闲置热门贴
const marketHotTableTimeRange = ref("2");
const marketHotTableData = ref([
  { district: "惠民社区", name: "英朗", browseNumber: 56 },
  { district: "观音阁社区", name: "拍立得", browseNumber: 18 },
  { district: "邛崃市", name: "8成新华为电脑", browseNumber: 16 },
  { district: "顺河社区", name: "奥特曼和各种玩具", browseNumber: 13 },
  { district: "黄坝社区", name: "空调", browseNumber: 12 },
]);
const marketHotTableColumns = [
  { prop: "district", label: "所属辖区", width: 120 },
  { prop: "name", label: "社区二手闲置标题", width: 180 },
  { prop: "browseNumber", label: "浏览量", width: 100 },
];
const marketHotTableLoading = ref(false);

// ========== 社区圈子相关数据 ==========
const circleActiveTab = ref("activity");

// 社区活动
const activityTimeRange = ref("0");
const activityHotTimeRange = ref("2");
const activityDistTimeRange = ref("2");

const activityOverviewData = ref({
  activityCount: 45,
  ongoingCount: 12,
  completedCount: 33,
});

const activityHotData = ref([
  { content: "社区运动会", count: 456 },
  { content: "文艺演出", count: 389 },
  { content: "志愿服务", count: 312 },
  { content: "亲子活动", count: 278 },
  { content: "健康讲座", count: 234 },
]);

const activityDistData = ref([
  {
    name: "文君街道",
    districtName: "文君街道",
    activityCount: 15,
    count: 15,
    participantCount: 456,
    userCount: 456,
    participants: 456,
  },
  {
    name: "临邛街道",
    districtName: "临邛街道",
    activityCount: 12,
    count: 12,
    participantCount: 378,
    userCount: 378,
    participants: 378,
  },
  {
    name: "羊安街道",
    districtName: "羊安街道",
    activityCount: 10,
    count: 10,
    participantCount: 289,
    userCount: 289,
    participants: 289,
  },
]);

const activityHotLoading = ref(false);
const activityDistLoading = ref(false);

// 热门社区活动表格
const activityHotTableTimeRange = ref("4");
const activityHotTableData = ref([
  { districtName: "顺河社区", title: '"传承非遗文化，共建和谐社区"', userCount: 1 },
  { districtName: "顺河社区", title: "首届户外亲子健步走活动", userCount: 0 },
  { districtName: "顺河社区", title: "时光里的她", userCount: 0 },
]);
const activityHotTableLoading = ref(false);

// 社区空间
const spaceTimeRange = ref("0");
const spaceHotTimeRange = ref("2");
const spaceDistTimeRange = ref("2");

const spaceOverviewData = ref({
  itemsCount: 28,
  clicksCount: 3456,
});

const spaceHotData = ref([
  { content: "社区会议室", count: 234 },
  { content: "健身房", count: 198 },
  { content: "图书阅览室", count: 167 },
  { content: "儿童活动室", count: 145 },
  { content: "多功能厅", count: 123 },
]);

const spaceDistData = ref([
  { district: { name: "文君街道" }, goods: 10, views: 1234 },
  { district: { name: "临邛街道" }, goods: 8, views: 987 },
  { district: { name: "羊安街道" }, goods: 6, views: 756 },
]);

const spaceHotLoading = ref(false);
const spaceDistLoading = ref(false);

// 热门社区空间表格
const spaceHotTableTimeRange = ref("4");
const spaceHotTableData = ref([
  { districtName: "花园巷社区", name: "文脉坊", browseNumber: 30 },
  { districtName: "顺河社区", name: "好邻居超市", browseNumber: 15 },
  { districtName: "铁花社区", name: "铁花巷", browseNumber: 14 },
  { districtName: "文君井社区", name: "文君井公园", browseNumber: 13 },
  { districtName: "花园巷社区", name: "新山书屋", browseNumber: 7 },
]);

const spaceHotTableColumns = [
  { prop: "districtName", label: "所属辖区", width: "120" },
  { prop: "name", label: "社区空间名称", width: "150" },
  { prop: "browseNumber", label: "参与人数", width: "100" },
];

const spaceHotTableLoading = ref(false);

// 社区匠人
const craftsmanTimeRange = ref("0");
const craftsmanHotTimeRange = ref("2");
const craftsmanDistTimeRange = ref("2");

const craftsmanOverviewData = ref({
  itemsCount: 67,
  clicksCount: 8901,
});

const craftsmanHotData = ref([
  { content: "手工艺大师", count: 345 },
  { content: "美食达人", count: 289 },
  { content: "园艺专家", count: 234 },
  { content: "修理能手", count: 198 },
  { content: "教育专家", count: 167 },
]);

const craftsmanDistData = ref([
  { district: { name: "文君街道" }, goods: 25, views: 3456 },
  { district: { name: "临邛街道" }, goods: 20, views: 2789 },
  { district: { name: "羊安街道" }, goods: 15, views: 2134 },
]);

const craftsmanHotLoading = ref(false);
const craftsmanDistLoading = ref(false);

// 热门社区匠人表格
const craftsmanHotTableTimeRange = ref("2");
const craftsmanHotTableData = ref([
  { districtName: "顺河社区", name: "补鞋匠", browseNumber: 16 },
  { districtName: "顺河社区", name: "中药制丸师", browseNumber: 6 },
  { districtName: "顺河社区", name: "磨刀匠", browseNumber: 4 },
]);
const craftsmanHotTableLoading = ref(false);
const craftsmanHotTableColumns = [
  { prop: "districtName", label: "所属辖区", width: 120 },
  { prop: "name", label: "社区匠人标题", width: 150 },
  { prop: "browseNumber", label: "浏览次数", width: 80 },
];

// ========== 信息公告相关数据 ==========
const informationTimeRange = ref("0");

// 信息公告概况
const informationOverviewData = ref({
  totalCount: 123768,
});

// 信息公告类型分布
const informationTypeData = ref([
  { name: "政策", value: 35, color: "#4A90E2" },
  { name: "政务", value: 28, color: "#50C878" },
  { name: "安全", value: 20, color: "#FFA500" },
  { name: "活动", value: 10, color: "#FF6B6B" },
  { name: "事件", value: 4, color: "#9B59B6" },
  { name: "其他", value: 3, color: "#95A5A6" },
]);
const informationTypeLoading = ref(false);

// 信息公告浏览量趋势
const informationTrendData = ref([
  { month: "1月", publishCount: 180, viewCount: 200 },
  { month: "2月", publishCount: 220, viewCount: 240 },
  { month: "3月", publishCount: 190, viewCount: 210 },
  { month: "4月", publishCount: 170, viewCount: 180 },
  { month: "5月", publishCount: 160, viewCount: 170 },
  { month: "6月", publishCount: 180, viewCount: 200 },
  { month: "7月", publishCount: 200, viewCount: 220 },
  { month: "8月", publishCount: 150, viewCount: 160 },
]);
const informationTrendLoading = ref(false);

// 最新信息公告动态表格
const informationTableData = ref([
  { area: "文君街道", title: "关于医师执业安全须知提醒", publishTime: "2025-07-16" },
  { area: "顺河街道", title: "关于医师执业安全须知提醒", publishTime: "2025-07-16" },
  { area: "羊安街道", title: "小学踢毽人数注册踢毽", publishTime: "2025-07-16" },
  { area: "手机街道", title: "小学踢毽人数注册踢毽", publishTime: "2025-07-16" },
  { area: "临邛街道", title: "小学踢毽人数注册踢毽", publishTime: "2025-07-16" },
  { area: "火井街道", title: "小学踢毽人数注册踢毽", publishTime: "2025-07-16" },
  { area: "观音阁社区", title: "小学踢毽人数注册踢毽", publishTime: "2025-07-16" },
  { area: "宁兴街道", title: "小学踢毽人数注册踢毽", publishTime: "2025-07-16" },
]);
const informationTableLoading = ref(false);

// 社区生活测试数据
const communityLifeTestOverviewData = ref({
  totalCount: 23768,
  totalViews: 2376902,
});

const communityLifeTestTypeData = ref([
  { name: "文学", value: 2 }, // 2%
  { name: "文化", value: 2 }, // 2%
  { name: "科学", value: 13 }, // 13%
  { name: "教育", value: 10 }, // 10%
  { name: "历史", value: 7 }, // 7%
  { name: "地理", value: 10 }, // 10%
  { name: "艺术", value: 15 }, // 15%
  { name: "社会科学", value: 11 }, // 11%
  { name: "工业", value: 17 }, // 17%
  { name: "自然科学", value: 13 }, // 13%
  { name: "经济", value: 0 }, // 0% (不显示)
]);

const communityLifeTestUsageData = ref([
  { name: "临邛街道", posts: 100, views: 137 },
  { name: "文君街道", posts: 100, views: 138 },
  { name: "固驿街道", posts: 110, views: 122 },
  { name: "羊安街道", posts: 90, views: 100 },
  { name: "牟礼街道", posts: 95, views: 130 },
  { name: "孔明街道", posts: 100, views: 130 },
  { name: "平乐镇", posts: 130, views: 140 },
  { name: "夹关镇", posts: 115, views: 142 },
]);

const communityLifeTestTrendData = ref([
  { month: "1月", bookCount: 180, viewCount: 100 },
  { month: "2月", bookCount: 140, viewCount: 120 },
  { month: "3月", bookCount: 230, viewCount: 100 },
  { month: "4月", bookCount: 120, viewCount: 130 },
  { month: "5月", bookCount: 220, viewCount: 200 },
  { month: "6月", bookCount: 200, viewCount: 220 },
  { month: "7月", bookCount: 157, viewCount: 216 },
  { month: "8月", bookCount: 144, viewCount: 216 },
]);

// ========== 社区生活相关数据（与Right2Component保持一致） ==========
const lifeTimeRange = ref("0");
const lifeOverviewData = ref({
  totalCount: 1256,
  totalViews: 8934,
});

const lifeTypeData = ref([
  { name: "文学", value: 2 },
  { name: "文化", value: 2 },
  { name: "科学", value: 13 },
  { name: "教育", value: 10 },
  { name: "历史", value: 7 },
  { name: "地理", value: 10 },
  { name: "艺术", value: 15 },
  { name: "社会科学", value: 11 },
  { name: "工业", value: 17 },
  { name: "自然科学", value: 13 },
]);

const lifeTrendData = ref([
  { month: "1月", bookCount: 180, viewCount: 100 },
  { month: "2月", bookCount: 140, viewCount: 120 },
  { month: "3月", bookCount: 230, viewCount: 100 },
  { month: "4月", bookCount: 120, viewCount: 130 },
  { month: "5月", bookCount: 220, viewCount: 200 },
  { month: "6月", bookCount: 200, viewCount: 220 },
  { month: "7月", bookCount: 157, viewCount: 216 },
  { month: "8月", bookCount: 144, viewCount: 216 },
]);

const lifeUsageData = ref([
  { name: "临邛街道", posts: 100, views: 137 },
  { name: "文君街道", posts: 100, views: 138 },
  { name: "固驿街道", posts: 110, views: 122 },
  { name: "羊安街道", posts: 90, views: 100 },
  { name: "牟礼街道", posts: 95, views: 130 },
  { name: "孔明街道", posts: 100, views: 130 },
  { name: "平乐镇", posts: 130, views: 140 },
  { name: "夹关镇", posts: 115, views: 142 },
]);

const lifeTopicData = ref([
  { name: "游戏", value: 25, percentage: "20%" },
  { name: "舞蹈", value: 62, percentage: "25%" },
  { name: "跑步", value: 77, percentage: "30%" },
  { name: "宠物", value: 44, percentage: "15%" },
  { name: "邻里服务", value: 18, percentage: "10%" },
]);

// ========== 社区图书数据 ==========
const bookOverviewData = ref({
  totalCount: 23768,
  totalViews: 237690,
});

const bookTypeData = ref([
  { name: "文学", value: 35, percentage: "35%" },
  { name: "历史", value: 22, percentage: "22%" },
  { name: "科学", value: 18, percentage: "18%" },
  { name: "艺术", value: 12, percentage: "12%" },
  { name: "哲学", value: 8, percentage: "8%" },
  { name: "其他", value: 5, percentage: "5%" },
]);

const bookTrendData = ref([
  { month: "1月", bookCount: 180, viewCount: 220 },
  { month: "2月", bookCount: 145, viewCount: 180 },
  { month: "3月", bookCount: 230, viewCount: 250 },
  { month: "4月", bookCount: 190, viewCount: 200 },
  { month: "5月", bookCount: 165, viewCount: 190 },
  { month: "6月", bookCount: 200, viewCount: 230 },
  { month: "7月", bookCount: 185, viewCount: 210 },
  { month: "8月", bookCount: 175, viewCount: 195 },
]);

const bookUsageData = ref([
  { name: "临邛街道", posts: 100, views: 142 },
  { name: "文君街道", posts: 100, views: 140 },
  { name: "固驿街道", posts: 110, views: 130 },
  { name: "羊安街道", posts: 90, views: 100 },
  { name: "牟礼街道", posts: 95, views: 130 },
  { name: "孔明街道", posts: 100, views: 130 },
  { name: "平乐镇", posts: 130, views: 140 },
  { name: "夹关镇", posts: 115, views: 122 },
]);

const lifeTypeLoading = ref(false);
const lifeTrendLoading = ref(false);
const lifeUsageLoading = ref(false);
const lifeTopicLoading = ref(false);
const lifeOverviewLoading = ref(false);

// 社区图书 loading 状态
const bookOverviewLoading = ref(false);
const bookTypeLoading = ref(false);
const bookTrendLoading = ref(false);
const bookUsageLoading = ref(false);

// 数据详情弹窗状态
const noticeDetailVisible = ref(false);
const noticeDetailTitle = ref("信息公告详情");
const noticeResourceType = ref("1"); // 1: 资源地图分布, 2: 辖区热力分布

// 社区响应概况数据详情弹窗状态
const communityResponseDetailVisible = ref(false);

// 社区活动数据详情弹窗状态
const activityDetailVisible = ref(false);

// 社区好物数据详情弹窗状态
const storeDetailVisible = ref(false);

// 社区匠人数据详情弹窗状态
const craftsmanDetailVisible = ref(false);

// 社区圈子数据详情弹窗状态
const communityGroupDetailVisible = ref(false);

// 社区图书数据详情弹窗状态
const bookDetailVisible = ref(false);

// ========== 页面切换函数 ==========
const handleGoBack = () => {
  if (fullscreenStore.screenMode === "wide" || fullscreenStore.isFullscreen) {
    // 宽屏模式和全屏模式下的逻辑 - 与Right2联动
    console.log("Right1", fullscreenStore.screenMode, "模式下执行上一页逻辑");
    // 在宽屏模式和全屏模式下，调用全屏状态管理来切换右侧页面（这会同时影响Right1和Right2）
    fullscreenStore.switchRightPage();
    console.log(
      "Right1",
      fullscreenStore.screenMode,
      "模式切换右侧页面，当前右侧页面:",
      fullscreenStore.rightPage
    );
    return;
  } else {
    // 窄屏模式下的正常切换逻辑
    if (currentPage.value > 1) {
      currentPage.value--;
    } else {
      currentPage.value = 6; // 协商铃的上一页应该是社区生活
    }
  }
  console.log("Right1 切换到页面:", currentPage.value);
  // 窄屏模式下需要初始化对应页面的数据
  initPageData(currentPage.value);
};

const handleGoNext = () => {
  if (fullscreenStore.screenMode === "wide" || fullscreenStore.isFullscreen) {
    // 宽屏模式和全屏模式下不允许独立切换，由Right2控制
    return;
  } else {
    // 窄屏模式下的正常切换逻辑
    if (currentPage.value < 6) {
      currentPage.value++;
    } else {
      currentPage.value = 1;
    }
  }
  console.log("Right1 切换到页面:", currentPage.value);
  // 窄屏模式下需要初始化对应页面的数据
  initPageData(currentPage.value);
};

// ========== Tab切换函数 ==========
const handleSharingTabChange = (tab: string) => {
  sharingActiveTab.value = tab;
  console.log("切换社区共享tab:", tab);
};

const handleCircleTabChange = (tab: string) => {
  circleActiveTab.value = tab;
  console.log("切换社区圈子tab:", tab);
};

const handleMutualTabChange = async (val: string) => {
  mutualTabValue.value = val;
  console.log("切换微互助tab:", val);
  // 切换tab时重新获取分布数据
  await getMutualDistData(mutualDistTimeRange.value);
};

const handleMutualHotTabChange = async (val: string) => {
  mutualHotTabValue.value = val;
  console.log("切换微互助热力图tab:", val);
  // 切换tab时重新获取分布数据
  await getMutualHotData(mutualHotTimeRange.value);
};

// ========== 页面数据初始化函数 ==========
const initPageData = (pageNumber: number) => {
  console.log(`初始化页面 ${pageNumber} 的数据`);

  switch (pageNumber) {
    case 1: // 协商铃
      getBellOverviewData(bellTimeRange.value);
      getBellProcessData(bellProcessTimeRange.value);
      getAiHotsData(bellHotTimeRange.value);
      getBellCallLineData();
      break;

    case 2: // 连心桥
      getMutualOverviewData(mutualTimeRange.value);
      getMutualDistData(mutualDistTimeRange.value);
      getMutualServiceData(mutualServiceTimeRange.value);
      getMutualHotData(mutualHotTimeRange.value);
      break;

    case 3: // 社区共享
      // 社区好物
      getGoodsOverviewData(goodsTimeRange.value);
      getGoodsHotData(goodsHotTimeRange.value);
      getGoodsDistData(goodsDistTimeRange.value);
      getGoodsHotTableData(goodsHotTableTimeRange.value);

      // 共享物品
      getSharedOverviewData(sharedTimeRange.value);
      getSharedHotData(sharedHotTimeRange.value);
      getSharedDistData(sharedDistTimeRange.value);
      getSharedHotTableData(sharedHotTableTimeRange.value);

      // 二手闲置
      getMarketOverviewData(marketTimeRange.value);
      getMarketHotData(marketHotTimeRange.value);
      getMarketDistData(marketDistTimeRange.value);
      getMarketHotTableData(marketHotTableTimeRange.value);
      break;

    case 4: // 社区圈子
      // 社区活动
      getActivityOverviewData(activityTimeRange.value);
      getActivityHotData(activityHotTimeRange.value);
      getActivityDistData(activityDistTimeRange.value);
      getActivityHotTableData(activityHotTableTimeRange.value);

      // 社区空间
      getSpaceOverviewData(spaceTimeRange.value);
      getSpaceHotData(spaceHotTimeRange.value);
      getSpaceDistData(spaceDistTimeRange.value);
      getSpaceHotTableData(spaceHotTableTimeRange.value);

      // 社区匠人
      getCraftsmanOverviewData(craftsmanTimeRange.value);
      getCraftsmanHotData(craftsmanHotTimeRange.value);
      getCraftsmanDistData(craftsmanDistTimeRange.value);
      getCraftsmanHotTableData(craftsmanHotTableTimeRange.value);
      break;

    case 5: // 信息公告
      getNoticeStatisticsData(informationTimeRange.value);
      break;

    case 6: // 社区生活
      // 社区生活数据初始化
      console.log("初始化社区生活页面数据");
      getCommunityLifeOverviewData(lifeTimeRange.value);
      getCommunityBookOverviewData(lifeTimeRange.value);
      break;
  }
};

// ========== 数据更新函数 ==========
// 协商铃数据获取函数
const getBellOverviewData = async (timeType: string, customAreaCode?: string) => {
  try {
    console.log("获取协商铃概览数据, timeType:", timeType);

    const areaCode = customAreaCode || getDistrictCode();
    const response = (await getBellInfoApi({
      areaCode,
      timeType: Number(timeType),
    })) as any;

    console.log("协商铃概览API响应:", response);

    if (response.code === 200 && response.message) {
      bellOverviewData.value = {
        callCount: response.message.callCount || 0,
        serviceCount: response.message.serviceCount || 0,
      };
      console.log("协商铃概览数据更新成功:", bellOverviewData.value);
    } else {
      throw new Error("API返回数据格式错误");
    }
  } catch (error) {
    console.error("获取协商铃概览数据失败:", error);
    // ElMessage.warning('获取协商铃概览数据失败，使用模拟数据')

    // 使用模拟数据作为后备
    const mockData = {
      "0": { callCount: 12, serviceCount: 8 },
      "1": { callCount: 58, serviceCount: 45 },
      "2": { callCount: 156, serviceCount: 98 },
      "3": { callCount: 285, serviceCount: 187 },
      "4": { callCount: 542, serviceCount: 356 },
    };
    bellOverviewData.value = mockData[timeType as keyof typeof mockData] || mockData["0"];
  }
};

// 协商铃处理情况数据获取函数
const getBellProcessData = async (timeType: string, customAreaCode?: string) => {
  try {
    console.log("获取协商铃处理情况数据, timeType:", timeType);

    const areaCode = customAreaCode || getDistrictCode();
    const response = (await getBellListApi({
      areaCode,
      timeType: Number(timeType),
    })) as any;

    console.log("协商铃处理情况API响应:", response);

    if (response.code === 200 && response.message?.list) {
      // 转换数据格式，将callCount映射为receivedCount，serviceCount映射为feedbackCount
      bellProcessData.value = response.message.list.map((item: any) => ({
        district: { name: item.district?.name || "未知区域" },
        receivedCount: item.callCount || 0, // 来电数作为接收数
        feedbackCount: item.serviceCount || 0, // 服务数作为反馈数
      }));

      console.log("协商铃处理情况数据更新成功:", bellProcessData.value);
    } else {
      throw new Error("API返回数据格式错误");
    }
  } catch (error) {
    console.error("获取协商铃处理情况数据失败:", error);
    // ElMessage.warning('获取协商铃处理情况数据失败，使用模拟数据')

    // 使用模拟数据作为后备
    const mockData = {
      "2": [
        { district: { name: "文君街道" }, receivedCount: 45, feedbackCount: 32 },
        { district: { name: "临邛街道" }, receivedCount: 32, feedbackCount: 25 },
        { district: { name: "羊安街道" }, receivedCount: 67, feedbackCount: 45 },
      ],
      "3": [
        { district: { name: "文君街道" }, receivedCount: 85, feedbackCount: 62 },
        { district: { name: "临邛街道" }, receivedCount: 72, feedbackCount: 55 },
        { district: { name: "羊安街道" }, receivedCount: 97, feedbackCount: 75 },
      ],
      "4": [
        { district: { name: "文君街道" }, receivedCount: 145, feedbackCount: 112 },
        { district: { name: "临邛街道" }, receivedCount: 132, feedbackCount: 105 },
        { district: { name: "羊安街道" }, receivedCount: 167, feedbackCount: 135 },
      ],
    };
    bellProcessData.value = mockData[timeType as keyof typeof mockData] || mockData["2"];
  }
};

// AI智能客服热门问题数据获取函数
const getAiHotsData = async (timeType: string, customAreaCode?: string) => {
  try {
    console.log("获取AI智能客服热门问题数据, timeType:", timeType);

    const areaCode = customAreaCode || getDistrictCode();
    const response = (await getAiHotsApi({
      areaCode,
      timeType: Number(timeType),
    })) as any;

    console.log("AI智能客服热门问题API响应:", response);

    if (response.code === 200 && response.message?.list) {
      // 转换数据格式为树状图组件需要的格式
      bellHotData.value = response.message.list.map((item: any) => ({
        content: item.content || "未知问题",
        count: item.count || 0,
      }));

      console.log("AI智能客服热门问题数据更新成功:", bellHotData.value);
    } else {
      throw new Error("API返回数据格式错误");
    }
  } catch (error) {
    console.error("获取AI智能客服热门问题数据失败:", error);
    // ElMessage.warning('获取AI智能客服热门问题数据失败，使用模拟数据')

    // 使用模拟数据作为后备
    const mockData = {
      "2": [
        { content: "如何申请社区服务？", count: 156 },
        { content: "垃圾分类指南", count: 142 },
        { content: "停车位申请", count: 128 },
        { content: "物业费查询", count: 115 },
        { content: "维修报修流程", count: 98 },
      ],
      "3": [
        { content: "如何申请社区服务？", count: 256 },
        { content: "垃圾分类指南", count: 242 },
        { content: "停车位申请", count: 228 },
        { content: "物业费查询", count: 215 },
        { content: "维修报修流程", count: 198 },
      ],
      "4": [
        { content: "如何申请社区服务？", count: 456 },
        { content: "垃圾分类指南", count: 442 },
        { content: "停车位申请", count: 428 },
        { content: "物业费查询", count: 415 },
        { content: "维修报修流程", count: 398 },
      ],
    };
    bellHotData.value = mockData[timeType as keyof typeof mockData] || mockData["2"];
  }
};

// 社区来电时段概况数据获取函数
const getBellCallLineData = async (customAreaCode?: string) => {
  try {
    console.log("获取社区来电时段概况数据");
    bellCurveLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const response = (await getBellCallLineApi({
      areaCode,
      timeType: 0, // 来电时段概况通常使用当天数据
    })) as any;

    console.log("社区来电时段概况API响应:", response);

    if (response.code === 200 && response.message?.list) {
      // 初始化24小时数据（0-23小时）
      const todayData = Array(24).fill(0);
      const yesterdayData = Array(24).fill(0);

      // 填充API返回的数据
      response.message.list.forEach((item: any) => {
        const hour = Math.floor(item.hour || 0);
        if (hour >= 0 && hour < 24) {
          todayData[hour] = item.todayCallCount || 0;
          yesterdayData[hour] = item.yesterdayCallCount || 0;
        }
      });

      // 更新为EchartsCallTrend组件期望的数据格式
      bellCurveData.value = {
        today: todayData,
        yesterday: yesterdayData,
      };

      console.log("社区来电时段概况数据更新成功:", bellCurveData.value);
    } else {
      throw new Error("API返回数据格式错误");
    }
  } catch (error) {
    console.error("获取社区来电时段概况数据失败:", error);
    // ElMessage.warning('获取社区来电时段概况数据失败，使用模拟数据')

    // 使用模拟数据作为后备
    bellCurveData.value = {
      today: [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      yesterday: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    };
  } finally {
    bellCurveLoading.value = false;
  }
};

// 协商铃
const changeBellOverview = async (value: string) => {
  bellTimeRange.value = value;
  console.log("协商铃概览时间变更:", value);
  await getBellOverviewData(value);
};

const changeBellProcess = async (value: string) => {
  bellProcessTimeRange.value = value;
  console.log("协商铃处理情况时间变更:", value);
  await getBellProcessData(value);
};

const changeBellHot = async (value: string) => {
  bellHotTimeRange.value = value;
  console.log("协商铃热门问题时间变更:", value);
  await getAiHotsData(value);
};

// 连心桥
const changeMutualOverview = async (value: string) => {
  mutualTimeRange.value = value;
  console.log("微互助概览时间变更:", value);
  await getMutualOverviewData(value);
};

const changeMutualDist = async (value: string) => {
  mutualDistTimeRange.value = value;
  console.log("微互助分布时间变更:", value);
  await getMutualDistData(value);
};

const changeMutualService = async (value: string) => {
  mutualServiceTimeRange.value = value;
  console.log("微互助服务时间变更:", value);
  await getMutualServiceData(value);
};

const changeMutualHot = async (value: string) => {
  mutualHotTimeRange.value = value;
  console.log("微互助热力图时间变更:", value);
  await getMutualHotData(value);
};

// 获取社区好物概览数据
const getGoodsOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取社区好物概览数据, timeRange:", timeRange);

    const areaCode = customAreaCode || getDistrictCode();
    const params: StoreInfoParams = {
      areaCode,
      timeType: Number(timeRange),
    };

    console.log("🔄 社区好物概览API参数:", params);
    const response = await getStoreInfoApi(params);
    console.log("🔄 社区好物概览API响应:", response);

    if (response.code === 200 && response.message) {
      goodsOverviewData.value = {
        itemsCount: response.message.goodsCount || 0,
        clicksCount: response.message.viewCount || 0,
      };
      console.log("✅ 社区好物概览数据更新成功:", goodsOverviewData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取社区好物概览数据失败:", error);
    ElMessage.warning("获取社区好物概览数据失败");
  }
};

// 获取社区好物热浏览数据
const getGoodsHotData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取社区好物热浏览数据, timeRange:", timeRange);
    goodsHotLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: StoreHotsParams = {
      areaCode,
      pageNum: 1,
      pageSize: 10,
      timeType: timeRange,
    };

    console.log("🔄 社区好物热浏览API参数:", params);
    const response = await getStoreHotsApi(params);
    console.log("🔄 社区好物热浏览API响应:", response);

    if (response.code === 200 && response.message?.list?.rows) {
      // 将API返回的数据转换为EchartsTreemap组件期望的格式
      goodsHotData.value = response.message.list.rows.map((item: StoreHotItem) => ({
        content: item.name,
        count: item.browseNumber,
      }));
      console.log("✅ 社区好物热浏览数据更新成功:", goodsHotData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取社区好物热浏览数据失败:", error);
    ElMessage.warning("获取社区好物热浏览数据失败");
  } finally {
    goodsHotLoading.value = false;
  }
};

// 获取社区好物分布数据
const getGoodsDistData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取社区好物分布数据, timeRange:", timeRange);
    goodsDistLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: StoreListParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange,
    };

    console.log("🔄 社区好物分布API参数:", params);
    const response = await getStoreListApi(params);
    console.log("🔄 社区好物分布API响应:", response);

    if (response.code === 200 && response.message?.list) {
      // 将API返回的数据转换为图表组件期望的格式
      goodsDistData.value = response.message.list.map((item: StoreListItem) => ({
        district: { name: item.district.name },
        goods: item.goodsCount,
        views: item.viewCount || 0,
      }));
      console.log("✅ 社区好物分布数据更新成功:", goodsDistData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取社区好物分布数据失败:", error);
    ElMessage.warning("获取社区好物分布数据失败");
  } finally {
    goodsDistLoading.value = false;
  }
};

// 获取社区好物热门贴数据
const getGoodsHotTableData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取社区好物热门贴数据, timeRange:", timeRange);
    goodsHotTableLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: StoreHotsParams = {
      areaCode,
      pageNum: 1,
      pageSize: 10,
      timeType: timeRange,
    };

    console.log("🔄 社区好物热门贴API参数:", params);
    const response = await getStoreHotsApi(params);
    console.log("🔄 社区好物热门贴API响应:", response);

    if (response.code === 200 && response.message?.list?.rows) {
      // 将API返回的数据转换为表格组件期望的格式
      goodsHotTableData.value = response.message.list.rows.map((item: StoreHotItem) => ({
        district: item.districtName,
        name: item.name,
        browseNumber: item.browseNumber,
      }));
      console.log("✅ 社区好物热门贴数据更新成功:", goodsHotTableData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取社区好物热门贴数据失败:", error);
    ElMessage.warning("获取社区好物热门贴数据失败");
  } finally {
    goodsHotTableLoading.value = false;
  }
};

// 获取共享物品概览数据
const getSharedOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取共享物品概览数据, timeRange:", timeRange);

    const areaCode = customAreaCode || getDistrictCode();
    const params: MarketInfoParams = {
      areaCode,
      timeType: Number(timeRange),
      type: 1, // 共享物品类型为1
    };

    console.log("🔄 共享物品概览API参数:", params);
    const response = await getMarketInfoApi(params);
    console.log("🔄 共享物品概览API响应:", response);

    if (response.code === 200 && response.message) {
      sharedOverviewData.value = {
        itemsCount: response.message.goodsCount || 0,
        clicksCount: response.message.viewCount || 0,
      };
      console.log("✅ 共享物品概览数据更新成功:", sharedOverviewData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取共享物品概览数据失败:", error);
    ElMessage.warning("获取共享物品概览数据失败");
  }
};

// 获取共享热浏览数据
const getSharedHotData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取共享热浏览数据, timeRange:", timeRange);
    sharedHotLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: MarketHotsParams = {
      areaCode,
      pageNum: 1,
      pageSize: 10,
      timeType: timeRange,
      type: 1, // 共享物品类型为1
    };

    console.log("🔄 共享热浏览API参数:", params);
    const response = await getMarketHotsApi(params);
    console.log("🔄 共享热浏览API响应:", response);

    if (response.code === 200 && response.message?.list?.rows) {
      // 将API返回的数据转换为EchartsTreemap组件期望的格式
      sharedHotData.value = response.message.list.rows.map((item: any) => ({
        content: item.name,
        count: item.browseNumber,
      }));
      console.log("✅ 共享热浏览数据更新成功:", sharedHotData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取共享热浏览数据失败:", error);
    ElMessage.warning("获取共享热浏览数据失败");
  } finally {
    sharedHotLoading.value = false;
  }
};

// 获取共享物品分布数据
const getSharedDistData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取共享物品分布数据, timeRange:", timeRange);
    sharedDistLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: MarketListParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange,
      type: 1, // 共享物品类型为1
    };

    console.log("🔄 共享物品分布API参数:", params);
    const response = await getMarketListApi(params);
    console.log("🔄 共享物品分布API响应:", response);

    if (response.code === 200 && response.message?.list) {
      // 将API返回的数据转换为EchartsReuseDistribution组件期望的格式
      sharedDistData.value = response.message.list.map((item: any) => ({
        district: {
          name: item.district?.name || "未知区域",
        },
        goods: item.goodsCount || 0,
        views: item.viewCount || 0,
      }));
      console.log("✅ 共享物品分布数据更新成功:", sharedDistData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取共享物品分布数据失败:", error);
    ElMessage.warning("获取共享物品分布数据失败");
  } finally {
    sharedDistLoading.value = false;
  }
};

// 获取共享物品热门贴数据
const getSharedHotTableData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取共享物品热门贴数据, timeRange:", timeRange);
    sharedHotTableLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: MarketHotsParams = {
      areaCode,
      pageNum: 1,
      pageSize: 10,
      timeType: timeRange,
      type: 1, // 共享物品类型为1
    };

    console.log("🔄 共享物品热门贴API参数:", params);
    const response = await getMarketHotsApi(params);
    console.log("🔄 共享物品热门贴API响应:", response);

    if (response.code === 200 && response.message?.list?.rows) {
      // 将API返回的数据转换为ReuseTable组件期望的格式
      sharedHotTableData.value = response.message.list.rows.map((item: any) => ({
        district: item.districtName || "未知区域",
        name: item.name || "未知物品",
        browseNumber: item.browseNumber || 0,
      }));
      console.log("✅ 共享物品热门贴数据更新成功:", sharedHotTableData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取共享物品热门贴数据失败:", error);
    ElMessage.warning("获取共享物品热门贴数据失败");
  } finally {
    sharedHotTableLoading.value = false;
  }
};

// 获取二手闲置概览数据
const getMarketOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取二手闲置概览数据, timeRange:", timeRange);

    const areaCode = customAreaCode || getDistrictCode();
    const params: MarketInfoParams = {
      areaCode,
      timeType: Number(timeRange),
      type: 0, // 二手闲置类型为0
    };

    console.log("🔄 二手闲置概览API参数:", params);
    const response = await getMarketInfoApi(params);
    console.log("🔄 二手闲置概览API响应:", response);

    if (response.code === 200 && response.message) {
      marketOverviewData.value = {
        itemsCount: response.message.goodsCount || 0,
        clicksCount: response.message.viewCount || 0,
      };
      console.log("✅ 二手闲置概览数据更新成功:", marketOverviewData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取二手闲置概览数据失败:", error);
    ElMessage.warning("获取二手闲置概览数据失败");
  }
};

// 获取闲置热浏览数据
const getMarketHotData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取闲置热浏览数据, timeRange:", timeRange);
    marketHotLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: MarketHotsParams = {
      areaCode,
      pageNum: 1,
      pageSize: 10,
      timeType: timeRange,
      type: 0, // 二手闲置类型为0
    };

    console.log("🔄 闲置热浏览API参数:", params);
    const response = await getMarketHotsApi(params);
    console.log("🔄 闲置热浏览API响应:", response);

    if (response.code === 200 && response.message?.list?.rows) {
      // 将API返回的数据转换为EchartsTreemap组件期望的格式
      marketHotData.value = response.message.list.rows.map((item: any) => ({
        content: item.name,
        count: item.browseNumber,
      }));
      console.log("✅ 闲置热浏览数据更新成功:", marketHotData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取闲置热浏览数据失败:", error);
    ElMessage.warning("获取闲置热浏览数据失败");
  } finally {
    marketHotLoading.value = false;
  }
};

// 获取闲置物品分布数据
const getMarketDistData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取闲置物品分布数据, timeRange:", timeRange);
    marketDistLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: MarketListParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange,
      type: 0, // 二手闲置类型为0
    };

    console.log("🔄 闲置物品分布API参数:", params);
    const response = await getMarketListApi(params);
    console.log("🔄 闲置物品分布API响应:", response);

    if (response.code === 200 && response.message?.list) {
      // 将API返回的数据转换为EchartsReuseDistribution组件期望的格式
      marketDistData.value = response.message.list.map((item: any) => ({
        district: {
          name: item.district?.name || "未知区域",
        },
        goods: item.goodsCount || 0,
        views: item.viewCount || 0,
      }));
      console.log("✅ 闲置物品分布数据更新成功:", marketDistData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取闲置物品分布数据失败:", error);
    ElMessage.warning("获取闲置物品分布数据失败");
  } finally {
    marketDistLoading.value = false;
  }
};

// 获取二手闲置热门贴数据
const getMarketHotTableData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取二手闲置热门贴数据, timeRange:", timeRange);
    marketHotTableLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: MarketHotsParams = {
      areaCode,
      pageNum: 1,
      pageSize: 10,
      timeType: timeRange,
      type: 0, // 二手闲置类型为0
    };

    console.log("🔄 二手闲置热门贴API参数:", params);
    const response = await getMarketHotsApi(params);
    console.log("🔄 二手闲置热门贴API响应:", response);

    if (response.code === 200 && response.message?.list?.rows) {
      // 将API返回的数据转换为ReuseTable组件期望的格式
      marketHotTableData.value = response.message.list.rows.map((item: any) => ({
        district: item.districtName || "未知区域",
        name: item.name || "未知物品",
        browseNumber: item.browseNumber || 0,
      }));
      console.log("✅ 二手闲置热门贴数据更新成功:", marketHotTableData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取二手闲置热门贴数据失败:", error);
    ElMessage.warning("获取二手闲置热门贴数据失败");
  } finally {
    marketHotTableLoading.value = false;
  }
};

// 获取社区活动概览数据
const getActivityOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取社区活动概览数据, timeRange:", timeRange);

    const areaCode = customAreaCode || getDistrictCode();
    const params: ActivityInfoParams = {
      areaCode,
      timeType: timeRange,
    };

    console.log("🔄 社区活动概览API参数:", params);
    const response = await getActivityInfoApi(params);
    console.log("🔄 社区活动概览API响应:", response);

    if (response.code === 200 && response.message) {
      activityOverviewData.value = {
        activityCount: response.message.total || 0, // 活动总数
        ongoingCount: response.message.ongoing || 0, // 进行中活动
        completedCount: response.message.ended || 0, // 已结束活动
      };
      console.log("✅ 社区活动概览数据更新成功:", activityOverviewData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取社区活动概览数据失败:", error);
    ElMessage.warning("获取社区活动概览数据失败");
  }
};

// 获取社区活动热浏览数据
const getActivityHotData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取社区活动热浏览数据, timeRange:", timeRange);
    activityHotLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: ActivityHotsParams = {
      areaCode,
      pageNum: 1,
      pageSize: 10,
      timeType: timeRange,
    };

    console.log("🔄 社区活动热浏览API参数:", params);
    const response = await getActivityHotsApi(params);
    console.log("🔄 社区活动热浏览API响应:", response);

    if (response.code === 200 && response.message?.list?.rows) {
      // 将API返回的数据转换为EchartsTreemap组件期望的格式
      activityHotData.value = response.message.list.rows.map((item: any) => ({
        content: item.title || "未知活动",
        count: item.userCount || 0, // 使用userCount作为参与人数
      }));
      console.log("✅ 社区活动热浏览数据更新成功:", activityHotData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取社区活动热浏览数据失败:", error);
    ElMessage.warning("获取社区活动热浏览数据失败");
  } finally {
    activityHotLoading.value = false;
  }
};

// 获取社区活动浏览及参与人数分布数据
const getActivityDistData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取社区活动浏览及参与人数分布数据, timeRange:", timeRange);
    activityDistLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: ActivityListParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange,
    };

    console.log("🔄 社区活动分布API参数:", params);
    const response = await getActivityListApi(params);
    console.log("🔄 社区活动分布API响应:", response);

    if (response.code === 200 && response.message?.list) {
      // 将API返回的数据转换为EchartsActivityDistribution组件期望的格式
      activityDistData.value = response.message.list.map((item: any) => ({
        name: item.district?.name || "未知区域", // 区域名称
        districtName: item.district?.name || "未知区域", // 备用字段
        activityCount: item.total || 0, // 活动总数 -> activityCount
        count: item.total || 0, // 活动总数 -> count (备用)
        participantCount: item.participants || 0, // 参与人数 -> participantCount
        userCount: item.participants || 0, // 参与人数 -> userCount (备用)
        participants: item.participants || 0, // 参与人数 -> participants (备用)
      }));
      console.log("✅ 社区活动分布数据更新成功:", activityDistData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取社区活动分布数据失败:", error);
    ElMessage.warning("获取社区活动分布数据失败");
  } finally {
    activityDistLoading.value = false;
  }
};

// 获取热门社区活动表格数据
const getActivityHotTableData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取热门社区活动表格数据, timeRange:", timeRange);
    activityHotTableLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: ActivityHotsParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange,
    };

    console.log("🔄 热门社区活动表格API参数:", params);
    const response = await getActivityHotsApi(params);
    console.log("🔄 热门社区活动表格API响应:", response);

    if (response.code === 200 && response.message?.list?.rows) {
      // 将API返回的数据转换为ReuseTable组件期望的格式
      activityHotTableData.value = response.message.list.rows.map((item: any) => ({
        districtName: item.districtName || "未知区域",
        title: item.title || "未知活动",
        userCount: item.userCount || 0,
      }));
      console.log("✅ 热门社区活动表格数据更新成功:", activityHotTableData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取热门社区活动表格数据失败:", error);
    ElMessage.warning("获取热门社区活动表格数据失败");
  } finally {
    activityHotTableLoading.value = false;
  }
};

// 获取社区空间概览数据
const getSpaceOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("🔄 获取社区空间概览数据, timeRange:", timeRange);

    const areaCode = customAreaCode || getDistrictCode();
    const params: SpaceInfoParams = {
      areaCode,
      timeType: timeRange,
    };

    console.log("🔄 社区空间概览API参数:", params);
    const response = await getSpaceInfoApi(params);
    console.log("🔄 社区空间概览API响应:", response);

    if (response.code === 200 && response.message) {
      spaceOverviewData.value = {
        itemsCount: response.message.goodsCount || 0, // 空间数量
        clicksCount: response.message.viewCount || 0, // 浏览量
      };
      console.log("✅ 社区空间概览数据更新成功:", spaceOverviewData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取社区空间概览数据失败:", error);
    ElMessage.warning("获取社区空间概览数据失败");
  }
};

// 社区空间热浏览概况数据获取函数
const getSpaceHotData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("获取社区空间热浏览概况数据, timeRange:", timeRange);

    const areaCode = customAreaCode || getDistrictCode();
    const params: SpaceHotParams = {
      areaCode,
      pageNum: 1,
      pageSize: 10,
      timeType: timeRange,
    };

    console.log("🔄 社区空间热浏览概况API参数:", params);
    const response = (await getSpaceHotList(params)) as any;
    console.log("🔄 社区空间热浏览概况API响应:", response);

    if (response.code === 200 && response.message?.list?.rows) {
      // 转换数据格式为矩形树图需要的格式
      const transformedData = response.message.list.rows.map((item: any) => ({
        content: item.name, // 社区空间名称
        count: item.browseNumber, // 浏览数量
      }));

      spaceHotData.value = transformedData;
      console.log("✅ 社区空间热浏览概况数据更新成功:", spaceHotData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取社区空间热浏览概况数据失败:", error);
    ElMessage.warning("获取社区空间热浏览概况数据失败");
  }
};

// 社区空间浏览及使用人数分布数据获取函数
const getSpaceDistData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("获取社区空间浏览及使用人数分布数据, timeRange:", timeRange);

    const areaCode = customAreaCode || getDistrictCode();
    const params: SpaceListParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange,
    };

    console.log("🔄 社区空间分布API参数:", params);
    const response = await getSpaceListApi(params);
    console.log("🔄 社区空间分布API响应:", response);

    if (response.code === 200 && response.message?.list) {
      // 转换数据格式为图表组件需要的格式
      const transformedData = response.message.list.map((item: any) => ({
        district: {
          name: item.district?.name || "未知区域",
        },
        name: item.district?.name || "未知区域", // 组件需要的字段
        districtName: item.district?.name || "未知区域", // 组件需要的备用字段
        goods: item.goodsCount || 0, // 社区空间数量
        views: item.viewCount || 0, // 浏览量
      }));

      spaceDistData.value = transformedData;
      console.log("✅ 社区空间分布数据更新成功:", spaceDistData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取社区空间分布数据失败:", error);
    ElMessage.warning("获取社区空间分布数据失败");
  }
};

// 热门社区空间表格数据获取函数
const getSpaceHotTableData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("获取热门社区空间表格数据, timeRange:", timeRange);
    spaceHotTableLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: SpaceHotParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange,
    };

    console.log("🔄 热门社区空间表格API参数:", params);
    const response = (await getSpaceHotList(params)) as any;
    console.log("🔄 热门社区空间表格API响应:", response);

    if (response.code === 200 && response.message?.list?.rows) {
      // 转换数据格式为表格需要的格式
      const transformedData = response.message.list.rows.map((item: any) => ({
        districtName: item.districtName || "未知辖区", // 所属辖区
        name: item.name || "未知空间", // 社区空间名称
        browseNumber: item.browseNumber || 0, // 参与人数（浏览数量）
      }));

      spaceHotTableData.value = transformedData;
      console.log("✅ 热门社区空间表格数据更新成功:", spaceHotTableData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取热门社区空间表格数据失败:", error);
    ElMessage.warning("获取热门社区空间表格数据失败");
  } finally {
    spaceHotTableLoading.value = false;
  }
};

// 社区匠人概览数据获取函数
const getCraftsmanOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("获取社区匠人概览数据, timeRange:", timeRange);

    const areaCode = customAreaCode || getDistrictCode();
    const params: CraftsmanInfoParams = {
      areaCode,
      timeType: timeRange,
    };

    console.log("🔄 社区匠人概览API参数:", params);
    const response = await getCraftsmanInfoApi(params);
    console.log("🔄 社区匠人概览API响应:", response);

    if (response.code === 200 && response.message) {
      craftsmanOverviewData.value = {
        itemsCount: response.message.goodsCount || 0, // 匠人数量
        clicksCount: response.message.viewCount || 0, // 总浏览量
      };
      console.log("✅ 社区匠人概览数据更新成功:", craftsmanOverviewData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取社区匠人概览数据失败:", error);
    ElMessage.warning("获取社区匠人概览数据失败");
  }
};

// 社区匠人热浏览概况数据获取函数
const getCraftsmanHotData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("获取社区匠人热浏览概况数据, timeRange:", timeRange);

    const areaCode = customAreaCode || getDistrictCode();
    const params: CraftsmanHotParams = {
      areaCode,
      pageNum: 1,
      pageSize: 10,
      timeType: timeRange,
    };

    console.log("🔄 社区匠人热浏览概况API参数:", params);
    const response = await getCraftsmanHotListApi(params);
    console.log("🔄 社区匠人热浏览概况API响应:", response);

    if (response.code === 200 && response.message?.list?.rows) {
      // 转换数据格式为矩形树图需要的格式
      const transformedData = response.message.list.rows.map((item: any) => ({
        content: item.name, // 匠人名称
        count: item.browseNumber, // 浏览数量
      }));

      craftsmanHotData.value = transformedData;
      console.log("✅ 社区匠人热浏览概况数据更新成功:", craftsmanHotData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取社区匠人热浏览概况数据失败:", error);
    ElMessage.warning("获取社区匠人热浏览概况数据失败");
  }
};

// 社区匠人浏览及关注人数分布数据获取函数
const getCraftsmanDistData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("获取社区匠人浏览及关注人数分布数据, timeRange:", timeRange);
    craftsmanDistLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: CraftsmanDistributionParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange,
    };

    console.log("🔄 社区匠人分布API参数:", params);
    const response = await getCraftsmanDistributionApi(params);
    console.log("🔄 社区匠人分布API响应:", response);

    if (response.code === 200 && response.message?.list) {
      // 转换数据格式为双系列柱状图需要的格式
      const transformedData = response.message.list.map((item: any) => ({
        district: {
          name: item.district?.name || "未知区域",
        },
        name: item.district?.name || "未知区域", // 兼容字段
        districtName: item.district?.name || "未知区域", // 兼容字段
        goods: item.goodsCount || 0, // 社区匠人数量
        views: item.viewCount || 0, // 浏览量
      }));

      craftsmanDistData.value = transformedData;
      console.log("✅ 社区匠人分布数据更新成功:", craftsmanDistData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取社区匠人分布数据失败:", error);
    ElMessage.warning("获取社区匠人分布数据失败");
  } finally {
    craftsmanDistLoading.value = false;
  }
};

// 热门社区匠人表格数据获取函数
const getCraftsmanHotTableData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("获取热门社区匠人表格数据, timeRange:", timeRange);
    craftsmanHotTableLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: CraftsmanHotParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange,
    };

    console.log("🔄 热门社区匠人表格API参数:", params);
    const response = await getCraftsmanHotListApi(params);
    console.log("🔄 热门社区匠人表格API响应:", response);

    if (response.code === 200 && response.message?.list?.rows) {
      // 转换数据格式为表格需要的格式
      const transformedData = response.message.list.rows.map((item: any) => ({
        districtName: item.districtName || "未知区域",
        name: item.name || "未知匠人",
        browseNumber: item.browseNumber || 0,
      }));

      craftsmanHotTableData.value = transformedData;
      console.log("✅ 热门社区匠人表格数据更新成功:", craftsmanHotTableData.value);
    } else {
      throw new Error(`API返回错误: ${response.msg}`);
    }
  } catch (error) {
    console.error("❌ 获取热门社区匠人表格数据失败:", error);
    ElMessage.warning("获取热门社区匠人表格数据失败");
  } finally {
    craftsmanHotTableLoading.value = false;
  }
};

// 社区共享 - 社区好物
const changeGoodsOverview = (value: string) => {
  goodsTimeRange.value = value;
  console.log("社区好物概览时间变更:", value);

  // 调用社区好物概览API
  getGoodsOverviewData(value);
};

const changeGoodsHot = (value: string) => {
  goodsHotTimeRange.value = value;
  console.log("社区好物热浏览时间变更:", value);

  // 调用社区好物热浏览API
  getGoodsHotData(value);
};

const changeGoodsDist = (value: string) => {
  goodsDistTimeRange.value = value;
  console.log("社区好物分布时间变更:", value);

  // 调用社区好物分布API
  getGoodsDistData(value);
};

const changeGoodsHotTable = (value: string) => {
  goodsHotTableTimeRange.value = value;
  console.log("社区好物热门贴时间变更:", value);

  // 调用社区好物热门贴API
  getGoodsHotTableData(value);
};

// 社区共享 - 社区物品
const changeSharedOverview = (value: string) => {
  sharedTimeRange.value = value;
  console.log("共享物品概览时间变更:", value);

  // 调用共享物品概览API
  getSharedOverviewData(value);
};

const changeSharedHot = (value: string) => {
  sharedHotTimeRange.value = value;
  console.log("共享物品热浏览时间变更:", value);
  getSharedHotData(value);
};

const changeSharedDist = (value: string) => {
  sharedDistTimeRange.value = value;
  console.log("共享物品分布时间变更:", value);
  getSharedDistData(value);
};

const changeSharedHotTable = (value: string) => {
  sharedHotTableTimeRange.value = value;
  console.log("共享物品热门贴时间变更:", value);
  getSharedHotTableData(value);
};

// 社区共享 - 跳蚤市场
const changeMarketOverview = (value: string) => {
  marketTimeRange.value = value;
  console.log("跳蚤市场概览时间变更:", value);
  getMarketOverviewData(value);
};

const changeMarketHot = (value: string) => {
  marketHotTimeRange.value = value;
  console.log("跳蚤市场热浏览时间变更:", value);
  getMarketHotData(value);
};

const changeMarketDist = (value: string) => {
  marketDistTimeRange.value = value;
  console.log("跳蚤市场分布时间变更:", value);
  getMarketDistData(value);
};

const changeMarketHotTable = (value: string) => {
  marketHotTableTimeRange.value = value;
  console.log("二手闲置热门贴时间变更:", value);
  getMarketHotTableData(value);
};

// 社区圈子 - 社区活动
const changeActivityOverview = (value: string) => {
  activityTimeRange.value = value;
  console.log("社区活动概览时间变更:", value);
  getActivityOverviewData(value);
};

const changeActivityHot = (value: string) => {
  activityHotTimeRange.value = value;
  console.log("社区活动热浏览时间变更:", value);
  getActivityHotData(value);
};

const changeActivityDist = (value: string) => {
  activityDistTimeRange.value = value;
  console.log("社区活动分布时间变更:", value);
  getActivityDistData(value);
};

const changeActivityHotTable = (value: string) => {
  activityHotTableTimeRange.value = value;
  console.log("热门社区活动时间变更:", value);
  getActivityHotTableData(value);
};

// 社区圈子 - 社区空间
const changeSpaceOverview = (value: string) => {
  spaceTimeRange.value = value;
  console.log("社区空间概览时间变更:", value);
  getSpaceOverviewData(value);
};

const changeSpaceHot = (value: string) => {
  spaceHotTimeRange.value = value;
  console.log("社区空间热浏览时间变更:", value);
  getSpaceHotData(value);
};

const changeSpaceDist = (value: string) => {
  spaceDistTimeRange.value = value;
  console.log("社区空间分布时间变更:", value);
  getSpaceDistData(value);
};

const changeSpaceHotTable = (value: string) => {
  spaceHotTableTimeRange.value = value;
  console.log("热门社区空间时间变更:", value);
  getSpaceHotTableData(value);
};

// 社区圈子 - 社区匠人
const changeCraftsmanOverview = (value: string) => {
  craftsmanTimeRange.value = value;
  console.log("社区匠人概览时间变更:", value);
  getCraftsmanOverviewData(value);
};

const changeCraftsmanHot = (value: string) => {
  craftsmanHotTimeRange.value = value;
  console.log("社区匠人热浏览时间变更:", value);
  getCraftsmanHotData(value);
};

const changeCraftsmanDist = (value: string) => {
  craftsmanDistTimeRange.value = value;
  console.log("社区匠人分布时间变更:", value);
  getCraftsmanDistData(value);
};

const changeCraftsmanHotTable = (value: string) => {
  craftsmanHotTableTimeRange.value = value;
  console.log("热门社区匠人表格时间变更:", value);
  getCraftsmanHotTableData(value);
};

// ========== 信息公告相关函数 ==========
const getNoticeStatisticsData = async (timeType: string, customAreaCode?: string) => {
  try {
    console.log("获取信息公告数据, timeType:", timeType);

    // 只在第一次加载时显示loading
    if (informationTypeData.value.length === 0) {
      informationTypeLoading.value = true;
      informationTrendLoading.value = true;
      informationTableLoading.value = true;
    }

    const areaCode = customAreaCode || getDistrictCode();
    const response = await getNoticeStatisticsApi({
      areaCode,
      timeType,
    });
    console.log("信息公告数据:", response);
    if (response.code === 200 && response.data) {
      const data = (response as any).data;
      console.log("API数据处理:", data);

      // 更新概况数据
      informationOverviewData.value = {
        totalCount: data.total || 0,
      };
      console.log("信息公告数据:", data.scatters);
      // 更新类型分布数据
      if (data.scatters && data.scatters.length > 0) {
        const colors = ["#4A90E2", "#50C878", "#FFA500", "#FF6B6B", "#9B59B6", "#95A5A6"];
        informationTypeData.value = data.scatters.map((item: any, index: number) => ({
          name: item.name,
          value: item.total,
          color: colors[index % colors.length],
        }));
      }

      // 更新趋势数据
      if (data.overviews && data.overviews.length > 0) {
        informationTrendData.value = data.overviews.map((item: any) => ({
          month: item.name,
          publishCount: item.total || 0,
          viewCount: item.browseNumber || 0,
        }));
        console.log("趋势数据处理完成:", informationTrendData.value);
      } else {
        console.log("趋势数据为空或不存在:", data.overviews);
      }

      // 更新表格数据
      if (data.noticeFiles && data.noticeFiles.length > 0) {
        informationTableData.value = data.noticeFiles.map((item: any) => ({
          area: item.districtName,
          title: item.title,
          publishTime: item.time,
        }));
      }
    }
  } catch (error) {
    console.error("获取信息公告数据失败:", error);
    ElMessage.error("获取信息公告数据失败");
  } finally {
    informationTypeLoading.value = false;
    informationTrendLoading.value = false;
    informationTableLoading.value = false;
  }
};

const changeInformationOverview = (value: string) => {
  informationTimeRange.value = value;
  console.log("信息公告概况时间变更:", value);
  // 更新所有信息公告相关数据
  getNoticeStatisticsData(value);
};

// 信息公告资源地图分布
const handleResourceMap = async (customAreaCode?: string | Event) => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = "notice";
    console.log("🎯 Right1Component设置激活撒点类型为: notice");

    // 修复：如果传入的是事件对象，则忽略它
    let areaCode: string;
    if (typeof customAreaCode === "string") {
      areaCode = customAreaCode;
    } else {
      areaCode = getDistrictCode();
    }

    console.log(
      "🔍 点击信息公告资源地图分布，当前时间范围:",
      informationTimeRange.value,
      "区域代码:",
      areaCode
    );

    const response = await getNoticeMapResourceApi({
      areaCode,
      timeType: informationTimeRange.value,
      resourceType: 1,
    });

    console.log("📊 信息公告资源地图分布API响应:", response);

    if (response.code === 200) {
      // 通知地图组件进行撒点 - 使用通用的 addResourceMapPoints 函数
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data);
        ElMessage.success(
          `信息公告资源地图分布撒点完成，共 ${
            Array.isArray(response.data) ? response.data.length : 0
          } 个点位`
        );
      } else {
        console.error("❌ 地图撒点函数未找到");
        ElMessage.error("地图撒点函数未找到");
      }
    } else {
      console.warn("⚠️ 信息公告资源地图分布API返回异常:", response);
      ElMessage.warning("获取信息公告资源地图分布数据异常");
    }
  } catch (error) {
    console.error("❌ 获取信息公告资源地图分布数据失败:", error);
    ElMessage.error("获取信息公告资源地图分布数据失败");
  }
};

// 信息公告辖区热力分布
const handleHeatMap = async () => {
  try {
    console.log("🔍 点击信息公告辖区热力分布，当前时间范围:", informationTimeRange.value);

    const areaCode = getDistrictCode();
    const timeType = informationTimeRange.value;

    console.log("📊 请求信息公告热力图数据:", { areaCode, timeType, resourceType: 2 });

    // 使用信息公告专门的接口
    const response = (await getNoticeMapResourceApi({
      areaCode,
      timeType,
      resourceType: 2, // 2: 辖区热力分布
    })) as any;

    console.log("📊 信息公告热力图API响应:", response);

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照其他热力分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log("✅ 调用热力图函数，原始数据:", response.data);

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0,
        }));

        console.log("✅ 转换后的数据:", convertedData);
        (window as any).addTotalHeatMap(convertedData);
        ElMessage.success(
          `信息公告辖区热力分布加载完成，共 ${
            Array.isArray(response.data) ? response.data.length : 0
          } 个热力点`
        );
      } else {
        console.error("❌ 地图热力图函数未找到");
        ElMessage.error("地图热力图函数未找到");
      }
    } else {
      console.warn("⚠️ 信息公告热力图API返回异常:", response);
      ElMessage.warning("获取信息公告热力图数据异常");
    }
  } catch (error: any) {
    console.error("❌ 获取信息公告热力图数据失败:", error);
    ElMessage.error(`获取信息公告热力图数据失败: ${error.message}`);
  }
};

// 数据详情
const handleDataDetail = () => {
  console.log("点击数据详情");
  noticeDetailTitle.value = "信息公告数据详情";
  noticeResourceType.value = "1"; // 数据详情默认为资源地图分布类型
  noticeDetailVisible.value = true;
};

// 社区响应概况数据详情
const handleCommunityResponseDataDetail = () => {
  console.log("🔍 点击社区响应概况数据详情，当前时间范围:", bellTimeRange.value);
  communityResponseDetailVisible.value = true;
};

// 协商铃资源地图分布
const handleCommunityResponseResourceMap = async (customAreaCode?: string | Event) => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = "call";
    console.log("🎯 Right1Component设置激活撒点类型为: call");

    // 修复：如果传入的是事件对象，则忽略它
    let areaCode: string;
    if (typeof customAreaCode === "string") {
      areaCode = customAreaCode;
    } else {
      areaCode = getDistrictCode();
    }

    console.log(
      "🔍 点击协商铃资源地图分布，当前时间范围:",
      bellTimeRange.value,
      "区域代码:",
      areaCode
    );

    const response = await getCallMapResourceApi({
      areaCode,
      timeType: bellTimeRange.value,
      resourceType: 1,
    });

    console.log("📊 协商铃资源地图分布API响应:", response);

    if (response.code === 200) {
      // 通知地图组件进行撒点 - 使用通用的 addResourceMapPoints 函数
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data);
        ElMessage.success(
          `协商铃资源地图分布撒点完成，共 ${
            Array.isArray(response.data) ? response.data.length : 0
          } 个点位`
        );
      } else {
        console.error("❌ 地图撒点函数未找到");
        ElMessage.error("地图撒点函数未找到");
      }
    } else {
      console.warn("⚠️ 协商铃资源地图分布API返回异常:", response);
      ElMessage.warning("获取协商铃资源地图分布数据异常");
    }
  } catch (error) {
    console.error("❌ 获取协商铃资源地图分布数据失败:", error);
    ElMessage.error("获取协商铃资源地图分布数据失败");
  }
};

// 协商铃辖区热力分布
const handleCommunityResponseHeatMap = async () => {
  try {
    console.log("🔍 点击协商铃辖区热力分布，当前时间范围:", bellTimeRange.value);

    const areaCode = getDistrictCode();
    const timeType = bellTimeRange.value;

    console.log("📊 请求协商铃热力图数据:", { areaCode, timeType, resourceType: 2 });

    // 使用协商铃专门的接口
    const response = (await getCallMapResourceApi({
      areaCode,
      timeType,
      resourceType: 2, // 2: 辖区热力分布
    })) as any;

    console.log("📊 协商铃热力图API响应:", response);

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照其他热力分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log("✅ 调用热力图函数，原始数据:", response.data);

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0,
        }));

        console.log("✅ 转换后的数据:", convertedData);
        (window as any).addTotalHeatMap(convertedData);
        ElMessage.success(
          `协商铃辖区热力分布加载完成，共 ${
            Array.isArray(response.data) ? response.data.length : 0
          } 个热力点`
        );
      } else {
        console.error("❌ 地图热力图函数未找到");
        ElMessage.error("地图热力图函数未找到");
      }
    } else {
      console.warn("⚠️ 协商铃热力图API返回异常:", response);
      ElMessage.warning("获取协商铃热力图数据异常");
    }
  } catch (error: any) {
    console.error("❌ 获取协商铃热力图数据失败:", error);
    ElMessage.error(`获取协商铃热力图数据失败: ${error.message}`);
  }
};

// 社区匠人数据详情
const handleCraftsmanDataDetail = () => {
  console.log("🔍 点击社区匠人数据详情，当前时间范围:", craftsmanTimeRange.value);
  craftsmanDetailVisible.value = true;
};

// 社区匠人资源地图分布
const handleCraftsmanResourceMap = async (customAreaCode?: string | Event) => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = "craftsman";
    console.log("🎯 Right1Component设置激活撒点类型为: craftsman");

    // 修复：如果传入的是事件对象，则忽略它
    let areaCode: string;
    if (typeof customAreaCode === "string") {
      areaCode = customAreaCode;
    } else {
      areaCode = getDistrictCode();
    }

    console.log(
      "🔍 点击社区匠人资源地图分布，当前时间范围:",
      craftsmanTimeRange.value,
      "区域代码:",
      areaCode
    );

    const response = await getCraftsmanMapResourceApi({
      areaCode,
      timeType: craftsmanTimeRange.value,
      resourceType: 1,
    });

    console.log("📊 社区匠人资源地图分布API响应:", response);

    if (response.code === 200) {
      // 通知地图组件进行撒点 - 使用通用的 addResourceMapPoints 函数
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data);
        ElMessage.success(
          `社区匠人资源地图分布撒点完成，共 ${
            Array.isArray(response.data) ? response.data.length : 0
          } 个点位`
        );
      } else {
        console.error("❌ 地图撒点函数未找到");
        ElMessage.error("地图撒点函数未找到");
      }
    } else {
      console.warn("⚠️ 社区匠人资源地图分布API返回异常:", response);
      ElMessage.warning("获取社区匠人资源地图分布数据异常");
    }
  } catch (error) {
    console.error("❌ 获取社区匠人资源地图分布数据失败:", error);
    ElMessage.error("获取社区匠人资源地图分布数据失败");
  }
};

// 社区好物资源地图分布
const handleGoodsResourceMap = async (customAreaCode?: string | Event) => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = "store";
    console.log("🎯 Right1Component设置激活撒点类型为: store");

    // 修复：如果传入的是事件对象，则忽略它
    let areaCode: string;
    if (typeof customAreaCode === "string") {
      areaCode = customAreaCode;
    } else {
      areaCode = getDistrictCode();
    }

    console.log(
      "🔍 点击社区好物资源地图分布，当前时间范围:",
      goodsTimeRange.value,
      "区域代码:",
      areaCode
    );

    const response = await getStoreMapResourceApi({
      areaCode,
      timeType: goodsTimeRange.value,
      resourceType: 1,
    });

    console.log("📊 社区好物资源地图分布API响应:", response);

    if (response.code === 200) {
      // 通知地图组件进行撒点 - 使用通用的 addResourceMapPoints 函数
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data);
        ElMessage.success(
          `社区好物资源地图分布撒点完成，共 ${
            Array.isArray(response.data) ? response.data.length : 0
          } 个点位`
        );
      } else {
        console.error("❌ 地图撒点函数未找到");
        ElMessage.error("地图撒点函数未找到");
      }
    } else {
      console.warn("⚠️ 社区好物资源地图分布API返回异常:", response);
      ElMessage.warning("获取社区好物资源地图分布数据异常");
    }
  } catch (error) {
    console.error("❌ 获取社区好物资源地图分布数据失败:", error);
    ElMessage.error("获取社区好物资源地图分布数据失败");
  }
};

// 社区好物辖区热力分布
const handleGoodsHeatMap = async () => {
  try {
    console.log("🔍 点击社区好物辖区热力分布，当前时间范围:", goodsTimeRange.value);

    const areaCode = getDistrictCode();
    const timeType = goodsTimeRange.value;

    console.log("📊 请求社区好物热力图数据:", { areaCode, timeType, resourceType: 2 });

    // 使用社区好物专门的接口
    const response = (await getStoreMapResourceApi({
      areaCode,
      timeType,
      resourceType: 2, // 2: 辖区热力分布
    })) as any;

    console.log("📊 社区好物热力图API响应:", response);

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照其他热力分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log("✅ 调用热力图函数，原始数据:", response.data);

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0,
        }));

        console.log("✅ 转换后的数据:", convertedData);
        (window as any).addTotalHeatMap(convertedData);
        ElMessage.success(
          `社区好物辖区热力分布加载完成，共 ${
            Array.isArray(response.data) ? response.data.length : 0
          } 个热力点`
        );
      } else {
        console.error("❌ 地图热力图函数未找到");
        ElMessage.error("地图热力图函数未找到");
      }
    } else {
      console.warn("⚠️ 社区好物热力图API返回异常:", response);
      ElMessage.warning("获取社区好物热力图数据异常");
    }
  } catch (error: any) {
    console.error("❌ 获取社区好物热力图数据失败:", error);
    ElMessage.error(`获取社区好物热力图数据失败: ${error.message}`);
  }
};

// 社区好物数据详情
const handleGoodsDataDetail = () => {
  console.log("🔍 点击社区好物数据详情，当前时间范围:", goodsTimeRange.value);
  storeDetailVisible.value = true;
};

// 社区活动资源地图分布
const handleActivityResourceMap = async (customAreaCode?: string | Event) => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = "activity";
    console.log("🎯 Right1Component设置激活撒点类型为: activity");

    // 修复：如果传入的是事件对象，则忽略它
    let areaCode: string;
    if (typeof customAreaCode === "string") {
      areaCode = customAreaCode;
    } else {
      areaCode = getDistrictCode();
    }

    console.log(
      "🔍 点击社区活动资源地图分布，当前时间范围:",
      activityTimeRange.value,
      "区域代码:",
      areaCode
    );

    const response = await getActivityMapResourceApi({
      areaCode,
      timeType: activityTimeRange.value,
      resourceType: 1,
    });

    console.log("📊 社区活动资源地图分布API响应:", response);

    if (response.code === 200) {
      // 通知地图组件进行撒点 - 使用通用的 addResourceMapPoints 函数
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data);
        ElMessage.success(
          `社区活动资源地图分布撒点完成，共 ${
            Array.isArray(response.data) ? response.data.length : 0
          } 个点位`
        );
      } else {
        console.error("❌ 地图撒点函数未找到");
        ElMessage.error("地图撒点函数未找到");
      }
    } else {
      console.warn("⚠️ 社区活动资源地图分布API返回异常:", response);
      ElMessage.warning("获取社区活动资源地图分布数据异常");
    }
  } catch (error) {
    console.error("❌ 获取社区活动资源地图分布数据失败:", error);
    ElMessage.error("获取社区活动资源地图分布数据失败");
  }
};

// 社区活动辖区热力分布
const handleActivityHeatMap = async () => {
  try {
    console.log("🔍 点击社区活动辖区热力分布，当前时间范围:", activityTimeRange.value);

    const areaCode = getDistrictCode();
    const timeType = activityTimeRange.value;

    console.log("📊 请求社区活动热力图数据:", { areaCode, timeType, resourceType: 2 });

    // 使用社区活动专门的接口
    const response = (await getActivityMapResourceApi({
      areaCode,
      timeType,
      resourceType: 2, // 2: 辖区热力分布
    })) as any;

    console.log("📊 社区活动热力图API响应:", response);

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照其他热力分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log("✅ 调用热力图函数，原始数据:", response.data);

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0,
        }));

        console.log("✅ 转换后的数据:", convertedData);
        (window as any).addTotalHeatMap(convertedData);
        ElMessage.success(
          `社区活动辖区热力分布加载完成，共 ${
            Array.isArray(response.data) ? response.data.length : 0
          } 个热力点`
        );
      } else {
        console.error("❌ 地图热力图函数未找到");
        ElMessage.error("地图热力图函数未找到");
      }
    } else {
      console.warn("⚠️ 社区活动热力图API返回异常:", response);
      ElMessage.warning("获取社区活动热力图数据异常");
    }
  } catch (error: any) {
    console.error("❌ 获取社区活动热力图数据失败:", error);
    ElMessage.error(`获取社区活动热力图数据失败: ${error.message}`);
  }
};

// 社区活动数据详情
const handleActivityDataDetail = () => {
  console.log("🔍 点击社区活动数据详情，当前时间范围:", activityTimeRange.value);
  activityDetailVisible.value = true;
};

// 社区匠人辖区热力分布
const handleCraftsmanHeatMap = async () => {
  try {
    console.log("🔍 点击社区匠人辖区热力分布，当前时间范围:", craftsmanTimeRange.value);

    const areaCode = getDistrictCode();
    const timeType = craftsmanTimeRange.value;

    console.log("📊 请求社区匠人热力图数据:", { areaCode, timeType, resourceType: 2 });

    // 使用社区匠人专门的接口
    const response = (await getCraftsmanMapResourceApi({
      areaCode,
      timeType,
      resourceType: 2, // 2: 辖区热力分布
    })) as any;

    console.log("📊 社区匠人热力图API响应:", response);

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照其他热力分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log("✅ 调用热力图函数，原始数据:", response.data);

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0,
        }));

        console.log("✅ 转换后的数据:", convertedData);
        (window as any).addTotalHeatMap(convertedData);
        ElMessage.success(
          `社区匠人辖区热力分布加载完成，共 ${
            Array.isArray(response.data) ? response.data.length : 0
          } 个热力点`
        );
      } else {
        console.error("❌ 地图热力图函数未找到");
        ElMessage.error("地图热力图函数未找到");
      }
    } else {
      console.warn("⚠️ 社区匠人热力图API返回异常:", response);
      ElMessage.warning("获取社区匠人热力图数据异常");
    }
  } catch (error: any) {
    console.error("❌ 获取社区匠人热力图数据失败:", error);
    ElMessage.error(`获取社区匠人热力图数据失败: ${error.message}`);
  }
};

// 监听屏幕模式变化
watch(
  () => fullscreenStore.screenMode,
  (screenMode) => {
    console.log("Right1 屏幕模式变化:", screenMode);
    if (screenMode === "wide" || screenMode === "fullscreen") {
      // 进入宽屏模式或全屏模式，使用store状态控制页面
      const right1Page = fullscreenStore.getRight1Page();
      if (right1Page !== null) {
        currentPage.value = right1Page;
        console.log("Right1 进入", screenMode, "模式，页面切换到:", right1Page);
        // 初始化对应页面的数据
        initPageData(right1Page);
      }
    }
  }
);

// 监听右侧页面变化
watch(
  () => fullscreenStore.rightPage,
  (newPage, oldPage) => {
    console.log(
      "Right1 监听到右侧页面变化:",
      oldPage,
      "->",
      newPage,
      "当前屏幕模式:",
      fullscreenStore.screenMode
    );
    if (fullscreenStore.screenMode === "wide" || fullscreenStore.screenMode === "fullscreen") {
      const right1Page = fullscreenStore.getRight1Page();
      if (right1Page !== null) {
        console.log("Right1 准备切换页面:", currentPage.value, "->", right1Page);
        currentPage.value = right1Page;
        console.log("Right1 右侧页面变化，切换到页面:", right1Page);
        // 初始化对应页面的数据
        initPageData(right1Page);
      }
    }
  }
);

// 监听右侧页面切换事件（已废弃，保留以防兼容性问题）
const handleRightPageChange = () => {
  console.log("Right1 handleRightPageChange 被调用（已废弃）");
  // 这个函数已经不再需要，因为我们现在使用 store 的响应式状态
};

// 组件挂载
onMounted(() => {
  console.log("Right1Component 组件已挂载");
  // 监听页面切换事件
  window.addEventListener("rightPageChange", handleRightPageChange);

  // 初始化当前页面数据
  initPageData(currentPage.value);

  // 初始化微互助数据
  getMutualOverviewData(mutualTimeRange.value);
  getMutualDistData(mutualDistTimeRange.value);
  getMutualServiceData(mutualServiceTimeRange.value);
  getMutualHotData(mutualHotTimeRange.value);

  // 初始化社区好物概览数据
  getGoodsOverviewData(goodsTimeRange.value);

  // 初始化社区好物热浏览数据
  getGoodsHotData(goodsHotTimeRange.value);

  // 初始化社区好物分布数据
  getGoodsDistData(goodsDistTimeRange.value);

  // 初始化社区好物热门贴数据
  getGoodsHotTableData(goodsHotTableTimeRange.value);

  // 初始化共享物品概览数据
  getSharedOverviewData(sharedTimeRange.value);

  // 初始化共享热浏览数据
  getSharedHotData(sharedHotTimeRange.value);

  // 初始化共享物品分布数据
  getSharedDistData(sharedDistTimeRange.value);

  // 初始化共享物品热门贴数据
  getSharedHotTableData(sharedHotTableTimeRange.value);

  // 初始化二手闲置概览数据
  getMarketOverviewData(marketTimeRange.value);

  // 初始化闲置热浏览数据
  getMarketHotData(marketHotTimeRange.value);

  // 初始化闲置物品分布数据
  getMarketDistData(marketDistTimeRange.value);

  // 初始化二手闲置热门贴数据
  getMarketHotTableData(marketHotTableTimeRange.value);

  // 初始化社区活动概览数据
  getActivityOverviewData(activityTimeRange.value);

  // 初始化社区活动热浏览数据
  getActivityHotData(activityHotTimeRange.value);

  // 初始化社区活动分布数据
  getActivityDistData(activityDistTimeRange.value);

  // 初始化热门社区活动表格数据
  getActivityHotTableData(activityHotTableTimeRange.value);

  // 初始化社区空间概览数据
  getSpaceOverviewData(spaceTimeRange.value);

  // 初始化社区空间热浏览数据
  getSpaceHotData(spaceHotTimeRange.value);

  // 初始化社区空间分布数据
  getSpaceDistData(spaceDistTimeRange.value);

  // 初始化热门社区空间表格数据
  getSpaceHotTableData(spaceHotTableTimeRange.value);

  // 初始化社区匠人概览数据
  getCraftsmanOverviewData(craftsmanTimeRange.value);

  // 初始化社区匠人热浏览数据
  getCraftsmanHotData(craftsmanHotTimeRange.value);

  // 初始化社区匠人分布数据
  getCraftsmanDistData(craftsmanDistTimeRange.value);

  // 初始化热门社区匠人表格数据
  getCraftsmanHotTableData(craftsmanHotTableTimeRange.value);

  // 初始化社区生活概况数据（仅在第6页时调用）
  if (currentPage.value === 6) {
    getCommunityLifeOverviewData(lifeTimeRange.value);
    getCommunityBookOverviewData(lifeTimeRange.value);
  }

  // 注册全局更新函数
  (window as any).updateRight1WithTownCode = updateAllInterfacesWithTownCode;

  // 注册清除激活状态的全局函数
  (window as any).clearActiveMapPointTypeRight1 = () => {
    activeMapPointType.value = "";
    console.log("🧹 Right1Component清除激活的地图撒点类型");
  };

  // 注册清除激活状态函数（Right1只处理信息公告）
  if (!(window as any).clearActiveMapPointType) {
    (window as any).clearActiveMapPointType = () => {
      activeMapPointType.value = "";
      console.log("🧹 Right1Component: 已清除激活的撒点类型");
    };
  }
});

// 监听页面切换，当切换到第6页时加载社区生活数据
watch(currentPage, (newPage, oldPage) => {
  console.log("Right1Component 页面切换:", oldPage, "->", newPage);

  if (newPage === 6) {
    console.log("切换到社区生活页面，开始加载数据");
    getCommunityLifeOverviewData(lifeTimeRange.value);
    getCommunityBookOverviewData(lifeTimeRange.value);
  }
});

// 创建全局更新函数，支持town_code参数
const updateAllInterfacesWithTownCode = (townCode: string) => {
  console.log("🔄 Right1Component开始使用town_code更新所有接口:", townCode);

  // 协商铃相关接口
  getBellOverviewData(bellTimeRange.value, townCode);
  getBellProcessData(bellProcessTimeRange.value, townCode);
  getAiHotsData(bellHotTimeRange.value, townCode);
  getBellCallLineData(townCode);

  // 连心桥相关接口
  getMutualOverviewData(mutualTimeRange.value, townCode);
  getMutualDistData(mutualDistTimeRange.value, townCode);
  getMutualServiceData(mutualServiceTimeRange.value, townCode);
  getMutualHotData(mutualHotTimeRange.value, townCode);

  // 社区好物相关接口
  getGoodsOverviewData(goodsTimeRange.value, townCode);
  getGoodsHotData(goodsHotTimeRange.value, townCode);
  getGoodsDistData(goodsDistTimeRange.value, townCode);
  getGoodsHotTableData(goodsHotTableTimeRange.value, townCode);

  // 共享物品相关接口
  getSharedOverviewData(sharedTimeRange.value, townCode);
  getSharedHotData(sharedHotTimeRange.value, townCode);
  getSharedDistData(sharedDistTimeRange.value, townCode);
  getSharedHotTableData(sharedHotTableTimeRange.value, townCode);

  // 二手闲置相关接口
  getMarketOverviewData(marketTimeRange.value, townCode);
  getMarketHotData(marketHotTimeRange.value, townCode);
  getMarketDistData(marketDistTimeRange.value, townCode);
  getMarketHotTableData(marketHotTableTimeRange.value, townCode);

  // 社区活动相关接口
  getActivityOverviewData(activityTimeRange.value, townCode);
  getActivityHotData(activityHotTimeRange.value, townCode);
  getActivityDistData(activityDistTimeRange.value, townCode);
  getActivityHotTableData(activityHotTableTimeRange.value, townCode);

  // 社区空间相关接口
  getSpaceOverviewData(spaceTimeRange.value, townCode);
  getSpaceHotData(spaceHotTimeRange.value, townCode);
  getSpaceDistData(spaceDistTimeRange.value, townCode);
  getSpaceHotTableData(spaceHotTableTimeRange.value, townCode);

  // 社区匠人相关接口
  getCraftsmanOverviewData(craftsmanTimeRange.value, townCode);
  getCraftsmanHotData(craftsmanHotTimeRange.value, townCode);
  getCraftsmanDistData(craftsmanDistTimeRange.value, townCode);
  getCraftsmanHotTableData(craftsmanHotTableTimeRange.value, townCode);

  // 社区生活相关接口
  getCommunityLifeOverviewData(lifeTimeRange.value, townCode);

  // 信息公告相关接口
  getNoticeStatisticsData(informationTimeRange.value, townCode);

  // 根据当前激活的撒点类型，重新请求对应的地图数据
  if (activeMapPointType.value === "notice") {
    console.log("🗺️ Right1Component检测到激活的信息公告撒点，开始重新请求地图数据");

    // 先清除现有点位
    if ((window as any).clearResourceMapPoints) {
      (window as any).clearResourceMapPoints();
    }

    // 重新请求信息公告地图数据
    console.log("🔄 重新请求信息公告地图数据");
    handleResourceMap(townCode);
  } else if (activeMapPointType.value === "call") {
    console.log("🗺️ Right1Component检测到激活的协商铃撒点，开始重新请求地图数据");

    // 先清除现有点位
    if ((window as any).clearResourceMapPoints) {
      (window as any).clearResourceMapPoints();
    }

    // 重新请求协商铃地图数据
    console.log("🔄 重新请求协商铃地图数据");
    handleCommunityResponseResourceMap(townCode);
  } else if (activeMapPointType.value === "craftsman") {
    console.log("🗺️ Right1Component检测到激活的社区匠人撒点，开始重新请求地图数据");

    // 先清除现有点位
    if ((window as any).clearResourceMapPoints) {
      (window as any).clearResourceMapPoints();
    }

    // 重新请求社区匠人地图数据
    console.log("🔄 重新请求社区匠人地图数据");
    handleCraftsmanResourceMap(townCode);
  } else if (activeMapPointType.value === "store") {
    console.log("🗺️ Right1Component检测到激活的社区好物撒点，开始重新请求地图数据");

    // 先清除现有点位
    if ((window as any).clearResourceMapPoints) {
      (window as any).clearResourceMapPoints();
    }

    // 重新请求社区好物地图数据
    console.log("🔄 重新请求社区好物地图数据");
    handleGoodsResourceMap(townCode);
  } else if (activeMapPointType.value === "activity") {
    console.log("🗺️ Right1Component检测到激活的社区活动撒点，开始重新请求地图数据");

    // 先清除现有点位
    if ((window as any).clearResourceMapPoints) {
      (window as any).clearResourceMapPoints();
    }

    // 重新请求社区活动地图数据
    console.log("🔄 重新请求社区活动地图数据");
    handleActivityResourceMap(townCode);
  }

  console.log("✅ Right1Component所有接口已使用新的town_code重新请求");
};

// 组件卸载时清理事件监听
onBeforeUnmount(() => {
  window.removeEventListener("rightPageChange", handleRightPageChange);
});

// 微互助概况数据获取函数
const getMutualOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("获取微互助概况数据, timeRange:", timeRange);
    mutualOverviewLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const response = (await getMutualAidInfoApi({
      areaCode,
      timeType: Number(timeRange),
    })) as any;

    console.log("微互助概况API响应:", response);

    if (response.code === 200 && response.message) {
      mutualOverviewData.value = {
        demandTotal: response.message.demandTotal || 0,
        demandEnded: response.message.demandEnded || 0,
        serviceTotal: response.message.serviceTotal || 0,
        serviceEnded: response.message.serviceEnded || 0,
      };

      console.log("微互助概况数据更新成功:", mutualOverviewData.value);
    } else {
      throw new Error("API返回数据格式错误");
    }
  } catch (error) {
    console.error("获取微互助概况数据失败:", error);
    // ElMessage.warning('获取微互助概况数据失败，使用模拟数据')

    // 使用模拟数据作为后备
    mutualOverviewData.value = {
      demandTotal: 156,
      demandEnded: 98,
      serviceTotal: 234,
      serviceEnded: 187,
    };
  } finally {
    mutualOverviewLoading.value = false;
  }
};

// 微互助达成情况分布数据获取函数
const getMutualDistData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("获取微互助达成情况分布数据, timeRange:", timeRange);
    mutualDistLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const response = (await getMutualAidListApi({
      areaCode,
      timeType: Number(timeRange),
      type: mutualTabValue.value,
    })) as any;

    console.log("微互助达成情况分布API响应:", response);

    if (response.code === 200 && response.message?.list) {
      mutualDistData.value = response.message.list.map((item: any) => ({
        district: {
          name: item.district?.name || "未知区域",
        },
        publishCount: item.total || 0,
        completeCount: item.ended || 0,
      }));

      console.log("微互助达成情况分布数据更新成功:", mutualDistData.value);
    } else {
      throw new Error("API返回数据格式错误");
    }
  } catch (error) {
    console.error("获取微互助达成情况分布数据失败:", error);
    ElMessage.warning("获取微互助达成情况分布数据失败，使用模拟数据");

    // 使用模拟数据作为后备
    mutualDistData.value = [
      { district: { name: "文君街道" }, publishCount: 45, completeCount: 32 },
      { district: { name: "临邛街道" }, publishCount: 32, completeCount: 25 },
      { district: { name: "羊安街道" }, publishCount: 67, completeCount: 45 },
    ];
  } finally {
    mutualDistLoading.value = false;
  }
};

// 微互助服务人数数据获取函数
const getMutualServiceData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("获取微互助服务人数数据, timeRange:", timeRange);
    mutualServiceLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const response = (await getMutualAidServiceListApi({
      areaCode,
      timeType: Number(timeRange),
    })) as any;

    console.log("微互助服务人数API响应:", response);

    if (response.code === 200 && response.message?.list) {
      mutualServiceData.value = response.message.list.map((item: any) => ({
        district: {
          name: item.district?.name || "未知区域",
        },
        wishData: item.wishParticipants || 0,
        serviceData: item.serviceParticipants || 0,
      }));

      console.log("微互助服务人数数据更新成功:", mutualServiceData.value);
    } else {
      throw new Error("API返回数据格式错误");
    }
  } catch (error) {
    console.error("获取微互助服务人数数据失败:", error);
    // ElMessage.warning('获取微互助服务人数数据失败，使用模拟数据')

    // 使用模拟数据作为后备
    mutualServiceData.value = [
      { district: { name: "文君街道" }, wishData: 120, serviceData: 180 },
      { district: { name: "临邛街道" }, wishData: 85, serviceData: 120 },
      { district: { name: "羊安街道" }, wishData: 150, serviceData: 200 },
      { district: { name: "桑园镇" }, wishData: 60, serviceData: 90 },
      { district: { name: "夹关镇" }, wishData: 110, serviceData: 160 },
    ];
  } finally {
    mutualServiceLoading.value = false;
  }
};

// 微互助热力图数据获取函数
const getMutualHotData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("获取微互助热力图数据, timeRange:", timeRange);
    mutualHotLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const response = (await getMutualAidHotsApi({
      areaCode,
      timeType: Number(timeRange),
      pageNum: 1,
      pageSize: 5,
    })) as any;

    console.log("微互助热力图API响应:", response);

    if (response.code === 200) {
      // 检查数据是否为空
      if (response.message?.list?.rows && response.message.list.rows.length > 0) {
        mutualHotData.value = response.message.list.rows.map((item: any) => ({
          content: item.title || "未知标题",
          count: item.userCount || 0,
        }));
        console.log("微互助热力图数据更新成功:", mutualHotData.value);
      } else {
        // 数据为空时显示暂无数据
        mutualHotData.value = [{ content: "暂无数据", count: 0 }];
        console.log("微互助热力图数据为空，显示暂无数据");
      }
    } else {
      throw new Error("API返回数据格式错误");
    }
  } catch (error) {
    console.error("获取微互助热力图数据失败:", error);
    ElMessage.warning("获取微互助热力图数据失败");

    // API调用失败时显示暂无数据
    mutualHotData.value = [{ content: "暂无数据", count: 0 }];
  } finally {
    mutualHotLoading.value = false;
  }
};

// ========== 社区生活事件处理函数（与Right2Component保持一致） ==========
const changeLifeOverview = async (timeRange: string) => {
  lifeTimeRange.value = timeRange;
  console.log("切换社区生活概况时间范围:", timeRange);
  await getCommunityLifeOverviewData(timeRange);
};

const handleLifeResourceMap = async () => {
  try {
    console.log('🔍 Right1 - 点击社区生活资源地图分布')
    
    const areaCode = getDistrictCode()
    const params: CommunityGroupMapResourceParams = {
      areaCode,
      timeType: Number(lifeTimeRange.value),
      resourceType: 1
    }

    console.log('🗺️ Right1 - 社区生活资源地图分布API参数:', params)
    const response = await getCommunityGroupMapResourceApi(params)
    console.log('🗺️ Right1 - 社区生活资源地图分布API响应:', response)

    if (response.code === 200) {
      // 通知地图组件进行撒点 - 使用通用的 addResourceMapPoints 函数
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data)
        ElMessage.success(`社区生活资源地图分布撒点完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个点位`)
      } else {
        console.error('❌ 地图撒点函数未找到')
        ElMessage.error('地图撒点函数未找到')
      }
    } else {
      console.warn('⚠️ 社区生活资源地图分布API返回异常:', response)
      ElMessage.warning('获取社区生活资源地图数据失败')
    }
  } catch (error) {
    console.error('❌ 社区生活资源地图分布失败:', error)
    ElMessage.error('社区生活资源地图分布失败')
  }
};

const handleLifeHeatMap = async () => {
  try {
    console.log('🔍 Right1 - 点击社区生活辖区热力分布，当前时间范围:', lifeTimeRange.value)

    const areaCode = getDistrictCode()
    const timeType = Number(lifeTimeRange.value)

    console.log('📊 Right1 - 请求社区生活热力图数据:', { areaCode, timeType, resourceType: 2 })

    // 使用社区圈子资源地图接口，resourceType: 2 表示辖区热力分布
    const response = await getCommunityGroupMapResourceApi({
      areaCode,
      timeType,
      resourceType: 2 // 2: 辖区热力分布
    }) as any

    console.log('📊 Right1 - 社区生活热力图API响应:', response)

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照其他热力分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log('✅ Right1 - 调用热力图函数，原始数据:', response.data)

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0
        }))

        console.log('✅ Right1 - 转换后的数据:', convertedData)
        ;(window as any).addTotalHeatMap(convertedData)
        ElMessage.success(`社区生活辖区热力分布加载完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个热力点`)
      } else {
        console.error('❌ 地图热力图函数未找到')
        ElMessage.error('地图热力图函数未找到')
      }
    } else {
      console.warn('⚠️ 社区生活热力图API返回异常:', response)
      ElMessage.warning('获取社区生活热力图数据异常')
    }
  } catch (error: any) {
    console.error('❌ Right1 - 获取社区生活热力图数据失败:', error)
    ElMessage.error(`获取社区生活热力图数据失败: ${error.message}`)
  }
};

const handleLifeDataDetail = () => {
  console.log('🔍 Right1 - 点击社区生活数据详情，当前时间范围:', lifeTimeRange.value)
  communityGroupDetailVisible.value = true
};

// ========== 社区图书处理函数 ==========
// 处理社区图书资源地图分布
const handleBookResourceMap = async () => {
  try {
    console.log('🔍 Right1 - 点击社区图书资源地图分布，当前时间范围:', lifeTimeRange.value)

    const areaCode = getDistrictCode()
    const params: CommunityBookMapResourceParams = {
      timeType: lifeTimeRange.value,
      areaCode,
      resourceType: 1  // 1: 资源地图分布
    }

    console.log('📚 Right1 - 社区图书资源地图分布API请求参数:', params)

    const response = await getCommunityBookMapResourceApi(params)

    console.log('📚 Right1 - 社区图书资源地图分布API响应:', response)

    if (response.code === 200 && response.data) {
      // 通知地图组件进行撒点
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data)
        ElMessage.success(`社区图书资源地图分布撒点完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个点位`)
      } else {
        console.error('❌ Right1 - 地图撒点函数未找到')
        ElMessage.error('地图撒点函数未找到')
      }
    } else {
      console.warn('⚠️ Right1 - 社区图书资源地图分布API返回异常:', response)
      ElMessage.warning('获取社区图书资源地图分布数据异常')
    }
  } catch (error: any) {
    console.error('❌ Right1 - 获取社区图书资源地图分布数据失败:', error)
    ElMessage.error(`获取社区图书资源地图分布数据失败: ${error.message}`)
  }
}

// 处理社区图书辖区热力分布
const handleBookHeatMap = async () => {
  try {
    console.log('🔍 Right1 - 点击社区图书辖区热力分布，当前时间范围:', lifeTimeRange.value)

    const areaCode = getDistrictCode()
    const params: CommunityBookMapResourceParams = {
      timeType: lifeTimeRange.value,
      areaCode,
      resourceType: 2  // 2: 辖区热力分布
    }

    console.log('🔥 Right1 - 社区图书辖区热力分布API请求参数:', params)

    const response = await getCommunityBookMapResourceApi(params)

    console.log('🔥 Right1 - 社区图书辖区热力分布API响应:', response)

    if (response.code === 200 && response.data) {
      // 转换数据格式为热力图需要的格式
      const heatPoints = response.data
        .filter((item: any) => item.longitude && item.latitude && item.total > 0)
        .map((item: any) => ({
          longitude: parseFloat(item.longitude),
          latitude: parseFloat(item.latitude),
          total: item.total,
          districtName: item.districtName || item.name,
          districtCode: item.districtCode || item.code
        }))

      if (heatPoints.length === 0) {
        console.warn('⚠️ Right1 - 没有有效的社区图书热力点位数据')
        ElMessage.warning('没有有效的社区图书热力分布数据')
        return
      }

      console.log('🔥 Right1 - 社区图书热力图数据:', heatPoints.slice(0, 3)) // 只显示前3条

      // 通知地图组件进行热力图展示
      if ((window as any).addTotalHeatMap) {
        (window as any).addTotalHeatMap(heatPoints)
        console.log('✅ Right1 - 已调用地图组件的社区图书热力图方法')
        ElMessage.success(`社区图书辖区热力分布加载完成，共 ${heatPoints.length} 个热力点`)
      } else {
        console.warn('⚠️ Right1 - 地图组件的 addTotalHeatMap 方法未找到')
        ElMessage.error('地图热力图函数未找到')
      }
    } else {
      console.warn('⚠️ Right1 - 社区图书辖区热力分布API返回异常:', response)
      ElMessage.warning('获取社区图书辖区热力分布数据异常')
    }
  } catch (error: any) {
    console.error('❌ Right1 - 获取社区图书辖区热力分布数据失败:', error)
    ElMessage.error(`获取社区图书辖区热力分布数据失败: ${error.message}`)
  }
}

// 处理社区图书数据详情
const handleBookDataDetail = () => {
  console.log('🔍 Right1 - 点击社区图书数据详情，当前时间范围:', lifeTimeRange.value)
  bookDetailVisible.value = true
}

// 社区生活概况数据获取函数（与Right2Component保持一致）
const getCommunityLifeOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log("获取社区生活概况数据, timeRange:", timeRange);

    // 设置加载状态
    lifeOverviewLoading.value = true;
    lifeTypeLoading.value = true;
    lifeUsageLoading.value = true;
    lifeTopicLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: CommunityLifeInfoParams = {
      areaCode,
      timeType: Number(timeRange),
    };

    console.log("🔄 社区生活概况API参数:", params);
    const response = await getCommunityLifeInfoApi(params);
    console.log("🔄 社区生活概况API响应:", response);

    if (response.code === 200 && response.data) {
      const data = response.data;

      // 更新社区生活概况数据
      lifeOverviewData.value = {
        totalCount: data.total || 1256, // 使用接口返回的total字段
        totalViews: data.browseNumber || 8934, // 使用接口返回的browseNumber字段
      };

      // 更新社区圈子类型分布数据（横向柱状图）- scatters
      if (data.scatters && Array.isArray(data.scatters)) {
        lifeTypeData.value = data.scatters.map((item: any) => ({
          name: item.name || "未知类型",
          value: item.total || 0,
        }));
        lifeTypeLoading.value = false;
        console.log("📊 Right1 - 圈子类型分布数据已更新:", lifeTypeData.value);
      }

      // 更新社区圈子信息数分布数据（双柱状图）- overviews
      if (data.overviews && Array.isArray(data.overviews)) {
        lifeUsageData.value = data.overviews.map((item: any) => ({
          name: item.name || "未知", // 街道名称
          posts: item.total || 0, // 圈子信息数（绿色柱子）
          views: item.browseNumber || 0, // 浏览量（蓝色柱子）
        }));
        lifeUsageLoading.value = false;
        console.log("📊 Right1 - 圈子信息数分布数据已更新:", lifeUsageData.value);
      }

      // 更新社区圈子热门话题分布数据（树状图）- themes
      if (data.themes && Array.isArray(data.themes)) {
        const totalThemes = data.themes.reduce(
          (sum: number, item: any) => sum + (item.total || 0),
          0
        );
        lifeTopicData.value = data.themes.map((item: any) => {
          const value = item.total || 0;
          const percentage = totalThemes > 0 ? Math.round((value / totalThemes) * 100) : 0;
          return {
            name: item.name || "未知话题",
            value: value,
            percentage: `${percentage}%`,
          };
        });
        lifeTopicLoading.value = false;
        console.log("📊 Right1 - 热门话题分布数据已更新:", lifeTopicData.value);
        console.log("📊 Right1 - 热门话题原始数据:", data.themes);
      }

      console.log("✅ Right1 - 社区生活数据更新成功:", {
        overview: lifeOverviewData.value,
        types: lifeTypeData.value,
        usage: lifeUsageData.value,
        topics: lifeTopicData.value,
      });

      console.log("✅ 社区生活概况数据更新成功:", lifeOverviewData.value);
    } else {
      console.warn("⚠️ 社区生活概况API返回异常:", response);
      // 保持默认数据
    }
  } catch (error) {
    console.error("❌ 获取社区生活概况数据失败:", error);
    ElMessage.warning("获取社区生活概况数据失败");
    // 保持默认数据
  } finally {
    lifeOverviewLoading.value = false;
    lifeTypeLoading.value = false;
    lifeUsageLoading.value = false;
    lifeTopicLoading.value = false;
  }
};

// ========== 社区图书数据获取函数 ==========
const getCommunityBookOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    // console.log('📚 Right1 - 开始获取社区图书数据, timeRange:', timeRange)

    // 设置加载状态
    bookOverviewLoading.value = true;
    bookTypeLoading.value = true;
    bookTrendLoading.value = true;
    bookUsageLoading.value = true;

    const areaCode = customAreaCode || getDistrictCode();
    const params: CommunityBookInfoParams = {
      areaCode,
      timeType: Number(timeRange),
    };

    // console.log('📚 Right1 - 社区图书API请求参数:', params)
    const response = await getCommunityBookInfoApi(params);
    // console.log('📚 Right1 - 社区图书API响应:', response)

    if (response.code === 200 && response.data) {
      const data = response.data;
      // console.log('📚 Right1 - 图书API响应数据1111:', data)
      // 更新社区图书概况数据
      bookOverviewData.value = {
        totalCount: data.total || 0,
        totalViews: data.borrowing || 0,
      };

      // 更新社区图书类型分布数据
      if (data.scatters && Array.isArray(data.scatters)) {
        const totalTypes = data.scatters.reduce(
          (sum: number, item: any) => sum + (item.count || 0),
          0
        );
        bookTypeData.value = data.scatters.map((item: any) => {
          const value = item.count || 0;
          const percentage = totalTypes > 0 ? Math.round((value / totalTypes) * 100) : 0;
          return {
            name: item.name || "未知类型",
            value: value,
            percentage: `${percentage}%`,
          };
        });
        // console.log('📚 Right1 - 图书类型分布数据已更新:', bookTypeData.value)
      }

      // 更新社区图书使用分布数据
      // console.log(data.overviews,'')
      if (data.overviews && Array.isArray(data.overviews)) {
        bookUsageData.value = data.overviews.map((item: any) => ({
          name: item.name || "未知区域",
          posts: item.total || 0,
          views: item.browseNumber || 0, // 修复字段名：borrowNumber -> browseNumber
        }));
        console.log("图书使用分布数据-----------:", bookUsageData.value);
      }

      // 更新社区图书使用趋势数据
      // console.log(data.trends,'---------------')
      if (data.trends && Array.isArray(data.trends)) {
        bookTrendData.value = data.trends.map((item: any) => ({
          month: item.name || "未知月份",
          bookCount: item.total || 0,
          viewCount: item.browseNumber || 0, // 修复字段名：borrowCount -> viewCount
        }));
        // console.log('📚 Right1 - 图书使用趋势数据已更新:', bookTrendData.value)
      }

      // console.log('✅ Right1 - 社区图书数据更新成功')
    } else {
      throw new Error("API返回数据格式错误");
    }
  } catch (error) {
    // console.error('❌ 获取社区图书数据失败:', error)
    ElMessage.warning("获取社区图书数据失败");
    // 保持默认数据
  } finally {
    bookOverviewLoading.value = false;
    bookTypeLoading.value = false;
    bookTrendLoading.value = false;
    bookUsageLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.right1-component {
  width: 100%;
  padding-bottom: 100px; /* 增加底部内边距确保最后内容可见 */
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.header-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  border-radius: 0;
  margin-bottom: 20px;
  background: url("@/assets/common/title-bg.png") no-repeat center center;

  .header-back {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.208vw 0.312vw;
    border-radius: 0;
    transition: all 0.3s ease;
    background: #0c162d;
    height: 44px;
    border: 0.026vw solid #24324b;
    width: 72px;
    justify-content: center;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    .back-text {
      color: #1c79e2;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .header-title {
    display: flex;
    align-items: center;
    gap: 8px;

    .title-icon {
      width: 0.781vw;
      height: 0.781vw;
      border-radius: 0;
      margin-top: -0.13vw;

      @media (max-width: 2000px) {
        width: 1.25vw;
        height: 1.25vw;
        margin-top: -0.208vw;
      }

      img {
        width: 100%;
        height: 100%;
      }
    }

    .title-text {
      background: linear-gradient(0deg, #f7f9fc 0%, #9d9da6 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-size: 20px;
      font-weight: 600;
      letter-spacing: 0.013vw;
    }
  }
}

.module-box {
  border: 1px solid #20497f;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
  background: rgba(0, 20, 40, 0.3);
}

// 社区共享选项卡样式
.sharing-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 20px;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 10px 16px;
    color: #9d9da6;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: url("@/assets/common/selected2.png") no-repeat center center;
    background-size: 100% 100%;
    border: none;

    &:hover {
      color: #fff;
    }

    &.active {
      color: #fff;
      background: url("@/assets/common/selected1.png") no-repeat center center;
      background-size: 100% 100%;
    }
  }
}

// 社区圈子选项卡样式
.circle-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 20px;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 10px 16px;
    color: #9d9da6;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: url("@/assets/common/selected2.png") no-repeat center center;
    background-size: 100% 100%;
    border: none;

    &:hover {
      color: #fff;
    }

    &.active {
      color: #fff;
      background: url("@/assets/common/selected1.png") no-repeat center center;
      background-size: 100% 100%;
    }
  }
}
.action-buttons {
  display: flex;
  gap: 8px;
  .action-btn {
    flex: 1;
    padding: 8px 12px;
    font-size: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    // background: transparent;
    // color: #9D9DA6;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  .bg1 {
    background: #107c9e;
    color: #fff;
  }
  .bg2 {
    background: #fba602;
    color: #fff;
  }
  .bg3 {
    background: #1990ff;
    color: #fff;
  }
}

// 测试内容样式
.test-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.test-item {
  background: rgba(23, 81, 159, 0.1);
  border: 1px solid rgba(23, 81, 159, 0.3);
  border-radius: 8px;
  padding: 15px;

  .test-title {
    color: #00d4ff;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
  }
}
</style>
