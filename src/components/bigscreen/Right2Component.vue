<template>
  <div class="right2-component">
    <!-- 标题栏 -->
    <div class="header-bar">
      <div class="header-back" @click="handleGoBack" :style="{ visibility: (fullscreenStore.screenMode === 'wide' || fullscreenStore.isFullscreen) ? 'hidden' : 'visible' }">
        <span class="back-text">上一页</span>
      </div>
      <div class="header-title">
        <div class="title-icon">
          <img src="@/assets/common/icon4.png" alt="title-icon">
        </div>
        <span class="title-text">{{ currentPageTitle }}</span>
      </div>
      <div class="header-back" @click="handleGoNext" :style="{ visibility: 'visible' }">
        <span class="back-text">下一页</span>
      </div>
    </div>

    <!-- 第一页：连心桥 (全屏模式下显示) -->
    <template v-if="currentPage === 1">
      <div class="module-box">
        <ModuleTitle title="微互助概况" />
        <DateGroup :index="mutualTimeRange" :dateData="timeOptions" @change="changeMutualOverview"/>
        <MutualAidOverview :overviewData="mutualOverviewData" :loading="mutualOverviewLoading" />
      </div>

      <div class="module-box">
        <ModuleTitle title="微互助达成情况分布" />
        <div style="display: flex; align-items: center;">
          <SplitTab
            v-model="mutualTabValue"
            :tabs="[
              { label: '微心愿', value: '0' },
              { label: '微服务', value: '1' }
            ]"
            @change="handleMutualTabChange"
          />
          <DateGroup :index="mutualDistTimeRange" :dateData="processTimeOptions" @change="changeMutualDist"/>
        </div>
        <EchartsCompletionDistribution :chartData="mutualDistData" :loading="mutualDistLoading" />
      </div>

      <div class="module-box">
        <ModuleTitle title="微互助服务人数" />
        <DateGroup :index="mutualServiceTimeRange" :dateData="processTimeOptions" @change="changeMutualService"/>
        <EchartsParticipantHorizontal :chartData="mutualServiceData" :loading="mutualServiceLoading"/>
      </div>

      <div class="module-box">
        <ModuleTitle title="微心愿/微服务热力图" />
        <div style="display: flex; align-items: center;">
          <SplitTab
            v-model="mutualHotTabValue"
            :tabs="[
              { label: '微心愿', value: '0' },
              { label: '微服务', value: '1' }
            ]"
            @change="handleMutualHotTabChange"
          />
          <DateGroup :index="mutualHotTimeRange" :dateData="processTimeOptions" @change="changeMutualHot"/>
        </div>
        <EchartsTreemap :chartData="mutualHotData" :loading="mutualHotLoading" />
      </div>
    </template>

    <!-- 第二页：社区圈子 (全屏模式下显示) -->
    <template v-else-if="currentPage === 2">
      <!-- 社区圈子选项卡 -->
      <div class="circle-tabs">
        <div
          class="tab-item"
          :class="{ active: circleActiveTab === 'activity' }"
          @click="handleCircleTabChange('activity')"
        >
          社区活动
        </div>
        <div
          class="tab-item"
          :class="{ active: circleActiveTab === 'space' }"
          @click="handleCircleTabChange('space')"
        >
          社区空间
        </div>
        <div
          class="tab-item"
          :class="{ active: circleActiveTab === 'craftsman' }"
          @click="handleCircleTabChange('craftsman')"
        >
          社区匠人
        </div>
      </div>

      <!-- 社区活动内容 -->
      <template v-if="circleActiveTab === 'activity'">
        <div class="module-box">
          <ModuleTitle title="社区活动概览" />
          <DateGroup :index="activityTimeRange" :dateData="timeOptions" @change="changeActivityOverview"/>
          <ActivityOverview :overviewData="activityOverviewData" />
          <div class="action-buttons" style="margin-top: 20px;">
            <button class="action-btn bg1" @click="handleActivityResourceMap">资源地图分布</button>
            <button class="action-btn bg2" @click="handleActivityHeatMap">辖区热力分布</button>
            <button class="action-btn bg3" @click="handleActivityDataDetail">数据详情</button>
          </div>
        </div>

        <div class="module-box">
          <ModuleTitle title="社区活动热浏览概况" />
          <DateGroup :index="activityHotTimeRange" :dateData="processTimeOptions" @change="changeActivityHot"/>
          <EchartsTreemap
            :chartData="activityHotData"
            :loading="activityHotLoading"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="社区活动浏览及参与人数分布" />
          <DateGroup :index="activityDistTimeRange" :dateData="processTimeOptions" @change="changeActivityDist"/>
          <EchartsActivityDistribution
            :chartData="activityDistData"
            :loading="activityDistLoading"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="热门社区活动" />
          <DateGroup :index="activityHotTableTimeRange" :dateData="timeOptions" @change="changeActivityHotTable"/>
          <ReuseTable
            :tableData="activityHotTableData"
            :columns="[
              { prop: 'districtName', label: '所属辖区' },
              { prop: 'title', label: '社区活动标题' },
              { prop: 'userCount', label: '参与人数' }
            ]"
            :loading="activityHotTableLoading"
          />
        </div>
      </template>

      <!-- 社区空间内容 -->
      <template v-else-if="circleActiveTab === 'space'">
        <div class="module-box">
          <ModuleTitle title="社区空间概览" />
          <DateGroup :index="spaceTimeRange" :dateData="timeOptions" @change="changeSpaceOverview"/>
          <ReuseOverview
            :overviewData="spaceOverviewData"
            :titleData="{itemsTitle:'社区空间',clicksTitle:'总浏览量'}"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="社区空间热浏览概况" />
          <DateGroup :index="spaceHotTimeRange" :dateData="processTimeOptions" @change="changeSpaceHot"/>
          <EchartsTreemap
            :chartData="spaceHotData"
            :loading="spaceHotLoading"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="社区空间浏览及使用人数分布" />
          <DateGroup :index="spaceDistTimeRange" :dateData="processTimeOptions" @change="changeSpaceDist"/>
          <EchartsReuseDistribution
            :chartData="spaceDistData"
            :loading="spaceDistLoading"
            :seriesNames="{
              goods: '社区空间数量',
              views: '浏览量'
            }"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="热门社区空间" />
          <DateGroup :index="spaceHotTableTimeRange" :dateData="timeOptions" @change="changeSpaceHotTable"/>
          <ReuseTable
            :tableData="spaceHotTableData"
            :columns="[
              { prop: 'districtName', label: '所属辖区' },
              { prop: 'name', label: '社区空间名称' },
              { prop: 'browseNumber', label: '参与人数' }
            ]"
            :loading="spaceHotTableLoading"
          />
        </div>
      </template>

      <!-- 社区匠人内容 -->
      <template v-else-if="circleActiveTab === 'craftsman'">
        <div class="module-box">
          <ModuleTitle title="社区匠人概览" />
          <DateGroup :index="craftsmanTimeRange" :dateData="timeOptions" @change="changeCraftsmanOverview"/>
          <ReuseOverview
            :overviewData="craftsmanOverviewData"
            :titleData="{itemsTitle:'社区匠人',clicksTitle:'总浏览量'}"
          />
           <div class="action-buttons" style="margin-top: 20px;">
            <button class="action-btn bg1" @click="handleCraftsmanResourceMap">资源地图分布</button>
            <button class="action-btn bg2" @click="handleCraftsmanHeatMap">辖区热力分布</button>
            <button class="action-btn bg3" @click="handleCraftsmanDataDetail">数据详情</button>
          </div>
        </div>

        <div class="module-box">
          <ModuleTitle title="社区匠人热浏览概况" />
          <DateGroup :index="craftsmanHotTimeRange" :dateData="processTimeOptions" @change="changeCraftsmanHot"/>
          <EchartsTreemap
            :chartData="craftsmanHotData"
            :loading="craftsmanHotLoading"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="社区匠人浏览及关注人数分布" />
          <DateGroup :index="craftsmanDistTimeRange" :dateData="processTimeOptions" @change="changeCraftsmanDist"/>
          <EchartsReuseDistribution
            :chartData="craftsmanDistData"
            :loading="craftsmanDistLoading"
            :seriesNames="{
              goods: '社区匠人数量',
              views: '浏览量'
            }"
          />
        </div>

        <div class="module-box">
          <ModuleTitle title="热门社区匠人" />
          <DateGroup :index="craftsmanHotTableTimeRange" :dateData="processTimeOptions" @change="changeCraftsmanHotTable"/>
          <ReuseTable
            :tableData="craftsmanHotTableData"
            :columns="[
              { prop: 'districtName', label: '所属辖区' },
              { prop: 'name', label: '社区匠人标题' },
              { prop: 'browseNumber', label: '浏览次数' }
            ]"
            :loading="craftsmanHotTableLoading"
          />
        </div>
      </template>
    </template>

    <!-- 第三页：社区生活 -->
    <template v-else-if="currentPage === 3">
      <div class="module-box">
        <ModuleTitle title="社区生活概况" />
        <DateGroup :index="lifeTimeRange" :dateData="timeOptions" @change="changeLifeOverview"/>
        <CommunityLifeOverview
          :overviewData="lifeOverviewData"
          :loading="lifeOverviewLoading"
          @resourceMap="handleLifeResourceMap"
          @heatMap="handleLifeHeatMap"
          @dataDetail="handleLifeDataDetail"
        />
      </div>
      <div class="module-box">
        <ModuleTitle title="社区圈子类型分布" />
        <EchartsCommunityLifeTypeBar
          :chartData="lifeTypeData"
          :loading="lifeTypeLoading"
        />
      </div>

      <div class="module-box">
        <ModuleTitle title="社区圈子信息数分布" />
        <EchartsCommunityLifeUsage
          :chartData="lifeUsageData"
          :loading="lifeUsageLoading"
        />
      </div>

      <div class="module-box">
        <ModuleTitle title="社区圈子热门话题分布" />
        <EchartsCommunityLifeTrend
          :chartData="lifeTopicData"
          :loading="lifeTopicLoading"
        />
      </div>

      <!-- 社区图书模块 -->
      <div class="module-box">
        <ModuleTitle title="社区图书概况" />
        <CommunityBookOverview
          :overview-data="bookOverviewData"
          :loading="bookOverviewLoading"
          @resourceMap="handleBookResourceMap"
          @heatMap="handleBookHeatMap"
          @dataDetail="handleBookDataDetail"
        />
      </div>

      <div class="module-box">
        <ModuleTitle title="社区图书类型分布" />
        <EchartsBookTypePie
          :chartData="bookTypeData"
          :loading="bookTypeLoading"
        />
      </div>

      <div class="module-box">
        <ModuleTitle title="社区图书使用分布" />
        <!-- 临时调试信息 -->
        <EchartsCommunityLifeUsage
          :chartData="bookUsageData"
          :loading="bookUsageLoading"
        />
      </div>

      <div class="module-box">
        <ModuleTitle title="社区图书使用趋势图" />
        <EchartsBookUsageTrend
          :chartData="bookTrendData"
          :loading="bookTrendLoading"
        />
      </div>

    </template>
  </div>

  <!-- 社区匠人数据详情弹窗 -->
  <CraftsmanDataDetailDialog
    v-model:visible="craftsmanDetailVisible"
    :area-code="getDistrictCode()"
    :time-range="craftsmanTimeRange"
  />

  <!-- 社区活动数据详情弹窗 -->
  <ActivityDataDetailDialog
    v-model:visible="activityDetailVisible"
    :area-code="getDistrictCode()"
    :time-range="activityTimeRange"
  />

  <!-- 社区生活数据详情弹窗 -->
  <CommunityGroupDataDetailDialog
    v-model:visible="lifeDetailVisible"
    :area-code="getDistrictCode()"
    :time-range="lifeTimeRange"
  />

  <!-- 社区图书数据详情弹窗 -->
  <CommunityBookDataDetailDialog
    v-model:visible="bookDetailVisible"
    :area-code="getDistrictCode()"
    :time-range="lifeTimeRange"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import ModuleTitle from './components/ModuleTitle.vue'
import DateGroup from './components/DateGroup.vue'
import ActivityOverview from './components/ActivityOverview.vue'
import ReuseOverview from './components/ReuseOverview.vue'

import EchartsActivityDistribution from './components/EchartsActivityDistribution.vue'
import EchartsReuseDistribution from './components/EchartsReuseDistribution.vue'
import MutualAidOverview from './components/MutualAidOverview.vue'
import EchartsCompletionDistribution from './components/EchartsCompletionDistribution.vue'
import EchartsParticipantHorizontal from './components/EchartsParticipantHorizontal.vue'
import EchartsTreemap from './components/EchartsTreemap.vue'
import SplitTab from './components/SplitTab.vue'
import ReuseTable from './components/ReuseTable.vue'
import CommunityLifeOverview from './components/CommunityLifeOverview.vue'
import CommunityBookOverview from './components/CommunityBookOverview.vue'
import EchartsBookTypePie from './components/EchartsBookTypePie.vue'
import EchartsBookUsageTrend from './components/EchartsBookUsageTrend.vue'
import EchartsCommunityLifeTrend from './components/EchartsCommunityLifeTrend.vue'
import EchartsCommunityLifeTypeBar from './components/EchartsCommunityLifeTypeBar.vue'
import EchartsCommunityLifeUsage from './components/EchartsCommunityLifeUsage.vue'
import { useFullscreenStore } from '@/stores/fullscreen'
// 导入连心桥相关的API函数和类型
import { getMutualAidInfoApi, getMutualAidListApi, getMutualAidServiceListApi, getMutualAidHotsApi, getActivityInfoApi, getActivityHotsApi, getActivityListApi, getSpaceInfoApi, getSpaceHotList, getSpaceListApi, getCraftsmanInfoApi, getCraftsmanHotListApi, getCraftsmanDistributionApi } from '@/api/suggestion'
import type { MutualAidInfoParams, MutualAidListParams, MutualAidServiceListParams, MutualAidHotsParams, ActivityInfoParams, ActivityHotsParams, ActivityListParams, SpaceInfoParams, SpaceHotParams, SpaceListParams, CraftsmanInfoParams, CraftsmanHotParams, CraftsmanDistributionParams } from '@/api/suggestion'
// 导入社区生活相关的API函数和类型
import { getCommunityLifeInfoApi, getCommunityBookInfoApi, getCommunityGroupMapResourceApi, getCommunityBookMapResourceApi } from '@/api/community'
import type { CommunityLifeInfoParams, CommunityBookInfoParams, CommunityGroupMapResourceParams, CommunityBookMapResourceParams } from '@/api/community'
import { getCraftsmanMapResourceApi, getActivityMapResourceApi } from '@/api/bigscreen'
import type { CraftsmanMapResourceParams, ActivityMapResourceParams } from '@/api/bigscreen'
import { getDistrictCode } from '@/utils/district'
import CraftsmanDataDetailDialog from './components/CraftsmanDataDetailDialog.vue'
import ActivityDataDetailDialog from './components/ActivityDataDetailDialog.vue'
import CommunityGroupDataDetailDialog from './components/CommunityGroupDataDetailDialog.vue'
import CommunityBookDataDetailDialog from './components/CommunityBookDataDetailDialog.vue'

// 全屏状态管理
const fullscreenStore = useFullscreenStore()

// 页面切换状态
const currentPage = ref(1) // 1: 连心桥, 2: 社区圈子, 3: 社区生活
const currentPageTitle = computed(() => {
  switch (currentPage.value) {
    case 1: return '连心桥'
    case 2: return '社区圈子'
    case 3: return '社区生活'
    default: return '连心桥'
  }
})

// 时间选项
const timeOptions = [
  { label: '当天', value: '0' },
  { label: '一周', value: '1' },
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]

const processTimeOptions = [
  { label: '近三月', value: '2' },
  { label: '近六月', value: '3' },
  { label: '近一年', value: '4' }
]

// ========== 连心桥相关数据 ==========
const mutualTimeRange = ref('0')
const mutualDistTimeRange = ref('2')
const mutualServiceTimeRange = ref('2')
const mutualTabValue = ref('0')

const mutualOverviewData = ref({
  demandTotal: 156,
  demandEnded: 98,
  serviceTotal: 234,
  serviceEnded: 187
})

const mutualDistData = ref([
  { district: { name: '文君街道' }, publishCount: 45, completeCount: 32 },
  { district: { name: '临邛街道' }, publishCount: 32, completeCount: 25 },
  { district: { name: '羊安街道' }, publishCount: 67, completeCount: 45 }
])

const mutualServiceData = ref([
  { district: { name: '文君街道' }, wishData: 120, serviceData: 180 },
  { district: { name: '临邛街道' }, wishData: 85, serviceData: 120 },
  { district: { name: '羊安街道' }, wishData: 150, serviceData: 200 },
  { district: { name: '桑园镇' }, wishData: 60, serviceData: 90 },
  { district: { name: '夹关镇' }, wishData: 110, serviceData: 160 }
])

const mutualDistLoading = ref(false)
const mutualServiceLoading = ref(false)
const mutualOverviewLoading = ref(false)

// 微互助热力图
const mutualHotTimeRange = ref('2')
const mutualHotTabValue = ref('0')

const mutualHotData = ref([
  { content: '我想要一盒水彩笔', count: 3 },
  { content: '免费为老人理发', count: 1 },
  { content: '上门做菜', count: 1 },
  { content: '提供房屋漏水修缮', count: 0 },
  { content: '测试', count: 0 }
])

const mutualHotLoading = ref(false)

// ========== 社区圈子相关数据 ==========
const circleActiveTab = ref('activity')

// 社区活动
const activityTimeRange = ref('0')
const activityHotTimeRange = ref('2')
const activityDistTimeRange = ref('2')

const activityOverviewData = ref({
  activityCount: 45,
  ongoingCount: 12,
  completedCount: 33
})

const activityHotData = ref([
  { content: '社区运动会', count: 456 },
  { content: '文艺演出', count: 389 },
  { content: '志愿服务', count: 312 },
  { content: '亲子活动', count: 278 },
  { content: '健康讲座', count: 234 }
])

const activityDistData = ref([
  { name: '文君街道', districtName: '文君街道', activityCount: 15, count: 15, participantCount: 456, userCount: 456, participants: 456 },
  { name: '临邛街道', districtName: '临邛街道', activityCount: 12, count: 12, participantCount: 378, userCount: 378, participants: 378 },
  { name: '羊安街道', districtName: '羊安街道', activityCount: 10, count: 10, participantCount: 289, userCount: 289, participants: 289 }
])

const activityHotLoading = ref(false)
const activityDistLoading = ref(false)

// 热门社区活动表格
const activityHotTableTimeRange = ref('4')
const activityHotTableData = ref([
  { districtName: '顺河社区', title: '"传承非遗文化，共建和谐社区"', userCount: 1 },
  { districtName: '顺河社区', title: '首届户外亲子健步走活动', userCount: 0 },
  { districtName: '顺河社区', title: '时光里的她', userCount: 0 }
])
const activityHotTableLoading = ref(false)

// 社区空间
const spaceTimeRange = ref('0')
const spaceHotTimeRange = ref('2')
const spaceDistTimeRange = ref('2')

const spaceOverviewData = ref({
  itemsCount: 28,
  clicksCount: 3456
})

const spaceHotData = ref([
  { content: '社区会议室', count: 234 },
  { content: '健身房', count: 198 },
  { content: '图书阅览室', count: 167 },
  { content: '儿童活动室', count: 145 },
  { content: '多功能厅', count: 123 }
])

const spaceDistData = ref([
  { district: { name: '文君街道' }, goods: 10, views: 1234 },
  { district: { name: '临邛街道' }, goods: 8, views: 987 },
  { district: { name: '羊安街道' }, goods: 6, views: 756 }
])

const spaceHotLoading = ref(false)
const spaceDistLoading = ref(false)

// 热门社区空间表格
const spaceHotTableTimeRange = ref('4')
const spaceHotTableData = ref([
  { districtName: '花园巷社区', name: '文脉坊', browseNumber: 30 },
  { districtName: '顺河社区', name: '好邻居超市', browseNumber: 15 },
  { districtName: '铁花社区', name: '铁花巷', browseNumber: 14 },
  { districtName: '文君井社区', name: '文君井公园', browseNumber: 13 },
  { districtName: '花园巷社区', name: '新山书屋', browseNumber: 7 }
])
const spaceHotTableLoading = ref(false)

// 社区匠人
const craftsmanTimeRange = ref('0')
const craftsmanHotTimeRange = ref('2')
const craftsmanDistTimeRange = ref('2')

const craftsmanOverviewData = ref({
  itemsCount: 67,
  clicksCount: 8901
})

const craftsmanHotData = ref([
  { content: '手工艺大师', count: 345 },
  { content: '美食达人', count: 289 },
  { content: '园艺专家', count: 234 },
  { content: '修理能手', count: 198 },
  { content: '教育专家', count: 167 }
])

const craftsmanDistData = ref([
  { district: { name: '文君街道' }, goods: 25, views: 3456 },
  { district: { name: '临邛街道' }, goods: 20, views: 2789 },
  { district: { name: '羊安街道' }, goods: 15, views: 2134 }
])

const craftsmanHotLoading = ref(false)
const craftsmanDistLoading = ref(false)

// 热门社区匠人表格
const craftsmanHotTableTimeRange = ref('2')
const craftsmanHotTableData = ref([
  { districtName: '顺河社区', name: '补鞋匠', browseNumber: 16 },
  { districtName: '顺河社区', name: '中药制丸师', browseNumber: 6 },
  { districtName: '顺河社区', name: '磨刀匠', browseNumber: 4 }
])
const craftsmanHotTableLoading = ref(false)

// 社区匠人数据详情弹窗状态
const craftsmanDetailVisible = ref(false)

// 社区活动数据详情弹窗状态
const activityDetailVisible = ref(false)

// 社区生活数据详情弹窗状态
const lifeDetailVisible = ref(false)

// 社区图书数据详情弹窗状态
const bookDetailVisible = ref(false)

// 跟踪当前激活的地图撒点类型
const activeMapPointType = ref('') // 'craftsman', 'activity'

// ========== 社区生活相关数据 ==========
const lifeTimeRange = ref('0')
const lifeOverviewData = ref({
  totalCount: 1256,
  totalViews: 8934
})

const lifeTypeData = ref([
  { name: '文学', value: 2 },
  { name: '文化', value: 2 },
  { name: '科学', value: 13 },
  { name: '教育', value: 10 },
  { name: '历史', value: 7 },
  { name: '地理', value: 10 },
  { name: '艺术', value: 15 },
  { name: '社会科学', value: 11 },
  { name: '工业', value: 17 },
  { name: '自然科学', value: 13 }
])

const lifeUsageData = ref([
  { name: "临邛街道", posts: 100, views: 137 },
  { name: "文君街道", posts: 100, views: 138 },
  { name: "固驿街道", posts: 110, views: 122 },
  { name: "羊安街道", posts: 90, views: 100 },
  { name: "牟礼街道", posts: 95, views: 130 },
  { name: "孔明街道", posts: 100, views: 130 },
  { name: "平乐镇", posts: 130, views: 140 },
  { name: "夹关镇", posts: 115, views: 142 },
])

const lifeTopicData = ref([
  { name: '游戏', value: 25, percentage: '20%' },
  { name: '舞蹈', value: 62, percentage: '25%' },
  { name: '跑步', value: 77, percentage: '30%' },
  { name: '宠物', value: 44, percentage: '15%' },
  { name: '邻里服务', value: 18, percentage: '10%' }
])

// ========== 社区图书数据 ==========
const bookOverviewData = ref({
  totalCount: 23768,
  totalViews: 237690
})

const bookTypeData = ref([
  { name: '文学', value: 35, percentage: '35%' },
  { name: '历史', value: 22, percentage: '22%' },
  { name: '科学', value: 18, percentage: '18%' },
  { name: '艺术', value: 12, percentage: '12%' },
  { name: '哲学', value: 8, percentage: '8%' },
  { name: '其他', value: 5, percentage: '5%' }
])

const bookTrendData = ref([
  { month: '1月', bookCount: 180, viewCount: 220 },
  { month: '2月', bookCount: 145, viewCount: 180 },
  { month: '3月', bookCount: 230, viewCount: 250 },
  { month: '4月', bookCount: 190, viewCount: 200 },
  { month: '5月', bookCount: 165, viewCount: 190 },
  { month: '6月', bookCount: 200, viewCount: 230 },
  { month: '7月', bookCount: 185, viewCount: 210 },
  { month: '8月', bookCount: 175, viewCount: 195 }
])

const bookUsageData = ref([
  { name: "临邛街道", posts: 100, views: 142 },
  { name: "文君街道", posts: 100, views: 140 },
  { name: "固驿街道", posts: 110, views: 130 },
  { name: "羊安街道", posts: 90, views: 100 },
  { name: "牟礼街道", posts: 95, views: 130 },
  { name: "孔明街道", posts: 100, views: 130 },
  { name: "平乐镇", posts: 130, views: 140 },
  { name: "夹关镇", posts: 115, views: 122 },
])

const lifeTypeLoading = ref(false)
const lifeUsageLoading = ref(false)
const lifeTopicLoading = ref(false)
const lifeOverviewLoading = ref(false)

// 社区图书 loading 状态
const bookOverviewLoading = ref(false)
const bookTypeLoading = ref(false)
const bookTrendLoading = ref(false)
const bookUsageLoading = ref(false)

// ========== 页面切换函数 ==========
const handleGoBack = () => {
  if (fullscreenStore.isFullscreen) {
    // 全屏模式下的逻辑 - 与Right1联动
    return
  } else {
    // 非全屏模式下的正常切换逻辑
    if (currentPage.value > 1) {
      currentPage.value--
    } else {
      currentPage.value = 3  // 支持3页循环
    }
  }
  console.log('Right2 切换到页面:', currentPage.value)
}

const handleGoNext = () => {
  console.log('Right2 handleGoNext 被调用')
  console.log('当前屏幕模式:', fullscreenStore.screenMode)
  console.log('当前页面:', currentPage.value)
  console.log('当前右侧页面:', fullscreenStore.rightPage)

  if (fullscreenStore.screenMode === 'wide' || fullscreenStore.isFullscreen) {
    // 宽屏模式和全屏模式下的逻辑 - 切换右侧页面
    console.log('执行宽屏/全屏模式右侧切换逻辑')
    fullscreenStore.switchRightPage()
    console.log('Right2 宽屏/全屏模式下切换页面，当前右侧页面:', fullscreenStore.rightPage)
    return
  } else {
    // 窄屏模式下的正常切换逻辑
    console.log('执行窄屏模式切换逻辑')
    if (currentPage.value < 3) {
      currentPage.value++
    } else {
      currentPage.value = 1
    }
    console.log('Right2 窄屏模式下切换到页面:', currentPage.value)
  }
}

// ========== Tab切换函数 ==========
const handleCircleTabChange = (tab: string) => {
  circleActiveTab.value = tab
  console.log('切换社区圈子tab:', tab)
}

// ========== 社区生活事件处理函数 ==========
const changeLifeOverview = async (timeRange: string) => {
  lifeTimeRange.value = timeRange
  console.log('切换社区生活概况时间范围:', timeRange)
  await getCommunityLifeOverviewData(timeRange)
}

const handleLifeResourceMap = async () => {
  try {
    console.log('🔍 点击社区圈子资源地图分布')
    
    const areaCode = getDistrictCode()
    const params: CommunityGroupMapResourceParams = {
      areaCode,
      timeType: Number(lifeTimeRange.value),
      resourceType: 1
    }

    console.log('🗺️ Right2 - 社区圈子资源地图分布API参数:', params)
    const response = await getCommunityGroupMapResourceApi(params)
    console.log('🗺️ Right2 - 社区圈子资源地图分布API响应:', response)

    if (response.code === 200) {
      // 通知地图组件进行撒点 - 使用通用的 addResourceMapPoints 函数
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data)
        ElMessage.success(`社区圈子资源地图分布撒点完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个点位`)
      } else {
        console.error('❌ 地图撒点函数未找到')
        ElMessage.error('地图撒点函数未找到')
      }
    } else {
      console.warn('⚠️ 社区圈子资源地图分布API返回异常:', response)
      ElMessage.warning('获取社区圈子资源地图数据失败')
    }
  } catch (error) {
    console.error('❌ 社区圈子资源地图分布失败:', error)
    ElMessage.error('社区圈子资源地图分布失败')
  }
}

const handleLifeHeatMap = async () => {
  try {
    console.log('🔍 Right2 - 点击社区生活辖区热力分布，当前时间范围:', lifeTimeRange.value)

    const areaCode = getDistrictCode()
    const timeType = Number(lifeTimeRange.value)

    console.log('📊 Right2 - 请求社区生活热力图数据:', { areaCode, timeType, resourceType: 2 })

    // 使用社区圈子资源地图接口，resourceType: 2 表示辖区热力分布
    const response = await getCommunityGroupMapResourceApi({
      areaCode,
      timeType,
      resourceType: 2 // 2: 辖区热力分布
    }) as any

    console.log('📊 Right2 - 社区生活热力图API响应:', response)

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照其他热力分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log('✅ Right2 - 调用热力图函数，原始数据:', response.data)

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0
        }))

        console.log('✅ Right2 - 转换后的数据:', convertedData)
        ;(window as any).addTotalHeatMap(convertedData)
        ElMessage.success(`社区生活辖区热力分布加载完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个热力点`)
      } else {
        console.error('❌ 地图热力图函数未找到')
        ElMessage.error('地图热力图函数未找到')
      }
    } else {
      console.warn('⚠️ 社区生活热力图API返回异常:', response)
      ElMessage.warning('获取社区生活热力图数据异常')
    }
  } catch (error: any) {
    console.error('❌ Right2 - 获取社区生活热力图数据失败:', error)
    ElMessage.error(`获取社区生活热力图数据失败: ${error.message}`)
  }
}

const handleLifeDataDetail = () => {
  console.log('🔍 点击社区生活数据详情，当前时间范围:', lifeTimeRange.value)
  lifeDetailVisible.value = true
}

// ========== 社区图书处理函数 ==========
// 处理社区图书资源地图分布
const handleBookResourceMap = async () => {
  try {
    console.log('🔍 Right2 - 点击社区图书资源地图分布，当前时间范围:', lifeTimeRange.value)

    const areaCode = getDistrictCode()
    const params: CommunityBookMapResourceParams = {
      timeType: lifeTimeRange.value,
      areaCode,
      resourceType: 1  // 1: 资源地图分布
    }

    console.log('📚 Right2 - 社区图书资源地图分布API请求参数:', params)

    const response = await getCommunityBookMapResourceApi(params)

    console.log('📚 Right2 - 社区图书资源地图分布API响应:', response)

    if (response.code === 200 && response.data) {
      // 通知地图组件进行撒点
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data)
        ElMessage.success(`社区图书资源地图分布撒点完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个点位`)
      } else {
        console.error('❌ Right2 - 地图撒点函数未找到')
        ElMessage.error('地图撒点函数未找到')
      }
    } else {
      console.warn('⚠️ Right2 - 社区图书资源地图分布API返回异常:', response)
      ElMessage.warning('获取社区图书资源地图分布数据异常')
    }
  } catch (error: any) {
    console.error('❌ Right2 - 获取社区图书资源地图分布数据失败:', error)
    ElMessage.error(`获取社区图书资源地图分布数据失败: ${error.message}`)
  }
}

// 处理社区图书辖区热力分布
const handleBookHeatMap = async () => {
  try {
    console.log('🔍 Right2 - 点击社区图书辖区热力分布，当前时间范围:', lifeTimeRange.value)

    const areaCode = getDistrictCode()
    const params: CommunityBookMapResourceParams = {
      timeType: lifeTimeRange.value,
      areaCode,
      resourceType: 2  // 2: 辖区热力分布
    }

    console.log('🔥 Right2 - 社区图书辖区热力分布API请求参数:', params)

    const response = await getCommunityBookMapResourceApi(params)

    console.log('🔥 Right2 - 社区图书辖区热力分布API响应:', response)

    if (response.code === 200 && response.data) {
      // 转换数据格式为热力图需要的格式
      const heatPoints = response.data
        .filter((item: any) => item.longitude && item.latitude && item.total > 0)
        .map((item: any) => ({
          longitude: parseFloat(item.longitude),
          latitude: parseFloat(item.latitude),
          total: item.total,
          districtName: item.districtName || item.name,
          districtCode: item.districtCode || item.code
        }))

      if (heatPoints.length === 0) {
        console.warn('⚠️ Right2 - 没有有效的社区图书热力点位数据')
        ElMessage.warning('没有有效的社区图书热力分布数据')
        return
      }

      console.log('🔥 Right2 - 社区图书热力图数据:', heatPoints.slice(0, 3)) // 只显示前3条

      // 通知地图组件进行热力图展示
      if ((window as any).addTotalHeatMap) {
        (window as any).addTotalHeatMap(heatPoints)
        console.log('✅ Right2 - 已调用地图组件的社区图书热力图方法')
        ElMessage.success(`社区图书辖区热力分布加载完成，共 ${heatPoints.length} 个热力点`)
      } else {
        console.warn('⚠️ Right2 - 地图组件的 addTotalHeatMap 方法未找到')
        ElMessage.error('地图热力图函数未找到')
      }
    } else {
      console.warn('⚠️ Right2 - 社区图书辖区热力分布API返回异常:', response)
      ElMessage.warning('获取社区图书辖区热力分布数据异常')
    }
  } catch (error: any) {
    console.error('❌ Right2 - 获取社区图书辖区热力分布数据失败:', error)
    ElMessage.error(`获取社区图书辖区热力分布数据失败: ${error.message}`)
  }
}

// 处理社区图书数据详情
const handleBookDataDetail = () => {
  console.log('🔍 Right2 - 点击社区图书数据详情，当前时间范围:', lifeTimeRange.value)
  bookDetailVisible.value = true
}

// ========== 数据更新函数 ==========
// 微互助概况数据获取函数
const getMutualOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('获取微互助概况数据, timeRange:', timeRange)
    mutualOverviewLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const response = await getMutualAidInfoApi({
      areaCode,
      timeType: Number(timeRange)
    }) as any

    console.log('微互助概况API响应:', response)

    if (response.code === 200 && response.message) {
      mutualOverviewData.value = {
        demandTotal: response.message.demandTotal || 0,
        demandEnded: response.message.demandEnded || 0,
        serviceTotal: response.message.serviceTotal || 0,
        serviceEnded: response.message.serviceEnded || 0
      }

      console.log('微互助概况数据更新成功:', mutualOverviewData.value)
    } else {
      throw new Error('API返回数据格式错误')
    }
  } catch (error) {
    console.error('获取微互助概况数据失败:', error)
    // ElMessage.warning('获取微互助概况数据失败，使用模拟数据')

    // 使用模拟数据作为后备
    mutualOverviewData.value = {
      demandTotal: 156,
      demandEnded: 98,
      serviceTotal: 234,
      serviceEnded: 187
    }
  } finally {
    mutualOverviewLoading.value = false
  }
}

// 微互助达成情况分布数据获取函数
const getMutualDistData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('获取微互助达成情况分布数据, timeRange:', timeRange)
    mutualDistLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const response = await getMutualAidListApi({
      areaCode,
      timeType: Number(timeRange),
      type: mutualTabValue.value
    }) as any

    console.log('微互助达成情况分布API响应:', response)

    if (response.code === 200 && response.message?.list) {
      mutualDistData.value = response.message.list.map((item: any) => ({
        district: {
          name: item.district?.name || '未知区域'
        },
        publishCount: item.total || 0,
        completeCount: item.ended || 0
      }))

      console.log('微互助达成情况分布数据更新成功:', mutualDistData.value)
    } else {
      throw new Error('API返回数据格式错误')
    }
  } catch (error) {
    console.error('获取微互助达成情况分布数据失败:', error)
    ElMessage.warning('获取微互助达成情况分布数据失败，使用模拟数据')

    // 使用模拟数据作为后备
    mutualDistData.value = [
      { district: { name: '文君街道' }, publishCount: 45, completeCount: 32 },
      { district: { name: '临邛街道' }, publishCount: 32, completeCount: 25 },
      { district: { name: '羊安街道' }, publishCount: 67, completeCount: 45 }
    ]
  } finally {
    mutualDistLoading.value = false
  }
}

// 微互助服务人数数据获取函数
const getMutualServiceData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('获取微互助服务人数数据, timeRange:', timeRange)
    mutualServiceLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const response = await getMutualAidServiceListApi({
      areaCode,
      timeType: Number(timeRange)
    }) as any

    console.log('微互助服务人数API响应:', response)

    if (response.code === 200 && response.message?.list) {
      mutualServiceData.value = response.message.list.map((item: any) => ({
        district: {
          name: item.district?.name || '未知区域'
        },
        wishData: item.wishParticipants || 0,
        serviceData: item.serviceParticipants || 0
      }))

      console.log('微互助服务人数数据更新成功:', mutualServiceData.value)
    } else {
      throw new Error('API返回数据格式错误')
    }
  } catch (error) {
    console.error('获取微互助服务人数数据失败:', error)
    // ElMessage.warning('获取微互助服务人数数据失败，使用模拟数据')

    // 使用模拟数据作为后备
    mutualServiceData.value = [
      { district: { name: '文君街道' }, wishData: 120, serviceData: 180 },
      { district: { name: '临邛街道' }, wishData: 85, serviceData: 120 },
      { district: { name: '羊安街道' }, wishData: 150, serviceData: 200 },
      { district: { name: '桑园镇' }, wishData: 60, serviceData: 90 },
      { district: { name: '夹关镇' }, wishData: 110, serviceData: 160 }
    ]
  } finally {
    mutualServiceLoading.value = false
  }
}

// 微互助热力图数据获取函数
const getMutualHotData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('获取微互助热力图数据, timeRange:', timeRange)
    mutualHotLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const response = await getMutualAidHotsApi({
      areaCode,
      timeType: Number(timeRange),
      pageNum: 1,
      pageSize: 5
    }) as any

    console.log('微互助热力图API响应:', response)

    if (response.code === 200) {
      // 检查数据是否为空
      if (response.message?.list?.rows && response.message.list.rows.length > 0) {
        mutualHotData.value = response.message.list.rows.map((item: any) => ({
          content: item.title || '未知标题',
          count: item.userCount || 0
        }))
        console.log('微互助热力图数据更新成功:', mutualHotData.value)
      } else {
        // 数据为空时显示暂无数据
        mutualHotData.value = [
          { content: '暂无数据', count: 0 }
        ]
        console.log('微互助热力图数据为空，显示暂无数据')
      }
    } else {
      throw new Error('API返回数据格式错误')
    }
  } catch (error) {
    console.error('获取微互助热力图数据失败:', error)
    ElMessage.warning('获取微互助热力图数据失败')

    // API调用失败时显示暂无数据
    mutualHotData.value = [
      { content: '暂无数据', count: 0 }
    ]
  } finally {
    mutualHotLoading.value = false
  }
}

// 连心桥数据变更函数
const changeMutualOverview = async (value: string) => {
  mutualTimeRange.value = value
  console.log('微互助概览时间变更:', value)
  await getMutualOverviewData(value)
}

const changeMutualDist = async (value: string) => {
  mutualDistTimeRange.value = value
  console.log('微互助分布时间变更:', value)
  await getMutualDistData(value)
}

const changeMutualService = async (value: string) => {
  mutualServiceTimeRange.value = value
  console.log('微互助服务时间变更:', value)
  await getMutualServiceData(value)
}

const handleMutualTabChange = async (val: string) => {
  mutualTabValue.value = val
  console.log('切换微互助tab:', val)
  await getMutualDistData(mutualDistTimeRange.value)
}

const handleMutualHotTabChange = async (val: string) => {
  mutualHotTabValue.value = val
  console.log('切换微互助热力图tab:', val)
  await getMutualHotData(mutualHotTimeRange.value)
}

const changeMutualHot = async (value: string) => {
  mutualHotTimeRange.value = value
  console.log('微互助热力图时间变更:', value)
  await getMutualHotData(value)
}

// 获取社区活动概览数据
const getActivityOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('🔄 获取社区活动概览数据, timeRange:', timeRange)

    const areaCode = customAreaCode || getDistrictCode()
    const params: ActivityInfoParams = {
      areaCode,
      timeType: timeRange
    }

    console.log('🔄 社区活动概览API参数:', params)
    const response = await getActivityInfoApi(params)
    console.log('🔄 社区活动概览API响应:', response)

    if (response.code === 200 && response.message) {
      activityOverviewData.value = {
        activityCount: response.message.total || 0,      // 活动总数
        ongoingCount: response.message.ongoing || 0,     // 进行中活动
        completedCount: response.message.ended || 0      // 已结束活动
      }
      console.log('✅ 社区活动概览数据更新成功:', activityOverviewData.value)
    } else {
      throw new Error(`API返回错误: ${response.msg}`)
    }
  } catch (error) {
    console.error('❌ 获取社区活动概览数据失败:', error)
    ElMessage.warning('获取社区活动概览数据失败')
  }
}

// 获取社区活动热浏览数据
const getActivityHotData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('🔄 获取社区活动热浏览数据, timeRange:', timeRange)
    activityHotLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const params: ActivityHotsParams = {
      areaCode,
      pageNum: 1,
      pageSize: 10,
      timeType: timeRange
    }

    console.log('🔄 社区活动热浏览API参数:', params)
    const response = await getActivityHotsApi(params)
    console.log('🔄 社区活动热浏览API响应:', response)

    if (response.code === 200 && response.message?.list?.rows) {
      // 将API返回的数据转换为EchartsTreemap组件期望的格式
      activityHotData.value = response.message.list.rows.map((item: any) => ({
        content: item.title || '未知活动',
        count: item.userCount || 0  // 使用userCount作为参与人数
      }))
      console.log('✅ 社区活动热浏览数据更新成功:', activityHotData.value)
    } else {
      throw new Error(`API返回错误: ${response.msg}`)
    }
  } catch (error) {
    console.error('❌ 获取社区活动热浏览数据失败:', error)
    ElMessage.warning('获取社区活动热浏览数据失败')
  } finally {
    activityHotLoading.value = false
  }
}

// 获取社区活动浏览及参与人数分布数据
const getActivityDistData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('🔄 获取社区活动浏览及参与人数分布数据, timeRange:', timeRange)
    activityDistLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const params: ActivityListParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange
    }

    console.log('🔄 社区活动分布API参数:', params)
    const response = await getActivityListApi(params)
    console.log('🔄 社区活动分布API响应:', response)

    if (response.code === 200 && response.message?.list) {
      // 将API返回的数据转换为EchartsActivityDistribution组件期望的格式
      activityDistData.value = response.message.list.map((item: any) => ({
        name: item.district?.name || '未知区域',    // 区域名称
        districtName: item.district?.name || '未知区域', // 备用字段
        activityCount: item.total || 0,     // 活动总数 -> activityCount
        count: item.total || 0,             // 活动总数 -> count (备用)
        participantCount: item.participants || 0, // 参与人数 -> participantCount
        userCount: item.participants || 0,  // 参与人数 -> userCount (备用)
        participants: item.participants || 0 // 参与人数 -> participants (备用)
      }))
      console.log('✅ 社区活动分布数据更新成功:', activityDistData.value)
    } else {
      throw new Error(`API返回错误: ${response.msg}`)
    }
  } catch (error) {
    console.error('❌ 获取社区活动分布数据失败:', error)
    ElMessage.warning('获取社区活动分布数据失败')
  } finally {
    activityDistLoading.value = false
  }
}

// 获取热门社区活动表格数据
const getActivityHotTableData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('🔄 获取热门社区活动表格数据, timeRange:', timeRange)
    activityHotTableLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const params: ActivityHotsParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange
    }

    console.log('🔄 热门社区活动表格API参数:', params)
    const response = await getActivityHotsApi(params)
    console.log('🔄 热门社区活动表格API响应:', response)

    if (response.code === 200 && response.message?.list?.rows) {
      // 将API返回的数据转换为ReuseTable组件期望的格式
      activityHotTableData.value = response.message.list.rows.map((item: any) => ({
        districtName: item.districtName || '未知区域',
        title: item.title || '未知活动',
        userCount: item.userCount || 0
      }))
      console.log('✅ 热门社区活动表格数据更新成功:', activityHotTableData.value)
    } else {
      throw new Error(`API返回错误: ${response.msg}`)
    }
  } catch (error) {
    console.error('❌ 获取热门社区活动表格数据失败:', error)
    ElMessage.warning('获取热门社区活动表格数据失败')
  } finally {
    activityHotTableLoading.value = false
  }
}

// 社区圈子 - 社区活动数据变更函数
const changeActivityOverview = (value: string) => {
  activityTimeRange.value = value
  console.log('社区活动概览时间变更:', value)
  getActivityOverviewData(value)
}

const changeActivityHot = (value: string) => {
  activityHotTimeRange.value = value
  console.log('社区活动热浏览时间变更:', value)
  getActivityHotData(value)
}

const changeActivityDist = (value: string) => {
  activityDistTimeRange.value = value
  console.log('社区活动分布时间变更:', value)
  getActivityDistData(value)
}

const changeActivityHotTable = (value: string) => {
  activityHotTableTimeRange.value = value
  console.log('热门社区活动时间变更:', value)
  getActivityHotTableData(value)
}

// 获取社区空间概览数据
const getSpaceOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('🔄 获取社区空间概览数据, timeRange:', timeRange)

    const areaCode = customAreaCode || getDistrictCode()
    const params: SpaceInfoParams = {
      areaCode,
      timeType: timeRange
    }

    console.log('🔄 社区空间概览API参数:', params)
    const response = await getSpaceInfoApi(params)
    console.log('🔄 社区空间概览API响应:', response)

    if (response.code === 200 && response.message) {
      spaceOverviewData.value = {
        itemsCount: response.message.goodsCount || 0,    // 空间数量
        clicksCount: response.message.viewCount || 0     // 浏览量
      }
      console.log('✅ 社区空间概览数据更新成功:', spaceOverviewData.value)
    } else {
      throw new Error(`API返回错误: ${response.msg}`)
    }
  } catch (error) {
    console.error('❌ 获取社区空间概览数据失败:', error)
    ElMessage.warning('获取社区空间概览数据失败')
  }
}

// 社区空间热浏览概况数据获取函数
const getSpaceHotData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('获取社区空间热浏览概况数据, timeRange:', timeRange)
    spaceHotLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const params: SpaceHotParams = {
      areaCode,
      pageNum: 1,
      pageSize: 10,
      timeType: timeRange
    }

    console.log('🔄 社区空间热浏览概况API参数:', params)
    const response = await getSpaceHotList(params) as any
    console.log('🔄 社区空间热浏览概况API响应:', response)

    if (response.code === 200 && response.message?.list?.rows) {
      // 转换数据格式为矩形树图需要的格式
      const transformedData = response.message.list.rows.map((item: any) => ({
        content: item.name,           // 社区空间名称
        count: item.browseNumber      // 浏览数量
      }))

      spaceHotData.value = transformedData
      console.log('✅ 社区空间热浏览概况数据更新成功:', spaceHotData.value)
    } else {
      throw new Error(`API返回错误: ${response.msg}`)
    }
  } catch (error) {
    console.error('❌ 获取社区空间热浏览概况数据失败:', error)
    ElMessage.warning('获取社区空间热浏览概况数据失败')
  } finally {
    spaceHotLoading.value = false
  }
}

// 社区空间浏览及使用人数分布数据获取函数
const getSpaceDistData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('获取社区空间浏览及使用人数分布数据, timeRange:', timeRange)
    spaceDistLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const params: SpaceListParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange
    }

    console.log('🔄 社区空间分布API参数:', params)
    const response = await getSpaceListApi(params)
    console.log('🔄 社区空间分布API响应:', response)

    if (response.code === 200 && response.message?.list) {
      // 转换数据格式为双系列柱状图需要的格式
      const transformedData = response.message.list.map((item: any) => ({
        district: {
          name: item.district?.name || '未知区域'
        },
        name: item.district?.name || '未知区域',  // 兼容字段
        districtName: item.district?.name || '未知区域',  // 兼容字段
        goods: item.goodsCount || 0,              // 社区空间数量
        views: item.viewCount || 0                // 浏览量
      }))

      spaceDistData.value = transformedData
      console.log('✅ 社区空间分布数据更新成功:', spaceDistData.value)
    } else {
      throw new Error(`API返回错误: ${response.msg}`)
    }
  } catch (error) {
    console.error('❌ 获取社区空间分布数据失败:', error)
    ElMessage.warning('获取社区空间分布数据失败')
  } finally {
    spaceDistLoading.value = false
  }
}

// 热门社区空间表格数据获取函数
const getSpaceHotTableData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('获取热门社区空间表格数据, timeRange:', timeRange)
    spaceHotTableLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const params: SpaceHotParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange
    }

    console.log('🔄 热门社区空间表格API参数:', params)
    const response = await getSpaceHotList(params) as any
    console.log('🔄 热门社区空间表格API响应:', response)

    if (response.code === 200 && response.message?.list?.rows) {
      // 转换数据格式为表格需要的格式
      const transformedData = response.message.list.rows.map((item: any) => ({
        districtName: item.districtName || '未知区域',
        name: item.name || '未知空间',
        browseNumber: item.browseNumber || 0
      }))

      spaceHotTableData.value = transformedData
      console.log('✅ 热门社区空间表格数据更新成功:', spaceHotTableData.value)
    } else {
      throw new Error(`API返回错误: ${response.msg}`)
    }
  } catch (error) {
    console.error('❌ 获取热门社区空间表格数据失败:', error)
    ElMessage.warning('获取热门社区空间表格数据失败')
  } finally {
    spaceHotTableLoading.value = false
  }
}

// 社区圈子 - 社区空间数据变更函数
const changeSpaceOverview = (value: string) => {
  spaceTimeRange.value = value
  console.log('社区空间概览时间变更:', value)
  getSpaceOverviewData(value)
}

const changeSpaceHot = (value: string) => {
  spaceHotTimeRange.value = value
  console.log('社区空间热浏览时间变更:', value)
  getSpaceHotData(value)
}

const changeSpaceDist = (value: string) => {
  spaceDistTimeRange.value = value
  console.log('社区空间分布时间变更:', value)
  getSpaceDistData(value)
}

const changeSpaceHotTable = (value: string) => {
  spaceHotTableTimeRange.value = value
  console.log('热门社区空间时间变更:', value)
  getSpaceHotTableData(value)
}

// 社区匠人概览数据获取函数
const getCraftsmanOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('获取社区匠人概览数据, timeRange:', timeRange)

    const areaCode = customAreaCode || getDistrictCode()
    const params: CraftsmanInfoParams = {
      areaCode,
      timeType: timeRange
    }

    console.log('🔄 社区匠人概览API参数:', params)
    const response = await getCraftsmanInfoApi(params)
    console.log('🔄 社区匠人概览API响应:', response)

    if (response.code === 200 && response.message) {
      craftsmanOverviewData.value = {
        itemsCount: response.message.goodsCount || 0,    // 匠人数量
        clicksCount: response.message.viewCount || 0     // 总浏览量
      }
      console.log('✅ 社区匠人概览数据更新成功:', craftsmanOverviewData.value)
    } else {
      throw new Error(`API返回错误: ${response.msg}`)
    }
  } catch (error) {
    console.error('❌ 获取社区匠人概览数据失败:', error)
    ElMessage.warning('获取社区匠人概览数据失败')
  }
}

// 社区匠人热浏览概况数据获取函数
const getCraftsmanHotData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('获取社区匠人热浏览概况数据, timeRange:', timeRange)
    craftsmanHotLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const params: CraftsmanHotParams = {
      areaCode,
      pageNum: 1,
      pageSize: 10,
      timeType: timeRange
    }

    console.log('🔄 社区匠人热浏览概况API参数:', params)
    const response = await getCraftsmanHotListApi(params)
    console.log('🔄 社区匠人热浏览概况API响应:', response)

    if (response.code === 200 && response.message?.list?.rows) {
      // 转换数据格式为矩形树图需要的格式
      const transformedData = response.message.list.rows.map((item: any) => ({
        content: item.name,           // 匠人名称
        count: item.browseNumber      // 浏览数量
      }))

      craftsmanHotData.value = transformedData
      console.log('✅ 社区匠人热浏览概况数据更新成功:', craftsmanHotData.value)
    } else {
      throw new Error(`API返回错误: ${response.msg}`)
    }
  } catch (error) {
    console.error('❌ 获取社区匠人热浏览概况数据失败:', error)
    ElMessage.warning('获取社区匠人热浏览概况数据失败')
  } finally {
    craftsmanHotLoading.value = false
  }
}

// 社区匠人浏览及关注人数分布数据获取函数
const getCraftsmanDistData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('获取社区匠人浏览及关注人数分布数据, timeRange:', timeRange)
    craftsmanDistLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const params: CraftsmanDistributionParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange
    }

    console.log('🔄 社区匠人分布API参数:', params)
    const response = await getCraftsmanDistributionApi(params)
    console.log('🔄 社区匠人分布API响应:', response)

    if (response.code === 200 && response.message?.list) {
      // 转换数据格式为双系列柱状图需要的格式
      const transformedData = response.message.list.map((item: any) => ({
        district: {
          name: item.district?.name || '未知区域'
        },
        name: item.district?.name || '未知区域',  // 兼容字段
        districtName: item.district?.name || '未知区域',  // 兼容字段
        goods: item.goodsCount || 0,              // 社区匠人数量
        views: item.viewCount || 0                // 浏览量
      }))

      craftsmanDistData.value = transformedData
      console.log('✅ 社区匠人分布数据更新成功:', craftsmanDistData.value)
    } else {
      throw new Error(`API返回错误: ${response.msg}`)
    }
  } catch (error) {
    console.error('❌ 获取社区匠人分布数据失败:', error)
    ElMessage.warning('获取社区匠人分布数据失败')
  } finally {
    craftsmanDistLoading.value = false
  }
}

// 热门社区匠人表格数据获取函数
const getCraftsmanHotTableData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('获取热门社区匠人表格数据, timeRange:', timeRange)
    craftsmanHotTableLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const params: CraftsmanHotParams = {
      areaCode,
      pageNum: 1,
      pageSize: 5,
      timeType: timeRange
    }

    console.log('🔄 热门社区匠人表格API参数:', params)
    const response = await getCraftsmanHotListApi(params)
    console.log('🔄 热门社区匠人表格API响应:', response)

    if (response.code === 200 && response.message?.list?.rows) {
      // 转换数据格式为表格需要的格式
      const transformedData = response.message.list.rows.map((item: any) => ({
        districtName: item.districtName || '未知区域',
        name: item.name || '未知匠人',
        browseNumber: item.browseNumber || 0
      }))

      craftsmanHotTableData.value = transformedData
      console.log('✅ 热门社区匠人表格数据更新成功:', craftsmanHotTableData.value)
    } else {
      throw new Error(`API返回错误: ${response.msg}`)
    }
  } catch (error) {
    console.error('❌ 获取热门社区匠人表格数据失败:', error)
    ElMessage.warning('获取热门社区匠人表格数据失败')
  } finally {
    craftsmanHotTableLoading.value = false
  }
}

// 社区圈子 - 社区匠人数据变更函数
const changeCraftsmanOverview = (value: string) => {
  craftsmanTimeRange.value = value
  console.log('社区匠人概览时间变更:', value)
  getCraftsmanOverviewData(value)
}

const changeCraftsmanHot = (value: string) => {
  craftsmanHotTimeRange.value = value
  console.log('社区匠人热浏览时间变更:', value)
  getCraftsmanHotData(value)
}

const changeCraftsmanDist = (value: string) => {
  craftsmanDistTimeRange.value = value
  console.log('社区匠人分布时间变更:', value)
  getCraftsmanDistData(value)
}

const changeCraftsmanHotTable = (value: string) => {
  craftsmanHotTableTimeRange.value = value
  console.log('热门社区匠人表格时间变更:', value)
  getCraftsmanHotTableData(value)
}

// 社区匠人数据详情
const handleCraftsmanDataDetail = () => {
  console.log('🔍 点击社区匠人数据详情，当前时间范围:', craftsmanTimeRange.value)
  craftsmanDetailVisible.value = true
}

// ========== 社区生活数据获取函数 ==========
// 社区生活概况数据获取函数
const getCommunityLifeOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('获取社区生活概况数据, timeRange:', timeRange)

    // 设置加载状态
    lifeOverviewLoading.value = true
    lifeTypeLoading.value = true
    lifeUsageLoading.value = true
    lifeTopicLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const params: CommunityLifeInfoParams = {
      areaCode,
      timeType: Number(timeRange)
    }

    console.log('🔄 社区生活概况API参数:', params)
    const response = await getCommunityLifeInfoApi(params)
    console.log('🔄 社区生活概况API响应:', response)

    if (response.code === 200 && response.data) {
      const data = response.data;

      // 更新社区生活概况数据
      lifeOverviewData.value = {
        totalCount: data.total || 1256,  // 使用接口返回的total字段
        totalViews: data.browseNumber || 8934  // 使用接口返回的browseNumber字段
      };

      // 更新社区圈子类型分布数据（横向柱状图）- scatters
      if (data.scatters && Array.isArray(data.scatters)) {
        lifeTypeData.value = data.scatters.map((item: any) => ({
          name: item.name || '未知类型',
          value: item.total || 0
        }));
        lifeTypeLoading.value = false;
        console.log('📊 Right2 - 圈子类型分布数据已更新:', lifeTypeData.value);
      }

      // 更新社区圈子信息数分布数据（双柱状图）- overviews
      if (data.overviews && Array.isArray(data.overviews)) {
        lifeUsageData.value = data.overviews.map((item: any) => ({
          name: item.name || '未知',  // 街道名称
          posts: item.total || 0,  // 圈子信息数（绿色柱子）
          views: item.browseNumber || 0  // 浏览量（蓝色柱子）
        }));
        lifeUsageLoading.value = false;
        console.log('📊 Right2 - 圈子信息数分布数据已更新:', lifeUsageData.value);
      }

      // 更新社区圈子热门话题分布数据（树状图）- themes
      if (data.themes && Array.isArray(data.themes)) {
        const totalThemes = data.themes.reduce((sum: number, item: any) => sum + (item.total || 0), 0);
        lifeTopicData.value = data.themes.map((item: any) => {
          const value = item.total || 0;
          const percentage = totalThemes > 0 ? Math.round((value / totalThemes) * 100) : 0;
          return {
            name: item.name || '未知话题',
            value: value,
            percentage: `${percentage}%`
          };
        });
        lifeTopicLoading.value = false;
        console.log('📊 Right2 - 热门话题分布数据已更新:', lifeTopicData.value);
        console.log('📊 Right2 - 热门话题原始数据:', data.themes);
      }

      console.log('✅ Right2 - 社区生活数据更新成功:', {
        overview: lifeOverviewData.value,
        types: lifeTypeData.value,
        usage: lifeUsageData.value,
        topics: lifeTopicData.value
      });

      console.log('📊 Right2 - 图表数据详情:');
      console.log('  - 圈子类型分布 (scatters):', data.scatters);
      console.log('  - 圈子信息分布 (overviews):', data.overviews);
      console.log('  - 热门话题分布 (themes):', data.themes);
      if (response.data.scatters) {
        console.log('📊 Right2 - 圈子类型分布:', response.data.scatters)
      }
      if (response.data.overviews) {
        console.log('📊 Right2 - 社区圈子信息分布:', response.data.overviews)
      }

      console.log('✅ 社区生活概况数据更新成功:', lifeOverviewData.value)
    } else {
      console.warn('⚠️ 社区生活概况API返回异常:', response)
      // 保持默认数据
    }
  } catch (error) {
    console.error('❌ 获取社区生活概况数据失败:', error)
    ElMessage.warning('获取社区生活概况数据失败')
    // 保持默认数据
  } finally {
    lifeOverviewLoading.value = false
    lifeTypeLoading.value = false
    lifeUsageLoading.value = false
    lifeTopicLoading.value = false
  }
}

// ========== 社区图书数据获取函数 ==========
const getCommunityBookOverviewData = async (timeRange: string, customAreaCode?: string) => {
  try {
    console.log('📚 Right2 - 开始获取社区图书数据, timeRange:', timeRange)

    // 设置加载状态
    bookOverviewLoading.value = true
    bookTypeLoading.value = true
    bookTrendLoading.value = true
    bookUsageLoading.value = true

    const areaCode = customAreaCode || getDistrictCode()
    const params: CommunityBookInfoParams = {
      areaCode,
      timeType: Number(timeRange)
    }

    console.log('📚 Right2 - 社区图书API请求参数:', params)
    const response = await getCommunityBookInfoApi(params)
    console.log('📚 Right2 - 社区图书API响应:', response)

    if (response.code === 200 && response.data) {
      const data = response.data

      // 更新社区图书概况数据
      bookOverviewData.value = {
        totalCount: data.total || 0,
        totalViews: data.borrowing || 0
      }

      // 更新社区图书类型分布数据
      if (data.scatters && Array.isArray(data.scatters)) {
        const totalTypes = data.scatters.reduce((sum: number, item: any) => sum + (item.count || 0), 0)
        bookTypeData.value = data.scatters.map((item: any) => {
          const value = item.count || 0
          const percentage = totalTypes > 0 ? Math.round((value / totalTypes) * 100) : 0
          return {
            name: item.name || '未知类型',
            value: value,
            percentage: `${percentage}%`
          }
        })
        console.log('📚 Right2 - 图书类型分布数据已更新:', bookTypeData.value)
      }

      // 更新社区图书使用分布数据
      if (data.overviews && Array.isArray(data.overviews)) {
        bookUsageData.value = data.overviews.map((item: any) => ({
          name: item.name || '未知区域',
          posts: item.total || 0,
          views: item.browseNumber || 0  // 修复字段名：borrowNumber -> browseNumber
        }))
        bookUsageLoading.value = false  // 设置加载完成
        console.log('📚 Right2 - 图书使用分布数据已更新:', bookUsageData.value)
        console.log('📚 Right2 - 原始 overviews 数据:', data.overviews)
      }

      // 更新社区图书使用趋势数据
      if (data.trends && Array.isArray(data.trends)) {
        bookTrendData.value = data.trends.map((item: any) => ({
          month: item.name || '未知月份',      // 修复字段名：month -> name
          bookCount: item.total || 0,
          viewCount: item.browseNumber || 0   // 修复字段名：borrowCount -> viewCount, borrowing -> browseNumber
        }))
        bookTrendLoading.value = false        // 设置加载完成
        console.log('📚 Right2 - 图书使用趋势数据已更新:', bookTrendData.value)
      }

      console.log('✅ Right2 - 社区图书数据更新成功')
    } else {
      throw new Error('API返回数据格式错误')
    }
  } catch (error) {
    console.error('❌ 获取社区图书数据失败:', error)
    ElMessage.warning('获取社区图书数据失败')
    // 保持默认数据
  } finally {
    bookOverviewLoading.value = false
    bookTypeLoading.value = false
    bookTrendLoading.value = false
    bookUsageLoading.value = false
  }
}

// 社区匠人资源地图分布
const handleCraftsmanResourceMap = async (customAreaCode?: string | Event) => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = 'craftsman'
    console.log('🎯 Right2Component设置激活撒点类型为: craftsman')

    // 修复：如果传入的是事件对象，则忽略它
    let areaCode: string
    if (typeof customAreaCode === 'string') {
      areaCode = customAreaCode
    } else {
      areaCode = getDistrictCode()
    }

    console.log('🔍 点击社区匠人资源地图分布，当前时间范围:', craftsmanTimeRange.value, '区域代码:', areaCode)

    const response = await getCraftsmanMapResourceApi({
      areaCode,
      timeType: craftsmanTimeRange.value,
      resourceType: 1
    })

    console.log('📊 社区匠人资源地图分布API响应:', response)

    if (response.code === 200) {
      // 通知地图组件进行撒点 - 使用通用的 addResourceMapPoints 函数
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data)
        ElMessage.success(`社区匠人资源地图分布撒点完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个点位`)
      } else {
        console.error('❌ 地图撒点函数未找到')
        ElMessage.error('地图撒点函数未找到')
      }
    } else {
      console.warn('⚠️ 社区匠人资源地图分布API返回异常:', response)
      ElMessage.warning('获取社区匠人资源地图分布数据异常')
    }
  } catch (error) {
    console.error('❌ 获取社区匠人资源地图分布数据失败:', error)
    ElMessage.error('获取社区匠人资源地图分布数据失败')
  }
}

// 社区活动资源地图分布
const handleActivityResourceMap = async (customAreaCode?: string | Event) => {
  try {
    // 设置当前激活的撒点类型
    activeMapPointType.value = 'activity'
    console.log('🎯 Right2Component设置激活撒点类型为: activity')

    // 修复：如果传入的是事件对象，则忽略它
    let areaCode: string
    if (typeof customAreaCode === 'string') {
      areaCode = customAreaCode
    } else {
      areaCode = getDistrictCode()
    }

    console.log('🔍 点击社区活动资源地图分布，当前时间范围:', activityTimeRange.value, '区域代码:', areaCode)

    const response = await getActivityMapResourceApi({
      areaCode,
      timeType: activityTimeRange.value,
      resourceType: 1
    })

    console.log('📊 社区活动资源地图分布API响应:', response)

    if (response.code === 200) {
      // 通知地图组件进行撒点 - 使用通用的 addResourceMapPoints 函数
      if ((window as any).addResourceMapPoints) {
        (window as any).addResourceMapPoints(response.data)
        ElMessage.success(`社区活动资源地图分布撒点完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个点位`)
      } else {
        console.error('❌ 地图撒点函数未找到')
        ElMessage.error('地图撒点函数未找到')
      }
    } else {
      console.warn('⚠️ 社区活动资源地图分布API返回异常:', response)
      ElMessage.warning('获取社区活动资源地图分布数据异常')
    }
  } catch (error) {
    console.error('❌ 获取社区活动资源地图分布数据失败:', error)
    ElMessage.error('获取社区活动资源地图分布数据失败')
  }
}

// 社区活动辖区热力分布
const handleActivityHeatMap = async () => {
  try {
    console.log('🔍 点击社区活动辖区热力分布，当前时间范围:', activityTimeRange.value)

    const areaCode = getDistrictCode()
    const timeType = activityTimeRange.value

    console.log('📊 请求社区活动热力图数据:', { areaCode, timeType, resourceType: 2 })

    // 使用社区活动专门的接口
    const response = await getActivityMapResourceApi({
      areaCode,
      timeType,
      resourceType: 2 // 2: 辖区热力分布
    }) as any

    console.log('📊 社区活动热力图API响应:', response)

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照其他热力分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log('✅ 调用热力图函数，原始数据:', response.data)

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0
        }))

        console.log('✅ 转换后的数据:', convertedData)
        ;(window as any).addTotalHeatMap(convertedData)
        ElMessage.success(`社区活动辖区热力分布加载完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个热力点`)
      } else {
        console.error('❌ 地图热力图函数未找到')
        ElMessage.error('地图热力图函数未找到')
      }
    } else {
      console.warn('⚠️ 社区活动热力图API返回异常:', response)
      ElMessage.warning('获取社区活动热力图数据异常')
    }
  } catch (error: any) {
    console.error('❌ 获取社区活动热力图数据失败:', error)
    ElMessage.error(`获取社区活动热力图数据失败: ${error.message}`)
  }
}

// 社区活动数据详情
const handleActivityDataDetail = () => {
  console.log('🔍 点击社区活动数据详情，当前时间范围:', activityTimeRange.value)
  activityDetailVisible.value = true
}

// 社区匠人辖区热力分布
const handleCraftsmanHeatMap = async () => {
  try {
    console.log('🔍 点击社区匠人辖区热力分布，当前时间范围:', craftsmanTimeRange.value)

    const areaCode = getDistrictCode()
    const timeType = craftsmanTimeRange.value

    console.log('📊 请求社区匠人热力图数据:', { areaCode, timeType, resourceType: 2 })

    // 使用社区匠人专门的接口
    const response = await getCraftsmanMapResourceApi({
      areaCode,
      timeType,
      resourceType: 2 // 2: 辖区热力分布
    }) as any

    console.log('📊 社区匠人热力图API响应:', response)

    if (response.code === 200) {
      // 通知地图组件显示热力图 - 完全按照其他热力分布的模式
      if ((window as any).addTotalHeatMap) {
        console.log('✅ 调用热力图函数，原始数据:', response.data)

        // 转换数据格式：将count字段转换为total字段
        const convertedData = response.data.map((item: any) => ({
          ...item,
          total: item.count || item.total || 0
        }))

        console.log('✅ 转换后的数据:', convertedData)
        ;(window as any).addTotalHeatMap(convertedData)
        ElMessage.success(`社区匠人辖区热力分布加载完成，共 ${Array.isArray(response.data) ? response.data.length : 0} 个热力点`)
      } else {
        console.error('❌ 地图热力图函数未找到')
        ElMessage.error('地图热力图函数未找到')
      }
    } else {
      console.warn('⚠️ 社区匠人热力图API返回异常:', response)
      ElMessage.warning('获取社区匠人热力图数据异常')
    }
  } catch (error: any) {
    console.error('❌ 获取社区匠人热力图数据失败:', error)
    ElMessage.error(`获取社区匠人热力图数据失败: ${error.message}`)
  }
}

// 监听屏幕模式变化
watch(() => fullscreenStore.screenMode, (screenMode) => {
  console.log('Right2 屏幕模式变化:', screenMode)
  if (screenMode === 'wide' || screenMode === 'fullscreen') {
    // 进入宽屏模式或全屏模式，使用store状态控制页面
    const right2Page = fullscreenStore.getRight2Page()
    if (right2Page !== null) {
      currentPage.value = right2Page
      console.log('Right2 进入', screenMode, '模式，页面切换到:', right2Page)
    }
  }
})

// 监听右侧页面变化
watch(() => fullscreenStore.rightPage, (newPage, oldPage) => {
  console.log('Right2 监听到右侧页面变化:', oldPage, '->', newPage, '当前屏幕模式:', fullscreenStore.screenMode)
  if (fullscreenStore.screenMode === 'wide' || fullscreenStore.screenMode === 'fullscreen') {
    const right2Page = fullscreenStore.getRight2Page()
    console.log('Right2 获取到的目标页面:', right2Page)
    if (right2Page !== null) {
      console.log('Right2 准备切换页面:', currentPage.value, '->', right2Page)
      currentPage.value = right2Page
      console.log('Right2 右侧页面变化，已切换到页面:', right2Page)
    }
  }
})

// 监听当前页面变化，自动初始化对应页面的数据
watch(() => currentPage.value, (newPage, oldPage) => {
  console.log('Right2 当前页面变化:', oldPage, '->', newPage)
  if (newPage === 3) {
    // 切换到社区生活页面时，初始化社区生活和图书数据
    console.log('Right2 切换到社区生活页面，初始化数据')
    getCommunityLifeOverviewData(lifeTimeRange.value)
    getCommunityBookOverviewData(lifeTimeRange.value)
  }
})

// 监听右侧页面切换事件（已废弃，保留以防兼容性问题）
const handleRightPageChange = () => {
  console.log('Right2 handleRightPageChange 被调用（已废弃）')
  // 这个函数已经不再需要，因为我们现在使用 store 的响应式状态
}

// 组件挂载
onMounted(() => {
  console.log('Right2Component 组件已挂载，当前页面:', currentPage.value)
  // 监听页面切换事件
  window.addEventListener('rightPageChange', handleRightPageChange)

  // 初始化连心桥数据
  getMutualOverviewData(mutualTimeRange.value)
  getMutualDistData(mutualDistTimeRange.value)
  getMutualServiceData(mutualServiceTimeRange.value)
  getMutualHotData(mutualHotTimeRange.value)

  // 初始化社区活动数据
  getActivityOverviewData(activityTimeRange.value)
  getActivityHotData(activityHotTimeRange.value)
  getActivityDistData(activityDistTimeRange.value)
  getActivityHotTableData(activityHotTableTimeRange.value)

  // 初始化社区空间数据
  getSpaceOverviewData(spaceTimeRange.value)
  getSpaceHotData(spaceHotTimeRange.value)
  getSpaceDistData(spaceDistTimeRange.value)
  getSpaceHotTableData(spaceHotTableTimeRange.value)

  // 初始化社区匠人数据
  getCraftsmanOverviewData(craftsmanTimeRange.value)
  getCraftsmanHotData(craftsmanHotTimeRange.value)
  getCraftsmanDistData(craftsmanDistTimeRange.value)
  getCraftsmanHotTableData(craftsmanHotTableTimeRange.value)

  // 初始化社区生活数据（仅在第3页时调用）
  if (currentPage.value === 3) {
    getCommunityLifeOverviewData(lifeTimeRange.value)
    getCommunityBookOverviewData(lifeTimeRange.value)
  }

  // 注册全局更新函数
  ;(window as any).updateRight2WithTownCode = updateAllInterfacesWithTownCode

  // 注册清除激活状态的全局函数
  ;(window as any).clearActiveMapPointTypeRight2 = () => {
    activeMapPointType.value = ''
    console.log('🧹 Right2Component清除激活的地图撒点类型')
  }
})



// 创建全局更新函数，支持town_code参数
const updateAllInterfacesWithTownCode = (townCode: string) => {
  console.log('🔄 Right2Component开始使用town_code更新所有接口:', townCode)

  // 连心桥相关接口
  getMutualOverviewData(mutualTimeRange.value, townCode)
  getMutualDistData(mutualDistTimeRange.value, townCode)
  getMutualServiceData(mutualServiceTimeRange.value, townCode)
  getMutualHotData(mutualHotTimeRange.value, townCode)

  // 社区活动相关接口
  getActivityOverviewData(activityTimeRange.value, townCode)
  getActivityHotData(activityHotTimeRange.value, townCode)
  getActivityDistData(activityDistTimeRange.value, townCode)
  getActivityHotTableData(activityHotTableTimeRange.value, townCode)

  // 社区空间相关接口
  getSpaceOverviewData(spaceTimeRange.value, townCode)
  getSpaceHotData(spaceHotTimeRange.value, townCode)
  getSpaceDistData(spaceDistTimeRange.value, townCode)
  getSpaceHotTableData(spaceHotTableTimeRange.value, townCode)

  // 社区匠人相关接口
  getCraftsmanOverviewData(craftsmanTimeRange.value, townCode)
  getCraftsmanHotData(craftsmanHotTimeRange.value, townCode)
  getCraftsmanDistData(craftsmanDistTimeRange.value, townCode)
  getCraftsmanHotTableData(craftsmanHotTableTimeRange.value, townCode)

  // 社区图书相关接口
  getCommunityBookInfoData(lifeTimeRange.value, townCode)

  // 根据当前激活的撒点类型，重新请求对应的地图数据
  if (activeMapPointType.value === 'craftsman') {
    console.log('🗺️ Right2Component检测到激活的社区匠人撒点，开始重新请求地图数据')

    // 先清除现有点位
    if ((window as any).clearResourceMapPoints) {
      (window as any).clearResourceMapPoints()
    }

    // 重新请求社区匠人地图数据
    console.log('🔄 重新请求社区匠人地图数据')
    handleCraftsmanResourceMap(townCode)
  } else if (activeMapPointType.value === 'activity') {
    console.log('🗺️ Right2Component检测到激活的社区活动撒点，开始重新请求地图数据')

    // 先清除现有点位
    if ((window as any).clearResourceMapPoints) {
      (window as any).clearResourceMapPoints()
    }

    // 重新请求社区活动地图数据
    console.log('🔄 重新请求社区活动地图数据')
    handleActivityResourceMap(townCode)
  }

  console.log('✅ Right2Component所有接口已使用新的town_code重新请求')
}

// 组件卸载时清理事件监听
onBeforeUnmount(() => {
  window.removeEventListener('rightPageChange', handleRightPageChange)
})
</script>

<style lang="scss" scoped>
.right2-component {
  width: 100%;
  padding-bottom: 100px;/* 增加底部内边距确保最后内容可见 */
  height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;

}

.header-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  border-radius: 0;
  margin-bottom: 20px;
  background: url('@/assets/common/title-bg.png') no-repeat center center;



  .header-back {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.208vw 0.312vw;
    border-radius: 0;
    transition: all 0.3s ease;
    background: #0c162d;
    height: 44px;
    border: 0.026vw solid #24324b;
    width: 72px;
    justify-content: center;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    .back-text {
      color: #1c79e2;
      font-size: 14px;
      font-weight: 500;


    }
  }

  .header-title {
    display: flex;
    align-items: center;
    gap: 0.208vw;

    @media (max-width: 2000px) {
      gap: 0.333vw;
    }

    .title-icon {
      width:19px;
      height:19px;
      border-radius: 0;
      margin-top: -0.130vw;


      img{
        width: 100%;
        height: 100%;
      }
    }

    .title-text {
      background: linear-gradient(0deg, #F7F9FC 0%, #9D9DA6 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-size:20px;
      font-weight: 600;
      letter-spacing: 0.013vw;


    }
  }
}

.module-box {
  border: 0.026vw solid #20497F;
  border-radius: 0.104vw;
  padding: 0.390vw;
  margin-bottom: 0.260vw;
  background: rgba(0, 20, 40, 0.3);

  @media (max-width: 2000px) {
    border: 0.041vw solid #20497F;
    border-radius: 0.166vw;
    padding: 0.625vw;
    margin-bottom: 0.416vw;
  }
}

// 社区圈子选项卡样式
.circle-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 20px;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 10px 16px;
    color: #9D9DA6;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: url('@/assets/common/selected2.png') no-repeat center center;
    background-size: 100% 100%;
    border: none;

    &:hover {
      color: #fff;
    }

    &.active {
      color: #fff;
      background: url('@/assets/common/selected1.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
}
 .action-buttons {
    display: flex;
    gap: 8px;
    .action-btn {
      flex: 1;
      padding: 8px 12px;
      font-size: 12px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 4px;
      // background: transparent;
      // color: #9D9DA6;
      cursor: pointer;
      transition: all 0.3s ease;

    }
     .bg1{
        background: #107C9E;
        color: #fff;
      }
      .bg2{
        background: #FBA602;
        color: #fff;
      }
      .bg3{
        background: #1990FF;
        color: #fff;
      }
  }
</style>
