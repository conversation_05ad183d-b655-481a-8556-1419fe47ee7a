<template>
  <div class="community-quantum">
    <!-- 社区量子标题 -->
    <div class="section-header">
      <div class="section-icon">
        <el-icon><DataAnalysis /></el-icon>
      </div>
      <h3 class="section-title">社区量子</h3>
    </div>

    <!-- 量子统计卡片 -->
    <div class="quantum-cards">
      <div class="quantum-card">
        <div class="card-icon today">
          <el-icon><Calendar /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">今日</div>
          <div class="card-value">{{ quantumData.today }}</div>
        </div>
      </div>
      
      <div class="quantum-card">
        <div class="card-icon week">
          <el-icon><Timer /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">本周</div>
          <div class="card-value">{{ quantumData.thisWeek }}</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <!-- 社区空间利用率 -->
      <div class="chart-item">
        <div class="chart-title">社区空间利用率</div>
        <div class="utilization-list">
          <div class="utilization-item" v-for="item in utilizationData" :key="item.name">
            <div class="utilization-info">
              <span class="utilization-name">{{ item.name }}</span>
              <span class="utilization-value">{{ item.value }}%</span>
            </div>
            <div class="utilization-bar">
              <div 
                class="utilization-progress" 
                :style="{ width: item.value + '%', backgroundColor: item.color }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 微互动服务人数 -->
      <div class="chart-item">
        <div class="chart-title">微互动服务人数</div>
        <v-chart :option="servicePersonOption" class="chart" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { DataAnalysis, Calendar, Timer } from '@element-plus/icons-vue'

// 量子数据
const quantumData = reactive({
  today: '2,156',
  thisWeek: '14,523'
})

// 空间利用率数据
const utilizationData = ref([
  { name: '活动中心', value: 85, color: '#3b82f6' },
  { name: '健身房', value: 72, color: '#10b981' },
  { name: '图书馆', value: 68, color: '#f59e0b' },
  { name: '会议室', value: 91, color: '#ef4444' },
  { name: '休息区', value: 76, color: '#8b5cf6' }
])

// 微互动服务人数图表配置
const servicePersonOption = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    top: '10%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
      axisPointer: {
        type: 'shadow'
      },
      axisLine: {
        lineStyle: {
          color: '#374151'
        }
      },
      axisLabel: {
        color: '#9ca3af',
        fontSize: 10
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '服务人数',
      min: 0,
      max: 1500,
      interval: 300,
      axisLabel: {
        formatter: '{value}',
        color: '#9ca3af'
      },
      axisLine: {
        lineStyle: {
          color: '#374151'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#374151'
        }
      }
    },
    {
      type: 'value',
      name: '满意度',
      min: 80,
      max: 100,
      interval: 5,
      axisLabel: {
        formatter: '{value}%',
        color: '#9ca3af'
      },
      axisLine: {
        lineStyle: {
          color: '#374151'
        }
      }
    }
  ],
  series: [
    {
      name: '服务人数',
      type: 'bar',
      data: [820, 932, 901, 934, 1290, 1330],
      barWidth: '50%',
      itemStyle: {
        color: '#3b82f6',
        borderRadius: [4, 4, 0, 0]
      }
    },
    {
      name: '满意度',
      type: 'line',
      yAxisIndex: 1,
      data: [95.2, 96.1, 95.8, 97.2, 98.1, 97.8],
      itemStyle: {
        color: '#f59e0b'
      },
      lineStyle: {
        width: 3,
        shadowColor: 'rgba(245, 158, 11, 0.4)',
        shadowBlur: 10
      },
      symbol: 'circle',
      symbolSize: 6,
      smooth: true
    }
  ]
})
</script>

<style scoped>
.community-quantum {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3);
}

.section-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, #ef4444, #dc2626);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.section-title {
  color: #00d4ff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.quantum-cards {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.quantum-card {
  flex: 1;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.quantum-card:hover {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.4);
  transform: translateY(-2px);
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.card-icon.today {
  background: linear-gradient(45deg, #ef4444, #dc2626);
}

.card-icon.week {
  background: linear-gradient(45deg, #06b6d4, #0891b2);
}

.card-content {
  flex: 1;
}

.card-title {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin-bottom: 4px;
}

.card-value {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
}

.charts-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-item {
  flex: 1;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 12px;
  padding: 16px;
}

.chart-title {
  color: #00d4ff;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  text-align: center;
}

.chart {
  width: 100%;
  height: 200px;
  min-height: 200px;
}

/* 利用率列表 */
.utilization-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 200px;
  justify-content: space-around;
}

.utilization-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.utilization-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.utilization-name {
  color: #fff;
  font-size: 13px;
  font-weight: 500;
}

.utilization-value {
  color: #00d4ff;
  font-size: 13px;
  font-weight: 600;
}

.utilization-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.utilization-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .quantum-cards {
    flex-direction: column;
  }
  
  .chart {
    height: 180px;
  }
  
  .utilization-list {
    height: 180px;
  }
}
</style> 