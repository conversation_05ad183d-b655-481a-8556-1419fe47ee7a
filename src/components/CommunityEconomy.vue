<template>
  <div class="community-economy">
    <!-- 崃建言标题 -->
    <div class="section-header">
      <div class="section-icon">
        <img src="@/assets/common/icon2.png" alt="崃建言">
      </div>
      <h3 class="section-title">崃建言</h3>
    </div>

    <!-- 经济统计卡片 -->
    <div class="economy-cards">
      <div class="economy-card">
        <div class="card-icon income">
          <el-icon><Money /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">月活动</div>
          <div class="card-value">{{ economyData.monthlyActivity }}</div>
        </div>
      </div>
      
      <div class="economy-card">
        <div class="card-icon facility">
          <el-icon><House /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">设施利用率</div>
          <div class="card-value">{{ economyData.facilityUtilization }}</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <!-- 服务满意度环形图 -->
      <div class="chart-item">
        <div class="chart-title">服务满意度</div>
        <v-chart :option="satisfactionOption" class="chart" />
      </div>

      <!-- 月度活动参与柱状图 -->
      <div class="chart-item">
        <div class="chart-title">月度活动参与</div>
        <v-chart :option="activityOption" class="chart" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { DataLine, Money, House } from '@element-plus/icons-vue'

// 经济数据
const economyData = reactive({
  monthlyActivity: '28',
  facilityUtilization: '78.5%'
})

// 服务满意度环形图配置
const satisfactionOption = ref({
  tooltip: {
    formatter: '{a} <br/>{b}: {c}%'
  },
  series: [
    {
      name: '满意度',
      type: 'gauge',
      center: ['50%', '60%'],
      startAngle: 200,
      endAngle: -40,
      min: 0,
      max: 100,
      splitNumber: 5,
      itemStyle: {
        color: '#58D9F9',
        shadowColor: 'rgba(0,138,255,0.45)',
        shadowBlur: 10,
        shadowOffsetX: 2,
        shadowOffsetY: 2,
      },
      progress: {
        show: true,
        roundCap: true,
        width: 12,
      },
      pointer: {
        icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
        length: '12%',
        width: 20,
        offsetCenter: [0, '-60%'],
        itemStyle: {
          color: 'auto'
        }
      },
      axisLine: {
        lineStyle: {
          width: 12,
          color: [
            [0.3, '#67e0e3'],
            [0.7, '#37a2da'],
            [1, '#fd666d']
          ]
        }
      },
      axisTick: {
        distance: -30,
        splitNumber: 5,
        lineStyle: {
          width: 2,
          color: '#999'
        }
      },
      splitLine: {
        distance: -30,
        length: 14,
        lineStyle: {
          width: 3,
          color: '#999'
        }
      },
      axisLabel: {
        distance: -40,
        color: '#999',
        fontSize: 12
      },
      anchor: {
        show: false
      },
      title: {
        show: false
      },
      detail: {
        valueAnimation: true,
        width: '60%',
        lineHeight: 40,
        borderRadius: 8,
        offsetCenter: [0, '-15%'],
        fontSize: 16,
        fontWeight: 'bolder',
        formatter: '{value} %',
        color: 'inherit'
      },
      data: [
        {
          value: 95.8
        }
      ]
    }
  ]
})

// 月度活动参与图表配置
const activityOption = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    top: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    axisLine: {
      lineStyle: {
        color: '#374151'
      }
    },
    axisLabel: {
      color: '#9ca3af',
      fontSize: 10
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: '#374151'
      }
    },
    axisLabel: {
      color: '#9ca3af'
    },
    splitLine: {
      lineStyle: {
        color: '#374151'
      }
    }
  },
  series: [
    {
      data: [820, 932, 901, 934, 1290, 1330],
      type: 'bar',
      barWidth: '50%',
      itemStyle: {
        color: '#188df0',
        borderRadius: [4, 4, 0, 0]
      }
    }
  ]
})
</script>

<style scoped>
.community-economy {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3);
}

.section-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 100%;
    height: 100%;
  }
}

.section-title {
  color: #00d4ff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.economy-cards {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.economy-card {
  flex: 1;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.economy-card:hover {
  background: rgba(16, 185, 129, 0.15);
  border-color: rgba(16, 185, 129, 0.4);
  transform: translateY(-2px);
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.card-icon.income {
  background: linear-gradient(45deg, #10b981, #059669);
}

.card-icon.facility {
  background: linear-gradient(45deg, #8b5cf6, #7c3aed);
}

.card-content {
  flex: 1;
}

.card-title {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin-bottom: 4px;
}

.card-value {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
}

.charts-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-item {
  flex: 1;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 12px;
  padding: 16px;
}

.chart-title {
  color: #00d4ff;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  text-align: center;
}

.chart {
  width: 100%;
  height: 200px;
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .economy-cards {
    flex-direction: column;
  }
  
  .chart {
    height: 180px;
  }
}
</style> 