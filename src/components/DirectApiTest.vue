<template>
  <div style="padding: 20px; border: 3px solid red; margin: 20px;">
    <h2 style="color: red;">🔥 直接API测试 - 绕过所有逻辑</h2>
    
    <div style="margin: 15px 0;">
      <button @click="testDirect" style="padding: 10px 20px; background: red; color: white; border: none; font-size: 16px;">
        🔥 直接测试接口 (点击这里)
      </button>
    </div>

    <div style="margin: 15px 0; font-family: monospace; font-size: 14px; background: #f0f0f0; padding: 10px;">
      <div v-for="log in logs" :key="log.id">{{ log.message }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import axios from 'axios'

const logs = ref<Array<{id: number, message: string}>>([])
let logId = 0

const addLog = (message: string) => {
  logs.value.push({ id: logId++, message: `[${new Date().toLocaleTimeString()}] ${message}` })
  console.log('🔥', message)
}

const testDirect = async () => {
  logs.value = []
  addLog('🔥 开始直接测试')
  
  try {
    addLog('📍 第一步：直接使用axios调用接口')
    
    const directResponse = await axios.post('https://qlzhsq.qlzhsq.cn:30200/prod-api/cockpit/statistics/storeInfo', {
      areaCode: '510100',
      timeType: 0
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    })
    
    addLog('✅ 直接axios调用成功')
    addLog(`📊 响应数据: ${JSON.stringify(directResponse.data)}`)
    
  } catch (error: any) {
    addLog('❌ 直接axios调用失败')
    addLog(`❌ 错误: ${error.message}`)
    
    if (error.response) {
      addLog(`❌ 状态码: ${error.response.status}`)
      addLog(`❌ 响应数据: ${JSON.stringify(error.response.data)}`)
    }
  }
  
  try {
    addLog('📍 第二步：测试使用我们的suggestionRequest')
    
    // 导入我们的API函数
    const { getStoreInfoApi } = await import('@/api/suggestion')
    
    addLog('📤 调用 getStoreInfoApi...')
    const response = await getStoreInfoApi({
      areaCode: '510100',
      timeType: 0
    })
    
    addLog('✅ suggestionRequest调用成功')
    addLog(`📊 响应数据: ${JSON.stringify(response)}`)
    
  } catch (error: any) {
    addLog('❌ suggestionRequest调用失败')
    addLog(`❌ 错误: ${error.message}`)
    
    if (error.response) {
      addLog(`❌ 状态码: ${error.response.status}`)
      addLog(`❌ 响应数据: ${JSON.stringify(error.response.data)}`)
    }
  }
  
  addLog('🔚 测试完成，请检查浏览器的Network标签页')
}
</script> 