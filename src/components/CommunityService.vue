<template>
  <div class="community-service">
    <!-- 智能物联标题 -->
    <div class="section-header">
      <div class="section-icon">
        <img src="@/assets/common/icon1.png" alt="智能物联">
      </div>
      <h3 class="section-title">智能物联</h3>
    </div>

    <!-- 服务统计卡片 -->
    <div class="service-cards">
      <div class="service-card">
        <div class="card-icon population">
          <el-icon><User /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">总人口</div>
          <div class="card-value">{{ serviceData.totalPopulation }}</div>
        </div>
      </div>
      
      <div class="service-card">
        <div class="card-icon satisfaction">
          <el-icon><Star /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">满意度</div>
          <div class="card-value">{{ serviceData.satisfaction }}</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <!-- 人口统计饼图 -->
      <div class="chart-item">
        <div class="chart-title">人口统计</div>
        <v-chart :option="populationOption" class="chart" />
      </div>

      <!-- 年龄分布柱状图 -->
      <div class="chart-item">
        <div class="chart-title">年龄分布</div>
        <v-chart :option="ageDistributionOption" class="chart" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { OfficeBuilding, User, Star } from '@element-plus/icons-vue'

// 服务数据
const serviceData = reactive({
  totalPopulation: '12,580',
  satisfaction: '95.8%'
})

// 人口统计图表配置
const populationOption = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    bottom: '5%',
    textStyle: {
      color: '#fff'
    }
  },
  series: [
    {
      name: '人口统计',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      data: [
        { value: 6800, name: '男性' },
        { value: 5780, name: '女性' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      itemStyle: {
        borderRadius: 8,
        borderColor: '#1e3a8a',
        borderWidth: 2
      }
    }
  ],
  color: ['#3b82f6', '#ec4899']
})

// 年龄分布图表配置
const ageDistributionOption = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    top: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['0-18岁', '19-35岁', '36-50岁', '51-65岁', '65岁以上'],
    axisLine: {
      lineStyle: {
        color: '#374151'
      }
    },
    axisLabel: {
      color: '#9ca3af',
      fontSize: 10
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: '#374151'
      }
    },
    axisLabel: {
      color: '#9ca3af'
    },
    splitLine: {
      lineStyle: {
        color: '#374151'
      }
    }
  },
  series: [
    {
      data: [2180, 4250, 3480, 2100, 570],
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        color: '#3b82f6',
        borderRadius: [4, 4, 0, 0]
      }
    }
  ]
})
</script>

<style scoped>
.community-service {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.312vw;
  margin-bottom: 0.520vw;
  padding-bottom: 0.312vw;
  border-bottom: 0.052vw solid rgba(59, 130, 246, 0.3);
  
  @media (max-width: 2000px) {
    gap: 0.5vw;
    margin-bottom: 0.833vw;
    padding-bottom: 0.5vw;
    border-bottom: 0.083vw solid rgba(59, 130, 246, 0.3);
  }
}

.section-icon {
  width: 0.9375vw;
  height: 0.9375vw;
  display: flex;
  align-items: center;
  justify-content: center;
  
  @media (max-width: 2000px) {
    width: 1.5vw;
    height: 1.5vw;
  }
  
  img {
    width: 100%;
    height: 100%;
  }
}

.section-title {
  color: #00d4ff;
  font-size: 0.468vw;
  font-weight: 600;
  margin: 0;
  
  @media (max-width: 2000px) {
    font-size: 0.75vw;
  }
}

.service-cards {
  display: flex;
  gap: 0.416vw;
  margin-bottom: 0.520vw;
  
  @media (max-width: 2000px) {
    gap: 0.666vw;
    margin-bottom: 0.833vw;
  }
}

.service-card {
  flex: 1;
  background: rgba(59, 130, 246, 0.1);
  border: 0.026vw solid rgba(59, 130, 246, 0.2);
  border-radius: 0.312vw;
  padding: 0.416vw;
  display: flex;
  align-items: center;
  gap: 0.312vw;
  transition: all 0.3s ease;
  
  @media (max-width: 2000px) {
    border: 0.041vw solid rgba(59, 130, 246, 0.2);
    border-radius: 0.5vw;
    padding: 0.666vw;
    gap: 0.5vw;
  }
}

.service-card:hover {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-0.052vw);
  
  @media (max-width: 2000px) {
    transform: translateY(-0.083vw);
  }
}

.card-icon {
  width: 1.041vw;
  height: 1.041vw;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.468vw;
  
  @media (max-width: 2000px) {
    width: 1.666vw;
    height: 1.666vw;
    font-size: 0.75vw;
  }
}

.card-icon.population {
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
}

.card-icon.satisfaction {
  background: linear-gradient(45deg, #f59e0b, #d97706);
}

.card-content {
  flex: 1;
}

.card-title {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin-bottom: 0.104vw;
  
 
}

.card-value {
  color: #fff;
  font-size: 0.520vw;
  font-weight: 600;
  
  @media (max-width: 2000px) {
    font-size: 0.833vw;
  }
}

.charts-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.520vw;
  
  @media (max-width: 2000px) {
    gap: 0.833vw;
  }
}

.chart-item {
  flex: 1;
  background: rgba(15, 23, 42, 0.6);
  border: 0.026vw solid rgba(59, 130, 246, 0.2);
  border-radius: 0.312vw;
  padding: 0.416vw;
  
  @media (max-width: 2000px) {
    border: 0.041vw solid rgba(59, 130, 246, 0.2);
    border-radius: 0.5vw;
    padding: 0.666vw;
  }
}

.chart-title {
  color: #00d4ff;
  font-size: 0.364vw;
  font-weight: 600;
  margin-bottom: 0.312vw;
  text-align: center;
  
  @media (max-width: 2000px) {
    font-size: 0.583vw;
    margin-bottom: 0.5vw;
  }
}

.chart {
  width: 100%;
  height: 5.208vw;
  min-height: 5.208vw;
  
  @media (max-width: 2000px) {
    height: 8.333vw;
    min-height: 8.333vw;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .service-cards {
    flex-direction: column;
  }
  
  .chart {
    height: 7.5vw;
  }
}
</style> 