<template>
  <div class="community-sharing">
    <!-- 找社区标题 -->
    <div class="section-header">
      <div class="section-icon">
        <img src="@/assets/common/icon3.png" alt="找社区">
      </div>
      <h3 class="section-title">找社区</h3>
    </div>

    <!-- 共享统计卡片 -->
    <div class="sharing-cards">
      <div class="sharing-card">
        <div class="card-icon population">
          <el-icon><UserFilled /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">社区人口</div>
          <div class="card-value">{{ sharingData.totalPopulation }}</div>
        </div>
      </div>
      
      <div class="sharing-card">
        <div class="card-icon activity">
          <el-icon><Trophy /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">活动空间</div>
          <div class="card-value">{{ sharingData.activitySpace }}</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <!-- 社区创新情况 -->
      <div class="chart-item">
        <div class="chart-title">社区创新情况</div>
        <div class="innovation-grid">
          <div class="innovation-item">
            <div class="innovation-icon smart">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="innovation-text">
              <div class="innovation-name">智慧社区</div>
              <div class="innovation-status">已建成</div>
            </div>
          </div>
          <div class="innovation-item">
            <div class="innovation-icon green">
              <el-icon><Sunny /></el-icon>
            </div>
            <div class="innovation-text">
              <div class="innovation-name">绿色环保</div>
              <div class="innovation-status">进行中</div>
            </div>
          </div>
          <div class="innovation-item">
            <div class="innovation-icon culture">
              <el-icon><Reading /></el-icon>
            </div>
            <div class="innovation-text">
              <div class="innovation-name">文化建设</div>
              <div class="innovation-status">规划中</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 微互动参与率分布 -->
      <div class="chart-item">
        <div class="chart-title">微互动参与率分布</div>
        <v-chart :option="participationOption" class="chart" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Share, UserFilled, Trophy, Monitor, Sunny, Reading } from '@element-plus/icons-vue'

// 共享数据
const sharingData = reactive({
  totalPopulation: '12,580',
  activitySpace: '15个'
})

// 微互动参与率分布图表配置
const participationOption = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    top: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['邻里互助', '文化活动', '体育健身', '志愿服务', '环保行动'],
    axisLine: {
      lineStyle: {
        color: '#374151'
      }
    },
    axisLabel: {
      color: '#9ca3af',
      fontSize: 10,
      rotate: 0
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: '#374151'
      }
    },
    axisLabel: {
      color: '#9ca3af',
      formatter: '{value}%'
    },
    splitLine: {
      lineStyle: {
        color: '#374151'
      }
    }
  },
  series: [
    {
      data: [85, 72, 68, 91, 76],
      type: 'bar',
      barWidth: '50%',
      itemStyle: {
        color: '#8b5cf6',
        borderRadius: [4, 4, 0, 0]
      }
    }
  ]
})
</script>

<style scoped>
.community-sharing {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3);
}

.section-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 100%;
    height: 100%;
  }
}

.section-title {
  color: #00d4ff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.sharing-cards {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.sharing-card {
  flex: 1;
  background: rgba(168, 85, 247, 0.1);
  border: 1px solid rgba(168, 85, 247, 0.2);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.sharing-card:hover {
  background: rgba(168, 85, 247, 0.15);
  border-color: rgba(168, 85, 247, 0.4);
  transform: translateY(-2px);
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.card-icon.population {
  background: linear-gradient(45deg, #a855f7, #7c3aed);
}

.card-icon.activity {
  background: linear-gradient(45deg, #f59e0b, #d97706);
}

.card-content {
  flex: 1;
}

.card-title {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin-bottom: 4px;
}

.card-value {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
}

.charts-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-item {
  flex: 1;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(168, 85, 247, 0.2);
  border-radius: 12px;
  padding: 16px;
}

.chart-title {
  color: #00d4ff;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  text-align: center;
}

.chart {
  width: 100%;
  height: 200px;
  min-height: 200px;
}

/* 创新项目网格 */
.innovation-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 200px;
  justify-content: space-around;
}

.innovation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(168, 85, 247, 0.05);
  border: 1px solid rgba(168, 85, 247, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.innovation-item:hover {
  background: rgba(168, 85, 247, 0.1);
  border-color: rgba(168, 85, 247, 0.3);
}

.innovation-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.innovation-icon.smart {
  background: linear-gradient(45deg, #06b6d4, #0891b2);
}

.innovation-icon.green {
  background: linear-gradient(45deg, #10b981, #059669);
}

.innovation-icon.culture {
  background: linear-gradient(45deg, #f59e0b, #d97706);
}

.innovation-text {
  flex: 1;
}

.innovation-name {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.innovation-status {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .sharing-cards {
    flex-direction: column;
  }
  
  .chart {
    height: 180px;
  }
  
  .innovation-grid {
    height: 180px;
  }
}
</style> 