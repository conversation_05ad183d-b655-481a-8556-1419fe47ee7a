declare module 'miniprogram-sm-crypto' {
  export const sm4: {
    encrypt(data: string, key: string): string
    decrypt(data: string, key: string): string
  }
  
  export const sm2: {
    generateKeyPairHex(): { publicKey: string; privateKey: string }
    doEncrypt(msgString: string, publicKey: string, cipherMode?: number): string
    doDecrypt(encryptData: string, privateKey: string, cipherMode?: number): string
  }
  
  export const sm3: {
    sm3(data: string): string
  }
} 