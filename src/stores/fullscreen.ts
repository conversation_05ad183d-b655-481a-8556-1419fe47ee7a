import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useFullscreenStore = defineStore('fullscreen', () => {
  // 是否为全屏模式
  const isFullscreen = ref(false)

  // 屏幕模式：'narrow' | 'wide' | 'fullscreen'
  const screenMode = ref('narrow') // narrow: 窄屏模式(left1+right1), wide: 宽屏模式(left1+left2+right1+right2), fullscreen: 全屏模式

  // 左侧页面状态 (1: 第一屏, 2: 第二屏)
  const leftPage = ref(1)

  // 右侧页面状态 (1: 第一屏, 2: 第二屏)
  const rightPage = ref(1)

  // 设置全屏模式
  const setFullscreen = (value: boolean) => {
    isFullscreen.value = value
    if (value) {
      screenMode.value = 'fullscreen'
    } else {
      // 退出全屏时恢复到之前的模式，默认为窄屏模式
      screenMode.value = screenMode.value === 'fullscreen' ? 'narrow' : screenMode.value
    }
  }

  // 设置屏幕模式
  const setScreenMode = (mode: 'narrow' | 'wide' | 'fullscreen') => {
    screenMode.value = mode
    if (mode === 'fullscreen') {
      isFullscreen.value = true
    } else {
      isFullscreen.value = false
    }
  }

  // 切换到宽屏模式
  const setWideMode = () => {
    setScreenMode('wide')
  }

  // 切换到窄屏模式
  const setNarrowMode = () => {
    setScreenMode('narrow')
  }

  // 切换左侧页面
  const switchLeftPage = () => {
    const oldPage = leftPage.value
    leftPage.value = leftPage.value === 1 ? 2 : 1
    console.log('fullscreen store switchLeftPage:', oldPage, '->', leftPage.value)
  }

  // 切换右侧页面
  const switchRightPage = () => {
    const oldPage = rightPage.value
    if (rightPage.value === 1) {
      rightPage.value = 2
    } else if (rightPage.value === 2) {
      rightPage.value = 3
    } else {
      rightPage.value = 1
    }
    console.log('fullscreen store switchRightPage:', oldPage, '->', rightPage.value)
  }

  // 设置左侧页面
  const setLeftPage = (page: number) => {
    leftPage.value = page
  }

  // 设置右侧页面
  const setRightPage = (page: number) => {
    rightPage.value = page
  }

  // 兼容旧的API（保持向后兼容）
  const fullscreenPage = ref(1)
  const switchFullscreenPage = () => {
    switchRightPage() // 默认切换右侧页面
  }
  const setFullscreenPage = (page: number) => {
    setRightPage(page)
  }

  // 获取左1组件应该显示的页面
  const getLeft1Page = () => {
    if (screenMode.value === 'narrow') {
      return null // 窄屏模式下返回null，组件使用自己的页面状态
    }

    // 宽屏模式和全屏模式使用相同的页面映射
    switch (leftPage.value) {
      case 1: return 1 // 智能物联
      case 2: return 3 // 社区服务
      default: return 1
    }
  }

  // 获取左2组件应该显示的页面
  const getLeft2Page = () => {
    if (screenMode.value === 'narrow') {
      return null // 窄屏模式下返回null，组件使用自己的页面状态
    }

    // 宽屏模式和全屏模式使用相同的页面映射
    switch (leftPage.value) {
      case 1: return 2 // 崃建言
      case 2: return 4 // 社区经济
      default: return 2
    }
  }

  // 获取右1组件应该显示的页面
  const getRight1Page = () => {
    if (screenMode.value === 'narrow') {
      return null // 窄屏模式下返回null，组件使用自己的页面状态
    }

    // 宽屏模式和全屏模式使用相同的页面映射
    switch (rightPage.value) {
      case 1: return 1 // 协商铃
      case 2: return 3 // 社区共享
      case 3: return 5 // 信息公告
      default: return 1
    }
  }

  // 获取右2组件应该显示的页面
  const getRight2Page = () => {
    if (screenMode.value === 'narrow') {
      return null // 窄屏模式下返回null，组件使用自己的页面状态
    }

    // 宽屏模式和全屏模式使用相同的页面映射
    switch (rightPage.value) {
      case 1: return 1 // 连心桥
      case 2: return 2 // 社区圈子
      case 3: return 3 // 社区生活
      default: return 1
    }
  }

  return {
    isFullscreen,
    screenMode,
    leftPage,
    rightPage,
    setFullscreen,
    setScreenMode,
    setWideMode,
    setNarrowMode,
    switchLeftPage,
    switchRightPage,
    setLeftPage,
    setRightPage,
    getLeft1Page,
    getLeft2Page,
    getRight1Page,
    getRight2Page,
    // 兼容旧的API
    fullscreenPage,
    switchFullscreenPage,
    setFullscreenPage
  }
})
