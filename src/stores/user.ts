import { ref } from 'vue'
import { defineStore } from 'pinia'
import { getCommunityUserInfoApi } from '@/api'
import type { UserInfo } from '@/api'
import { cleanInvalidStorage, safeGetStorage, safeSetStorage } from '@/utils/storage'

export const useUserStore = defineStore('user', () => {
  // 用户信息
  const userInfo = ref<UserInfo | null>(null)
  const communityUserInfo = ref<UserInfo | null>(null)
  const token = ref<string>('')
  const isLoggedIn = ref<boolean>(false)

  // 初始化用户信息（从本地存储）
  const initUserInfo = () => {
    // 先清理无效数据
    cleanInvalidStorage()
    
    const storedToken = localStorage.getItem('token')
    const storedUserInfo = safeGetStorage('userInfo')
    const storedCommunityUserInfo = safeGetStorage('communityUserInfo')
    
    if (storedToken && storedToken !== 'undefined' && storedToken !== 'null') {
      token.value = storedToken
      isLoggedIn.value = true
    }
    
    if (storedUserInfo) {
      userInfo.value = storedUserInfo
    }
    
    if (storedCommunityUserInfo) {
      communityUserInfo.value = storedCommunityUserInfo
    }
  }

  // 设置用户信息
  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
    safeSetStorage('userInfo', info)
  }

  // 设置社区用户信息
  const setCommunityUserInfo = (info: UserInfo) => {
    communityUserInfo.value = info
    safeSetStorage('communityUserInfo', info)
  }

  // 设置token
  const setToken = (tokenValue: string) => {
    token.value = tokenValue
    isLoggedIn.value = true
    localStorage.setItem('token', tokenValue)
  }

  // 获取社区用户信息
  const fetchCommunityUserInfo = async () => {
    try {
      const result = await getCommunityUserInfoApi()
      setCommunityUserInfo(result.data)
      return result.data
    } catch (error) {
      console.error('获取社区用户信息失败:', error)
      throw error
    }
  }

  // 清除用户信息（登出）
  const clearUserInfo = () => {
    userInfo.value = null
    communityUserInfo.value = null
    token.value = ''
    isLoggedIn.value = false
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('communityUserInfo')
  }

  // 检查是否有权限
  const hasPermission = (permission: string) => {
    return communityUserInfo.value?.permissions?.includes(permission) || false
  }

  // 检查是否有角色
  const hasRole = (role: string) => {
    return communityUserInfo.value?.roles?.includes(role) || false
  }

  return {
    // 状态
    userInfo,
    communityUserInfo,
    token,
    isLoggedIn,
    
    // 方法
    initUserInfo,
    setUserInfo,
    setCommunityUserInfo,
    setToken,
    fetchCommunityUserInfo,
    clearUserInfo,
    hasPermission,
    hasRole
  }
}) 