# 社区数据大屏展示项目

这是一个基于Vue 3 + TypeScript构建的社区数据大屏展示项目，适用于社区管理、数据展示等场景。

## 🚀 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - JavaScript的超集
- **Vite** - 下一代前端构建工具
- **Vue Router** - Vue.js官方路由管理器
- **Pinia** - Vue状态管理库
- **Element Plus** - Vue 3组件库
- **ECharts** - 数据可视化图表库
- **ESLint** - 代码质量检查工具

## 📊 功能特性

- 🎨 **现代化UI设计** - 采用深色主题，适合大屏展示
- 📈 **丰富的图表类型** - 支持饼图、柱状图、折线图、仪表盘等
- ⏰ **实时时间显示** - 自动更新当前时间
- 📱 **响应式布局** - 适配不同屏幕尺寸
- 🔄 **数据实时更新** - 支持数据动态刷新
- 🎯 **模块化设计** - 易于扩展和维护

## 🛠️ 安装和运行

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

访问 `http://localhost:5173` 查看项目

### 生产构建

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 📁 项目结构

```
src/
├── components/          # 可复用组件
├── views/              # 页面组件
│   └── BigScreenView.vue  # 大屏主页面
├── router/             # 路由配置
├── stores/             # 状态管理
├── assets/             # 静态资源
└── main.ts            # 应用入口
```

## 🎯 页面布局

大屏页面采用经典的三栏布局：

- **顶部区域**: 标题和时间显示
- **左侧面板**: 人口统计、年龄分布图表
- **中央面板**: 社区地图区域
- **右侧面板**: 服务满意度、活动参与图表
- **底部区域**: 关键数据指标卡片

## 🔧 自定义配置

### 修改图表数据

在 `src/views/BigScreenView.vue` 中找到对应的图表配置对象，修改 `data` 数组即可：

```javascript
// 例如修改人口统计数据
const populationOption = {
  series: [{
    data: [
      { value: 548, name: '男性' },
      { value: 710, name: '女性' }
    ]
  }]
}
```

### 添加新的图表

1. 在 `main.ts` 中导入所需的ECharts组件
2. 在页面中创建新的图表配置对象
3. 在模板中使用 `<v-chart>` 组件

### 修改主题色彩

在组件的 `<style>` 部分修改CSS变量或直接修改样式类。

## 🚀 部署

### 静态部署

构建完成后，将 `dist` 目录部署到任何静态文件服务器即可。

### Docker部署

```bash
# 构建镜像
docker build -t community-bigscreen .

# 运行容器
docker run -p 8080:80 community-bigscreen
```

## 📝 开发指南

### 添加新页面

1. 在 `src/views/` 目录下创建新的Vue组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 根据需要添加导航链接

### 状态管理

使用Pinia进行状态管理，在 `src/stores/` 目录下创建store文件。

### API接口

建议在 `src/api/` 目录下创建API接口文件，统一管理数据请求。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## �� 许可证

MIT License
